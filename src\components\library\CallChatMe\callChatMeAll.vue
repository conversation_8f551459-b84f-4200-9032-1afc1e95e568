<template>
  <div>
    <!-- Breadcrumb User Detail -->
    <v-breadcrumbs :items="itemsBreadcrumb" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
      <template v-slot:divider>
        <v-icon color="#3EC6B6">mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item
          href=""
          :disabled="item.disabled"
          @click="gotoBannerPage(item)"
        >
          <span :style="{ color: item.color, cursor: item.disabled !== true ? 'pointer' : 'none', fontSize: '16px' }" v-snip="1">{{ item.category_name }}</span>
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <div class="container">
      <v-row>
        <v-col cols="12" class="ml-6">
          <span style="font-weight: bold; font-size: 24px; line-height: 32px;color: rgb(51, 51, 51);">
            <v-icon color="#27AB9C" @click="backtoUserProfile()">mdi-chevron-left</v-icon> {{ $t('MenuBuyer.menuMyChat') }}
          </span>
        </v-col>
      </v-row>
      <v-row  class="px-4">
        <v-col cols="12">
          <iframe id='iframechatbot' frameborder='0' style='background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 600px; max-width: 100%; max-height: 610px; margin: 0px; overflow: hidden; display: block;' src=""></iframe>
        </v-col>
      </v-row>
    </div>
  <!-- <div class="row no-gutters mt-6 mx-4" style="border: 1px solid #e3e3e3;">
      <div class="col-md-4 border-right" style="max-height: 610px; overflow-y: auto; border: 1px solid #e3e3e3;">
      <div class="settings-tray">
          <img class="profile-image-h my-1 mt-1" src="@/assets/chatbot-cartoon.png" alt="Profile img">
          <span class="settings-tray--right"> -->
          <!-- <i class="material-icons">cached</i>
          <i class="material-icons">message</i>
          <i class="material-icons">menu</i> -->
          <!-- </span>
      </div>
      <div class="search-box-">
          <div class="input-wrapper-"> -->
          <!-- <i class="material-icons">search</i> -->
          <!-- <v-text-field
              v-model="search"
              class="mx-2 mt-2"
              label="ค้นหา"
              append-outer-icon="mdi-magnify"
            ></v-text-field> -->
          <!-- <input placeholder="Search here" type="text"> -->
          <!-- </div>
      </div>
      <span v-if="listShowChats.length !== 0">
      <div v-for="(item, index) in listShowChats" :key="index">
              <div @click="changeChatMe(item)" class="friend-drawer friend-drawer--onhover" >
                  <v-avatar class="profile-image-user" style="margin-top: -9px; margin-left: -9px;">
                <v-img
                style="width: 50px; height: auto;"
                  :alt="item.botName"
                  :src="item.ImgShop"
                ></v-img>
              </v-avatar>
                  <div class="text">
                  <h6>{{ item.botName }}</h6> -->
                  <!-- <p class="text-muted">Hey, you're arrested!</p> -->
                  <!-- </div> -->
                  <!-- <span class="time text-muted small">13:21</span> -->
              <!-- </div>
              <hr>
          </div>
        </span>
        <span v-else>
          <div style="height: 610px;">
          <div style="display: flex; justify-content: center;">
            <v-img
            src="@/assets/chatbot.png"
            width="100%"
            max-width="150px"
            />
          </div>
          <div style="display: flex; justify-content: center; font-size: 16px; font-weight: 700;"><div style="margin-left: -30px;">ยังไม่มีแชท</div>
          </div>
        </div>
        </span>
      </div>
      <div class="col-md-8"> -->
      <!-- <div class="settings-tray">
          <div class="friend-drawer no-gutters friend-drawer--grey">
          <img class="profile-image" src="https://www.clarity-enhanced.net/wp-content/uploads/2020/06/robocop.jpg" alt="">
          <div class="text">
              <h6>Robo Cop</h6>
              <p class="text-muted">Layin' down the law since like before Christ...</p>
          </div>
          <span class="settings-tray--right">
              <i class="material-icons">cached</i>
              <i class="material-icons">message</i>
              <i class="material-icons">menu</i>
          </span>
          </div>
      </div> -->
      <!-- <div v-if="listShowChats.length !== 0" class="chat-panel">
          <iframe id='iframechatbot' frameborder='0' style='background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 600px; max-width: 100%; max-height: 610px; margin: 0px; overflow: hidden; display: block;' src=""></iframe>
      </div>
      <div v-else>
        <div class="pt-1" style="background: #2562af; height: 55px;">
        </div>
        <div class="" style="margin-top: 30%;">
        <div style="display: flex; justify-content: center;">
            <v-img
            src="@/assets/bubble-chat.png"
            width="100%"
            max-width="150px"
            />
          </div>
      </div>
    </div>
      <div >

      </div>
      </div>
  </div>
  </div> -->
  <!-- </v-container> -->
  </div>
</template>
<script>
import { createChat } from './callChatMe'
import { Decode } from '@/services'
// import '@fortawesome/fontawesome-free/css/all.css'
// import '@fortawesome/fontawesome-free/js/all.js'
export default {
  data () {
    return {
      oneData: null,
      itemsBreadcrumb: [],
      status: false,
      search: '',
      listChat: []
    }
  },
  async created () {
    this.status = localStorage.getItem('oneData') !== null
    if (this.status) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // console.log('oneData', this.oneData)
    }
    this.getBreadcrumb()
    await this.initChat()
  },
  computed: {
    listShowChats () {
      if (this.search !== '') {
        return this.listChat.filter(item => {
          return item.shopNGSName.toLowerCase().includes(this.search.toLocaleLowerCase())
        })
      } else {
        return this.listChat
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  mounted () {
    this.initChat()
  },
  methods: {
    getBreadcrumb () {
      this.itemsBreadcrumb = []
      this.itemsBreadcrumb = [
        {
          category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
          id: 0,
          disabled: false,
          color: '#636363',
          href: '/'
        },
        {
          category_name: `${this.$t('MenuBuyer.titleUserProfile')}`,
          id: 1,
          disabled: false,
          color: '#636363',
          href: '/userprofile'
        },
        {
          category_name: `${this.$t('MenuBuyer.menuMyChat')}`,
          id: 3,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        }
      ]
    },
    gotoBannerPage (val) {
      this.$router.push({ path: `${val.href}` }).catch(() => {})
    },
    async initChat () {
      this.$store.commit('openLoader')
      const sharetoken = await createChat.sharetoken()
      const req = {
        user_id: this.oneData.user.user_id
      }
      await this.$store.dispatch('actionslistBotChatWithUser', req)
      var res = await this.$store.state.ModuleHompage.statelistBotChatWithUser
      if (res.result === 'SUCCESS') {
        this.listChat = await res.data.listChat
        // document.getElementById('iframechatbot').src = await 'https://uatchat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin?type=user&bot_id=' + this.listChat[0].botID + '&sharetoken=' + sharetoken.data.shared_token
        document.getElementById('iframechatbot').src = await 'https://chat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin/authen?type=user&sharetoken=' + sharetoken.data.shared_token
        // document.getElementById('iframechatbot').src = await 'https://uatchat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin/authen?type=user&sharetoken=' + sharetoken.data.shared_token
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
      }
      // console.log(res, 'res')
    },
    async changeChatMe (item) {
      const sharetoken = await createChat.sharetoken()
      // document.getElementById('iframechatbot').src = await 'https://uatchat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin?type=user&bot_id=' + item.botID + '&sharetoken=' + sharetoken.data.shared_token
      document.getElementById('iframechatbot').src = await 'https://chat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin/authen?type=user&sharetoken=' + sharetoken.data.shared_token
    },
    backtoUserProfile () {
      if (this.MobileSize) {
        this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/userprofile' }).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>
.backgroundPage{
  background-color: #F6F6F6;
}
.container {
  max-width: 100%;
}
.v-breadcrumbs {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  flex: 0 1 auto;
  white-space: nowrap;
  list-style-type: none;
  margin-bottom: 24px;
  /* padding: 8px 0px 8px 75px !important; */
}
.v-breadcrumbs__item  {
  color: #27AB9C !important;
}
.v-breadcrumbs li .v-icon {
  color: #27AB9C !important;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
}
</style>

<style scoped lang="scss">
$blue: #74b9ff;
.container {
  margin: 0px auto;
  background: #ffffff;
  padding: 0;
  border-radius: 7px;
}

.profile-image-user {
  width: 50px;
  height: auto;
  border-radius: 40px;
}
.profile-image {
  width: 50px;
  height: 50px;
  border-radius: 40px;
}
.profile-image-h {
  width: 40px;
  height: 40px;
}
.settings-tray {
  background: #2562af;
  padding: 3px 15px 3px 7px;
//   border-radius: 7px;
  .no-gutters {
    padding: 0;
  }
  &--right {
    float: right;
    i {
      margin-top: 10px;
      font-size: 25px;
      color: grey;
      margin-left: 14px;
      transition: .3s;
      &:hover {
        color: $blue;
        cursor: pointer;
      }
    }
  }
}
.search-box {
  background: #fafafa;
  padding: 10px 13px;
  .input-wrapper {
    background: #fff;
    border-radius: 40px;
    i {
      color: grey;
      margin-left: 7px;
      vertical-align: middle;
    }
  }
}
input {
  border: none;
  border-radius: 30px;
  width: 80%;
  &::placeholder {
    color: #e3e3e3;
    font-weight: 300;
    margin-left: 20px;
  }
  &:focus {
    outline: none;
  }
}
.friend-drawer {
  padding: 10px 15px;
  display: flex;
  vertical-align: baseline;
  background: #fff;
  transition: .3s ease;
  &--grey {
    background: #eee;
  }
  .text {
    margin-left: 12px;
    width: 70%;
    h6 {
      margin-top: 6px;
      margin-bottom: 0;
    }
    p {
      margin: 0;
    }
  }
  .time {
    color: grey;
  }
  &--onhover:hover {
    background: $blue;
    cursor: pointer;
    p,
    h6,
    .time {
      color: #fff !important;
    }
  }
}
hr {
  margin: 5px auto;
  width: 100%;
}
.chat-bubble {
  padding: 10px 14px;
  background: #eee;
  margin: 10px 30px;
  border-radius: 9px;
  position: relative;
  animation: fadeIn 1s ease-in;
  &:after {
    content: '';
    position: absolute;
    top: 50%;
    width: 0;
    height: 0;
    border: 20px solid transparent;
    border-bottom: 0;
    margin-top: -10px;
  }
  &--left {
     &:after {
      left: 0;
      border-right-color: #eee;
      border-left: 0;
      margin-left: -20px;
      }
  }
  &--right {
    &:after {
      right: 0;
      border-left-color: $blue;
      border-right: 0;
      margin-right: -20px;
    }
  }
}
@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
.offset-md-9 {
  .chat-bubble {
    background: $blue;
    color: #fff;
  }
}
.chat-box-tray {
  background: #eee;
  display: flex;
  align-items: baseline;
  padding: 10px 15px;
  align-items: center;
  margin-top: 19px;
  bottom: 0;
  input {
    margin: 0 10px;
    padding: 6px 2px;
  }
  i {
    color: grey;
    font-size: 30px;
    vertical-align: middle;
    &:last-of-type {
      margin-left: 25px;
    }
  }
}
</style>
