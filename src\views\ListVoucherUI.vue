<template>
  <div>
    <v-breadcrumbs :items="items" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FAFAFA' }">
      <template v-slot:divider>
        <v-icon color="#27AB9C">mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }" >
        <v-breadcrumbs-item
          href=""
          :disabled="item.disabled"
          @click="gotoBannerPage(item)"
        >
          <span :style="{ color: item.color, cursor: item.disabled !== true ? 'pointer' : 'none', fontSize: '16px' }" v-snip="2">{{ item.category_name }}</span>
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <v-container class="pa-0 pt-4">
      <v-col cols="12" class="pa-0">
        <v-row no-gutters>
          <v-col :cols="MobileSize? '12':IpadSize ? '9' : '10'" class="pa-0">
          <div class="group searchAll">
            <!-- <v-col cols="12" class="pa-0 searchAll"> -->
            <svg viewBox="0 0 24 24" aria-hidden="true" class="icon">
              <g>
                <path
                  d="M21.53 20.47l-3.66-3.66C19.195 15.24 20 13.214 20 11c0-4.97-4.03-9-9-9s-9 4.03-9 9 4.03 9 9 9c2.215 0 4.24-.804 5.808-2.13l3.66 3.66c.*************.53.22s.385-.073.53-.22c.295-.293.295-.767.002-1.06zM3.5 11c0-4.135 3.365-7.5 7.5-7.5s7.5 3.365 7.5 7.5-3.365 7.5-7.5 7.5-7.5-3.365-7.5-7.5z"
                ></path>
              </g>
            </svg>
            <input v-model="search" @keypress="getSearch($event, 'keyup')" class="input" type="search" placeholder="Search" />
            <v-btn dark height="40px" class="BtnSearch" @click="getSearch()" color="#27AB9C">ค้นหา</v-btn>
            <!-- </v-col> -->
          </div>
          </v-col>
          <v-col :cols=" MobileSize ? '12':IpadSize? '3' : '2'" :class="MobileSize? 'pt-4' : 'pl-2'">
          <v-autocomplete
            ref="selectProvince"
            dense outlined
            class="autocompleteInput"
            @change="getSearch($event, 'selectProvince')"
            v-model="itemProvince"
            :items="dataProvince"
            label="เลือกจังหวัดที่ค้นหา"
            no-data-text="ไม่มีจังหวัดที่คุณค้นหา"
          >
          </v-autocomplete>
        </v-col>
      </v-row>
      </v-col>
    </v-container>
    <v-container grid-list-xs>
      <v-overlay :value="overlay2">
        <v-progress-circular indeterminate size="64"></v-progress-circular>
      </v-overlay>
      <v-row dense align-content="center" justify="center">
        <v-col cols="12" md="12" xs="12">
          <v-row class="mt-2">
            <v-spacer class="spacerStyleProductInDetailPage"></v-spacer>
            <v-chip
            class="ma-2"
            color="#BDE7D9"
            label
            >
              <h2 v-if="header === 'สินค้ายอดนิยม'" class="pt-3 fontHeaderListProduct">{{headerCategory}}</h2>
              <h2 v-else class="pt-3 fontHeaderListProduct">{{header}}</h2>
            </v-chip>
            <v-spacer class="spacerStyleProductInDetailPage"></v-spacer>
          </v-row>
        </v-col>
      </v-row>
      <div v-if="showSkeletonLoader === true">
        <v-row dense class="pt-12">
          <v-col cols="6" md="2" sm="3" xs="4" v-for="item in 6" :key="item">
            <v-skeleton-loader
              type="image, list-item-two-line"
            ></v-skeleton-loader>
          </v-col>
        </v-row>
      </div>
      <div v-else>
        <v-row  class="pt-10" no-gutters justify="center" v-if="AllProduct.length === 0">ยังไม่มีเวาเชอร์</v-row>
        <v-row justify="start" class="pt-12"  v-if="AllProduct.length !== 0 && !MobileSize && !IpadSize">
          <v-col cols="12" md="2" sm="3" xs="4" v-for="(item, index) in paginated" :key="index">
            <CardProducts :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="start" class="pt-12"  v-if="AllProduct.length !== 0 && !MobileSize && IpadSize">
          <v-col cols="12" md="2" sm="3" xs="6" v-for="(item, index) in paginated" :key="index">
            <CardProductsResponsive :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="start" class="pt-4"  v-if="AllProduct.length !== 0 && MobileSize && !IpadSize">
          <v-col cols="6" md="6" sm="6" xs="6" v-for="(item, index) in paginated" :key="index">
            <CardProductsResponsive :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="center" class="my-6">
          <v-pagination
          color="#27AB9C"
          v-model="pageNumber"
          :length="pageMax"
          :total-visible="7"
          @input="pageChange($event)"
          ></v-pagination>
        </v-row>
      </div>
      <!-- <a-row type="flex" :gutter="[16, 8]">
        <a-col :span="24" style="text-align:center">
          <span class="display-1">{{header}}</span>
        </a-col>
        <a-col :span="24">
          <span style="font-weight: bold;">ค้นพบสินค้า {{ productCount }} รายการสำหรับ "{{ header }}"</span>
        </a-col>
        <a-col :span="24" style="padding: 0; margin-top: -20px;">
          <a-divider></a-divider>
        </a-col>
        <div v-if="AllProduct.length !== 0">
          <a-col :span="24" :md="4" :sm="12" :xs="24" v-for="(item,index) in AllProduct" :key="index">
            <CardProducts :itemProduct="item" />
          </a-col>
        </div>
        <div v-else>
          <h2>ยังไม่มีรายการสินค้า{{ header }}</h2>
        </div>
      </a-row> -->
    </v-container>
  </div>
</template>

<script>
import { Decode } from '@/services'
import provinces from '@/enum/province'
// import { msgErr } from '@/enum/GetError'
const FakeData = []
for (let i = 0; i < 48; i++) {
  FakeData.push({
    product_id: i,
    name: `Data Title newArrivals ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
export default {
  components: {
    CardProducts: () => import('@/components/Card/ProductCardUI'),
    CardProductsResponsive: () => import('@/components/Card/ProductCardResponsive')
  },
  data () {
    return {
      toFrom: false,
      selectedProvinceEng: '',
      province: '',
      dataAll: [],
      dataProvince: [],
      itemProvince: '',
      search: '',
      idCat: '359',
      limit: 48,
      category: false,
      // headerId: 1,
      checkPageCategory: false,
      RowUserData: '',
      pathShopSale: '',
      header: 'เวาเชอร์',
      headerCategory: 'เวาเชอร์',
      valueID: '',
      FakeData,
      overlay2: false,
      productCount: null,
      AllProduct: [],
      pageMax: null,
      current: 1,
      pageSize: 48,
      shopID: null,
      hierachy: '',
      typeproduct: '',
      dataRole: '',
      items: [
        {
          category_name: 'หน้าแรก',
          disabled: false,
          color: '#636363',
          href: '/'
        },
        {
          category_name: 'เวาเชอร์',
          disabled: false,
          color: '#636363',
          href: '/'
        }
      ],
      // itemsPop: [
      //   {
      //     category_name: 'หน้าแรก',
      //     disabled: false,
      //     color: '#636363',
      //     href: '/'
      //   },
      //   {
      //     category_name: 'สินค้ายอดนิยม',
      //     disabled: false,
      //     color: '#27AB9C',
      //     href: '/'
      //   }
      // ],
      showSkeletonLoader: false
    }
  },
  watch: {
    async $route (to, from) {
      this.toFrom = true
      // console.log('to====11>', to)
      // console.log('from====>11', from)
      var getIDToParams = to.query.page
      var getIDFromParams = from.query.page
      var getNameToParams = to.query.province
      var getNameFromParams = from.query.province
      // console.log('getNameToParams', getNameToParams)
      // console.log('getNameFromParams', getNameFromParams)
      if (getIDFromParams !== undefined && getIDToParams !== undefined) {
        if ((parseInt(getIDToParams) !== parseInt(getIDFromParams)) || (getNameToParams !== getNameFromParams)) {
          this.province = this.$route.query.province
          this.pageNumber = parseInt(this.$route.query.page)
          this.itemProvince = this.province
          this.translate(this.itemProvince)
          // this.pageChange(this.pageNumber)
          this.getProvince('pathChange')
          // this.pageChange(this.pageNumber)
          // this.getVoucher()
        }
      }
    }
    // itemProvince (val) {
    //   console.log('val====1aaaaaaaaa>', val)
    //   var text = this.translate(val)
    //   const encodedEvent = encodeURIComponent(text)
    //   const fullPath = `/ListVoucher/go?&province=${encodedEvent}&page=${this.pageNumber}`
    //   this.$router.push({ path: `${fullPath}` }).catch(() => {})
    // }
  },
  async created () {
    // console.log('header------>', provinces)
    if (this.$route.query.page !== undefined) {
      this.pageNumber = parseInt(this.$route.query.page)
    }
    this.showSkeletonLoader = true
    this.oneData = []
    if (localStorage.getItem('roleUser') !== null) {
      this.dataR = JSON.parse(localStorage.getItem('roleUser')).role
    } else {
      this.dataR = 'ext_buyer'
    }
    if (this.dataR === 'sale_order' || this.dataR === 'sale_order_no_JV') {
      this.pathShopSale = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale'))).path
      // console.log('pathShop', this.pathShopSale)
    }
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (localStorage.getItem('roleUser') !== null) {
        this.RowUserData = JSON.parse(localStorage.getItem('roleUser')).role
      }
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.companyId = localStorage.getItem('PartnerID')
        this.pathShopSale = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale'))).path
      } else {
        this.companyId = ''
      }
    }
    this.$EventBus.$emit('getPath')
    this.keyWorld = ''
    this.typeProduct = this.header
    this.province = this.$route.query.province
    // this.selectedProvinceEng = this.$route.query.province
    this.itemProvince = this.province
    // console.log('this.itemProvince', this.itemProvince)
    this.translate(this.itemProvince)
    if (this.$route.query.page !== undefined) {
      this.pageNumber = parseInt(this.$route.query.page)
    } else {
      this.pageNumber = 1
      const fullPath = `/ListVoucher/go?&province=${this.province}&page=1`
      this.$router.push({ path: `${fullPath}` }).catch(() => {})
    }
    // if (this.toFrom === false) {
    await this.getProvince()
    await this.getVoucher()
    // }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  // computed: {
  //   pageNumber: {
  //     get () {
  //       return parseInt(this.$route.query.pageNumber) || 1
  //     },
  //     set (newPage) {
  //       this.$router.push(`/ListProductUI/${this.header}?pageNumber=${newPage}`).catch(() => {})
  //     }
  //   }
  // },
  computed: {
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      // console.log('this.AllProduct', this.AllProduct.slice(this.indexStart, this.indexEnd))
      // return this.AllProduct.slice(this.indexStart, this.indexEnd)
      return this.AllProduct
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    gotoBannerPage (val) {
      var dataR = JSON.parse(localStorage.getItem('roleUser')).role
      if (dataR === 'sale_order' || dataR === 'sale_order_no_JV') {
        this.$router.push({ path: this.pathShopSale }).catch(() => {})
      } else {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    },
    async getProvince (val) {
      this.dataProvince = []
      var roleUser = ''
      if (localStorage.getItem('roleUser')) {
        roleUser = JSON.parse(localStorage.getItem('roleUser')).role
      } else {
        roleUser = 'ext_buyer'
      }
      var data = {
        role_user: roleUser
      }
      await this.$store.dispatch('actionsGetProvince', data)
      var res = await this.$store.state.ModuleShop.stateGetProvince
      if (res.ok === 'y') {
        this.dataAll = res.query_result
        this.dataAll.push({ id: 359, category_name: 'ทั้งหมด' })
        this.dataAll.forEach((e, i) => {
          if (e.category_name !== undefined) {
            this.dataProvince.push(e.category_name)
          }
        })
        this.getCatID(val)
      } else {
        this.dataProvince = []
      }
      // console.log('dataProvince', this.dataProvince)
    },
    getCatID (val) {
      if (this.dataAll) {
        // console.log('this.itemProvinceแฟะะะ', this.itemProvince)
        this.dataAll.forEach(e => {
          if (e.category_name === this.itemProvince) {
            this.idCat = e.id
          } else if (this.itemProvince === 'ทั้งหมด' || this.itemProvince === '' || this.itemProvince === 'All') {
            this.idCat = e.id
            // console.log('e.id', e.id)
          }
        })
      }
      if (val === 'pathChange') {
        // console.log('this.val', this.pageNumber, this.idCat)
        this.pageChange(this.pageNumber)
      }
      // console.log('item====>', this.itemProvince, this.idCat)
    },
    translate (val) {
      // console.log('vallltranslate=====>', val)
      // this.selectedProvinceEng = ''
      if (val !== '' && val !== 'ทั้งหมด' && val !== 'All') {
        // console.log('1')
        provinces.forEach(e => {
          if (e.thai === val) {
            this.selectedProvinceEng = e.english
            this.itemProvince = e.thai
          } else if (val === e.english) {
            this.selectedProvinceEng = e.english
            this.itemProvince = e.thai
          }
        })
      } else {
        // console.log('2')
        this.selectedProvinceEng = 'All'
      }
      // console.log('this.selectedProvinceEng', this.selectedProvinceEng)
      return this.selectedProvinceEng
    },
    async getSearch (event, text) {
      // console.log('event', event)
      if (this.itemProvince === '' || this.itemProvince === 'ทั้งหมด') {
        this.itemProvince = 'All'
      }
      if (event !== undefined) {
        if (text === 'selectProvince') {
          this.pageNumber = 1
          this.itemProvince = event
          this.getCatID()
          // await this.getVoucher()
          this.pathChange()
        } else {
          if (event.key === 'Enter') {
            this.getVoucher()
            // this.pathChange()
          }
        }
      } else {
        this.getVoucher()
      }
      this.$refs.selectProvince.blur()
      // console.log('this.itemProvince', this.itemProvince)
      // console.log('this.dataProvince', this.dataProvince)
    },
    async pageChange (val) {
      // console.log('pageChange', this.idCat)
      if (this.itemProvince === '' || this.itemProvince === 'ทั้งหมด') {
        this.itemProvince = 'All'
      }
      this.pageNumber = val
      // this.pathChange()
      await this.getVoucher()
    },
    pathChange () {
      // console.log('this.pageNumber', this.pageNumber)
      var text = this.translate(this.itemProvince)
      const encodedEvent = encodeURIComponent(text)
      const fullPath = `/ListVoucher/go?&province=${encodedEvent}&page=${this.pageNumber}`
      // console.log('', fullPath)
      this.$router.push({ path: `${fullPath}` }).catch(() => {})
    },
    async getVoucher () {
      // console.log('test====>2222', this.idCat)
      // this.pathChange()
      this.$store.commit('openLoader')
      // this.pathChange()
      this.showSkeletonLoader = true
      var data
      var roleUser = ''
      if (localStorage.getItem('roleUser')) {
        roleUser = JSON.parse(localStorage.getItem('roleUser')).role
      } else {
        roleUser = 'ext_buyer'
      }
      var ShopID = JSON.parse(localStorage.getItem('shopID'))
      var companyID = ''
      if (localStorage.getItem('SetRowCompany') !== null && roleUser === 'purchaser') {
        var companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyId.company.company_id
      }
      if (roleUser === 'sale_order' || roleUser === 'sale_order_no_JV') {
        companyID = JSON.parse(localStorage.getItem('PartnerID'))
        ShopID = JSON.parse(localStorage.getItem('ShopID'))
      } else {
        ShopID = JSON.parse(localStorage.getItem('shopID'))
      }
      // console.log('companyID', companyID)
      data = {
        company_id: this.dataRole.role !== 'ext_buyer' ? companyID : '-1',
        role_user: roleUser,
        category: this.idCat === undefined ? 359 : this.idCat,
        seller_shop_id: this.typeProduct === 'เวาเชอร์' ? -1 : ShopID,
        orderBy: '',
        page: this.pageNumber,
        status_product: '',
        keyword: this.search,
        limit: this.typeProduct === 'เวาเชอร์' ? 48 : this.limit
      }
      // console.log('data', data)
      await this.$store.dispatch('actionsSelectCategoryShopList', data)
      var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      // console.log('getProductSameShop=======>', response)
      if (response.ok === 'y') {
        this.$store.commit('closeLoader')
        if (response.query_result.length !== 0) {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = await [...response.query_result]
          this.pageMax = parseInt(response.pagination.max_page) === 0 ? 1 : parseInt(response.pagination.max_page)
          // this.pageNumber = parseInt(this.$route.query.page)
          this.productCount = response.query_result.length
          this.showSkeletonLoader = false
          // this.pathChange()
          // console.log('this.pageMax', this.pageMax)
        } else {
          this.pageMax = parseInt(response.pagination.max_page) === 0 ? 1 : parseInt(response.pagination.max_page)
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
          this.showSkeletonLoader = false
          // this.pathChange()
        }
      } else {
        this.$store.commit('closeLoader')
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
        this.showSkeletonLoader = false
      }
    }
  }
}
</script>

<style scoped>
.BtnSearch {
border-radius: 0px 8px 8px 0px !important;
box-shadow: none;
}
.group {
  display: flex;
  /* background-color: #fff; */
  line-height: 28px;
  align-items: center;
  position: relative;
  /* max-width: 100vw; */
}
.searchAll {
  border-radius: 8px;
  background-color: #fff;
  transition: 0.3s ease;
}
.autocompleteInput {
  border: 1px solid #27AB9C;
  margin-top: 0px !important;
  /* width: 100%; */
  height: 40px;
  line-height: 28px;
  padding: 0 1rem;
  /* padding-left: 2.5rem; */
  border: 2px solid transparent;
  border-radius: 8px;
  outline: none;
  background-color: #fff;
  color: #27AB9C;
  transition: 0.3s ease;
}
.input {
  width: 100%;
  height: 40px;
  line-height: 28px;
  padding: 0 1rem;
  padding-left: 2.5rem;
  border: 2px solid transparent;
  border-radius: 8px;
  outline: none;
  background-color: #fff;
  color: #27AB9C;
  transition: 0.3s ease;
}

.input::placeholder {
  color: #27AB9C;
}

.input:focus,
/* .autocompleteInput:focus, */
/* input:hover {
  outline: none;
  border-color: rgba(58, 168, 142, 0.58);
  background-color: #fff;
  box-shadow: 0 0 0 4px rgb(58 168 142 / 10%);
} */
.searchAll:hover {
  outline: none;
  border-color: rgba(58, 168, 142, 0.58);
  background-color: #fff;
  box-shadow: 0 0 0 4px rgb(58 168 142 / 10%);
}

.icon {
  position: absolute;
  left: 1rem;
  fill: #27AB9C;
  width: 1rem;
  height: 1rem;
}
.v-breadcrumbs {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  flex: 0 1 auto;
  white-space: nowrap;
  list-style-type: none;
  /* margin-bottom: 24px; */
  /* padding: 8px 0px 8px 75px !important; */
}
.v-application a {
  color: #636363 !important;
}
.v-breadcrumbs__item  {
  color: #27AB9C !important;
}
.v-breadcrumbs li .v-icon {
  color: #27AB9C !important;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
}
</style>
