<template>
  <v-container>
    <v-row v-if="!MobileSize">
      <!-- Website -->
      <v-col cols="12" md="3" sm="4" xs="12" v-if="!MobileSize && !IpadSize && !IpadProSize">
        <v-card class="mt-6" max-height="100%" height="1000" style="border-radius: 8px; border: 1px solid #F2F2F2;" elevation="0">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-storefront</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="white-space: normal; font-weight: bold; font-size: 18px !important; line-height: 30px;">กลับไปหน้าการซื้อขาย</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <div v-if="authority.can_use_function_in_company.set_company === '1' || authority.can_use_function_in_company.set_permission === '1' || authority.can_use_function_in_company.partner === '1' || authority.can_use_function_in_company.report === '1' || authority.can_use_function_in_company.order === '1' || authority.can_use_function_in_company.payment === '1'">
              <v-list-group
                v-for="item in MenuCompany"
                :key="item.key"
                v-model="item.active"
                :prepend-icon="item.action"
                no-action
                color="#27AB9C"
              >
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 18px !important; line-height: 30px;">{{ item.title }}</v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group
                  v-model="defaultSelect"
                  :mandatory="defaultSelect === 9 ? false : defaultSelect === 10 ? false : defaultSelect === 11 ? false : defaultSelect === 12 ? false : defaultSelect === 13 ? false : defaultSelect === 14 ? false : defaultSelect === 15 ? false : defaultSelect === 16 ? false : defaultSelect === 17 ?  false : defaultSelect === 18 ?  false : defaultSelect === 19 ?  false : defaultSelect === 20 ?  false : defaultSelect === 21 ?  false : true"
                >
                  <v-list-item
                    v-for="child in item.items"
                    :key="child.key"
                    :disabled="child.isDisabled"
                    color="#27AB9C"
                    dense
                    class="pl-16"
                    style="font-size: 18px; line-height: 26px;"
                  >
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- รายการสั่งซื้อ -->
            <div v-if="authority.can_use_function_in_company.order === '1' || authority.can_use_function_in_company.payment === '1'">
              <v-list-group
                v-show="showSecondMenu"
                v-for="item in itemsOrder"
                :key="item.keys"
                v-model="item.active"
                :prepend-icon="item.action"
                no-action
                color="#27AB9C"
              >
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 18px !important; line-height: 30px;">{{ item.title }}</v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group
                  v-model="select"
                  :mandatory="defaultSelect === 8 ? true : defaultSelect === 9 ? true : defaultSelect === 10 ? true : defaultSelect === 11 ? true : defaultSelect === 12 ? true : defaultSelect === 13 ? true : defaultSelect === 14 ? true : defaultSelect === 15 ? true : defaultSelect === 16 ? true : defaultSelect === 17 ? true : defaultSelect === 18 ? true : defaultSelect === 19 ? true : defaultSelect === 20 ? true : defaultSelect === 21 ? true : false"
                >
                  <v-list-item
                    v-for="child in item.items"
                    :key="child.key"
                    :disabled="child.isDisabled"
                    color="#27AB9C"
                    dense
                    class="pl-16"
                    style="font-size: 18px; line-height: 26px;"
                  >
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
          </v-list>
        </v-card>
      </v-col>
      <!-- IPAD PRO -->
      <v-col cols="12" md="3" sm="4" xs="12" v-else-if="!MobileSize && !IpadSize && IpadProSize">
        <v-card class="mt-6" max-height="100%" height="1000" style="border-radius: 8px; border: 1px solid #F2F2F2;" elevation="0">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-storefront</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="white-space: normal; font-weight: bold; line-height: 30px;">กลับไปหน้าการซื้อขาย</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <div v-if="authority.can_use_function_in_company.set_company === '1' || authority.can_use_function_in_company.set_permission === '1' || authority.can_use_function_in_company.partner === '1' || authority.can_use_function_in_company.report === '1' || authority.can_use_function_in_company.order === '1' || authority.can_use_function_in_company.payment === '1'">
              <v-list-group
                v-for="item in MenuCompany"
                :key="item.key"
                v-model="item.active"
                :prepend-icon="item.action"
                no-action
                color="#27AB9C"
              >
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title style="font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group
                  v-model="defaultSelect"
                  :mandatory="defaultSelect === 9 ? false : defaultSelect === 10 ? false : defaultSelect === 11 ? false : defaultSelect === 12 ? false : defaultSelect === 13 ? false : defaultSelect === 14 ? false : defaultSelect === 15 ? false : defaultSelect === 16 ? false : defaultSelect === 17 ? false : defaultSelect === 18 ? false : defaultSelect === 19 ? false : defaultSelect === 20 ?  false : defaultSelect === 21 ?  false : true"
                >
                  <v-list-item
                    v-for="child in item.items"
                    :key="child.key"
                    :disabled="child.isDisabled"
                    color="#27AB9C"
                    dense
                    class="pl-16"
                    style="font-size: 16px; line-height: 26px;"
                  >
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- รายการสั่งซื้อ -->
            <div v-if="authority.can_use_function_in_company.order === '1' || authority.can_use_function_in_company.payment === '1'">
              <v-list-group
                v-show="showSecondMenu"
                v-for="item in itemsOrder"
                :key="item.keys"
                v-model="item.active"
                :prepend-icon="item.action"
                no-action
                color="#27AB9C"
              >
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 20px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group
                  v-model="select"
                  :mandatory="defaultSelect === 9 ? true : defaultSelect === 10 ? true : defaultSelect === 11 ? true : defaultSelect === 12 ? true : defaultSelect === 13 ? true : defaultSelect === 14 ? true : defaultSelect === 15 ? true : defaultSelect === 16 ? true : defaultSelect === 17 ? true : defaultSelect === 18 ? true : defaultSelect === 19 ? true : defaultSelect === 20 ? true : defaultSelect === 21 ? true : false"
                >
                  <v-list-item
                    v-for="child in item.items"
                    :key="child.key"
                    :disabled="child.isDisabled"
                    color="#27AB9C"
                    dense
                    class="pl-16"
                    style="font-size: 16px; line-height: 26px;"
                  >
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
          </v-list>
        </v-card>
      </v-col>
      <!-- IPAD -->
      <v-col cols="12" md="3" sm="4" xs="12" v-else-if="!MobileSize && IpadSize && !IpadProSize">
        <v-card class="mt-6" max-height="100%" height="1000px" style="border-radius: 8px; border: 1px solid #F2F2F2;" elevation="0">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-storefront</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="white-space: normal; font-weight: bold; line-height: 30px;">กลับไปหน้าการซื้อขาย</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <div v-if="authority.can_use_function_in_company.set_company === '1' || authority.can_use_function_in_company.set_permission === '1' || authority.can_use_function_in_company.partner === '1' || authority.can_use_function_in_company.report === '1' || authority.can_use_function_in_company.order === '1' || authority.can_use_function_in_company.payment === '1'">
              <v-list-group
                v-for="item in MenuCompany"
                :key="item.key"
                v-model="item.active"
                :prepend-icon="item.action"
                no-action
                color="#27AB9C"
              >
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title style="font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group
                  v-model="defaultSelect"
                  :mandatory="defaultSelect === 9 ? false : defaultSelect === 10 ? false : defaultSelect === 11 ? false : defaultSelect === 12 ? false : defaultSelect === 13 ? false : defaultSelect === 14 ? false : defaultSelect === 15 ? false : defaultSelect === 16 ? false : defaultSelect === 17 ? false : defaultSelect === 18 ? false : defaultSelect === 19 ? false  : defaultSelect === 20 ? false : defaultSelect === 21 ? false : true"
                >
                  <v-list-item
                    v-for="child in item.items"
                    :key="child.key"
                    :disabled="child.isDisabled"
                    color="#27AB9C"
                    dense
                    class="pl-16"
                    style="font-size: 14px; line-height: 26px;"
                  >
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- รายการสั่งซื้อ -->
            <div v-if="authority.can_use_function_in_company.order === '1' || authority.can_use_function_in_company.payment === '1'">
              <v-list-group
                v-show="showSecondMenu"
                v-for="item in itemsOrder"
                :key="item.keys"
                v-model="item.active"
                :prepend-icon="item.action"
                no-action
                color="#27AB9C"
              >
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group
                  v-model="select"
                  :mandatory="defaultSelect === 9 ? true : defaultSelect === 10 ? true : defaultSelect === 11 ? true : defaultSelect === 12 ? true : defaultSelect === 13 ? true : defaultSelect === 14 ? true : defaultSelect === 15 ? true : defaultSelect === 16 ? true : defaultSelect === 17 ? true : defaultSelect === 18 ? true : defaultSelect === 19 ? true : defaultSelect === 20 ? true  : defaultSelect === 21 ? true : false"
                >
                  <v-list-item
                    v-for="child in item.items"
                    :key="child.key"
                    :disabled="child.isDisabled"
                    color="#27AB9C"
                    dense
                    class="pl-16"
                    style="font-size: 14px; line-height: 26px;"
                  >
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
          </v-list>
        </v-card>
      </v-col>
      <!-- </v-navigation-drawer> -->
      <v-col cols="12" md="9" sm="8" xs="12" class="pl-0 pr-0">
        <v-main style="padding: 0px;">
          <v-container>
            <v-row dense class="mt-3" v-if="checkCreateShop">
              <v-col cols="12" md="12" class="mt-0 pt-0">
                <v-img src="@/assets/ImageINET-Marketplace/ICONShop/BannerCreateShop.png" max-height="468" max-width="100%" contain></v-img>
              </v-col>
            </v-row>
            <div v-if="this.$router.currentRoute.name === 'designShopUI' || this.$router.currentRoute.name === 'EditPicturesShop' || this.$router.currentRoute.name === 'EditShop'" max-height="100%" height="100%" width="100%" >
              <router-view></router-view>
            </div>
            <v-card v-else max-height="100%" height="100%" width="100%" class="mt-3" style="border-radius: 8px; border: 1px solid #F2F2F2;" elevation="0">
              <router-view></router-view>
            </v-card>
          </v-container>
        </v-main>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  // components: {
  //   Footer: () => import('@/components/Home/Footer')
  // },
  data () {
    return {
      shopname: '',
      ImgShop: '',
      authority: '',
      checkCreateShop: false,
      activeMenu: true,
      dataBreadcrumb: [
        {
          name: 'หน้าหลัก',
          path: 'selleI'
        },
        {
          name: 'รายการสินค้า',
          path: 'seller'
        }
      ],
      BtnLink: [
        { name: 'รายละเอียดร้านค้า', icon: 'idcard', path: 'shop' },
        { name: 'ออกจากระบบ', icon: 'export', path: '' }
      ],
      defaultSelect: 1,
      showSecondMenu: false,
      select: 0,
      itemsShop: [],
      MenuCompany: [],
      items: [
        {
          key: 1,
          action: 'mdi-domain',
          active: true,
          title: 'บริษัท',
          items: [
            { key: 4, title: 'จัดการข้อมูลบริษัท', path: 'manageCompany?Status=Create' }
          ]
        }
      ],
      listMenu: [
        {
          key: 1,
          action: 'mdi-domain',
          active: true,
          title: 'บริษัท',
          items: [
            { key: 1, title: '', path: '', isDisabled: true },
            { key: 2, title: 'จัดการข้อมูลบริษัท', path: '/detailCompany', isDisabled: false },
            { key: 3, title: 'จัดการตำแหน่งภายในบริษัท', path: '', isDisabled: false },
            { key: 4, title: 'เพิ่มผู้ใช้ภายใต้บริษัท', path: '', isDisabled: false },
            { key: 5, title: 'ร้านค้าคู่ค้า', path: '/Partner', isDisabled: false },
            // { key: 6, title: 'จัดการการชำระเงิน', path: '', isDisabled: false },
            { key: 6, title: 'รายงานรายจ่าย', path: '/report', isDisabled: false },
            { key: 7, title: 'ติดตามสถานะสินค้า', path: '/TackingCompany', isDisabled: false },
            { key: 8, title: 'ติดตามการคืนสินค้า', path: '/refundCompany', isDisabled: false },
            // { key: 10, title: 'ตั้งค่าใบเสนอราคา', path: '', isDisabled: false },
            { key: 10, title: 'จัดการตำแหน่งและสิทธิ์การใช้งาน', path: '/ManagePosition', isDisabled: false },
            { key: 11, title: 'จัดการผู้ใช้งาน', path: '/ManageCompanyPostionUser', isDisabled: false },
            { key: 12, title: 'คูปองและคะแนน', path: '/CompanyCouposAndPoints', isDisabled: false },
            { key: 13, title: 'e-Withholding Tax', path: '/eWHTCompany', isDisabled: false }
          ]
        }
      ],
      itemsOrder: [
        {
          keys: 2,
          action: 'mdi-file-document-outline',
          active: false,
          title: 'จัดการรายการสั่งซื้อ',
          items: [
            { key: 0, title: 'รายการสั่งซื้อ', path: '/orderCompany', isDisabled: false },
            { key: 1, title: 'ใบเสนอราคา', path: '/QUCompany', isDisabled: false },
            { key: 2, title: 'ประวัติรายการสั่งซื้อ', path: '/orderRecordCompany', isDisabled: false },
            { key: 3, title: 'จัดการรูปแบบการอนุมัติ', path: '/listApprovePosition', isDisabled: false },
            { key: 4, title: 'จัดการลำดับการอนุมัติ', path: '/listApproveSequence', isDisabled: false },
            { key: 5, title: 'รายการอนุมัติ', path: '/listApproveCompany', isDisabled: false }
            // { key: 6, title: 'รับของ', path: '/ReceiveItems', isDisabled: false }
            // { key: 2, title: 'การร้องขอราคาพิเศษ', path: '/specialPriceBuyerRequest', isDisabled: false },
            // { key: 3, title: 'การประเมินความพึงพอใจ', path: '/reviewCompany', isDisabled: false },
            // { key: 4, title: 'รายการสั่งซื้อสินค้าแบบเครดิตเทอม', path: '/companyListCreditOrder', isDisabled: false }
          ]
        }
      ]
    }
  },
  created () {
    // console.log('เข้า create in shop')
    // var dataUser = JSON.parse(Decode.decode(localStorage.getItem('DetailUser')))
    // console.log('dataUser', dataUser)
    this.$EventBus.$emit('resetSearch')
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' })
    } else {
      this.$EventBus.$emit('getPath')
      this.$EventBus.$emit('CheckFooter')
      this.$EventBus.$on('ChangeActiveMenu', this.ChangeActiveMenu)
      this.$EventBus.$on('chackAuthorityCompanyMenu', this.chackAuthorityCompanyMenu)
      this.chackAuthorityCompanyMenu()
      this.checkPathCompany()
      this.SelectPathCompany()
    }
  },
  watch: {
    MobileSize (val) {
      if (this.$router.currentRoute.name === 'detailCompany' || this.$router.currentRoute.name === 'detailCompanyMobile') {
        if (val === true) {
          this.$router.push({ path: '/detailCompanyMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/detailCompany' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'ManagePosition' || this.$router.currentRoute.name === 'ManagePositionMobile') {
        if (val === true) {
          this.$router.push({ path: '/ManagePositionMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/ManagePosition' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'ManageCompanyPostionUser' || this.$router.currentRoute.name === 'ManageCompanyPostionUserMobile') {
        if (val === true) {
          this.$router.push({ path: '/ManageCompanyPostionUserMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/ManageCompanyPostionUser' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'Partner' || this.$router.currentRoute.name === 'PartnerMobile') {
        if (val === true) {
          this.$router.push({ path: '/PartnerMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/Partner' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'Report' || this.$router.currentRoute.name === 'ReportMobile') {
        if (val === true) {
          this.$router.push({ path: '/ReportMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/Report' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'CompanyCouposAndPoints' || this.$router.currentRoute.name === 'CompanyCouposAndPointsMobile') {
        if (val === true) {
          this.$router.push({ path: '/CompanyCouposAndPointsMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/CompanyCouposAndPoints' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'orderCompany' || this.$router.currentRoute.name === 'orderCompanyMobile') {
        if (val === true) {
          this.$router.push({ path: '/orderCompanyMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/orderCompany' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'orderRecordCompany' || this.$router.currentRoute.name === 'orderRecordCompanyMobile') {
        if (val === true) {
          this.$router.push({ path: '/orderRecordCompanyMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/orderRecordCompany' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'listApprovePosition' || this.$router.currentRoute.name === 'listApprovePositionMobile') {
        if (val === true) {
          this.$router.push({ path: '/listApprovePositionMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/listApprovePosition' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'listApproveSequence' || this.$router.currentRoute.name === 'listApproveSequenceMobile') {
        if (val === true) {
          this.$router.push({ path: '/listApproveSequenceMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/listApproveSequence' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'listApproveCompany' || this.$router.currentRoute.name === 'listApproveCompanyMobile') {
        if (val === true) {
          this.$router.push({ path: '/listApproveCompanyMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/listApproveCompany' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'ReceiveItems' || this.$router.currentRoute.name === 'ReceiveItemsMobile') {
        if (val === true) {
          this.$router.push({ path: '/ReceiveItemsMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/ReceiveItems' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'detailPosition' || this.$router.currentRoute.name === 'detailPositionMobile') {
        var positionID = localStorage.getItem('positionID')
        if (val === true) {
          this.$router.push({ path: `/detailPositionMobile?positionID=${positionID}` }).catch(() => { })
        } else {
          this.$router.push({ path: `/detailPosition?positionID=${positionID}` }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'ManageBuyerApprove' || this.$router.currentRoute.name === 'ManageBuyerApproveMobile') {
        if (val === true) {
          this.$router.push({ path: '/ManageBuyerApproveMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/ManageBuyerApprove' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'listApproveOrder' || this.$router.currentRoute.name === 'listApproveOrderMobile') {
        if (val === true) {
          this.$router.push({ path: '/listApproveOrderMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/listApproveOrder' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'ListApprove' || this.$router.currentRoute.name === 'ListApproveMobile') {
        if (val === true) {
          this.$router.push({ path: '/ListApproveMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/ListApprove' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'specialPriceBuyerRequest' || this.$router.currentRoute.name === 'specialPriceBuyerRequestMobile') {
        if (val === true) {
          this.$router.push({ path: '/specialPriceBuyerRequestMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/specialPriceBuyerRequest' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'listCreaditOrder' || this.$router.currentRoute.name === 'listCreaditOrderMobile') {
        if (val === true) {
          this.$router.push({ path: '/companyListCreditOrderMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/companyListCreditOrder' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'listCreaditTerm' || this.$router.currentRoute.name === 'listCreaditTermMobile') {
        var number = JSON.parse(localStorage.getItem('creditTermOrderNumber'))
        if (val === true) {
          this.$router.push({ path: `/companyListCreditTermMobile?order_number=${number}` }).catch(() => { })
        } else {
          this.$router.push({ path: '/companyListCreditTerm' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'invoicePDF' || this.$router.currentRoute.name === 'invoicePDFMobile') {
        // console.log('val', val)
        var number1 = JSON.parse(Decode.decode(localStorage.getItem('creditTerm')))
        // console.log('number', number1)
        if (val === true) {
          this.$router.push({ path: `/invoicePDFMobile?order_number=${number1.order_number}` }).catch(() => { })
        } else {
          this.$router.push({ path: '/invoicePDF' }).catch(() => { })
        }
      } else if (this.$router.currentRoute.name === 'QUCompany' || this.$router.currentRoute.name === 'QUCompanyMobile') {
        if (val === true) {
          this.$router.push({ path: '/QUCompanyMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/QUCompany' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'QUCompanyDetail' || this.$router.currentRoute.name === 'QUCompanyDetailMobile') {
        var itemsQU = JSON.parse(Decode.decode(localStorage.getItem('detailItemQU')))
        if (val === false) {
          this.$router.push({ path: `/QUCompanyDetail?ordernumber=${itemsQU.qu_number}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/QUCompanyDetailMobile?ordernumber=${itemsQU.qu_number}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'refundCompanyMobile' || this.$router.currentRoute.name === 'refundCompany') {
        if (val === true) {
          this.$router.push({ path: '/refundCompanyMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/refundCompany' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'refundDetailCompanyMobile' || this.$router.currentRoute.name === 'refundDetailCompany') {
        var data
        var OrderNumber
        if (localStorage.getItem('orderRefundNumber') !== null) {
          data = JSON.parse(Decode.decode(localStorage.getItem('orderRefundNumber')))
          OrderNumber = data.reference_id
        }
        if (val === true) {
          this.$router.push({ path: `/refundDetailCompanyMobile?orderNumber=${OrderNumber}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/refundDetailCompany?orderNumber=${OrderNumber}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageAddressCompany' || this.$router.currentRoute.name === 'ManageAddressCompanyMobile') {
        if (val === true) {
          this.$router.push({ path: '/ManageAddressCompanyMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageAddressCompany' }).catch(() => {})
        }
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    this.$EventBus.$on('changeNavCompany', this.SelectPathCompany)
    this.$EventBus.$on('checkPathCompany', this.checkPathCompany)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('changeNavCompany')
      this.$EventBus.$off('checkPathCompany')
    })
  },
  methods: {
    chackAuthorityCompanyMenu () {
      this.authority = ''
      if (localStorage.getItem('list_Company_detail') !== null) {
        this.authority = JSON.parse(Decode.decode(localStorage.getItem('list_Company_detail')))
      }
      // console.log('authority', this.authority)
      var item1 = [{ key: 1, title: this.authority.compant_name_th, path: '', isDisabled: true }]
      var item2 = []
      if (this.authority.can_use_function_in_company.set_company === '1') {
        item1.push({ key: 2, title: 'จัดการข้อมูลบริษัท', path: '/detailCompany', isDisabled: false })
      }
      if (this.authority.can_use_function_in_company.set_company === '1') {
        item1.push({ key: 3, title: 'จัดการที่อยู่บริษัท', path: '/ManageAddressCompany', isDisabled: false })
      }
      if (this.authority.can_use_function_in_company.set_permission === '1') {
        item1.push({ key: 4, title: 'จัดการตำแหน่งภายในบริษัท', path: '/ManagePosition', isDisabled: false })
      }
      if (this.authority.can_use_function_in_company.set_permission === '1') {
        item1.push({ key: 5, title: 'จัดการผู้ใช้งานภายในบริษัท', path: '/ManageCompanyPostionUser', isDisabled: false })
      }
      if (this.authority.can_use_function_in_company.partner === '1') {
        item1.push({ key: 6, title: 'ร้านค้าคู่ค้า', path: '/Partner', isDisabled: false })
      }
      if (this.authority.can_use_function_in_company.report === '1') {
        item1.push({ key: 7, title: 'รายงานรายจ่าย', path: '/Report', isDisabled: false })
      }
      if (this.authority.can_use_function_in_company.set_company === '1') {
        item1.push({ key: 8, title: 'คูปองและคะแนน', path: '/CompanyCouposAndPoints', isDisabled: false })
      }
      if (this.authority.can_use_function_in_company.set_company === '1') {
        item1.push({ key: 9, title: 'e-Withholding Tax', path: '/eWHTCompany', isDisabled: false })
      }
      if (this.authority.can_use_function_in_company.order === '1' || this.authority.can_use_function_in_company.payment === '1') {
        item2.push({ key: 0, title: 'รายการสั่งซื้อ', path: '/orderCompany', isDisabled: false })
      }
      if (this.authority.can_use_function_in_company.order === '1' || this.authority.can_use_function_in_company.payment === '1') {
        item2.push({ key: 1, title: 'ประวัติรายการสั่งซื้อ', path: '/orderRecordCompany', isDisabled: false })
      }
      if (this.authority.can_use_function_in_company.approve_order === '1') {
        item2.push({ key: 2, title: 'จัดการรูปแบบการอนุมัติ', path: '/listApprovePosition', isDisabled: false })
      }
      if (this.authority.can_use_function_in_company.approve_order === '1') {
        item2.push({ key: 3, title: 'จัดการลำดับการอนุมัติ', path: '/listApproveSequence', isDisabled: false })
      }
      // if (this.authority.can_use_function_in_company.approve_order === '1') {
      //   item2.push({ key: 2, title: 'จัดการผู้ซื้อองค์กร', path: '/ManageBuyerApprove', isDisabled: false })
      // }
      // if (this.authority.can_use_function_in_company.approve_order === '1') {
      //   item2.push({ key: 3, title: 'รายการอนุมัติ', path: '/listApproveOrder', isDisabled: false })
      // }
      // if (this.authority.can_use_function_in_company.approve_order === '1') {
      //   item2.push({ key: 4, title: 'รายการอนุมัติการสั่งซื้อ', path: '/listApprove', isDisabled: false })
      // }
      // if (this.authority.can_use_function_in_company.order === '1' || this.authority.can_use_function_in_company.payment === '1') {
      //   item2.push({ key: 5, title: 'การร้องขอราคาพิเศษ', path: '/specialPriceBuyerRequest', isDisabled: false })
      // }
      if (this.authority.can_use_function_in_company.order === '1' || this.authority.can_use_function_in_company.payment === '1') {
        item2.push({ key: 6, title: 'ใบเสนอราคา', path: '/QUCompany', isDisabled: false })
      }
      if (this.authority.can_use_function_in_company.approve_order === '1') {
        item2.push({ key: 7, title: 'รายการอนุมัติ', path: '/listApproveCompany', isDisabled: false })
      }
      if (this.authority.can_use_function_in_company.order === '1' || this.authority.can_use_function_in_company.payment === '1') {
        item2.push({ key: 8, title: 'รับของ', path: '/ReceiveItems', isDisabled: false })
      }
      // if (this.authority.can_use_function_in_company.order === '1' || this.authority.can_use_function_in_company.payment === '1') {
      //   item2.push({ key: 7, title: 'รายการสั่งซื้อสินค้าแบบเครดิตเทอม', path: '/companyListCreditOrder', isDisabled: false })
      // }
      // if (this.authority.can_use_function_in_company.tracking === '1') {
      //   item2.push({ key: 8, title: 'ติดตามสถานะสินค้า', path: '/TackingCompany', isDisabled: false })
      // }
      // if (this.authority.can_use_function_in_company.refund === '1') {
      //   item2.push({ key: 9, title: 'ติดตามการคืนสินค้า', path: '/refundCompany', isDisabled: false })
      // }
      // if (this.authority.can_use_function_in_company.review === '1') {
      //   item2.push({ key: 10, title: 'การประเมินความพึงพอใจ', path: '/reviewCompany', isDisabled: false })
      // }
      this.listMenu[0].items = []
      this.itemsOrder[0].items = []
      this.listMenu[0].items = item1
      this.itemsOrder[0].items = item2
    },
    checkPathCompany () {
      if (this.$route.query.Status !== 'Create') {
        this.MenuCompany = this.listMenu
        this.showSecondMenu = true
        if (localStorage.getItem('SetRowCompany') !== null) {
          var dataCompany = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
          this.MenuCompany[0].items[0].title = dataCompany.company.compant_name_th
        } else {
          var data = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
          this.MenuCompany[0].items[0].title = data.name_th
        }
        // console.log('MenuCompany', this.MenuCompany)
      } else {
        this.MenuCompany = this.items
        this.showSecondMenu = false
      }
    },
    async Gopage (val) {
      this.$router.push(val.path).catch(() => {})
    },
    changePage (val) {
      this.$EventBus.$emit('resetAdminShop')
      this.$router.push({ path: `${val}` }).catch(() => {})
    },
    ChangeActiveMenu (val) {
      // console.log('ChangeActiveMenu------>', val)
      this.activeMenu = val
    },
    async SelectPathCompany () {
      // console.log('----->.', this.$router.currentRoute.name)
      // console.log('itemsOrder----->.', this.itemsOrder)
      // console.log('Record----->', this.itemsOrder[0].items.findIndex((result) => {
      //   return result.path === this.$router.currentRoute.path
      // }))
      this.defaultSelect = null
      // console.log(this.defaultSelect)
      // console.log('select =>>>>>', this.select)
      if (this.$router.currentRoute.name === 'Company' || this.$router.currentRoute.name === 'manageCompany' || this.$router.currentRoute.name === 'detailCompany') {
        if (this.$router.currentRoute.name === 'manageCompany' || this.$router.currentRoute.name === 'detailCompany') {
          this.defaultSelect = this.listMenu[0].items.findIndex((result) => {
            return result.path === '/Company'
          })
        } else {
          this.defaultSelect = this.listMenu[0].items.findIndex((result) => {
            return result.path === this.$router.currentRoute.path
          })
        }
        // this.defaultSelect = 1
        this.select = 18
      } else if (this.$router.currentRoute.name === 'Partner') {
        this.defaultSelect = this.listMenu[0].items.findIndex((result) => {
          return result.path === this.$router.currentRoute.path
        })
        // console.log(this.defaultSelect)
        // this.defaultSelect = 4
        this.select = 21
        this.listMenu[0].active = true
        this.itemsOrder[0].active = false
      } else if (this.$router.currentRoute.name === 'ManagePosition') {
        this.defaultSelect = await this.listMenu[0].items.findIndex((result) => {
          return result.path === this.$router.currentRoute.path
        })
        this.select = 19
        // this.defaultSelect = 1
        this.listMenu[0].active = true
        this.itemsOrder[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageTaxInvoice') {
        this.defaultSelect = await this.listMenu[0].items.findIndex((result) => {
          return result.path === this.$router.currentRoute.path
        })
        this.select = 20
        // this.defaultSelect = 1
        this.listMenu[0].active = true
        this.itemsOrder[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageCompanyPostionUser') {
        this.defaultSelect = this.listMenu[0].items.findIndex((result) => {
          return result.path === this.$router.currentRoute.path
        })
        this.select = 20
        // this.defaultSelect = 3
        this.listMenu[0].active = true
        this.itemsOrder[0].active = false
      } else if (this.$router.currentRoute.name === 'CompanyCouposAndPoints') {
        this.defaultSelect = this.listMenu[0].items.findIndex((result) => {
          return result.path === this.$router.currentRoute.path
        })
        this.select = 24
        this.defaultSelect = 7
        this.listMenu[0].active = true
        this.itemsOrder[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageAddressCompany') {
        this.defaultSelect = this.listMenu[0].items.findIndex((result) => {
          return result.path === this.$router.currentRoute.path
        })
        this.select = 23
        // this.defaultSelect = 6
        this.listMenu[0].active = true
        this.itemsOrder[0].active = false
      } else if (this.$router.currentRoute.name === 'refundCompany' || this.$router.currentRoute.name === 'refundDetailCompany') {
        if (this.$router.currentRoute.name === 'refundDetailCompany') {
          this.defaultSelect = this.listMenu[0].items.findIndex((result) => {
            return result.path === '/refundCompany'
          })
        } else {
          this.defaultSelect = this.listMenu[0].items.findIndex((result) => {
            return result.path === this.$router.currentRoute.path
          })
        }
        this.select = 9
        this.defaultSelect = 16
        this.listMenu[0].active = false
        this.itemsOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'orderCompany' || this.$router.currentRoute.name === 'orderDetailCompany' || this.$router.currentRoute.name === 'Change' || this.$router.currentRoute.name === 'RenewOrder' || this.$router.currentRoute.name === 'ChangeAndRenewOrder') {
        if (this.$router.currentRoute.name === 'orderDetail') {
          this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
            return result.path === '/orderCompany'
          })
        } else {
          this.defaultSelect = this.listMenu[0].items.findIndex((result) => {
            return result.path === this.$router.currentRoute.path
          })
        }
        this.select = 0
        this.defaultSelect = 9
        this.listMenu[0].active = false
        this.itemsOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'QUCompany' || this.$router.currentRoute.name === 'QUCompanyDetail') {
        if (this.$router.currentRoute.name === 'QUCompanyDetail') {
          this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
            return result.path === '/QUCompany'
          })
        } else {
          this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
            return result.path === this.$router.currentRoute.path
          })
        }
        this.select = 4
        this.defaultSelect = 13
        this.listMenu[0].active = false
        this.itemsOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'reviewCompany') {
        this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
          return result.path === this.$router.currentRoute.path
        })
        this.select = 10
        this.defaultSelect = 17
        this.listMenu[0].active = false
        this.itemsOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'QuotationAll' || this.$router.currentRoute.name === 'QuotationDetail') {
        if (this.$router.currentRoute.name === 'QuotationDetail') {
          this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
            return result.path === '/QuotationAll'
          })
        } else {
          this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
            return result.path === this.$router.currentRoute.path
          })
        }
        this.select = 3
        // this.defaultSelect = 3
        this.listMenu[0].active = true
        this.itemsOrder[0].active = false
      } else if (this.$router.currentRoute.name === 'Report') {
        this.defaultSelect = this.listMenu[0].items.findIndex((result) => {
          return result.path === this.$router.currentRoute.path
        })
        this.select = 22
        // this.defaultSelect = 5
        this.listMenu[0].active = true
        this.itemsOrder[0].active = false
      } else if (this.$router.currentRoute.name === 'TackingCompany') {
        this.defaultSelect = this.listMenu[0].items.findIndex((result) => {
          return result.path === this.$router.currentRoute.path
        })
        this.select = 8
        this.defaultSelect = 15
        this.listMenu[0].active = false
        this.itemsOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'specialPriceBuyerRequest') {
        this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
          return result.path === this.$router.currentRoute.path
        })
        this.select = 5
        this.defaultSelect = 12
        this.listMenu[0].active = false
        this.itemsOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'listCreaditOrder' || this.$router.currentRoute.name === 'listCreaditTerm') {
        this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
          return result.path === this.$router.currentRoute.path
        })
        this.select = 7
        this.defaultSelect = 14
        this.listMenu[0].active = false
        this.itemsOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'ManageBuyerApprove') {
        this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
          return result.path === this.$router.currentRoute.path
        })
        this.select = 2
        this.defaultSelect = 9
        this.listMenu[0].active = false
        this.itemsOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'eWHTCompany') {
        this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
          return result.path === this.$router.currentRoute.path
        })
        this.select = 9
        this.defaultSelect = 8
        this.listMenu[0].active = true
        this.itemsOrder[0].active = false
      } else if (this.$router.currentRoute.name === 'listApproveOrder' || this.$router.currentRoute.name === 'DetailApproveOrder') {
        this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
          return result.path === this.$router.currentRoute.path
        })
        this.select = 3
        this.defaultSelect = 10
        this.listMenu[0].active = false
        this.itemsOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'ListApprove' || this.$router.currentRoute.name === 'DetailListApprove') {
        this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
          return result.path === this.$router.currentRoute.path
        })
        this.select = 4
        this.defaultSelect = 11
        this.listMenu[0].active = false
        this.itemsOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'orderRecordCompany') {
        this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
          return result.path === '/orderRecordCompany'
        })
        // console.log('defaultSelect', this.defaultSelect)
        this.select = 1
        this.defaultSelect = 7
        this.listMenu[0].active = false
        this.itemsOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'listApprovePosition') {
        this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
          return result.path === '/listApprovePosition'
        })
        // console.log('select', this.select)
        this.select = 2
        this.defaultSelect = 18
        this.listMenu[0].active = false
        this.itemsOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'listApproveSequence') {
        this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
          return result.path === '/listApproveSequence'
        })
        // console.log('select', this.select)
        this.select = 3
        this.defaultSelect = 19
        this.listMenu[0].active = false
        this.itemsOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'listApproveCompany' || this.$router.currentRoute.name === 'detailApproveCompany') {
        this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
          return result.path === '/listApproveCompany'
        })
        // console.log('select', this.select)
        this.select = 5
        this.defaultSelect = 20
        this.listMenu[0].active = false
        this.itemsOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'ReceiveItems' || this.$router.currentRoute.name === 'ReceiveItemsDetailMobile') {
        this.defaultSelect = this.itemsOrder[0].items.findIndex((result) => {
          return result.path === '/ReceiveItems'
        })
        // console.log('select', this.select)
        this.select = 6
        this.defaultSelect = 21
        this.listMenu[0].active = false
        this.itemsOrder[0].active = true
      }
      // console.log(this.defaultSelect)
      // console.log(this.select)
    }
  }
}
</script>

<style scoped>
.v-application ul, .v-application ol {
    padding: 0px 0px !important;
}
.v-application ol, .v-application ul {
    padding: 0px 0px !important;
}
</style>
