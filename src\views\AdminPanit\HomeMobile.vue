<template>
  <v-container>
    <v-row class="mb-12" v-if="MobileSize">
      <v-col cols="12" md="12" sm="12" xs="12">
        <v-card class="mt-6" max-height="100%" height="100%" style="border-radius: 8px; border: 1px solid #F2F2F2;" elevation="0">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-storefront</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="white-space: normal; font-weight: bold; line-height: 30px;">กลับไปหน้าการซื้อขาย</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <v-list-item-group
              color="#27AB9C"
            >
              <v-list-item
                v-for="(item, i) in items"
                :key="i"
                :disabled="item.isDisabled"
                @click="changePage(item.path)"
              >
                <v-list-item-icon>
                  <v-icon>{{ item.action }}</v-icon>
                </v-list-item-icon>
                <v-list-item-content v-if="item.title === 'แชทแอดมิน'" @click="openChat()">
                  <v-list-item-action>{{ item.title }}</v-list-item-action>
                </v-list-item-content>
                <v-list-item-content v-else>
                  <v-list-item-title style="font-size: 16px; line-height: 24px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
                <v-list-item-action v-if="item.action === ''">
                  <v-icon>mdi-chevron-right</v-icon>
                </v-list-item-action>
              </v-list-item>
            </v-list-item-group>
          </v-list>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { createChat } from '@/components/library/CallChatMe/callChatMe'
export default {
  // components: {
  //   UserProfile: () => import('@/components/UserProfile/UserProfileUI')
  // },
  created () {
    this.$EventBus.$emit('closeModalLogin')
    this.$EventBus.$emit('closeModalRegister')
    this.$EventBus.$emit('closeModalSuccess')
    this.$EventBus.$emit('getPath')
    this.$EventBus.$emit('resetSearch')
    // this.$EventBus.$emit('CheckFooter')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    selectedItem (val) {
      // console.log(val)
    },
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardShopAdminMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'BackToMobile')
        this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  data () {
    return {
      selectedItem: 0,
      select: 0,
      items: [
        { key: 0, action: 'mdi-chart-box', title: 'Dashboard Admin NGC', path: '', isDisabled: true },
        { key: 1, action: '', title: 'แดชบอร์ดแอดมิน', path: '/dashboardShopAdminMobile', isDisabled: false },
        { key: 2, action: '', title: 'แดชบอร์ด JV', path: '/dashboardJVMobile', isDisabled: false },
        { key: 3, action: '', title: 'แดชบอร์ด User Active', path: '/dashboardUserActiveMobile', isDisabled: false },
        { key: 50, action: '', title: 'แดชบอร์ด คำที่ค้นหาภายในระบบ', path: '/DashboardAdminSearchMobile', isDisabled: false },
        { key: 58, action: '', title: 'แดชบอร์ด OTOP', path: '/dashboardOTOPMobile', isDisabled: false },
        { key: 4, action: 'mdi-bookshelf', title: 'Dashboard ขนส่ง', path: '', isDisabled: true },
        { key: 5, action: '', title: 'Operation Dashboard', path: '/dashboardTransportMobile', isDisabled: false },
        { key: 6, action: '', title: 'Order stage คงค้าง', path: '/pendingOrderStageMobile', isDisabled: false },
        { key: 7, action: '', title: 'ข้อมูลออเดอร์คงค้าง', path: '/pendingOrderInfoMobile', isDisabled: false },
        { key: 8, action: 'mdi-shield-account', title: 'จัดการผู้ใช้ NGC', path: '', isDisabled: true },
        { key: 9, action: '', title: 'จัดการแอดมินของระบบ', path: '/adminPanitManageMobile', isDisabled: false },
        { key: 10, action: '', title: 'รายชื่อผู้ใช้งานในระบบ', path: '/adminUserWebMobile', isDisabled: false },
        { key: 11, action: '', title: 'จัดการผู้ใช้งานร้านค้า', path: '/manageUserShopMobile', isDisabled: false },
        { key: 12, action: '', title: 'จัดการผู้ใช้งานบริษัท', path: '/manageUserCompanyMobile', isDisabled: false },
        { key: 13, action: 'mdi-domain', title: 'จัดการร้านค้าและบริษัท', path: '', isDisabled: true },
        { key: 14, action: '', title: 'จัดการร้านค้าของระบบ', path: '/adminShopManageMobile', isDisabled: false },
        { key: 15, action: '', title: 'จัดการบริษัทของระบบ', path: '/adminBusinessManageMobile', isDisabled: false },
        // { key: 57, action: '', title: 'จัดการนิติบุคคลของระบบ', path: '/BusinessManageMobile', isDisabled: false },
        { key: 16, action: '', title: 'รายการ Stock สินค้าทั้งหมด', path: '/stockAdminMobile', isDisabled: false },
        { key: 17, action: '', title: 'หน้ารายงาน e-Tax ของระบบ', path: '/eTaxAdminMobile', isDisabled: false },
        { key: 18, action: '', title: 'ร้านค้าเปิด-ปิดในระบบ', path: '/dashboardStatusShopMobile', isDisabled: false },
        { key: 49, action: '', title: 'ร้านค้าที่มีสินค้าในระบบ', path: '/DashboardProductShopMobile', isDisabled: false },
        { key: 19, action: '', title: 'จัดการหมวดหมู่สินค้า', path: '/ManageCategoryMobile', isDisabled: false },
        { key: 19, action: '', title: 'รายชื่อร้านค้าที่สนใจเข้าร่วม', path: '/ManageRegisterShopMobile', isDisabled: false },
        { key: 62, action: '', title: 'รายการลงทะเบียนคู่ค้า', path: '/ManageRegisterInfoPartnerMobile', isDisabled: false },
        { key: 63, action: '', title: 'รายการลงทะเบียนเปิดร้าน', path: '/ManageRegisterInfoShopMobile', isDisabled: false },
        { key: 20, action: 'mdi-truck', title: 'จัดการขนส่งระบบ', path: '', isDisabled: true },
        { key: 21, action: '', title: 'จัดการขนส่งระบบ', path: '/manageShippingMobile', isDisabled: false },
        { key: 22, action: '', title: 'จัดการรายการขนส่งระบบ', path: '/manageOrderShippingMobile', isDisabled: false },
        { key: 57, action: '', title: 'แก้ไขที่อยู่ขนส่ง', path: '/UpdateShippingAddressMobile', isDisabled: false },
        { key: 60, action: 'mdi-truck', title: 'จัดการโอนเงิน', path: '', isDisabled: true },
        { key: 61, action: '', title: 'จัดการรายการโอนเงิน', path: '/paymentManageMobile', isDisabled: false },
        { key: 23, action: 'mdi-monitor-dashboard', title: 'จัดการ Landing Page', path: '', isDisabled: true },
        { key: 24, action: '', title: 'จัดการ Landing Page', path: '/adminBannerManageMobile', isDisabled: false },
        { key: 40, action: '', title: 'จัดการ Flash Sale ร้านค้า', path: '/adminFlashsaleManageMobile', isDisabled: false },
        { key: 25, action: '', title: 'จัดการประเภทร้านค้า', path: '/groupStoreMobile', isDisabled: false },
        { key: 26, action: '', title: 'จัดการลำดับร้านค้า', path: '/manageListStoreMobile', isDisabled: false },
        { key: 26, action: '', title: 'แบนเนอร์โฆษณาและข่าวสาร', path: '/AdminManagePopupMobile', isDisabled: false },
        { key: 45, action: '', title: 'ปรับแต่งประเภทร้านค้า', path: '/adminGroupShopBGManageMobile', isDisabled: false },
        { key: 55, action: '', title: 'ปรับแต่ง Banner', path: '/customBannerMobile', isDisabled: false },
        { key: 59, action: '', title: 'จัดการสินค้าแนะนำ', path: '/recommentProductManageMobile', isDisabled: false },
        { key: 27, action: 'mdi-forum-outline', title: 'Chat Admin NGC', path: '', isDisabled: true },
        { key: 28, action: '', title: 'แชทแอดมิน', path: '', isDisabled: false },
        { key: 29, action: 'mdi-cart-outline', title: 'รายการสั่งซื้อ', path: '', isDisabled: true },
        { key: 30, action: '', title: 'Order JV', path: '/orderJVMobile', isDisabled: false },
        { key: 31, action: '', title: 'Search Order', path: '/SearchOrderMobile', isDisabled: false },
        { key: 32, action: '', title: 'สร้าง Order JV ERP', path: '/CreateOrderJVERPMobile', isDisabled: false },
        // { key: 49, action: '', title: 'รายการยกเลิก order ร้านค้า', path: '/AdminListOrderCancelMobile', isDisabled: false },
        { key: 33, action: 'mdi-ticket-percent', title: 'จัดการโปรโมชัน', path: '', isDisabled: true },
        { key: 34, action: '', title: 'จัดการคูปอง', path: '/adminManageCouponMobile', isDisabled: false },
        { key: 56, action: '', title: 'จัดการแจ้งเตือน', path: '/adminManageNotificationMobile', isDisabled: false },
        { key: 35, action: 'mdi mdi-chart-line', title: 'โปรแกรม Affiliate', path: '', isDisabled: true },
        { key: 36, action: '', title: 'ผู้สมัครเข้าร่วม Affiliate', path: '/userJoinAffiliateMobile', isDisabled: false },
        { key: 37, action: '', title: 'ร้านค้าเข้าร่วม Affiliate', path: '/sellerJoinAffiliateMobile', isDisabled: false },
        { key: 38, action: '', title: 'ผู้สมัครเข้าร่วมร้านค้า Affiliate', path: '/userJoinSellerAffiliateMobile', isDisabled: false },
        { key: 39, action: '', title: 'รายงานค่าคอมมิชชัน Affiliate', path: '/reportCommissionAffiliateAdminMobile', isDisabled: false },
        { key: 40, action: 'mdi-cog', title: 'ERP', path: '', isDisabled: true },
        { key: 41, action: '', title: 'จัดการ ERP', path: '/ManageERPMobile', isDisabled: false },
        { key: 42, action: 'mdi-handshake', title: 'Software Marketplace', path: '', isDisabled: true },
        { key: 43, action: '', title: 'สินค้าบริการของ Partner', path: '/AdminManageProductPartnerMobile', isDisabled: false },
        { key: 44, action: '', title: 'ร้านค้าที่เชื่อมต่อ Partner', path: '/AdminManageShopPartnerMobile', isDisabled: false },
        { key: 46, action: '', title: 'จัดการ Partner', path: '/managePositionBussinessMobile', isDisabled: false },
        { key: 49, action: '', title: 'จัดการคู่มือการใช้งานและสัมมนา', path: '/AdminAddUserManualAndSeminarLinkMobile', isDisabled: false },
        { key: 47, action: 'mdi-thumb-up', title: 'Reach', path: '', isDisabled: true },
        { key: 48, action: '', title: 'การเข้าชม', path: '/AdminReachMobile', isDisabled: false }
      ]
      // {
      //   action: 'mdi-file-document-outline',
      //   key: 2,
      //   items: [{ key: 5, title: 'รายการสั่งซื้อของฉัน', path: 'pobuyeruiProfile' }],
      //   title: 'รายการสั่งซื้อ'
      // }
    }
  },
  methods: {
    async openChat () {
      const sharedToken = await createChat.sharetoken()
      // https://www.chatme.co.th/uat/chatmeservice/converseme/AuthPlugin?shared_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************&shop_name=ADMIN-NGC&shop_id=99
      const adminData = await 'https://www.chatme.co.th/chatmeservice/converseme/AuthPlugin?shared_token=' + sharedToken.data.shared_token + '&shop_name=ADMIN-NGC&shop_id=1'
      // console.log('adminData', adminData)
      await window.open(adminData)
    },
    changePage (val) {
      this.$router.push({ path: `${val}` }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.backgroundPage{
  background-color: #F6F6F6;
}
.container {
  max-width: 100%;
}
.v-application ul, .v-application ol {
    padding: 0px 0px !important;
}
.v-application ol, .v-application ul {
    padding: 0px 0px !important;
}
</style>
