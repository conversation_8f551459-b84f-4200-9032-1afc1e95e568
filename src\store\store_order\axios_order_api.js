import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    // console.log('ONEDATA NAAAa', oneData)
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  }
}

export default {
  // Product Detail
  async GetOrderList (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_order_buyer_v2`, val, auth)
      // console.log('data orderlist', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async OrderDetail (val) {
    const auth = await GetToken()
    // console.log(val)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_order_v2`, val, auth)
      // console.log('data orderdetail', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async OrderListSeller (val) {
    const auth = await GetToken()
    // const data = {
    //   seller_shop_id: val.seller_shop_id.toString()
    // }
    // console.log('data', data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_order_seller_v2`, val, auth)
      // console.log('data orderdetail=====>', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async OrderListSellerDetail (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_order_purchaser`, val, auth)
      // console.log('data detail_order_purchaser=====>', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async OrderRenew (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/order_renew`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async OrderDetailSeller (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_order_seller_v2`, val, auth)
      // console.log('Shoooooooppppp   orderdetail', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateStatusSeller (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/update_order_status`, val, auth)
      // console.log('update send status ', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CancelStatusSeller (val) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/cancel_order_mobilyst`, val)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateStatusBuyer (val) {
    const auth = await GetToken()
    // console.log('ออกมาแล้ววววว', val)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/buyer_update_status_order`, val, auth)
      // console.log('update send status ', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListOrderApprove () {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/detail_approver_order_v2`, auth)
      // console.log('List Order Approve ', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ApproveData (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approve_order`, val, auth)
      // console.log('List Order Approve ', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AcceptProduct (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/accecpt_product/accecpt_product`, val, auth)
      // console.log('AcceptProduct', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // CheckAcceptProduct
  async CheckAcceptProduct (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/accecpt_product/check_accept`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListOrder (val) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_ups_order`, val)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Refund buyer list
  async RefundBuyerList (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_refund_buyer`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Refund order detail
  async RefundOrderDetail (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_refund_buyer`, val, auth)
      // console.log('data orderdetail=====>', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Send Refund Buyer
  async SendRefundBuyer (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/send_refund`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Send Approve Refund Seller
  async ApproveRefundSeller (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approve_refund_seller`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Detaile Approve Refund Seller
  async ApproveDetailRefundSeller (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_refund_approve_seller`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetallProductReviewSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_all_product_review_seller`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateReplyComment (data) {
    const shopSellerID = localStorage.getItem('shopSellerID').toString()
    const edit = await {
      feedback_id: shopSellerID,
      reply_comment: '555555555'
    }
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_reply_comment`, edit)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditReplyComment (data) {
    const shopSellerID = localStorage.getItem('shopSellerID').toString()
    const edit = await {
      reply_id: shopSellerID,
      reply_edit: ''
    }
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_reply_comment`, edit)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailReviewSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_product_review_seller_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ChangeStatusReview (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/seller_update_status_comment`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailFeedbackReview (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_product_feedback_seller`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async Reportb2bSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}report/b2b/seller`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ReportLink (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}exports/reports/order`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async POSellerb2b (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_order_seller_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async OrderChange (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/order_change`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchProduct (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}search/product/search_bar`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async OrderRenewAndChange (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/order_change_renew`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async OrderBuyAgain (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}order/order_repeat`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CheckUpdateOrderRepeat (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/check_update_order_repeat`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UploadOrderDocument (val) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: {
        Authorization: `Bearer ${oneData.user.access_token}`,
        'Content-Type': 'multipart/form-data'
      }
    }
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}orderDocument/uploadOrderDocument`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetOrderDocument (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}orderDocument/getOrderDocument`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CancelOrder (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/cancel_order_by_user`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async OrderListERP (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/orderlist_shopV2`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetCancelOrderDetails (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_cancel_order`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetServiceID (val) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/check_shop`, val)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ApproveAndRejectOrder (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approve_cancel_order`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListServicw (val) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/check_service`, val)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EtaxCreditNote (val) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}etax/credit_note`, val)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EtaxCheckShop (val) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}etax/checkshop`, val)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async OrderPaymentPartner (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}shop/list_payment`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailOrderPaymentPartner (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}shop/list_payment_detail`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailOrderPurchasePartner (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}shop/list_payment_purchase`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDetailOrderBuyer (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_order_v3`, val, auth)
      return response.data
    } catch (err) {
      return err.response.data
    }
  },
  async GetOrderListV3 (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_order_buyer_v3`, val, auth)
      // console.log('data orderlist', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetPaymentMethodWithOrderPlatform (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_payment_method_with_order_platform`, val, auth)
      // console.log('data orderlist', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async POSellerb2bV3 (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_order_seller_v3`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PONewListBuyer (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/new_list_order_buyer`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ReplyComment (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}shop/reply_comment`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetPaymentUsers (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}users/get_payment_users`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
