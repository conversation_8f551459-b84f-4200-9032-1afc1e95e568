<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <!-- <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" >จัดการผู้ใช้งานร้านค้า</v-card-title> -->
      <v-card-title v-if="!MobileSize" style="font-weight: 700; font-size: 24px;" ><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>จัดการผู้ใช้งานร้าน <span style="color: #27AB9C; margin-left: .5vw;">   {{ this.$route.query.shop_name }}</span></v-card-title>
      <v-card-title v-else style="font-weight: 700;" ><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>จัดการผู้ใช้งานร้าน <span style="color: #27AB9C; margin-left: .5vw;"> {{ this.$route.query.shop_name }}</span></v-card-title>
      <!-- table user list -->
      <v-row>
        <v-col cols="12">
          <v-col cols="12" md="6" sm="12">
            <v-text-field v-model="search" placeholder="ค้นหาจากผู้ใช้ร้านค้าในระบบ" outlined rounded dense hide-details>
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col :style="MobileSize || IpadSize ? { display: 'flex', flexDirection: 'column', gap: '2.5vw' } : {display: 'flex'}" :cols="MobileSize ? 12 : IpadSize ? 12 : ''" :class="IpadSize ? 'pt-0' : ''">
            <span
              style="
              font-size: 18px;
              line-height: 24px;
              align-items: center;
              color: #333333;
              font-weight: 600;" >
              รายชื่อผู้ใช้งานในระบบทั้งหมด {{ showCountRequest }} รายการ
            </span>
            <v-spacer></v-spacer>
            <v-btn
              class="mr-2"
              @click="modalMenuPosition = true"
              outlined
              style="border-radius: 2vw; color: #27AB9C;">
              <v-icon>mdi-plus-circle</v-icon><br>
              <span>จัดการตำแหน่งและสิทธิ์</span>
            </v-btn>
            <v-btn
              @click="OpenDialogAddUser"
              style="
              border-radius: 2vw;
              color: #fff;
              background-color: #27AB9C;">
              <v-icon>mdi-plus-circle</v-icon><br>
              <span>เพิ่มผู้ใช้งานร้านค้า</span>
            </v-btn>
          </v-col>
          <v-col cols="12">
            <v-data-table
            :headers="headers"
            :search="search"
            :items="listUser"
            style="width:100%; text-align: center; white-space: nowrap;"
            height="100%"
            @pagination="countRequest"
            no-results-text="ไม่พบรายการร้านค้าในระบบ"
            no-data-text="ไม่พบรายการร้านค้าในระบบ"
            :items-per-page="10"
            class="elevation-1 mt-4"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            >
              <template v-slot:[`item.indexOfUser`]="{ index }">
                  {{ index + 1 }}
                </template>
              <!-- action edit user -->
              <template v-slot:[`item.action`]="{ item }">
                <v-btn text color="primary" @click="OpenDialogEditUser(item)">
                  <v-icon>mdi-square-edit-outline</v-icon><br>
                  <span>แก้ไข</span>
                </v-btn>
                <v-btn text color="error" @click="SwalDeleteUser(item)">
                  <v-icon>mdi-delete-outline</v-icon><br>
                  <span>ลบ</span>
                </v-btn>
              </template>
              <!-- show name user -->
              <template v-slot:[`item.first_name_th`]="{ item }">
                <span v-if="item.first_name_th" color="#27AB9C" > {{ item.first_name_th + ' ' + item.last_name_th }}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.email`]="{ item }">
                <span v-if="item.email" color="#27AB9C" > {{ item.email }}</span>
                <span v-else>-</span>
              </template>
              <!-- show position in shop -->
              <template v-slot:[`item.position_and_role`]="{ item }">
                <div v-if="MobileSize">
                  <span v-if="item.position_and_role">
                    <v-select
                      v-model="selectedRole"
                      :items="item.position_and_role.map(role => role.position_name)"
                      solo
                      placeholder="สิทธิ์ผู้ใช้งานร้านค้า"
                      label="ตำแหน่ง"
                    ></v-select>
                  </span>
                  <span v-else>-</span>
                </div>
                <div v-else>
                  <span v-if="item.position_and_role !== []">
                    <span v-for="(role, index) in item.position_and_role" :key="index">
                      {{ role.position_name }}
                      <span v-if="index < item.position_and_role.length - 1">, </span>
                    </span>
                  </span>
                  <span v-if="item.position_and_role.length === 0">
                    <span>-</span>
                  </span>
                </div>
              </template>
              </v-data-table>
          </v-col>
          <!-- diglog edit user -->
          <v-dialog content-class="elevation-0" v-model="modalEditUser" :width="MobileSize ? '100%' : '40%'">
            <v-card style="border-radius: 1.5vw;">
              <v-card-title class="backgroundHead">
                <v-row>
                  <v-col style="text-align: center;" class="pt-4">
                    <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>แก้ไขข้อมูลผู้ใช้งานร้านค้า</b></span>
                  </v-col>
                  <v-btn fab small @click="cancel()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
                </v-row>
              </v-card-title>

              <v-card-text class="pt-4">
                <div>
                  <span>อีเมล : </span>
                  <v-text-field disabled style="border-radius: 8px;" class="input_text namedoc_input" v-model="email" placeholder="อีเมลผู้ใช้" outlined dense>
                    <v-icon slot="append" color="#c6c6c6">mdi-square-edit-outline</v-icon>
                  </v-text-field>
                </div>
                <div>
                  <span>ชื่อ-นามสกุล : </span>
                  <v-text-field disabled style="border-radius: 8px;" class="input_text namedoc_input" v-model="name" placeholder="ชื่อ-นามสกุล" outlined dense>
                    <v-icon slot="append" color="#c6c6c6">mdi-square-edit-outline</v-icon>
                  </v-text-field>
                </div>
                <div>
                  <span>สิทธิ์ผู้ใช้งาน : </span>
                  <v-select
                    v-model="selectedRoleID"
                    :items="listPosition"
                    item-text="position_name"
                    item-value="position_id"
                    label="สิทธิ์ผู้ใช้งาน"
                    multiple
                    solo
                    chips
                    style="border-radius: 8px; max-height: 100px; overflow-y: auto;"
                    placeholder="เลือกสิทธิ์ผู้ใช้งาน"
                    :menu-props="{ maxHeight: '230px' }"
                  ></v-select>
                </div>
              </v-card-text>

              <v-card-actions style="display: flex; justify-content: center; gap: 1vw; padding-bottom: 2vw;">
                <v-btn
                  color="primary"
                  outlined
                  rounded
                  width="8vw"
                  height="40"
                  @click="cancel()"
                >
                  ยกเลิก
                </v-btn>
                <v-btn
                  color="primary"
                  width="8vw"
                  height="40"
                  rounded
                  @click="EditUserShop()"
                >
                  ตกลง
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
          <!-- diglog add user -->
          <v-dialog content-class="elevation-0" v-model="modalAddUser" :width="MobileSize || IpadSize ? '100%' : IpadProSize ? '80%' : '48%'">
            <v-card width="100%" style="border-radius: 1.5vw;">
              <v-card-title class="backgroundHead">
                <v-row>
                  <v-col style="text-align: center;" class="pt-4">
                    <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>เพิ่มข้อมูลผู้ใช้งานร้านค้า</b></span>
                  </v-col>
                  <v-btn fab small @click="cancel()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
                </v-row>
              </v-card-title>

              <v-card-text class="pt-4">
                <div>
                  <span>เพิ่มผู้ใช้งานจาก Email หรือ ชื่อ : </span>
                  <v-col cols="12">
                    <div style="display: flex; gap: .5vw; margin-top: .5vw;">
                    <v-text-field @keyup.enter="SearchUserShop()" style="border-radius: 8px;" class="input_text namedoc_input" v-model="email" placeholder="อีเมลผู้ใช้" outlined dense></v-text-field>
                    <v-btn color="primary" @click="SearchUserShop()">ค้นหา</v-btn>
                    </div>
                  </v-col>
                </div>
                <div v-if="showForm">
                  <v-col cols="12" :style="MobileSize ? 'margin-top: -9vw;' : 'margin-top: -2.5vw;'">
                    <v-card :class="MobileSize ? 'd-flex flex-column mb-3 pa-2 align-center' : 'd-flex mb-3 pa-2 align-center'" style="border-radius: 1vw;" v-for="(item, index) in listUserEmail" :key="index">
                      <v-col :cols="MobileSize ? 12 : 3" :class="MobileSize ? 'd-flex justify-center' : ''">
                        <span v-if="(item.first_name_th !== '' && item.first_name_th !== null) || (item.last_name_th !== '' && item.last_name_th !== null)">{{ item.first_name_th }} {{item.last_name_th}}</span>
                        <span v-else>ไม่มีชื่อผู้ใช้</span>
                      </v-col>
                      <v-col :cols="MobileSize ? 12 : 3" :class="MobileSize ? 'd-flex justify-center' : ''" :style="MobileSize ? 'margin-top: -4vw' : ''">
                        <span v-if="item.email !== '' && item.email !== null">{{ item.email }}</span>
                        <span v-else>ไม่มีอีเมล</span>
                      </v-col>
                      <v-col :cols="MobileSize ? 12 : 3" :class="MobileSize ? 'd-flex justify-center' : ''" :style="MobileSize ? 'margin-top: -4vw' : ''">
                        <v-chip v-if="item.in_shop === 'yes'" :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">มีตำแหน่ง</v-chip>
                        <v-chip v-else :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#f7c5ad" text-color="#f50">ไม่มีตำแหน่ง</v-chip>
                      </v-col>
                      <v-col :cols="MobileSize ? 12 : 3" :class="MobileSize ? 'd-flex justify-center' : ''" :style="MobileSize ? 'margin-top: -4vw' : ''">
                        <v-btn color="#27AB9C" text :disabled="item.in_shop === 'yes'" @click="OpenDialogSelectUser(item)">
                          <span>
                            <v-icon color="#27AB9C" class="">mdi-account</v-icon>เพิ่มตำแหน่ง
                          </span>
                        </v-btn>
                      </v-col>
                    </v-card>
                  </v-col>
                </div>
              </v-card-text>
            </v-card>
          </v-dialog>
          <!-- add user -->
          <v-dialog content-class="elevation-0" v-model="selectUser" :width="MobileSize ? '100%' : '35%'">
            <v-card width="100%" style="border-radius: 1.5vw;">
              <v-card-title class="backgroundHead">
                <v-row>
                  <v-col style="text-align: center;" class="pt-4">
                    <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>เพิ่มข้อมูลผู้ใช้งานร้านค้า</b></span>
                  </v-col>
                  <v-btn fab small @click="cancelAddUser()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
                </v-row>
              </v-card-title>

              <v-card-text class="pt-4">
                <div>
                  <div>
                    <span>ชื่อ-นามสกุล : </span>
                    <v-text-field disabled style="border-radius: 8px;" class="input_text namedoc_input" v-model="nameSelect" placeholder="ชื่อ-นามสกุล" outlined dense></v-text-field>
                  </div>
                  <div>
                    <span>อีเมล : </span>
                    <v-text-field disabled style="border-radius: 8px;" class="input_text namedoc_input" v-model="emailSelect" placeholder="ชื่อ-นามสกุล" outlined dense></v-text-field>
                  </div>
                  <div>
                    <span>สิทธิ์ผู้ใช้งาน : </span>
                    <v-select
                      v-model="selectedPositions"
                      :items="listPosition"
                      item-text="position_name"
                      item-value="position_id"
                      label="สิทธิ์ผู้ใช้งาน"
                      multiple
                      solo
                      chips
                      style="border-radius: 8px; max-height: 100px; overflow-y: auto;"
                      placeholder="เลือกสิทธิ์ผู้ใช้งาน"
                      :menu-props="{ maxHeight: '230px' }"
                    ></v-select>
                  </div>
                </div>
              </v-card-text>

              <v-card-actions style="display: flex; justify-content: center; gap: 1vw; padding-bottom: 2vw;">
              <!-- <v-spacer></v-spacer> -->
                <v-btn
                  color="primary"
                  outlined
                  rounded
                  width="8vw"
                  height="40"
                  @click="cancelAddUser()"
                >
                  ยกเลิก
                </v-btn>
                <v-btn
                  color="primary"
                  width="8vw"
                  height="40"
                  rounded
                  :disabled="toggleConfirm"
                  @click="AddUserShop()"
                >
                  ตกลง
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
          <!-- menu position -->
          <v-dialog content-class="elevation-0" v-model="modalMenuPosition" width="500px">
            <v-card style="max-width: 500px; border-radius: 1vw;">
              <v-toolbar height="50px" style="background-color: #27AB9C;">
                <v-row class="d-flex justify-end">
                  <v-col style="text-align: center;" class="pt-4">
                    <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>จัดการตำแหน่งและสิทธิ์</b></span>
                  </v-col>
                  <v-btn fab small @click="modalMenuPosition = false" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
                </v-row>
              </v-toolbar>
              <v-col>
                <v-row class="pa-3">
                  <v-col cols="6" style="display: flex; align-items: center; justify-content: center;">
                    <v-btn class="formatBtn" height="185" :width="MobileSize ? 140 : 185" elevation="0" @click="openDialogAddPosition">
                      <v-col>
                        <v-row class="d-flex justify-center">
                          <v-avatar rounded :size="MobileSize ? 80 : 100">
                            <v-img src="@/assets/ImageINET-Marketplace/ICONShop/addPositionShop.png"></v-img>
                          </v-avatar>
                          <!-- <v-img v-if="!MobileSize" src="@/assets/ImageINET-Marketplace/ICONShop/addPositionShop.png" height="100" width="100"></v-img>
                          <img v-else src="@/assets/ImageINET-Marketplace/ICONShop/addPositionShop.png" width="100"> -->
                        </v-row>
                        <v-row style="justify-content: center;">
                          <span :style="MobileSize ? 'font-size: small;' : ''" class="pt-2">
                            เพิ่มตำแหน่งและสิทธิ์
                          </span>
                        </v-row>
                      </v-col>
                    </v-btn>
                  </v-col>
                  <v-col cols="6" style="display: flex; align-items: center; justify-content: center;">
                    <v-btn class="formatBtn" height="185" :width="MobileSize ? 140 : 185" elevation="0" @click="openDialogDetailPosition">
                      <v-col>
                        <v-row class="d-flex justify-center">
                          <v-avatar rounded :size="MobileSize ? 80 : 100">
                            <v-img src="@/assets/ImageINET-Marketplace/ICONShop/editPositionShop.png"></v-img>
                          </v-avatar>
                          <!-- <v-img v-if="!MobileSize" src="@/assets/coupon_image/shipping.png" height="140" width="125"></v-img>
                          <img v-else src="@/assets/coupon_image/shipping.png" width="100"> -->
                        </v-row>
                        <v-row style="justify-content: center;">
                          <span :style="MobileSize ? 'font-size: small;' : ''" class="pt-2">
                            แก้ไขตำแหน่งและสิทธิ์
                          </span>
                        </v-row>
                      </v-col>
                    </v-btn>
                  </v-col>
                </v-row>
              </v-col>
            </v-card>
          </v-dialog>
          <!-- dialog add position -->
          <v-dialog content-class="elevation-0" v-model="modalAddPosition" :width="MobileSize ? '100%' : IpadSize ? '70%' : IpadProSize ? '50%' : '35%'">
            <v-card>
              <v-toolbar height="50px" style="background-color: #27AB9C;">
                <v-row class="d-flex justify-end">
                  <v-col style="text-align: center;" class="pt-4">
                    <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>กำหนดตำแหน่งและสิทธิ์การใช้งาน</b></span>
                  </v-col>
                  <v-btn fab small @click="closeModalCreate" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
                </v-row>
              </v-toolbar>

              <v-card-text class="pt-4">
                <v-row v-if="MobileSize || IpadSize || IpadProSize">
                  <v-col class="d-flex align-center">
                    <v-img
                      lazy-src="@/assets/Businessman.png"
                      max-height="40"
                      max-width="40"
                      src="@/assets/Businessman.png"
                      contain
                      class="mr-2"
                    ></v-img>
                    <span style="font-size: inherit;">รายละเอียดตำแหน่งและสิทธิ์การใช้งาน</span>
                  </v-col>
                </v-row>
                <v-row class="d-flex align-center" v-else>
                  <v-col cols="12" md="2" sm="2">
                    <v-img
                      lazy-src="@/assets/Businessman.png"
                      max-height="100"
                      max-width="200"
                      src="@/assets/Businessman.png"
                      contain
                    ></v-img>
                  </v-col>
                  <v-col cols="12" md="10" style="font-size: medium;">
                    รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                  </v-col>
                </v-row>
              </v-card-text>

              <v-card-text :style="MobileSize ? 'margin-top: -10vw;' : 'margin-top: -2vw;'">
                <v-row style="margin: -5px" dense no-gutters>
                  <v-col cols="12" class="mt-8">
                    <span>ชื่อตำแหน่ง <span style="color: red;">*</span></span>
                    <v-text-field placeholder="ระบุตำแหน่ง" outlined dense v-model="position_name" :rules="Rules.position_name" ></v-text-field>
                  </v-col>
                  <v-col cols="12" :style="MobileSize ? '' : 'margin-top: -1vw;'">
                    <v-list>
                      <v-subheader :class="MobileSize ? 'px-0' : ''" style="font-weight: 400; font-size: 16px; line-height: 1px; color: #00000099;">เมนูร้านค้าที่คุณต้องการแสดง :</v-subheader>
                      <v-list-item-group
                        v-model="positionType"
                        multiple
                      >
                        <template v-for="(item, i) in itemMenuShop">
                          <v-list-item
                            :key="i"
                            :value="item.key"
                            v-model="item.checked"
                            :input-value="item.checked"
                            :class="MobileSize ? 'px-0' : ''"
                            class="max-v-list-height"
                            dense
                          >
                            <template v-slot:default="{ }">
                              <v-list-item-content :style="MobileSize ? 'font-weight: 400; font-size: 16px; line-height: 16px; color: #00000099;' : 'font-weight: 400; font-size: 16px; line-height: 16px; color: #00000099;'">
                                <!-- <v-list-item-title v-text="item.title"></v-list-item-title> -->
                                {{item.title}}
                              </v-list-item-content>

                              <v-list-item-action>
                                <v-checkbox
                                  v-model="item.checked"
                                  :input-value="item.checked"
                                  return-o
                                ></v-checkbox>
                              </v-list-item-action>
                            </template>
                            </v-list-item>
                            </template>
                      </v-list-item-group>
                    </v-list>
                  </v-col>
                </v-row>
              </v-card-text>

              <v-card-actions :class="MobileSize ? 'justify-center pb-5' : ''" style="display: flex; justify-content: end; gap: .5vw; padding-bottom: 2vw;">
                <v-btn
                  color="primary"
                  outlined
                  rounded
                  @click="closeModalCreate"
                >
                  ยกเลิก
                </v-btn>
                <v-btn
                  color="primary"
                  rounded
                  :disabled="(position_name !== ' ' && position_name !== '') ? false : true"
                  @click="createPositionShop()"
                >
                  ตกลง
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
          <!-- dialog list position -->
          <v-dialog content-class="elevation-0" v-model="modalListPosition" :width="MobileSize ? '100%' : IpadSize ? '70%' : IpadProSize ? '50%' : '35%'">
            <v-card>
              <v-toolbar height="50px" style="background-color: #27AB9C;">
                <v-row class="d-flex justify-end">
                  <v-col style="text-align: center;" class="pt-4">
                    <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ตำแหน่งและสิทธิ์การใช้งาน</b></span>
                  </v-col>
                  <v-btn fab small @click="closeDialogDetailPosition" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
                </v-row>
              </v-toolbar>

              <v-card-text class="pt-4">
                <v-row v-if="MobileSize || IpadSize || IpadProSize">
                  <v-col class="d-flex align-center">
                    <v-img
                      lazy-src="@/assets/Businessman.png"
                      max-height="40"
                      max-width="40"
                      src="@/assets/Businessman.png"
                      contain
                      class="mr-2"
                    ></v-img>
                    <span style="font-size: inherit;">รายละเอียดตำแหน่งและสิทธิ์การใช้งาน</span>
                  </v-col>
                </v-row>
                <v-row class="d-flex align-center" v-else>
                  <v-col cols="12" md="2" sm="2">
                    <v-img
                      lazy-src="@/assets/Businessman.png"
                      max-height="100"
                      max-width="200"
                      src="@/assets/Businessman.png"
                      contain
                    ></v-img>
                  </v-col>
                  <v-col cols="12" md="10" style="font-size: medium;">
                    รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                  </v-col>
                </v-row>
              </v-card-text>

              <v-card-text class="mt-5">
                <v-row>
                  <v-col style="margin-top: -1vw;">
                    <span>รายการตำแหน่ง :</span>
                    <v-text-field
                    v-model="searchPosition"
                    dense
                    outlined
                    rounded
                    placeholder="ค้นหาจากตำแหน่ง"
                    >
                      <v-icon slot="append" color="#27AB9C">mdi-magnify </v-icon>
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" :style="MobileSize ? 'margin-top: -9vw;' : 'margin-top: -2.5vw;'">
                    <v-card :class="MobileSize ? 'd-flex flex-column mb-3 pa-2 align-center' : 'd-flex mb-3 pa-2 align-center'" style="border-radius: 1vw;" v-for="(item, index) in filterPositions" :key="index">
                      <v-col :cols="MobileSize ? 12 : 4" :class="MobileSize ? 'd-flex justify-center' : ''">
                        <span>{{ item.position_name || '-' }}</span>
                      </v-col>
                      <v-col :cols="MobileSize ? 12 : 4" :class="MobileSize ? 'd-flex justify-center' : ''" :style="MobileSize ? 'margin-top: -4vw' : ''">
                        <v-chip v-if="item.status === 'active'" :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">กำลังใช้งาน</v-chip>
                        <v-chip v-else :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#f7c5ad" text-color="#f50">ยกเลิก</v-chip>
                      </v-col>
                      <v-col :cols="MobileSize ? 12 : 4" :class="MobileSize ? 'd-flex justify-center' : ''" :style="MobileSize ? 'margin-top: -4vw' : ''">
                        <v-btn color="#27AB9C" text @click="detailPositoin(item)">
                          <span>
                            <v-icon color="#27AB9C" class="">mdi-file-document-outline</v-icon>รายละเอียด
                          </span>
                        </v-btn>
                      </v-col>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-dialog>
          <!-- dialog detail position -->
          <v-dialog content-class="elevation-0" v-model="modalDetailPosition" :width="MobileSize ? '100%' : IpadSize ? '70%' : IpadProSize ? '50%' : '35%'">
            <v-card class=".rounded-lg">
              <v-toolbar height="50px" style="background-color: #27AB9C;">
                <v-row class="d-flex justify-end">
                  <v-col style="text-align: center;" class="pt-4">
                    <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ตำแหน่งและสิทธิ์การใช้งาน</b></span>
                  </v-col>
                  <v-btn fab small @click="closeDialogShowDetail" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
                </v-row>
              </v-toolbar>
              <v-card-text class="pt-4">
                <v-row v-if="MobileSize || IpadSize || IpadProSize">
                  <v-col class="d-flex align-center">
                    <v-img
                      lazy-src="@/assets/Businessman.png"
                      max-height="40"
                      max-width="40"
                      src="@/assets/Businessman.png"
                      contain
                      class="mr-2"
                    ></v-img>
                    <span style="font-size: inherit;">รายละเอียดตำแหน่งและสิทธิ์การใช้งาน</span>
                  </v-col>
                </v-row>
                <v-row class="d-flex align-center" v-else>
                  <v-col cols="12" md="2" sm="2">
                    <v-img
                      lazy-src="@/assets/Businessman.png"
                      max-height="100"
                      max-width="200"
                      src="@/assets/Businessman.png"
                      contain
                    ></v-img>
                  </v-col>
                  <v-col cols="12" md="10" style="font-size: medium;">
                    รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                  </v-col>
                </v-row>
              </v-card-text>
              <v-card-text class="mb-15">
                <v-row class="mt-5">
                  <v-col cols="12" md="12">
                    <v-card class="rounded-lg"  elevation="0" style="border: 1px solid #E6E6E6;">
                      <v-row>
                        <v-col cols="12" md="12" sm="12" xs="12">
                          <v-row>
                            <v-col cols="12">
                              <v-container>
                                <v-row no-gutters>
                                  <v-col cols="10" md="10">
                                    <p class="mt-0"
                                      style="font-weight: 400; font-size: 16px; line-height: 20px; color: #333333;">
                                      ตำแหน่ง : {{ position_NameUser }}
                                    </p>
                                  </v-col>
                                  <v-col cols="2" md="2" align="right">
                                    <div class="mr-3" style="margin-Top: -8px">
                                      <v-btn v-if="openStatus === 'active'" @click="ShowEditPosition()" icon dense>
                                        <v-avatar rounded size="18">
                                          <v-img contain :src="require('@/assets/icons/Union.png')"></v-img>
                                        </v-avatar>
                                      </v-btn>
                                    </div>
                                  </v-col>
                                  <v-col cols="12" md="12">
                                    <p class="mt-0"
                                      style="font-weight: 400; font-size: 16px; line-height: 8px; color: #27AB9C;">
                                      สิทธิ์การใช้งาน
                                    </p>
                                  </v-col>
                                  <v-col v-for="(item, index) in itemMenuShop2" :key="index" cols="12" md="12">
                                    <v-row dense>
                                      <v-col cols="10" md="11">
                                        <v-list-item-content
                                          :style="!MobileSize ? 'font-weight: 400; font-size: 16px; line-height: 1px; color:: #333333;' : 'font-weight: 400; font-size: 16px; line-height: 16px; color:: #333333; padding-top: 8px'">
                                          {{item.title}}
                                        </v-list-item-content>
                                      </v-col>
                                      <v-col cols="2" md="1">
                                        <v-checkbox readonly v-model="item.checked" style="margin-Top: -1px" dense
                                          color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                      </v-col>
                                    </v-row>
                                  </v-col>
                                </v-row>
                              </v-container>
                            </v-col>
                            <!-- <v-col cols="2">
                              <div class="mt-4">
                                <v-btn @click="EditPosition()" icon dense>
                                  <v-avatar rounded size="18">
                                    <v-img contain :src="require('@/assets/icons/Union.png')"></v-img>
                                  </v-avatar>
                                </v-btn>
                              </div>
                            </v-col> -->
                          </v-row>
                        </v-col>
                        <v-col cols="12" md="12" sm="12" xs="12" align="right" class="pr-8">
                          <v-row no-gutters>
                            <v-col cols="12" md="12" sm="12" xs="12">
                              <div disable class=" mb-6 ml-2" style="border-bottom: #E6E6E6 4px dashed;"></div>
                            </v-col>
                            <v-col cols="12" md="12" sm="12" xs="12">
                              <span style="font-weight: 500; font-size: 12px; line-height: 1px; color: #333333;">สร้างเมื่อ
                                :  {{ new Date(created_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'short', day: 'numeric' }) }}</span>
                            </v-col>
                            <v-col cols="12" md="12" sm="12" xs="12">
                              <span style="font-weight: 500; font-size: 12px; line-height: 1px; color: #333333;">แก้ไขเมื่อ
                                : {{ new Date(update_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'short', day: 'numeric' }) }}</span>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
              <v-card-actions>
                <v-container style="display: flex; justify-content: flex-end">
                  <!-- <v-btn dense dark color="success" class="pl-7 pr-7 mt-2" @click="activePosition('active', positionId, typeId)" v-if="openStatus === 'inactive'">
                    เปิดใช้งาน
                  </v-btn> -->
                  <v-btn dense color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" :style="MobileSize ? 'margin-top: -15vw;' : 'margin-top: -4vw;'" @click="closeDialogShowDetail">
                    ย้อนกลับ
                  </v-btn>
                </v-container>
              </v-card-actions>
            </v-card>
          </v-dialog>
          <!-- dialog edit position -->
          <v-dialog content-class="elevation-0" v-model="modalEditPosition" :width="MobileSize ? '100%' : IpadSize ? '70%' : IpadProSize ? '50%' : '35%'">
            <v-card>
              <v-toolbar height="50px" style="background-color: #27AB9C;">
                <v-row class="d-flex justify-end">
                  <v-col style="text-align: center;" class="pt-4">
                    <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>แก้ไขตำแหน่งและสิทธิ์การใช้งาน</b></span>
                  </v-col>
                  <v-btn fab small @click="modalEditPosition = false" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
                </v-row>
              </v-toolbar>

              <v-card-text class="pt-4">
                <v-row v-if="MobileSize || IpadSize || IpadProSize">
                  <v-col class="d-flex align-center">
                    <v-img
                      lazy-src="@/assets/Businessman.png"
                      max-height="40"
                      max-width="40"
                      src="@/assets/Businessman.png"
                      contain
                      class="mr-2"
                    ></v-img>
                    <span style="font-size: inherit;">รายละเอียดตำแหน่งและสิทธิ์การใช้งาน</span>
                  </v-col>
                </v-row>
                <v-row class="d-flex align-center" v-else>
                  <v-col cols="12" md="2" sm="2">
                    <v-img
                      lazy-src="@/assets/Businessman.png"
                      max-height="100"
                      max-width="200"
                      src="@/assets/Businessman.png"
                      contain
                    ></v-img>
                  </v-col>
                  <v-col cols="12" md="10" style="font-size: medium;">
                    รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                  </v-col>
                </v-row>
              </v-card-text>

              <v-card-text>
                <v-row style="margin: -5px" dense no-gutters>
                  <v-col class="mt-8" cols="12" md="12">
                    <p style="font-weight: 400; font-size: 16px; line-height: 1px; color:: #333333;">
                      ชื่อตำแหน่ง
                    </p>
                    <v-text-field placeholder="ระบุตำแหน่ง" v-model="position_NameUser" @keydown.enter.prevent="submit"  :rules="Rules.position_name" dense outlined></v-text-field>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" xs="12" dense>
                    <v-row dense v-for="(item, i) in itemMenuShopAll[0]" :key="i">
                      <v-list-item
                        :value="item.key"
                        :class="MobileSize ? 'px-0' : ''"
                        class="max-v-list-height"
                        dense
                      >
                      <template v-slot:default="{ active }">
                        <v-list-item-content :style="MobileSize ? 'font-weight: 400; font-size: 15px; line-height: 16px; color: #00000099;' : 'font-weight: 400; font-size: 15px; line-height: 1px; color: #00000099;'">
                          <!-- <v-list-item-title  v-text="item.title"></v-list-item-title> -->
                          {{ item.title }}
                        </v-list-item-content>

                        <v-list-item-action>
                          <v-checkbox
                            :input-value="active"
                            v-model="item.checked"
                            :v-model="active"
                            style="margin-Top: -1px" dense color="#27AB9C" hide-details
                          class="shrink"
                          ></v-checkbox>
                        </v-list-item-action>
                        </template>
                      </v-list-item>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>

              <v-card-actions style="display: flex; justify-content: end; gap: .5vw; padding-bottom: 2vw;">
                <v-btn
                  color="primary"
                  outlined
                  rounded
                  @click="closeDialogEditPosition"
                >
                  ยกเลิก
                </v-btn>
                <v-btn
                  color="primary"
                  rounded
                  @click="editForm()"
                >
                  ตกลง
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </v-col>
      </v-row>
  </v-card>
</v-container>
</template>

<script>
import { Encode } from '@/services'
export default {
  data () {
    return {
      selectUser: false,
      itemSelectUser: [],
      nameSelect: '',
      emailSelect: '',
      itemMenuShop: [],
      itemMenuShop2: [],
      itemMenuShop3: [],
      itemMenuShopAll: [],
      listUser: [],
      listUserEmail: [],
      listPosition: [],
      positionType: [],
      listPositionShop: [],
      selectedPositions: [],
      checked: false,
      search: '',
      showCountRequest: false,
      modalEditUser: false,
      modalAddUser: false,
      modalAddPosition: false,
      modalMenuPosition: false,
      modalEditPosition: false,
      modalListPosition: false,
      modalDetailPosition: false,
      detail: [],
      searchPosition: '',
      dataToShow: '',
      email: '',
      name: '',
      status: '',
      checkedAffiliate: '0',
      showForm: false,
      listStatus: ['กำลังใช้งาน', 'ยกเลิก'],
      emailSearch: '<EMAIL>',
      headers: [
        { text: 'ลำดับ', value: 'indexOfUser', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'อีเมล', value: 'email', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ-นามสกุล', value: 'first_name_th', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะผู้ใช้งาน', value: 'status', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สิทธิ์ผู้ใช้งาน', value: 'position_and_role', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'action', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      toggleConfirm: true,
      selectedRoles: [],
      selectedRoleID: [],
      roles: [],
      selectedRole: '',
      position_name: '',
      setCompany: '0',
      setPermission: '0',
      Partner: '0',
      Order: '0',
      approveOrder: '0',
      Payment: '0',
      Report: '0',
      Tracking: '0',
      Refund: '0',
      Review: '0',
      SaleOrder: '0',
      ApproveOrerSale: '0',
      ManageSaleOrder: '0',
      ManagePromotion: '0',
      ManageAffiliate: '0',
      ManagePartner: '0',
      ManageAccountBank: '0',
      position_NameUser: '',
      created_date: '',
      update_date: '',
      openStatus: '',
      Rules: {
        position_name: [
          v => !!v || 'กรุณากรอกตำแหน่ง',
          v => v.charAt(0) !== ' ' || 'กรุณากรอกตำแหน่ง'
        ]
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    filterPositions () {
      if (!this.searchPosition) {
        return this.listPositionShop
      }
      return this.listPositionShop.filter((item) =>
        item.position_name.toLowerCase().includes(this.searchPosition.toLowerCase())
      )
    }
  },
  watch: {
    MobileSize (val) {
      var id = Number(this.$route.query.seller_shop_id)
      var shopName = this.$route.query.shop_name
      localStorage.setItem('paramID', id)
      localStorage.setItem('shopName', shopName)
      if (val === true) {
        this.$router.push({ path: `/manageUserListMobile?seller_shop_id=${id}&shop_name=${shopName}` }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'manageUserList')
        this.$router.push({ path: `/manageUserList?seller_shop_id=${id}&shop_name=${shopName}` }).catch(() => {})
      }
    }
    // checked (val) {
    //   console.log(val, 555)
    // }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    // var id = Number(this.$route.query.seller_shop_id)
    if (localStorage.getItem('oneData') !== null) {
      this.listUserShop()
      this.getListPosition()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    var id = Number(this.$route.query.seller_shop_id)
    var shopName = this.$route.query.shop_name
    localStorage.setItem('paramID', id)
    localStorage.setItem('shopName', shopName)
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    // detailPositoin (item) {
    //   this.detail = item
    //   console.log(this.detail.id)
    //   this.modalEditPosition = true
    // },
    async getConsent () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: Number(this.$route.query.seller_shop_id)
      }
      await this.$store.dispatch('actionsAffiliateConsent', data)
      var res = await this.$store.state.ModuleAffiliate.stateAffiliateConsent
      if (res.isSeller === '1') {
        this.checkedAffiliate = res.isSeller
      }
      await this.getListShop()
      this.$store.commit('closeLoader')
    },
    openDialogAddPosition () {
      this.modalMenuPosition = false
      this.modalAddPosition = true
    },
    openDialogDetailPosition () {
      this.modalMenuPosition = false
      this.modalListPosition = true
    },
    closeDialogDetailPosition () {
      this.searchPosition = ''
      this.modalListPosition = false
      this.modalMenuPosition = true
    },
    openDialogShowDetail () {
      this.searchPosition = ''
      this.modalListPosition = false
      this.modalDetailPosition = true
    },
    closeDialogShowDetail () {
      this.modalDetailPosition = false
      this.modalListPosition = true
    },
    ShowEditPosition () {
      this.modalDetailPosition = false
      this.modalEditPosition = true
    },
    closeDialogEditPosition () {
      this.modalEditPosition = false
      this.modalDetailPosition = true
    },
    async detailPositoin (val) {
      this.$store.commit('openLoader')
      this.itemMenuShopAll = []
      this.itemMenuShop2 = []
      this.itemMenuShop3 = []
      this.openStatus = []
      const dataSent = {
        // seller_shop_id: Number(this.$route.query.seller_shop_id),
        position_id: val.id
      }
      await this.$store.dispatch('actionDetailPosition', dataSent)
      var { data = [], result = '', message } = await this.$store.state.ModuleShop.stateDetailPosition
      if (result === 'SUCCESS') {
        this.created_date = data.created_at
        this.update_date = data.updated_at
        this.position_NameUser = data.position_name
        this.position_ID = data.id
        this.positionId = data.id
        this.typeId = dataSent.seller_shop_id
        this.openStatus = data.status
        if (data.manage_product === '1') {
          const dataManageProduct = {
            key: 1,
            title: 'การจัดการข้อมูลของสินค้า',
            manage_product: data.manage_product,
            checked: true
          }
          this.itemMenuShop2.push(dataManageProduct)
        } else {
          const dataManageProduct = {
            key: 1,
            title: 'การจัดการข้อมูลของสินค้า',
            set_company: data.manage_product,
            checked: false
          }
          this.itemMenuShop3.push(dataManageProduct)
        }
        if (data.manage_setting_shop === '1') {
          const dataManageSettingShop = {
            key: 2,
            title: 'การจัดการการตั้งค่าภายในร้านค้า',
            manage_setting_shop: data.manage_setting_shop,
            checked: true
          }
          this.itemMenuShop2.push(dataManageSettingShop)
        } else {
          const dataManageSettingShop = {
            key: 2,
            title: 'การจัดการการตั้งค่าภายในร้านค้า',
            manage_setting_shop: data.manage_setting_shop,
            checked: false
          }
          this.itemMenuShop3.push(dataManageSettingShop)
        }
        if (data.manage_stock === '1') {
          const dataManageStock = {
            key: 3,
            title: 'การจัดการสต็อกของสินค้า',
            manage_stock: data.manage_stock,
            checked: true
          }
          this.itemMenuShop2.push(dataManageStock)
        } else {
          const dataManageStock = {
            key: 3,
            title: 'การจัดการสต็อกของสินค้า',
            manage_stock: data.manage_stock,
            checked: false
          }
          this.itemMenuShop3.push(dataManageStock)
        }
        if (data.manage_order === '1') {
          const dataManageOrder = {
            key: 4,
            title: 'การจัดการออเดอร์ที่มีการเข้ามาภายในร้าน',
            manage_order: data.manage_order,
            checked: true
          }
          this.itemMenuShop2.push(dataManageOrder)
        } else {
          const dataManageOrder = {
            key: 4,
            title: 'การจัดการออเดอร์ที่มีการเข้ามาภายในร้าน',
            manage_order: data.manage_order,
            checked: false
          }
          this.itemMenuShop3.push(dataManageOrder)
        }
        if (data.manage_dashboard === '1') {
          const dataManageDashboard = {
            key: 5,
            title: 'การจัดการแดชบอร์ดของร้านค้า',
            manage_dashboard: data.manage_dashboard,
            checked: true
          }
          this.itemMenuShop2.push(dataManageDashboard)
        } else {
          const dataManageDashboard = {
            key: 5,
            title: 'การจัดการแดชบอร์ดของร้านค้า',
            manage_dashboard: data.manage_dashboard,
            checked: false
          }
          this.itemMenuShop3.push(dataManageDashboard)
        }
        if (data.manage_income === '1') {
          const dataManageIncome = {
            key: 6,
            title: this.ActiveAttorney === 'no' ? 'การจัดการรายได้' : 'การจัดการรายได้ (ใช้หนังสือมอบอำนาจ)',
            manage_income: data.manage_income,
            checked: true
          }
          this.itemMenuShop2.push(dataManageIncome)
        } else {
          const dataManageIncome = {
            key: 6,
            title: this.ActiveAttorney === 'no' ? 'การจัดการรายได้' : 'การจัดการรายได้ (ใช้หนังสือมอบอำนาจ)',
            manage_income: data.manage_income,
            checked: false
          }
          this.itemMenuShop3.push(dataManageIncome)
        }
        if (data.manage_user_with_position === '1') {
          const datamanageUserWithPosition = {
            key: 7,
            title: 'การจัดการสิทธิ์และตำแหน่ง',
            manage_user_with_position: data.manage_user_with_position,
            checked: true
          }
          this.itemMenuShop2.push(datamanageUserWithPosition)
        } else {
          const datamanageUserWithPosition = {
            key: 7,
            title: 'การจัดการสิทธิ์และตำแหน่ง',
            manage_user_with_position: data.manage_user_with_position,
            checked: false
          }
          this.itemMenuShop3.push(datamanageUserWithPosition)
        }
        if (data.manage_product_refund === '1') {
          const dataManageProductRefund = {
            key: 8,
            title: 'การจัดการการคืนสินค้า',
            manage_product_refund: data.manage_product_refund,
            checked: true
          }
          this.itemMenuShop2.push(dataManageProductRefund)
        } else {
          const dataManageProductRefund = {
            key: 8,
            title: 'การจัดการการคืนสินค้า',
            manage_product_refund: data.manage_product_refund,
            checked: false
          }
          this.itemMenuShop3.push(dataManageProductRefund)
        }
        if (data.manage_tracking === '1') {
          const dataManageTracking = {
            key: 9,
            title: 'การติดตามสินค้าของทางร้าน',
            manage_tracking: data.manage_tracking,
            checked: true
          }
          this.itemMenuShop2.push(dataManageTracking)
        } else {
          const dataManageTracking = {
            key: 9,
            title: 'การติดตามสินค้าของทางร้าน',
            manage_tracking: data.manage_tracking,
            checked: false
          }
          this.itemMenuShop3.push(dataManageTracking)
        }
        if (data.manage_shipping === '1') {
          const datamanageShipping = {
            key: 10,
            title: 'การจัดการขนส่งสินค้า',
            manage_shipping: data.manage_shipping,
            checked: true
          }
          this.itemMenuShop2.push(datamanageShipping)
        } else {
          const datamanageShipping = {
            key: 10,
            title: 'การจัดการขนส่งสินค้า',
            manage_shipping: data.manage_shipping,
            checked: false
          }
          this.itemMenuShop3.push(datamanageShipping)
        }
        if (data.sale_order === '1') {
          const dataSaleOrder = {
            key: 11,
            title: this.ActiveAttorney === 'no' ? 'การจัดการใบสั่งขาย' : 'การจัดการใบสั่งขาย (ใช้หนังสือมอบอำนาจ)',
            manage_shipping: data.sale_order,
            checked: true
          }
          this.itemMenuShop2.push(dataSaleOrder)
        } else {
          const dataSaleOrder = {
            key: 11,
            title: this.ActiveAttorney === 'no' ? 'การจัดการใบสั่งขาย' : 'การจัดการใบสั่งขาย (ใช้หนังสือมอบอำนาจ)',
            manage_shipping: data.sale_order,
            checked: false
          }
          this.itemMenuShop3.push(dataSaleOrder)
        }
        if (data.manage_approve_order === '1') {
          const datamanageApproveOrder = {
            key: 12,
            title: 'การจัดการอนุมัติใบสั่งขาย',
            manage_shipping: data.manage_approve_order,
            checked: true
          }
          this.itemMenuShop2.push(datamanageApproveOrder)
        } else {
          const datamanageApproveOrder = {
            key: 12,
            title: 'การจัดการอนุมัติใบสั่งขาย',
            manage_shipping: data.manage_approve_order,
            checked: false
          }
          this.itemMenuShop3.push(datamanageApproveOrder)
        }
        if (data.manage_sale_order === '1') {
          const datamanageApproveOrder = {
            key: 13,
            title: 'การจัดการรายการฝ่ายขาย',
            manage_shipping: data.manage_sale_order,
            checked: true
          }
          this.itemMenuShop2.push(datamanageApproveOrder)
        } else {
          const datamanageApproveOrder = {
            key: 13,
            title: 'การจัดการรายการฝ่ายขาย',
            manage_shipping: data.manage_sale_order,
            checked: false
          }
          this.itemMenuShop3.push(datamanageApproveOrder)
        }
        if (data.manage_promotion === '1') {
          const datamanagePromotion = {
            key: 14,
            title: 'การจัดการโปรโมชันร้านค้า',
            manage_shipping: data.manage_promotion,
            checked: true
          }
          this.itemMenuShop2.push(datamanagePromotion)
        } else {
          const datamanagePromotion = {
            key: 14,
            title: 'การจัดการโปรโมชันร้านค้า',
            manage_shipping: data.manage_promotion,
            checked: false
          }
          this.itemMenuShop3.push(datamanagePromotion)
        }
        if (data.manage_affiliate === '1') {
          const datamanageAffiliate = {
            key: 15,
            title: 'การจัดการโปรแกรม Affiliate',
            manage_shipping: data.manage_affiliate,
            checked: true
          }
          this.itemMenuShop2.push(datamanageAffiliate)
        } else {
          const datamanageAffiliate = {
            key: 15,
            title: 'การจัดการโปรแกรม Affiliate',
            manage_shipping: data.manage_affiliate,
            checked: false
          }
          this.itemMenuShop3.push(datamanageAffiliate)
        }
        if (data.manage_partner === '1') {
          const datamanagePartner = {
            key: 16,
            title: 'จัดการโปรแกรม partner',
            manage_shipping: data.manage_partner,
            checked: true
          }
          this.itemMenuShop2.push(datamanagePartner)
        } else {
          const datamanagePartner = {
            key: 16,
            title: 'จัดการโปรแกรม partner',
            manage_shipping: data.manage_partner,
            checked: false
          }
          this.itemMenuShop3.push(datamanagePartner)
        }
        if (data.manage_account_bank === '1') {
          const datamanageAccount = {
            key: 17,
            title: 'จัดการบัญชีร้านค้า',
            manage_shipping: data.manage_account_bank,
            checked: true
          }
          this.itemMenuShop2.push(datamanageAccount)
        } else {
          const datamanageAccount = {
            key: 17,
            title: 'จัดการบัญชีร้านค้า',
            manage_shipping: data.manage_account_bank,
            checked: false
          }
          this.itemMenuShop3.push(datamanageAccount)
        }
        this.itemMenuShopAll.push([...this.itemMenuShop2, ...this.itemMenuShop3].sort((a, b) => a.key - b.key))
        // console.log('Val: ', this.sumZero, this.sumOne)
        // this.modalDetailPosition = !this.modalDetailPosition
        this.openDialogShowDetail()
        this.$store.commit('closeLoader')
      } else {
        if (message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>คุณไม่สามารถเข้าถึงการใช้งานฟังก์ชั่นนี้ได้</h3>' })
        }
        this.$store.commit('closeLoader')
      }
    },
    backtoPage () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/manageUserShop' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageUserShopMobile' }).catch(() => {})
      }
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    // list user
    async listUserShop () {
      this.$store.commit('openLoader')
      var body = { seller_shop_id: Number(this.$route.query.seller_shop_id) }
      await this.$store.dispatch('actionsListPositionSellerUser', body)
      var response = await this.$store.state.ModuleAdminManage.stateListPositionSellerUser
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.listUser = response.data.list_user
        // console.log('ffff', this.listUser)
      } else if (response.code === 400) {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'ไม่มีผู้ใช้งานร้านค้า' })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    showLoader () {
      this.$store.commit('openLoader')
      setTimeout(() => {
        this.$store.commit('closeLoader')
      }, 1000)
    },
    // list position
    async ManageListPosition () {
      this.$store.commit('openLoader')
      var sellerShopId = { seller_shop_id: Number(this.$route.query.seller_shop_id) }
      await this.$store.dispatch('actionsManageListPosition', sellerShopId)
      var response = await this.$store.state.ModuleAdminManage.stateManageListPosition
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.listPosition = response.data.map(item => ({
          position_id: item.id,
          position_name: item.position_name
        }))
      } else if (response.message === 'TNot Found Position In Shop.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'ไม่พบตำแหน่งในร้านค้า'
        })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${response.message}`
        })
      }
    },
    // search user -- email
    async SearchUserShop () {
      // this.toggleConfirm = false
      this.$store.commit('openLoader')
      var sellerShopId = Number(this.$route.query.seller_shop_id)
      var email = this.email
      var sendSearch = { email, seller_shop_id: sellerShopId }
      await this.$store.dispatch('actionsSearchUserShop', sendSearch)
      var response = await this.$store.state.ModuleAdminManage.stateSearchUserShop
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.showForm = true
        this.toggleConfirm = false
        this.listUserEmail = response.data
        this.OpenDialogAddUser()
        // this.ManageListPosition()
        // this.OpenDialogEditUser()
      } else if (response.message === "You don't enter email or name.") {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'ไม่ได้ใส่ email หรือ ชื่อ' })
      } else if (response.message === 'This User Have Shop.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'User อยู่ในร้านค้าแล้ว' })
      } else if (response.message === 'Not Found Email or Name.') {
        this.$store.commit('closeLoader')
        this.showForm = false
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'ไม่พบ email หรือ ชื่อ' })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    // add user
    async AddUserShop () {
      this.$store.commit('openLoader')
      var userID = this.itemSelectUser.id
      var sellerShopId = Number(this.$route.query.seller_shop_id)
      var data = { user_id: userID, seller_shop_id: sellerShopId, position_id: this.selectedPositions.length === 0 ? '' : this.selectedPositions }
      await this.$store.dispatch('actionsAddUserShop', data)
      var response = await this.$store.state.ModuleAdminManage.stateAddUserShop
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.addUserShop = response.data
        await this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'success', text: 'เพิ่มผู้ใช้ร้านค้าสำเร็จ' })
        this.modalAddUser = false
        this.cancelAddUser()
        this.cancel()
        this.listUserShop()
      } else if (response.message === "You Don't Enter User Id.") {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'ไม่ได้ใส่ user id' })
      } else if (response.message === "You Don't Enter Seller Shop Id.") {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'ไม่ได้ใส่ seller shop id' })
      } else if (response.message === "You Don't Enter Position Id.") {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'ไม่ได้ใส่ position id' })
      } else if (response.message === 'Position is in user.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'มี position ใน user แล้ว' })
      } else if (response.message === 'You are not an admin.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'คุุณไม่ใช่ Admin' })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    // edit user
    async EditUserShop () {
      this.$store.commit('openLoader')
      var data = { user_shop_id: this.userID, position_id: this.selectedRoleID }
      await this.$store.dispatch('actionsEditUserShop', data)
      var response = await this.$store.state.ModuleAdminManage.stateEditUserShop
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.editUserShop = response.data
        await this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'success', text: 'แก้ไขข้อมูลผู้ใช้งานร้านค้าสำเร็จ' })
        this.modalEditUser = false
        this.listUserShop()
      } else if (response.message === "You Don't Enter User Shop Id.") {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'success', text: 'ไม่ได้ใส่ user_shop_id' })
      } else if (response.message === "You Don't Enter Position Id.") {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'success', text: 'ไม่ได้ใส่ position id' })
      } else if (response.message === 'You are not an admin.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'success', text: 'คุุณไม่ใช่ Admin' })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    async DeleteUserShop () {
      this.$store.commit('openLoader')
      var data = { id_user_shop: this.userID }
      await this.$store.dispatch('actionsDeleteUserShop', data)
      var response = await this.$store.state.ModuleAdminManage.stateDeleteUserShop
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.editUserShop = response.data
        await this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'success', text: 'ลบ user สำเร็จ' })
        this.listUserShop()
      } else if (response.message === "User In Shop Can't Delete.") {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'ลบ user ใน ร้านค้านี้ไม่ได้' })
      } else if (response.message === 'You are not an admin.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'คุุณไม่ใช่ Admin' })
      } else if (response.message === 'Delete User In Shop Success.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'ลบข้อมูล สำเร็จ' })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    async SwalDeleteUser (item) {
      this.listUserShop()
      this.userID = item.user_shop_id
      this.$swal.fire({
        title: 'คุณต้องการลบผู้ใช้งานนี้หรือไม่ ?',
        icon: 'warning',
        text: 'ผู้ใช้งานจะถูกลบออกจากร้านค้านี้',
        showCancelButton: true,
        confirmButtonText: 'ตกลง',
        cancelButtonText: 'ยกเลิก',
        confirmButtonColor: '#38b2a4',
        reverseButtons: true
      }).then((result) => {
        if (result.isConfirmed) {
          this.DeleteUserShop()
        }
      })
    },
    async getListPosition () {
      this.$store.commit('openLoader')
      this.itemMenuShop = []
      var data = {
        seller_shop_id: this.$route.query.seller_shop_id
      }
      await this.$store.dispatch('actionsAdminListPosition', data)
      var res = await this.$store.state.ModuleAdminManage.stateAdminListPosition
      if (res.code === 200) {
        this.listPositionShop = res.data
        this.itemMenuShop = [
          { key: 1, title: 'การจัดการข้อมูลของสินค้า', manage_product: '0', checked: false },
          { key: 2, title: 'การจัดการการตั้งค่าภายในร้านค้า', manage_setting_shop: '0', checked: false },
          { key: 3, title: 'การจัดการสต๊อกของสินค้า', manage_stock: '0', checked: false },
          { key: 4, title: 'การจัดการออเดอร์ที่มีการเข้ามาภายในร้าน', manage_order: '0', checked: false },
          { key: 5, title: 'การจัดการแดชบอร์ดของร้านค้า', manage_dashboard: '0', checked: false },
          { key: 6, title: 'การจัดการรายได้ (ใช้หนังสือมอบอำนาจ)', manage_income: '0', checked: false },
          { key: 7, title: 'การจัดการสิทธิ์และตำแหน่ง', manage_user_with_position: '0', checked: false },
          { key: 8, title: 'การจัดการการคืนสินค้า', manage_product_refund: '0', checked: false },
          { key: 9, title: 'การติดตามสินค้าของทางร้าน', manage_tracking: '0', checked: false },
          { key: 10, title: 'การจัดการขนส่งสินค้า', manage_shipping: '0', checked: false },
          { key: 11, title: 'การจัดการใบสั่งขาย (ใช้หนังสือมอบอำนาจ)', sale_order: '0', checked: false },
          { key: 12, title: 'การจัดการอนุมัติใบสั่งขาย', manage_approve_order: '0', checked: false },
          { key: 13, title: 'การจัดการรายการฝ่ายขาย', manage_sale_order: '0', checked: false },
          { key: 14, title: 'การจัดการโปรโมชันร้านค้า', manage_promotion: '0', checked: false }
        ]
        if (this.checkedAffiliate === '1') {
          this.itemMenuShop.push({
            key: 15,
            title: 'การจัดการโปรแกรม Affiliate',
            manage_promotion: '0',
            checked: false
          })
        }
        this.itemMenuShop.push(
          {
            key: 16,
            title: 'จัดการโปรแกรม partner',
            manage_partner: '0',
            checked: false
          }
        )
        this.itemMenuShop.push(
          {
            key: 17,
            title: 'จัดการบัญชีร้านค้า',
            manage_account_bank: '0',
            checked: false
          }
        )
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
        this.$store.commit('closeLoader')
      }
    },
    async createPositionShop () {
      this.$store.commit('openLoader')
      for (const value in this.itemMenuShop) {
        // console.log('createPosition**', value)
        if (this.itemMenuShop[value].key === 1 && this.itemMenuShop[value].checked === true) {
          this.setCompany = '1'
        }
        if (this.itemMenuShop[value].key === 2 && this.itemMenuShop[value].checked === true) {
          this.setPermission = '1'
        }
        if (this.itemMenuShop[value].key === 3 && this.itemMenuShop[value].checked === true) {
          this.Partner = '1'
        }
        if (this.itemMenuShop[value].key === 4 && this.itemMenuShop[value].checked === true) {
          this.Order = '1'
        }
        if (this.itemMenuShop[value].key === 5 && this.itemMenuShop[value].checked === true) {
          this.approveOrder = '1'
        }
        if (this.itemMenuShop[value].key === 6 && this.itemMenuShop[value].checked === true) {
          this.Payment = '1'
        }
        if (this.itemMenuShop[value].key === 7 && this.itemMenuShop[value].checked === true) {
          this.Report = '1'
        }
        if (this.itemMenuShop[value].key === 8 && this.itemMenuShop[value].checked === true) {
          this.Tracking = '1'
        }
        if (this.itemMenuShop[value].key === 9 && this.itemMenuShop[value].checked === true) {
          this.Refund = '1'
        }
        if (this.itemMenuShop[value].key === 10 && this.itemMenuShop[value].checked === true) {
          this.Review = '1'
        }
        if (this.itemMenuShop[value].key === 11 && this.itemMenuShop[value].checked === true) {
          this.SaleOrder = '1'
        }
        if (this.itemMenuShop[value].key === 12 && this.itemMenuShop[value].checked === true) {
          this.ApproveOrerSale = '1'
        }
        if (this.itemMenuShop[value].key === 13 && this.itemMenuShop[value].checked === true) {
          this.ManageSaleOrder = '1'
        }
        if (this.itemMenuShop[value].key === 14 && this.itemMenuShop[value].checked === true) {
          this.ManagePromotion = '1'
        }
        if (this.itemMenuShop[value].key === 15 && this.itemMenuShop[value].checked === true) {
          this.ManageAffiliate = '1'
        }
        if (this.itemMenuShop[value].key === 16 && this.itemMenuShop[value].checked === true) {
          this.ManagePartner = '1'
        }
        if (this.itemMenuShop[value].key === 17 && this.itemMenuShop[value].checked === true) {
          this.ManageAccountBank = '1'
        }
        // console.log('setCompany', value, key, this.setCompany)
      }
      // [FormsAll.qu_detail.buyer_name !== '' && FormsAll.qu_detail.qu_number !== ''].includes(true)
      const dataSent = {
        position_id: '-1',
        seller_shop_id: this.$route.query.seller_shop_id,
        position_name: this.position_name,
        status: 'active',
        manage_product: this.setCompany,
        manage_setting_shop: this.setPermission,
        manage_stock: this.Partner,
        manage_order: this.Order,
        manage_dashboard: this.approveOrder,
        manage_income: this.Payment,
        manage_user_with_position: this.Report,
        manage_product_refund: this.Tracking,
        manage_tracking: this.Refund,
        manage_shipping: this.Review,
        sale_order: this.SaleOrder,
        manage_approve_order: this.ApproveOrerSale,
        manage_sale_order: this.ManageSaleOrder,
        manage_promotion: this.ManagePromotion,
        manage_affiliate: this.ManageAffiliate,
        manage_partner: this.ManagePartner,
        manage_account_bank: this.ManageAccountBank
      }
      // console.log(dataSent, 6897)
      await this.$store.dispatch('actionsCreatePositionShop', dataSent)
      var { result = '' } = await this.$store.state.ModuleAdminManage.stateCreatePositionShop
      var data = await this.$store.state.ModuleShop.stateCreatePositionShop
      if (result === 'SUCCESS') {
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>สร้างรายละเอียดตำแหน่งและสิทธิ์การใช้งานสำเร็จ</h3>' })
        this.modalAddPosition = false
        this.modalMenuPosition = false
        this.position_name = ''
        this.$store.commit('closeLoader')
        this.getListPosition()
        this.listUserShop()
      } else {
        this.$swal.fire({ icon: 'error', title: data.message, showConfirmButton: false, timer: 2000 })
        this.modalAddPosition = !this.modalAddPosition
        this.$store.commit('closeLoader')
      }
    },
    async editForm () {
      // console.log(this.selectType3)
      // var status = false
      this.$store.commit('openLoader')
      const ArrSum = this.itemMenuShopAll[0]
      for (const value in ArrSum) {
        if (ArrSum[value].key === 1 && ArrSum[value].checked === true) {
          this.setCompany = '1'
        }
        if (ArrSum[value].key === 2 && ArrSum[value].checked === true) {
          this.setPermission = '1'
        }
        if (ArrSum[value].key === 3 && ArrSum[value].checked === true) {
          this.Partner = '1'
        }
        if (ArrSum[value].key === 4 && ArrSum[value].checked === true) {
          this.Order = '1'
        }
        if (ArrSum[value].key === 5 && ArrSum[value].checked === true) {
          this.approveOrder = '1'
        }
        if (ArrSum[value].key === 6 && ArrSum[value].checked === true) {
          this.Payment = '1'
        }
        if (ArrSum[value].key === 7 && ArrSum[value].checked === true) {
          this.Report = '1'
        }
        if (ArrSum[value].key === 8 && ArrSum[value].checked === true) {
          this.Tracking = '1'
        }
        if (ArrSum[value].key === 9 && ArrSum[value].checked === true) {
          this.Refund = '1'
        }
        if (ArrSum[value].key === 10 && ArrSum[value].checked === true) {
          this.Review = '1'
        }
        if (ArrSum[value].key === 11 && ArrSum[value].checked === true) {
          this.SaleOrder = '1'
        }
        if (ArrSum[value].key === 12 && ArrSum[value].checked === true) {
          this.ApproveOrerSale = '1'
        }
        if (ArrSum[value].key === 13 && ArrSum[value].checked === true) {
          this.ManageSaleOrder = '1'
        }
        if (ArrSum[value].key === 14 && ArrSum[value].checked === true) {
          this.ManagePromotion = '1'
        }
        if (ArrSum[value].key === 15 && ArrSum[value].checked === true) {
          this.ManageAffiliate = '1'
        }
        if (ArrSum[value].key === 16 && ArrSum[value].checked === true) {
          this.ManagePartner = '1'
        }
        if (ArrSum[value].key === 17 && ArrSum[value].checked === true) {
          this.ManageAccountBank = '1'
        }
        // this.num++
        // if (this.num > 9) {
        //   status = true
        // }
      }
      const dataSent = await {
        position_id: this.position_ID,
        seller_shop_id: this.$route.query.seller_shop_id,
        position_name: this.position_NameUser,
        status: 'active',
        manage_product: this.setCompany,
        manage_setting_shop: this.setPermission,
        manage_stock: this.Partner,
        manage_order: this.Order,
        manage_dashboard: this.approveOrder,
        manage_income: this.Payment,
        manage_user_with_position: this.Report,
        manage_product_refund: this.Tracking,
        manage_tracking: this.Refund,
        manage_shipping: this.Review,
        sale_order: this.SaleOrder,
        manage_approve_order: this.ApproveOrerSale,
        manage_sale_order: this.ManageSaleOrder,
        manage_promotion: this.ManagePromotion,
        manage_affiliate: this.ManageAffiliate,
        manage_partner: this.ManagePartner,
        manage_account_bank: this.ManageAccountBank
      }
      // console.log(dataSent, 6897)
      // this.positionId = await this.position_ID
      // this.typeId = await localStorage.getItem('shopSellerID')
      await this.$store.dispatch('actionsCreatePositionShop', dataSent)
      var { result = '' } = await this.$store.state.ModuleAdminManage.stateCreatePositionShop
      // var data = await this.$store.state.ModuleShop.stateCreatePositionShop
      if (result === 'SUCCESS') {
        await this.$store.dispatch('actionsAuthorityUser')
        var response = await this.$store.state.ModuleUser.stateAuthorityUser
        this.list_seller = response.data.list_shop_detail
        var shopSellerID = localStorage.getItem('shopSellerID')
        for (let i = 0; i < this.list_seller.length; i++) {
          if (shopSellerID === (this.list_seller[i].seller_shop_id === null ? '' : this.list_seller[i].seller_shop_id.toString())) {
            localStorage.setItem('list_shop_detail', Encode.encode(this.list_seller[i]))
          }
        }
        await this.$EventBus.$emit('AuthorityUser')
        await this.$EventBus.$emit('AuthorityUsers')
        this.modalEditPosition = false
        this.itemMenuShop2 = []
        this.itemMenuShop3 = []
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>แก้ไขรายละเอียดตำแหน่งและสิทธิ์การใช้งานสำเร็จ</h3>' })
        this.setCompany = '0'
        this.setPermission = '0'
        this.Partner = '0'
        this.Order = '0'
        this.approveOrder = '0'
        this.Payment = '0'
        this.Report = '0'
        this.Tracking = '0'
        this.Refund = '0'
        this.Review = '0'
        this.SaleOrder = '0'
        this.ApproveOrerSale = '0'
        this.ManageSaleOrder = '0'
        this.ManagePromotion = '0'
        this.ManageAffiliate = '0'
        this.ManagePartner = '0'
        this.ManageAccountBank = '0'
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
      }
    },
    closeModalCreate () {
      this.modalAddPosition = false
      this.position_name = ''
      this.modalMenuPosition = true
    },
    // dialog edit
    OpenDialogEditUser (item) {
      this.email = item.email
      this.name = item.first_name_th + ' ' + item.last_name_th
      this.status = item.status
      this.selectedRoles = item.position_and_role.map(role => role.position_name)
      this.selectedRoleID = item.position_and_role.map(role => role.id)
      this.userID = item.user_shop_id
      this.modalEditUser = true
      this.ManageListPosition()
      this.listUserShop()
    },
    // dialog add
    OpenDialogAddUser () {
      this.modalAddUser = true
      // if (this.listUserEmail) {
      //   this.email = this.listUserEmail.email || ''
      //   this.name = this.listUserEmail.first_name_th + ' ' + this.listUserEmail.last_name_th || ''
      // } else {
      //   this.email = ''
      //   this.name = ''
      // }
      // this.status = ''
    },
    OpenDialogSelectUser (val) {
      if (val) {
        this.nameSelect = (val.first_name_th === '' || val.first_name_th === null) && (val.last_name_th === '' || val.last_name_th === null) ? 'ไม่มีชื่อผู้ใช้' : val.first_name_th + ' ' + val.last_name_th
        this.emailSelect = val.email === '' || val.email === null ? 'ไม่มีอีเมล' : val.email
        this.itemSelectUser = val
      } else {
        this.nameSelect = 'ไม่มีชื่อผู้ใช้'
        this.emailSelect = 'ไม่มีอีเมล'
        this.itemSelectUser = []
      }
      this.ManageListPosition()
      this.selectUser = true
    },
    cancel () {
      this.modalEditUser = false
      this.modalAddUser = false
      this.email = ''
      this.name = ''
      this.listUserEmail = null
      this.showForm = false
      this.toggleConfirm = true
    },
    cancelAddUser () {
      this.selectUser = false
      this.selectedPositions = []
      this.emailSelect = ''
      this.nameSelect = ''
      this.itemSelectUser = []
    }
  }
}
</script>

<style scoped>
  .theme--light.v-data-table > .v-data-table__wrapper > table > thead > tr:last-child > th {
    white-space: nowrap !important;
    text-align: center !important;
  }
  .v-data-table > .v-data-table__wrapper > table > tbody > tr > td {
    text-align: center !important;
  }
  .swal2-popup {
    border-radius: 2vw !important;
  }
  .swal2-cancel,
  .swal2-confirm {
    border-radius: 2vw !important;
  }
</style>

<style lang="scss" scoped>
::v-deep table {
    tbody {
    tr {
        td:nth-child(6) {
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        z-index: 10;
        background: white;
        }
    }
    }
    thead {
    tr {
        th:nth-child(1) {
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        z-index: 10;
        background: white;
        }
    }
    }
    thead {
    tr {
        th:nth-child(6) {
        z-index: 11;
        background: white;
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        }
    }
    }
}
.v-dialog > .v-card > .v-card__text {
  padding: 0px 29px 0px;
}
.formatBtn {
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px !important;
  background-color: #fff;
  border-radius: 1.5vw;
}
</style>
