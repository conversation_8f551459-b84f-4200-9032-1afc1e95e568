<template>
  <v-app class="backgroundPage OverflowX" id="app">
    <!-- modal สำหรับ ไป Mobile -->
    <v-snackbar v-model="dialogConfirmToApp" :timeout="-1" top style="border-radius: 16px;" height="80">
      <v-row dense>
        <v-col cols="2" class="pt-2">
          <v-img
            max-width="30"
            max-height="30"
            src="@/assets/icon.png"
          ></v-img>
        </v-col>
        <v-col cols="10">
          <span style="font-size: 14px; color: #FFFFFF; font-weight: 400;">ไปที่ NexGen ไหม</span><br/>
          <span style="font-size: 12px; color: #FFFFFF; font-weight: 400;">เว็บไซต์นี้ต้องการเปิดแอป NexGen</span>
        </v-col>
      </v-row>
      <template v-slot:action="{ attrs }">
        <v-btn
          color="#FFFFFF"
          text
          v-bind="attrs"
          style="font-size: 12px;"
          @click="gotoApp()"
        >
          ต่อไป
        </v-btn>
        <v-btn
          color="#FFFFFF"
          icon
          v-bind="attrs"
          style="font-size: 12px;"
          @click="dialogConfirmToApp = false"
        >
          <v-icon small>mdi-close</v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <v-dialog v-model="openSelectOS" width="425px">
      <v-card width="100%" elevation="0" style="background-color: #FAFFFF;">
        <v-card-text>
          <v-row dense>
            <v-col cols="6">
              <v-img :src="require('@/assets/buyer_qrcode.jpg')"></v-img>
              <span style="display: flex; justify-content: center;">{{ $t('DialogDownloadApp.ForBuyer') }}</span>
            </v-col>
            <v-col cols="6">
              <v-img :src="require('@/assets/seller_qrcode.jpg')"></v-img>
              <span style="display: flex; justify-content: center;">{{ $t('DialogDownloadApp.ForSeller') }}</span>
            </v-col>
          </v-row>
          <v-row dense justify="center">
            <span class="pr-2" style="display: flex; align-items: center;">{{ $t('DialogDownloadApp.textDownload') }}</span>
            <v-img alt="appstore" :src="require('@/assets/app_store.png')" max-height="30px" max-width="80px" class="pl-2 mr-2" contain></v-img>
            <v-img alt="googleplay" :src="require('@/assets/google_play.png')" max-height="30px" max-width="80px" contain></v-img>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <div :style="!MobileSize ? 'min-height: 136px;' : 'min-height: 70px;'">
      <!--system bar  -->
      <v-system-bar
       v-if="!MobileSize"
       height="36"
       app
       color="#F6F8FB"
       class="d-flex justify-end"
       :class="IpadSize ? 'styleSytemBarIPad' : IpadProSize ? 'styleSytemBarIPadPro' : 'styleSytemBar'"
      >
        <span class="textSystemBar pr-2" style="gap: 24px !important;" @click="gotoAppSelect()">{{ $t('SystemBar.DownloadApp') }}</span>
        <span class="textSystemBar pr-2" style="gap: 24px !important;" @click="gotoFooter()">{{ $t('SystemBar.ContactUs') }}</span>
        <span class="textSystemBar pr-2" style="gap: 24px !important;" @click="gotoShopRegister('shop')">{{ $t('SystemBar.OpenShop') }}</span>
        <span class="textSystemBar pr-2" style="gap: 24px !important;" @click="gotoShopRegister('partner')">{{ $t('SystemBar.RegisterPartner') }}</span>
        <!-- <span class="textSystemBar pr-2" style="gap: 24px !important;">ช่วยเหลือ</span> -->
        <span class="textSystemBar Language" @click="changeLang('th')" :style="$i18n.locale === 'th' ? 'color: #3EC6B6; font-weight: 600;' : 'color : #545A67;'">ไทย</span>
        <span class="textSystemBar Language" style="color: #D0D4D3;">|</span>
        <span class="textSystemBar Language" @click="changeLang('en')" :style="$i18n.locale === 'en' ? 'color: #3EC6B6; font-weight: 600;' : 'color : #545A67;'">English</span>
      </v-system-bar>
      <AppBar :style="MobileSize ? 'height: 110px;' : ''" v-if="main === false || (main === true && readyCarousel === true)" />
    </div>
    <CarouselHome v-if="main === true" />
    <ListMenuCustom loading="lazy" v-if="(main === true && readyCarousel === true) && roleUser === 'ext_buyer'"/>
    <v-main :style="{'max-width': MobileSize ? widthDevice : '100%'}" class="overflow-x-hidden overflow-y-hidden pt-0">
      <CarouselFlashSell loading="lazy" v-if="(main === true && readyCarousel === true) && roleUser === 'ext_buyer'"/>
      <RegisterWithUs loading="lazy" v-if="main === true && (MobileSize || show) && dataRole !== 'ext_buyer'" />
      <router-view v-on:scroll="closeModal" v-if="main === false || (main === true && readyCarousel === true)"/>
      <CallChatMe v-if="chatAll === true" class="mb-8"/>
    </v-main>
    <ModalPDPA ref="ModalPDPA"/>
    <ModalCheckName ref="ModalCheckName"/>
    <ModalDetailTax ref="ModalDetailTax"/>
    <Footer id="Footer" loading="lazy" v-if="show" />
    <PopUp v-if="main === true" />
  </v-app>
</template>

<script>
import { Decode } from '@/services'
export default {
  name: 'App',
  components: {
    AppBar: () => import(/* webpackPrefetch: true */ '@/components/Home/AppBarUI'),
    Footer: () => import(/* webpackPrefetch: true */ '@/components/Home/FooterUI'),
    CarouselHome: () => import(/* webpackPrefetch: true */ '@/components/Carousel/CarouselHome'),
    ListMenuCustom: () => import(/* webpackPrefetch: true */ '@/components/Home/ListMenuCustom'),
    // LiveStream: () => import(/* webpackPrefetch: true */ '@/components/Home/Live/CircleLive'),
    // HomeCoupon: () => import(/* webpackPrefetch: true */ '@/components/Home/HomeCoupon/HomeCoupon'),
    // BannerA: () => import(/* webpackPrefetch: true */ '@/components/Carousel/CarouselBannerA'),
    // BannerC: () => import(/* webpackPrefetch: true */ '@/components/Carousel/CarouselBannerC'),
    // PopularProductUI: () => import(/* webpackPrefetch: true */ '@/components/Carousel/PopularProductUI'),
    // CategoryList: () => import(/* webpackPrefetch: true */ '@/components/Category/CategoryHome'),
    // EditGroupShop: () => import(/* webpackPrefetch: true */ '@/components/Carousel/EditGroupShop'),
    // EditBannerGroupShop: () => import(/* webpackPrefetch: true */ '@/components/Carousel/EditBannerGroupShop'),
    // RegisterWithUsTypeGroup: () => import(/* webpackPrefetch: true */ '@/components/Carousel/RegisterWithUsTypeGroup'),
    RegisterWithUs: () => import(/* webpackPrefetch: true */ '@/components/Carousel/RegisterWithUs'),
    CallChatMe: () => import(/* webpackPrefetch: true */ '@/components/library/CallChatMe/callChatMeMore'),
    CarouselFlashSell: () => import(/* webpackPrefetch: true */ '@/components/Carousel/FlashProductUI'),
    ModalPDPA: () => import(/* webpackPrefetch: true */ '@/components/Modal/PDPA'),
    ModalCheckName: () => import(/* webpackPrefetch: true */ '@/components/Modal/CheckName'),
    // ModalDetailTax: () => import(/* webpackPrefetch: true */ '@/components/Modal/CheckDetailTax')
    ModalDetailTax: () => import(/* webpackPrefetch: true */ '@/components/Modal/CheckDetailTax'),
    PopUp: () => import(/* webpackPrefetch: true */ '@/components/Home/Popup')
  },
  metaInfo () {
    return {
      title: 'Nex Gen Commerce | ซื้อขายออนไลน์ ส่งเสริมธุรกิจไทย',
      titleTemplate: '%s',
      htmlAttrs: {
        lang: 'th-TH'
      },
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { property: 'og:title', content: 'Nex Gen Commerce | ซื้อขายออนไลน์ ส่งเสริมธุรกิจไทย', vmid: 'og:title' }
      ],
      link: [
        { rel: 'canonical', href: 'https://nexgencommerce.one.th/' }
      ]
    }
  },
  data: () => ({
    dataRole: '',
    show: false,
    path: false,
    fab: false,
    dialogInside: false,
    widthChat: '535px;',
    heightChat: '520px;',
    chat: '',
    showChat: false,
    onechatToken: '',
    botID: '',
    valueDot: '',
    chatDot: '',
    time: 0,
    pos1: 0,
    pos2: 0,
    pos3: 0,
    pos4: 0,
    elmnt: '',
    checkAppBar: 'Panit',
    widthDevice: '',
    readyCarousel: false,
    roleUser: '',
    isBuyer: '',
    isLogin: false,
    isImageNational: '',
    isNationalId: '',
    isAddress: '',
    dialogConfirmToApp: false,
    linkToApp: '',
    linkToStore: '',
    dataToGetPath: '',
    activeCustomGroupShop: true,
    typeGroupShop: '',
    openSelectOS: ''
  }),
  destroyed () {
    window.removeEventListener('scroll', this.closeModal)
  },
  async created () {
    this.$EventBus.$emit('CheckFooter')
    this.$EventBus.$emit('getPath')
    this.$EventBus.$on('ChangeShow', this.ChangeShow)
    if (localStorage.getItem('roleUser') !== null) {
      this.dataRole = JSON.parse(localStorage.getItem('roleUser')).role
    } else {
      this.dataRole = 'ext_buyer'
    }
    if (localStorage.getItem('oneData') !== null) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.onedata = oneData
      if (oneData.user !== undefined) {
        this.isLogin = true
        if (oneData.user.shared_token !== undefined && oneData.user.shared_token !== null && oneData.user.shared_token !== '') {
          this.onechatToken = oneData.user.shared_token
        } else {
          this.onechatToken = ''
        }
      } else {
        localStorage.removeItem('oneData')
      }
      if (localStorage.getItem('roleUser') !== null) {
        var roleUser = JSON.parse(localStorage.getItem('roleUser')).role
        if ((roleUser === 'sale_order' || roleUser === 'sale_order_no_JV') && this.$route.name === 'HomeProduct') {
          const shopDetail = JSON.parse(Decode.decode(localStorage.getItem('ShopDetailSale')))
          if (shopDetail.name_th !== undefined) {
            const shopCleaned = encodeURIComponent(shopDetail.name_th.replace(/\s/g, '-'))
            this.$router.push('/shoppage/' + shopCleaned + '-' + shopDetail.id).catch(() => {})
          } else {
            const shopCleaned = encodeURIComponent(shopDetail.shop_name.replace(/\s/g, '-'))
            this.$router.push('/shoppage/' + shopCleaned + '-' + shopDetail.seller_shop_id).catch(() => {})
          }
        }
      }
      if (oneData.user === undefined && oneData.user === '') {
        localStorage.removeItem('oneData')
      }
      // เช็คว่ามีข้อมูลของ Gracz ไหม ถ้ามีให้เปลี่ยนเป็น Appbar ของ Gracz ที่ต้องเช็คเพราะในกรณี mobile แล้วรีเฟช emit บางครั้งไม่ทำงาน
      if (localStorage.getItem('UserDetailSelling') !== null) {
        this.ChangeAppBarGracz()
      } else {
        this.ChangeAppBarPanit()
      }
    }
    window.addEventListener('click', (e) => {
      if (e.target.matches('#chatMeAll')) {
        var x = document.getElementsByClassName('chatMainSub')
        for (var i = 0; i < x.length; i++) {
          if (x[i].style.display === 'block') {
            x[i].style.display = 'none'
          } else {
            x[i].style.display = 'block'
          }
        }
      }
    })
    window.addEventListener('scroll', this.closeModal)
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.reload()
      }
    })
    const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera
    if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
      this.dialogConfirmToApp = true
    } else if (/android/i.test(userAgent)) {
      this.dialogConfirmToApp = true
    } else {
      this.dialogConfirmToApp = false
    }
    this.getActiveGroupShop()
    this.getTypeGroupShop()
    if (this.$route.query.utm_source) {
      sessionStorage.setItem('utm_source', this.$route.query.utm_source)
    }
    if (this.$route.query.utm_medium) {
      sessionStorage.setItem('utm_medium', this.$route.query.utm_medium)
    }
    if (localStorage.getItem('lang') !== null) {
      var lang = localStorage.getItem('lang')
      this.changeLang(lang)
    } else {
      localStorage.setItem('lang', 'th')
      this.changeLang('th')
    }
  },
  computed: {
    main () {
      return this.$route.name === 'HomeProduct'
    },
    chatAll () {
      return this.$route.name !== 'chatAll'
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.widthChat = '280px;'
        this.heightChat = '380px;'
        var path = ''
        var cleanPath = ''
        var Id = ''
        var iosAppLink = ''
        var androidAppLink = ''
        var queryParams = ''
        var anId = ''
        // var responseShortURL = ''
        // var resultFromShortURL = ''
        // var pathUniversalLink = ''
        if (this.$router.currentRoute.name === 'DetailProduct') {
          // path = this.$router.currentRoute.params.data
          // cleanPath = path.split('-')
          // Id = cleanPath[cleanPath.length - 1]
          // this.dataToGetPath = {
          //   product_id: Id,
          //   seller_shop_id: '',
          //   type: 'product'
          // }
          path = this.$router.currentRoute.params.data
          cleanPath = path.split('-')
          Id = cleanPath[cleanPath.length - 1]
          queryParams = this.$route.query

          if (Object.entries(queryParams).length === 0) {
            this.dataToGetPath = {
              product_id: Id,
              seller_shop_id: '',
              type: 'product'
            }
          } else {
            anId = ''
            // ตรวจสอบ utm_medium และ utm_source
            if (queryParams.utm_medium === 'affiliate' && queryParams.utm_source) {
              anId = queryParams.utm_source // ใช้ค่า utm_source เป็น an_id
            }
            this.dataToGetPath = {
              product_id: Id,
              seller_shop_id: '',
              type: 'affiliate',
              an_id: anId
            }
          }
        } else if (this.$router.currentRoute.name === 'ShoppageUI') {
          path = this.$router.currentRoute.params.data
          cleanPath = path.split('-')
          Id = cleanPath[cleanPath.length - 1]
          this.dataToGetPath = {
            product_id: '',
            seller_shop_id: Id,
            type: 'sellerShop'
          }
        } else if (this.$router.currentRoute.name === 'GroupShoppage') {
          path = this.$router.currentRoute.params.data
          cleanPath = path.split('-')
          Id = cleanPath[cleanPath.length - 1]

          this.dataToGetPath = {
            group_id: Id,
            type: 'group'
          }
        } else {
          this.dataToGetPath = {
            product_id: '',
            seller_shop_id: '',
            type: 'home'
          }
        }
        await this.$store.dispatch('actionsGetLinkMobile', this.dataToGetPath)
        const response = await this.$store.state.ModuleHompage.stateGetLinkMobile
        const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera
        // const now = new Date().getTime()
        if (response.message === 'Success') {
          // try {
          //   responseShortURL = await fetch(response.data.short_url)
          //   resultFromShortURL = await responseShortURL.json()
          //   if (resultFromShortURL.message === 'Success') {
          //     if (resultFromShortURL.data.action_type === 'viewProductDetail') {
          //       pathUniversalLink = `${process.env.VUE_APP_DOMAIN}api/backend_2/?action_type=${resultFromShortURL.data.action_type}&product_id=${resultFromShortURL.data.product_id}&have_attribute=${resultFromShortURL.data.have_attribute}`
          //     } else if (resultFromShortURL.data.action_type === 'viewShopDetail') {
          //       pathUniversalLink = `${process.env.VUE_APP_DOMAIN}api/backend_2/?action_type=${resultFromShortURL.data.action_type}&shop_id=${resultFromShortURL.data.shop_id}`
          //     } else {
          //       pathUniversalLink = `${process.env.VUE_APP_DOMAIN}api/backend_2/?action_type=${resultFromShortURL.data.action_type}`
          //     }
          //   } else {
          //     pathUniversalLink = `${process.env.VUE_APP_DOMAIN}api/backend_2/?action_type=viewHome`
          //   }
          // } catch (error) {
          // }
          iosAppLink = response.data.short_url.replace(/https/g, 'nexgencommerce')
          androidAppLink = response.data.intentLink
          // iosAppLink = pathUniversalLink.replace(/https/g, 'nexgencommerce')
          // androidAppLink = pathUniversalLink
        } else {
          // กลับสู่หน้า Home
          var linkBeforeReplace = `${process.env.VUE_APP_DOMAIN}api/backend_2/home`
          iosAppLink = linkBeforeReplace.replace(/https/g, 'nexgencommerce')
          androidAppLink = `${process.env.VUE_APP_DOMAIN}api/backend_2/home`
          // var linkBeforeReplace = `${process.env.VUE_APP_DOMAIN}api/backend_2/?action_type=viewHome`
          // iosAppLink = linkBeforeReplace.replace(/https/g, 'nexgencommerce')
          // androidAppLink = `${process.env.VUE_APP_DOMAIN}api/backend_2/?action_type=viewHome`
        }
        const iosStoreLink = 'https://apps.apple.com/th/app/nexgen-commerce/id6651839687?l=th'
        const androidStoreLink = 'https://play.google.com/store/apps/details?id=com.inet.nexgenshop'

        // Determine the platform and set the appropriate URL scheme
        let appLink
        let storeLink
        if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
          appLink = iosAppLink
          storeLink = iosStoreLink
        } else if (/android/i.test(userAgent)) {
          appLink = androidAppLink
          storeLink = androidStoreLink
        } else {
          // Fallback for unsupported platforms
          // window.location.href = 'https://nexgencommerce.one.th/'
          // return
        }

        // Attempt to open the app using the custom URL scheme
        this.linkToApp = appLink
        this.linkToStore = storeLink
        if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
          this.dialogConfirmToApp = true
        } else if (/android/i.test(userAgent)) {
          this.dialogConfirmToApp = true
        }
      } else {
        this.widthChat = '535px;'
        this.heightChat = '520px;'
        this.dialogConfirmToApp = false
      }
    }
  },
  mounted () {
    this.checkWidthScreen()
    if (localStorage.getItem('roleUser') !== null) {
      this.roleUser = JSON.parse(localStorage.getItem('roleUser')).role
    } else {
      this.roleUser = 'ext_buyer'
    }
    window.addEventListener('scroll', (event) => {
      this.show = true
    })
    window.addEventListener('mouseup', (event) => {
      this.show = true
    })
    window.addEventListener('resize', function (event) {
      this.widthDevice = event.target.innerWidth.toString() + 'px'
    })
    if (localStorage.getItem('oneData') !== null) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (oneData.user !== '' && oneData.user !== undefined) {
        this.isLogin = true
        this.checkPDPA()
      }
    }
    if (this.MobileSize) {
      setTimeout(async () => {
        await this.GetLink()
      }, 1500)
    }
    this.$EventBus.$on('GetLink', this.GetLink)
    this.$EventBus.$on('checkPDPA', this.checkPDPA)
    this.$EventBus.$on('changeAppBarGracz', this.ChangeAppBarGracz)
    this.$EventBus.$on('changeAppBarPanit', this.ChangeAppBarPanit)
    this.$EventBus.$on('CheckFooter', this.CheckPathFooter)
    this.$EventBus.$on('changeReadyCarousel', this.changeReadyCarousel)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('CheckFooter')
      this.$EventBus.$off('changeAppBarGracz')
      this.$EventBus.$off('changeAppBarPanit')
      this.$EventBus.$off('changeReadyCarousel')
    })
    if (this.$route.query.utm_source) {
      sessionStorage.setItem('utm_source', this.$route.query.utm_source)
    }
    if (this.$route.query.utm_medium) {
      sessionStorage.setItem('utm_medium', this.$route.query.utm_medium)
    }
  },
  methods: {
    changeLang (value) {
      // console.log('lang =====>', value)
      localStorage.setItem('lang', value)
      this.$i18n.locale = value
      this.$forceUpdate()
    },
    gotoAppSelect () {
      this.openSelectOS = true
    },
    gotoFooter () {
      const element = document.getElementById('Footer')
      element.scrollIntoView({ behavior: 'smooth', block: 'start' })
    },
    gotoShopRegister (page) {
      localStorage.setItem('page', page)
      if (localStorage.getItem('oneData') !== null) {
        if (this.MobileSize) {
          this.$router.push({ path: `/InfoRegisterShopMobile?page=${page}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/InfoRegisterShop?page=${page}` }).catch(() => {})
        }
      } else {
        if (this.MobileSize) {
          this.$router.push({ path: '/shopRegisterMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/shopRegister' }).catch(() => {})
        }
      }
    },
    async getActiveGroupShop () {
      var data = {
        status: ''
      }
      await this.$store.dispatch('actionActiveCustomGroupShop', data)
      var response = await this.$store.state.ModuleShop.stateActiveCustomGroupShop
      if (response.code === 200) {
        if (response.data.status === 'active') {
          this.activeCustomGroupShop = true
        } else if (response.data.status === 'inactive') {
          this.activeCustomGroupShop = false
        }
      } else {
        this.activeCustomGroupShop = false
      }
    },
    async getTypeGroupShop () {
      var data = {
        status: ''
      }
      await this.$store.dispatch('actionTypeGroupShop', data)
      var response = await this.$store.state.ModuleShop.stateTypeGroupShop
      if (response.code === 200) {
        this.typeGroupShop = response.data.status_edit
      }
    },
    async GetLink () {
      var path = ''
      var cleanPath = ''
      var Id = ''
      var iosAppLink = ''
      var androidAppLink = ''
      var queryParams = ''
      var anId = ''
      // var responseShortURL = ''
      // var resultFromShortURL = ''
      // var pathUniversalLink = ''
      if (this.$router.currentRoute.name === 'DetailProduct') {
        // path = this.$router.currentRoute.params.data
        // cleanPath = path.split('-')
        // Id = cleanPath[cleanPath.length - 1]
        // this.dataToGetPath = {
        //   product_id: Id,
        //   seller_shop_id: '',
        //   type: 'product'
        // }
        path = this.$router.currentRoute.params.data
        cleanPath = path.split('-')
        Id = cleanPath[cleanPath.length - 1]
        queryParams = this.$route.query

        if (Object.entries(queryParams).length === 0) {
          this.dataToGetPath = {
            product_id: Id,
            seller_shop_id: '',
            type: 'product'
          }
        } else {
          anId = ''
          // ตรวจสอบ utm_medium และ utm_source
          if (queryParams.utm_medium === 'affiliate' && queryParams.utm_source) {
            anId = queryParams.utm_source // ใช้ค่า utm_source เป็น an_id
          }
          this.dataToGetPath = {
            product_id: Id,
            seller_shop_id: '',
            type: 'affiliate',
            an_id: anId
          }
        }
      } else if (this.$router.currentRoute.name === 'ShoppageUI') {
        path = this.$router.currentRoute.params.data
        cleanPath = path.split('-')
        Id = cleanPath[cleanPath.length - 1]
        this.dataToGetPath = {
          product_id: '',
          seller_shop_id: Id,
          type: 'sellerShop'
        }
      } else if (this.$router.currentRoute.name === 'GroupShoppage') {
        path = this.$router.currentRoute.params.data
        cleanPath = path.split('-')
        Id = cleanPath[cleanPath.length - 1]

        this.dataToGetPath = {
          group_id: Id,
          type: 'group'
        }
      } else {
        this.dataToGetPath = {
          product_id: '',
          seller_shop_id: '',
          type: 'home'
        }
      }
      await this.$store.dispatch('actionsGetLinkMobile', this.dataToGetPath)
      const response = await this.$store.state.ModuleHompage.stateGetLinkMobile
      if (response.message === 'Success') {
        iosAppLink = response.data.short_url.replace(/https/g, 'nexgencommerce')
        androidAppLink = response.data.intentLink
        // try {
        //   responseShortURL = await fetch(response.data.short_url)
        //   resultFromShortURL = await responseShortURL.json()
        //   if (resultFromShortURL.message === 'Success') {
        //     if (resultFromShortURL.data.action_type === 'viewProductDetail') {
        //       pathUniversalLink = `${process.env.VUE_APP_DOMAIN}api/backend_2/?action_type=${resultFromShortURL.data.action_type}&product_id=${resultFromShortURL.data.product_id}&have_attribute=${resultFromShortURL.data.have_attribute}`
        //     } else if (resultFromShortURL.data.action_type === 'viewShopDetail') {
        //       pathUniversalLink = `${process.env.VUE_APP_DOMAIN}api/backend_2/?action_type=${resultFromShortURL.data.action_type}&shop_id=${resultFromShortURL.data.shop_id}`
        //     } else {
        //       pathUniversalLink = `${process.env.VUE_APP_DOMAIN}api/backend_2/?action_type=${resultFromShortURL.data.action_type}`
        //     }
        //   } else {
        //     pathUniversalLink = `${process.env.VUE_APP_DOMAIN}api/backend_2/?action_type=viewHome`
        //   }
        // } catch (error) {
        // }
        // // iosAppLink = response.data.short_url.replace(/https/g, 'nexgencommerce')
        // // androidAppLink = response.data.short_url
        // iosAppLink = pathUniversalLink.replace(/https/g, 'nexgencommerce')
        // androidAppLink = pathUniversalLink
      } else {
        // กลับสู่หน้า Home
        var linkBeforeReplace = `${process.env.VUE_APP_DOMAIN}api/backend_2/home`
        iosAppLink = linkBeforeReplace.replace(/https/g, 'nexgencommerce')
        androidAppLink = `${process.env.VUE_APP_DOMAIN}api/backend_2/home`
        // var linkBeforeReplace = `${process.env.VUE_APP_DOMAIN}api/backend_2/?action_type=viewHome`
        // iosAppLink = linkBeforeReplace.replace(/https/g, 'nexgencommerce')
        // androidAppLink = `${process.env.VUE_APP_DOMAIN}api/backend_2/?action_type=viewHome`
      }
      const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera
      // Set URLs for iOS and Android
      const iosStoreLink = 'https://apps.apple.com/th/app/nexgen-commerce/id6651839687?l=th'
      const androidStoreLink = 'https://play.google.com/store/apps/details?id=com.inet.nexgenshop'

      // Determine the platform and set the appropriate URL scheme
      let appLink
      let storeLink
      if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        appLink = iosAppLink
        storeLink = iosStoreLink
      } else if (/android/i.test(userAgent)) {
        appLink = androidAppLink
        storeLink = androidStoreLink
      }
      // Attempt to open the app using the custom URL scheme
      this.linkToApp = appLink
      this.linkToStore = storeLink
    },
    async gotoApp () {
      // const now = Date.now()
      // window.location.href = this.linkToApp
      // setTimeout(() => {
      // }, 1500)
      let isAppOpened = false
      let timeout
      const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera

      // ตรวจจับการเปลี่ยนแปลง visibility
      const onVisibilityChange = () => {
        // console.log(document.hidden)
        if (document.hidden) {
          isAppOpened = true // แอปเปิดสำเร็จ
          clearTimeout(timeout)
        }
      }

      document.addEventListener('visibilitychange', onVisibilityChange)

      const now = Date.now()
      window.location.href = this.linkToApp // Custom URL Scheme

      // ตั้ง Timeout เพื่อตรวจสอบการ fallback
      if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        timeout = setTimeout(() => {
          if (!isAppOpened && Date.now() - now < 5000) {
            window.location.href = this.linkToStore // Fallback ไปยังหน้าติดตั้ง
          }

          // ทำความสะอาด
          document.removeEventListener('visibilitychange', onVisibilityChange)
        }, 4000)
      }
      // const hiddenEvent = () => document.hidden

      // // พยายามเปิดแอป
      // setTimeout(() => {
      //   window.location.href = this.linkToApp
      // }, 100) // เพิ่มระยะเวลาหน่วงเล็กน้อย

      // ตรวจสอบว่าแอปเปิดสำเร็จหรือไม่
      // if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
      //   const checkApp = setInterval(() => {
      //     if (Date.now() - startTime > 1000) {
      //       clearInterval(checkApp)
      //       // clearTimeout(timeout)
      //       isAppOpened = true
      //     }
      //   }, 100)
      // } else if (/android/i.test(userAgent)) {
      //   // ถ้าผู้ใช้เปิดแอปสำเร็จ
      //   document.addEventListener('visibilitychange', () => {
      //     if (hiddenEvent() && Date.now() - startTime < 2000) {
      //       // clearTimeout(timeout)
      //       isAppOpened = true
      //     }
      //   })
      // }

      // // ตรวจสอบว่าผู้ใช้ไม่ได้ติดตั้งแอป (หรือไม่ได้ตอบสนองภายในเวลาที่กำหนด)
      // const timeout = setTimeout(() => {
      //   if (!isAppOpened) {
      //     window.location.href = this.linkToStore // Redirect ไปยังหน้าติดตั้ง
      //   }
      // }, 5000)
    },
    ChangeShow () {
      this.show = true
    },
    checkWidthScreen () {
      window.addEventListener('resize', function (event) {
        this.widthDevice = event.target.innerWidth.toString() + 'px'
      })
    },
    ChangeAppBarGracz () {
      this.checkAppBar = 'Gracz'
      if (document.getElementById('botChat') !== null) {
        document.getElementById('botChat').outerHTML = '<></>'
        document.getElementById('minBotChat').outerHTML = '<></>'
      }
    },
    async ChangeAppBarPanit () {
      this.checkAppBar = 'Panit'
      if (localStorage.getItem('oneData') !== null) {
        const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        this.onedata = oneData
        if (oneData.user !== undefined) {
          if (oneData.user.shared_token !== undefined && oneData.user.shared_token !== null && oneData.user.shared_token !== '') {
            this.onechatToken = oneData.user.shared_token
          } else {
            this.onechatToken = ''
          }
        } else {
          localStorage.removeItem('oneData')
          // this.$store.commit('closeLoader')
        }
        // console.log('oneData===>', oneData.user)
        if (oneData.user === undefined && oneData.user === '') {
          localStorage.removeItem('oneData')
        }
      } else {
      }
    },
    closeModal () {
      this.$EventBus.$emit('closeModalLogin')
      this.$EventBus.$emit('closeModalRegister')
      this.$EventBus.$emit('closeModalSuccess')
      this.$EventBus.$emit('closeModalCartNoLogin')
      this.$EventBus.$emit('closeModalCart')
      this.$EventBus.$emit('OpenNotification')
      this.$EventBus.$emit('OpenChatAll')
      this.$EventBus.$emit('Open_No_Notification')
    },
    async CheckPathFooter () {
      var currentRoutepath = this.$router.currentRoute.path
      if (currentRoutepath === '/shop' || currentRoutepath === '/poseller' || currentRoutepath === '/manageproduct' || currentRoutepath === '/orderdetailseller' || currentRoutepath === '/seller') {
        this.path = false
      } else {
        this.path = true
      }
    },
    closeChat () {
      this.showChat = !this.showChat
    },
    async getBotChat () {
      var data
      const auth = {
        headers: { Authorization: `Bearer ${this.onedata.user.access_token}` }
      }
      if (localStorage.getItem('roleUser') !== null) {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        data = {
          role_user: dataRole.role
        }
      } else {
        data = {
          role_user: 'ext_buyer'
        }
      }
      var res = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/send_quickreply`, data, auth)
      if (res.data.result === 'SUCCESS') {
      }
    },
    async getChat () {
      // await this.getBotChat()
      // console.log(this.widthChat, this.heightChat)
      // -------------------- New One Chat UAT -----------------------
      var divChatbot = document.createElement('div')
      document.getElementsByTagName('body')[0].appendChild(divChatbot)
      var imgminchat = 'https://devinet-eprocurement.one.th/static/img/logo_chat.png'
      var sharetoken = this.onechatToken
      // var botId = this.botID
      // console.log(sharetoken)
      var link = 'https://uatchat-plugin.one.th/web-plugin/firstpage-oneid/E-Procurement?bot_id=92d8d32e-04d1-44e7-aac3-790f9e9a0e0b&sharetoken=' + sharetoken
      // var link = 'https://uatchat-plugin.one.th/web-plugin/firstpage-oneid/E-Procurement?bot_id=' + botId + '&sharetoken=' + sharetoken
      if (!this.MobileSize) {
        divChatbot.outerHTML = "<div id='botChat' style='border-radius: 10px;position: absolute;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent; overflow: hidden; position: fixed; z-index: 16000004; right: 30px; bottom: 30px;display: none; width: 535px; height: 520px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 3px 2px;'><div id='botTitleBar' style='z-index: 16000005;height: 25px; width: 535px; position:fixed; cursor: pointer; border-top-left-radius: 10px; border-top-right-radius: 10px;'> <i class='fa fa-minus-circle' id='botCloseplugin' style='float: right; margin-top: 15px; color:white; margin-right: 15px; width: 20px; height: 20px;'></i></div><iframe id='iframechatbot' frameborder='0' style='background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; max-width: 100%; max-height: 100%; margin: 0px; overflow: hidden; display: block;'src='" + link + "'></iframe></div><div id='minBotChat' style='border-radius: 10px;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent;position: fixed; z-index: 16000002; width: 90px; height: 90px; right: 10px; bottom: 15px;'><div id='minBotChatTitle' style='z-index: 16000003;height: 90px; width: 90px; position:fixed; cursor: pointer;'></div><img src='" + imgminchat + "' style='width: 70px;height: 70px;border-radius: 6px;'/></div>"
      } else {
        divChatbot.outerHTML = "<div id='botChat' style='border-radius: 10px;position: absolute;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent; overflow: hidden; position: fixed; z-index: 16000004; right: 30px; bottom: 30px;display: none; width: 75vw; height: 60vh; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 3px 2px;'><div id='botTitleBar' style='z-index: 16000005;height: 25px; width: 290px; position:fixed; cursor: pointer; border-top-left-radius: 10px; border-top-right-radius: 10px;'> <i class='fa fa-minus-circle' id='botCloseplugin' style='float: right; margin-top: 15px; color:white; margin-right: 15px; width: 20px; height: 20px;'></i></div><iframe id='iframechatbot' frameborder='0' style='background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; max-width: 100%; max-height: 100%; margin: 0px; overflow: hidden; display: block;'src='" + link + "'></iframe></div><div id='minBotChat' style='border-radius: 10px;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent;position: fixed; z-index: 16000002; width: 60px; height: 60px; right: 20px; bottom: 15px;'><div id='minBotChatTitle' style='z-index: 16000003;height: 90px; width: 90px; margin-right: 30px; margin-botton: 20px; position:fixed; cursor: pointer;'></div><img src='" + imgminchat + "' style='width: 70px;height: 70px;border-radius: 6px;'/></div>"
      }
      document.querySelector('body').addEventListener('click', function (e) {
        e.target.matches = e.target.matches || e.target.msMatchesSelector
        if (e.target.matches('#botCloseplugin')) {
          document.getElementById('botChat').style.display = 'none'
          document.getElementById('minBotChat').style.display = 'block'
        } else if (e.target.matches('#minBotChatTitle')) {
          document.getElementById('botChat').style.display = 'block'
          document.getElementById('minBotChat').style.display = 'none'
        }
      })
      document.getElementById('minBotChatTitle').addEventListener('click', this.getBotChat)
      this.dragElement(document.getElementById('botChat'))
      // -------------------- New One Chat PRO -----------------------
      // var divChatbot = document.createElement('div')
      // document.getElementsByTagName('body')[0].appendChild(divChatbot)
      // var imgminchat = 'https://devinet-eprocurement.one.th/static/img/logo_chat.png'
      // var sharetoken = this.onechatToken
      // var link = 'https://chat-plugin.one.th/web-plugin/firstpage-oneid/E-Procurement?bot_id=bdfea3a3-61be-459a-b9d5-ef212e82f9ed&sharetoken=' + sharetoken

      // divChatbot.outerHTML = "<div id='botChat' style='border-radius: 10px;position: absolute;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent; overflow: hidden; position: fixed; z-index: 16000004; right: 30px; bottom: 30px;display: none; width: 535px; height: 520px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 3px 2px;'><div id='botTitleBar' style='z-index: 16000005;height: 25px; width: 535px; position:fixed; cursor: pointer; border-top-left-radius: 10px; border-top-right-radius: 10px;'> <i class='fa fa-minus-circle' id='botCloseplugin' style='float: right;margin-top: 10px;color:white;margin-right: 10px;'></i></div><iframe id='iframechatbot' frameborder='0' style='background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; max-width: 100%; max-height: 100%; margin: 0px; overflow: hidden; display: block;'src='" + link + "'></iframe></div><div id='minBotChat' style='border-radius: 10px;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent;position: fixed; z-index: 16000002; width: 90px; height: 90px; right: 10px; bottom: 15px;'><div id='minBotChatTitle' style='z-index: 16000003;height: 90px; width: 90px; position:fixed; cursor: pointer;'></div><img src='" + imgminchat + "' style='width: 70px;height: 70px;border-radius: 6px;'/></div>"
      // document.querySelector('body').addEventListener('click', function (e) {
      //   e.target.matches = e.target.matches || e.target.msMatchesSelector
      //   if (e.target.matches('#botCloseplugin')) {
      //     document.getElementById('botChat').style.display = 'none'
      //     document.getElementById('minBotChat').style.display = 'block'
      //   } else if (e.target.matches('#minBotChatTitle')) {
      //     document.getElementById('botChat').style.display = 'block'
      //     document.getElementById('minBotChat').style.display = 'none'
      //   }
      // })
      // document.getElementById('minBotChatTitle').addEventListener('click', this.getBotChat)
      // this.dragElement(document.getElementById('botChat'))
    },
    dragElement (elmnt) {
      // console.log(elmnt)
      var pos1 = 0
      var pos2 = 0
      var pos3 = 0
      var pos4 = 0
      if (document.getElementById('botTitleBar')) {
        // console.log('เข้า')
        document.getElementById('botTitleBar').onmousedown = dragMouseDown
      } else {
        elmnt.onmousedown = dragMouseDown
      }
      function dragMouseDown (e) {
        e = e || window.event
        e.preventDefault()
        pos3 = e.clientX
        pos4 = e.clientY
        document.onmouseup = closeDragElement
        document.onmousemove = elementDrag
      }
      function elementDrag (e) {
        e = e || window.event
        e.preventDefault()
        // calculate the new cursor position:
        pos1 = pos3 - e.clientX
        pos2 = pos4 - e.clientY
        pos3 = e.clientX
        pos4 = e.clientY
        // set the element's new position:
        elmnt.style.top = (elmnt.offsetTop - pos2) + 'px'
        elmnt.style.left = (elmnt.offsetLeft - pos1) + 'px'
      }
      function closeDragElement () {
        document.onmouseup = null
        document.onmousemove = null
      }
    },
    // dragMouseDown (e) {
    //   e = e || window.event
    //   e.preventDefault()
    //   this.pos3 = e.clientX
    //   this.pos4 = e.clientY
    //   document.onmouseup = this.closeDragElement()
    //   document.onmousemove = this.elementDrag()
    // },
    // elementDrag (e) {
    //   e = e || window.event
    //   e.preventDefault()
    //   this.pos1 = this.pos3 - e.clientX
    //   this.pos2 = this.pos4 - e.clientY
    //   this.pos3 = e.clientX
    //   this.$rootpos4 = e.clientY
    //   document.getElementById('botChat').style.top = (document.getElementById('botChat').offsetTop - this.pos2) + 'px'
    //   document.getElementById('botChat').style.left = (document.getElementById('botChat').offsetLeft - this.pos1) + 'px'
    // },
    // closeDragElement () {
    //   document.onmouseup = null
    //   document.onmousemove = null
    // }
    changeReadyCarousel () {
      this.readyCarousel = true
    },
    async checkPDPA () {
      await this.$store.dispatch('actionsAuthorityUser')
      var res = this.$store.state.ModuleUser.stateAuthorityUser
      var checkCon = res.data.consent_platform_approved.data.accept
      if (checkCon === 'waiting') {
        if (this.$refs.ModalPDPA) {
          await this.$refs.ModalPDPA.open()
        }
      } else {
        if (res.data.first_name_th === '' || res.data.last_name_th === '' || res.data.first_name_th === null || res.data.last_name_th === null) {
          await this.$refs.ModalCheckName.open(res.data.phone, res.data.email, res.data.username)
        }
      }
      this.isBuyer = res.data.affiliate_consent.isBuyer
      this.isImageNational = res.data.affiliate_consent.isImageNational
      this.isNationalId = res.data.affiliate_consent.isNationalId
      this.isAddress = res.data.affiliate_consent.isAddress
      if (this.isBuyer === '1') {
        if (this.isNationalId === '0') {
          if (res.data.affiliate_list_buyer_detail !== undefined) {
            if (res.data.affiliate_list_buyer_detail.national_id === '' || res.data.affiliate_list_buyer_detail.national_id === null || res.data.affiliate_list_buyer_detail.national_id === undefined) {
              await this.$refs.ModalDetailTax.open()
            } else {
              if (this.isAddress === '0') {
                this.$swal.fire({
                  icon: 'warning',
                  html: 'กรุณาเพิ่มข้อมูลที่อยู่ตามบัตรประชาชน<br>สำหรับโปรแกรม Affiliate',
                  showConfirmButton: true,
                  confirmButtonColor: '#27AB9C',
                  confirmButtonText: 'เพิ่ม',
                  allowOutsideClick: false
                }).then((result) => {
                  if (result.isConfirmed) {
                    if (this.MobileSize) {
                      this.$router.push({ path: '/editpayMobile' }).catch(() => {})
                    } else {
                      this.$router.push({ path: '/editpay' }).catch(() => {})
                    }
                  }
                })
              }
            }
          }
        }
      }
    }
  }
}
</script>

<style scoped>
.styleSytemBar {
  z-index: 12 !important;
  padding: 8px 96px;
  gap: 10px;
}
.styleSytemBarIPadPro {
  z-index: 12 !important;
  padding: 8px 24px;
  gap: 10px;
}
.styleSytemBarIPad {
  z-index: 12 !important;
  padding: 8px 24px;
  gap: 10px;
}
.textSystemBar {
  color: #545A67;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
}
.Language {
  gap: 16px !important;
}
.backgroundPage{
  background-color: #FAFFFF;
}
.OverflowX {
  overflow-x: hidden !important;
  overflow-y: hidden !important;
 }
</style>
