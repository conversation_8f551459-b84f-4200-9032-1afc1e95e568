<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize"><v-icon class="mr-2" style="color: #27AB9C;" @click="cancelShipping()">mdi-chevron-left</v-icon> สร้างใบส่งสินค้า</v-card-title>
      <v-card-title style="font-weight: 700;" v-else>
          <v-icon class="mr-2" style="color: #27AB9C;" @click="cancelShipping()">mdi-chevron-left</v-icon> สร้างใบส่งสินค้า
      </v-card-title>

      <v-col>
        <v-form ref="form" v-model="formValid">
          <v-row>
            <v-col cols="12">
              <v-chip color="#F5F5F5" style="font-size: 16px; height: 40px;">
                <v-avatar rounded>
                  <v-img contain src="@/assets/shopDelivery/delivery3.png"></v-img>
                </v-avatar>
                <span class="pl-2"><b>รหัสการสั่งซื้อ : {{ this.orderNumber }}</b></span>
              </v-chip>
            </v-col>
            <v-col cols="12">
              <v-row>
                <!-- วันที่จัดส่งสินค้า -->
                <v-col cols="12" md="3">
                  <span style="font-size: 16px;">วันที่จัดส่งสินค้า</span>
                  <v-dialog v-model="modalRangeDate" persistent width="290px">
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        readonly
                        v-model="deliveryList.deliveryDate"
                        v-bind="attrs"
                        v-on="on"
                        outlined
                        dense
                        hide-details
                        placeholder="วว/ดด/ปปปป"
                        style="border-radius: 8px;"
                      >
                      <template v-slot:append>
                        <v-icon color="#CCCCCC">
                          mdi-clock-outline
                        </v-icon>
                      </template>
                    </v-text-field>
                  </template>
                    <v-date-picker
                      color="#27AB9C"
                      v-model="selectDeliveryDate"
                      scrollable
                      reactive
                      locale="Th-th"
                      :allowed-dates="allowedDates"
                    >
                      <v-spacer></v-spacer>
                      <v-btn outlined color="red" @click="modalRangeDate = false">ยกเลิก</v-btn>
                      <v-btn outlined color="primary" :disabled="!selectDeliveryDate"  @click="setDate(selectDeliveryDate)">ตกลง</v-btn>
                    </v-date-picker>
                  </v-dialog>
                </v-col>

                <!-- เวลาที่จัดส่งสินค้า -->
                <v-col cols="12" md="3">
                  <span style="font-size: 16px;">เวลาที่จัดส่งสินค้า</span>
                  <v-dialog v-model="modalTimePicker" persistent width="290px">
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        readonly
                        v-model="deliveryList.deliveryTime"
                        v-bind="attrs"
                        v-on="on"
                        outlined
                        dense
                        hide-details
                        placeholder="00:00"
                        style="border-radius: 8px;"
                        >
                        <template v-slot:append>
                          <v-icon color="#CCCCCC">
                            mdi-clock-outline
                          </v-icon>
                        </template>
                      </v-text-field>
                    </template>
                    <v-time-picker
                      v-model="selectedTime"
                      color="#27AB9C"
                      format="24hr"
                      :allowed-hours="allowedHours"
                      :allowed-minutes="allowedMinutes"
                    >
                      <v-spacer></v-spacer>
                      <v-btn outlined color="red" @click="modalTimePicker = false">ยกเลิก</v-btn>
                      <v-btn outlined color="primary" :disabled="!selectedTime"  @click="setTime(selectedTime)">ตกลง</v-btn>
                    </v-time-picker>
                  </v-dialog>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12">
              <v-row>
                <v-col cols="6" class="align-center">
                  <v-avatar rounded size="25">
                    <v-img contain src="@/assets/shopDelivery/delivery4.png"></v-img>
                  </v-avatar>
                  <span class="pl-2" style="font-size: 18px;"><b>รายการสินค้า</b></span>
                </v-col>
                <v-col cols="6" class="d-flex align-center">
                  <v-col>
                    <v-row class="d-flex align-center" style="display: flex; justify-content: flex-end;">
                      <div>
                        <v-checkbox
                          v-model="allProductsShipped"
                          label="จัดส่งสินค้าทั้งหมด"
                          hide-details
                          class="mt-0"
                          style="padding-top: 0px; font-size: 16px;"
                          @change="toggleAllProducts($event)"
                        ></v-checkbox>
                      </div>
                      <div class="pl-5">
                        <span style="font-size: 16px; cursor: pointer;" :class="{ 'disabled-text': selectedProducts.length === 0 }" @click="removeDeliveryList()"><v-btn icon style="background-color: #F2F2F2;"><v-icon :disabled="selectedProducts.length === 0" color="#E31C26">mdi-trash-can-outline</v-icon></v-btn> ลบทั้งหมด</span>
                      </div>
                    </v-row>
                  </v-col>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12">
              <span style="font-size: 16px;">สินค้าที่ต้องการจัดส่ง</span>
              <v-select
                v-model="selectedProduct"
                :items="productsDetails"
                :label="selectedProduct ? '' : 'เลือกสินค้า'"
                solo
                dense
                hide-details
                return-object
                ref="select"
                @update:modelValue="closeMenu"
              >
                <template v-slot:selection>
                  <span v-if="!selectedProduct">เลือกสินค้า</span>
                </template>
                <template v-slot:item="{ item }">
                  <v-list-item @click="selectItem(item)">
                      <v-img
                        v-if="item.image && item.image !== '-'"
                        :src="item.image"
                        max-height="70"
                        max-width="70"
                        class="mr-2"
                      ></v-img>
                      <img
                        v-else
                        src="@/assets/NoImage.png"
                        style="max-width: 70px; max-height: 70px;"
                        class="mr-2"
                      />
                    <v-list-item-content>
                      <v-list-item-title style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 700px;">
                        {{ item.product_info }}
                      </v-list-item-title>
                      <v-list-item-subtitle>รหัสสินค้า: {{ item.product_id }}</v-list-item-subtitle>
                      <v-list-item-subtitle v-if="item.color && item.color !== '-'">
                        {{ item.color }}
                      </v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                </template>
              </v-select>
            </v-col>
            <v-col cols="12">
              <v-card class="custom-card elevation-0">
                <div class="card-header">
                  รายการส่งสินค้าทั้งหมด {{ selectedProducts.length }} รายการ
                </div>
                <v-card-text style="max-height: 700px; overflow-y: auto;">
                  <v-col v-if="selectedProducts.length">
                    <v-row  v-for="(item, index) in selectedProducts" :key="index">
                      <v-col cols="12" style="align-items: center;">
                        <v-row>
                          <v-col cols="6" class="pb-0">
                            <span style="font-size: 16px; color: #27AB9C;"><b>รายการที่ {{ index + 1 }}</b></span>
                          </v-col>
                          <v-col cols="6" class="pb-0" style="text-align: end;">
                            <v-btn icon style="background-color: #F2F2F2;" @click="removeItem(index)">
                              <v-icon color="#E31C26">mdi-trash-can-outline</v-icon>
                            </v-btn>
                          </v-col>
                        </v-row>
                      </v-col>
                      <v-col :cols="MobileSize ? 12 : 9">
                        <v-list class="pa-0">
                          <v-list-item>
                            <v-img
                              v-if="item.image && item.image !== '-'"
                              :src="item.image"
                              max-height="100"
                              max-width="100"
                              class="mr-2"
                            ></v-img>
                            <img
                              v-else
                              src="@/assets/NoImage.png"
                              style="max-width: 100px; max-height: 100px;"
                              class="mr-2"
                            />
                            <v-list-item-content>
                              <v-list-item-title style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 650px;">
                                {{ item.product_info }}
                              </v-list-item-title>
                              <v-list-item-subtitle style="font-size: 16px;">รหัสสินค้า: {{ item.product_id }}</v-list-item-subtitle>
                              <v-list-item-subtitle v-if="item.color && item.color !== '-'">
                                {{ item.color }}
                              </v-list-item-subtitle>
                            </v-list-item-content>
                          </v-list-item>
                        </v-list>
                      </v-col>
                      <v-col :cols="MobileSize ? 12 : 3">
                        <v-col>
                          <v-row>
                            <span>จำนวนสินค้า</span>
                          </v-row>
                          <v-row>
                            <v-text-field
                              v-model="item.addQuantity"
                              outlined
                              dense
                              oninput="this.value = this.value.replace(/[^0-9\s]/g, '')"
                              :rules="[v => {
                                let quantity = Number(v) || 0
                                let maxQuantity = item.quantity || 0
                                if (quantity <= 0) {
                                  return 'กรุณากรอกจำนวนที่ถูกต้อง'
                                }
                                return quantity <= maxQuantity || `สินค้าคงเหลือของคุณไม่พอ (สูงสุด ${maxQuantity})`
                              }]"
                              @input="updateAddQuantity(item)"
                            ></v-text-field>
                          </v-row>
                          <v-row class="align-center mt-2">
                            <v-avatar rounded size="30">
                              <v-img contain src="@/assets/shopDelivery/delivery2.png"></v-img>
                            </v-avatar>
                            <span class="pl-2">คงเหลือ: <b>{{ item.quantity }}</b></span>
                            <v-spacer></v-spacer>
                            <v-checkbox
                              v-model="item.allProductsQuantity"
                              label="ส่งทั้งหมด"
                              hide-details
                              class="mt-0"
                              style="padding-top: 0px; font-size: 16px;"
                              @change="toggleSingleProduct(item)"
                            ></v-checkbox>
                          </v-row>
                        </v-col>
                      </v-col>
                      <v-col cols="12" class="pa-1">
                        <v-divider class="mt-1" style="background-color: #F2F2F2;"></v-divider>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" style="padding: 20px; display: flex; flex-direction: column; align-items: center; justify-content: center;" v-if="selectedProducts.length === 0">
                    <v-avatar rounded size="120">
                      <v-img contain src="@/assets/shopDelivery/delivery5.png"></v-img>
                    </v-avatar>
                    <br>
                    <span style="font-size: 16px; color: #989898;">ยังไม่มีรายการสินค้าที่ต้องการจัดส่ง</span>
                  </v-col>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col cols="12" class="d-flex justify-center">
              <v-btn rounded class="ma-5" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C;" @click="cancelShipping()"><b>ยกเลิก</b></v-btn>
              <!-- <v-btn rounded class="ma-5" color="#27AB9C" style="color: white;" :disabled="!isComplete" @click="confirmShipping()"><b>สร้างใบส่งสินค้า</b></v-btn> -->
              <v-btn rounded class="ma-5" color="#27AB9C" style="color: white;" :disabled="!formValid || !isComplete" @click="confirmShipping()"><b>สร้างใบส่งสินค้า</b></v-btn>
            </v-col>
          </v-row>
        </v-form>
      </v-col>
    </v-card>

    <v-dialog v-model="dialogCancel" persistent max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogCancel = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #fffef5">
          <v-img
            src="@/assets/shopDelivery/info.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 24px;"><b>ยกเลิกรายการ</b></span><br><br>
            <span style="font-size: 16px;">คุณแน่ใจหรือไม่ว่าต้องการยกเลิก<br>การสร้างใบส่งสินค้า</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px; font-size: 16px;" @click="dialogCancel = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white; font-size: 16px;" @click="clearShippingData()">ยืนยัน</v-btn>
        </v-card-actions>
        <br>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogDelete" persistent max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogDelete = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center pb-0" style="border-radius: 35px 35px 0 0; background: #FDFAF9">
          <div>
            <v-img
              src="@/assets/shopDelivery/delete.png"
              contain
              max-width="200"
            ></v-img>
          </div>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 24px;"><b>ลบข้อมูล</b></span><br><br>
            <span style="font-size: 16px;">คุณแน่ใจหรือไม่ว่าต้องการลบข้อมูลนี้</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px; font-size: 16px;" @click="dialogDelete = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white; font-size: 16px;" @click="removeDelivery()">ยืนยัน</v-btn>
        </v-card-actions>
        <br>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogSuccessDelete" persistent max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto"  @click="dialogSuccessDelete = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #F8FFF5">
          <v-img
            src="@/assets/shopDelivery/success.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 24px;"><b>ยกเลิกรายการสำเร็จ</b></span><br><br>
            <span style="font-size: 16px;">คุณได้ทำยกเลิกการสร้างใบส่งสินค้าแล้ว</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white; font-size: 16px;" @click="dialogSuccessDelete = false">ปิด</v-btn>
        </v-card-actions>
        <br>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogSuccessCancel" persistent max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto"  @click="backToListDelivery()" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #F8FFF5">
          <v-img
            src="@/assets/shopDelivery/success.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 24px;"><b>ยกเลิกรายการสำเร็จ</b></span><br><br>
            <span style="font-size: 16px;">คุณได้ทำยกเลิกการสร้างใบส่งสินค้าแล้ว</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white; font-size: 16px;" @click="backToListDelivery()">ปิด</v-btn>
        </v-card-actions>
        <br>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogCreate" persistent max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogCreate = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #fffef5">
          <v-img
            src="@/assets/shopDelivery/info.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <br>
            <span style="font-size: 24px;"><b>สร้างใบส่งสินค้า</b></span><br><br>
            <span style="font-size: 16px;">คุณแน่ใจหรือไม่ว่าต้องการสร้างใบส่งสินค้า</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px; font-size: 16px;" @click="dialogCreate = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white; font-size: 16px;" @click="createShipping()">ยืนยัน</v-btn>
        </v-card-actions>
        <br>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogCreateSuccess" persistent max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto"  @click="backToListDelivery2()" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #F8FFF5">
          <v-img
            src="@/assets/shopDelivery/success.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 24px;"><b>สร้างใบส่งสินค้าสำเร็จ</b></span><br><br>
            <span style="font-size: 16px;">คุณได้ทำการสร้างใบส่งสินค้าแล้ว</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white; font-size: 16px;" @click="backToListDelivery2()">ปิด</v-btn>
        </v-card-actions>
        <br>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
export default {
  data () {
    return {
      formValid: false,
      selectedProduct: null,
      selectedProducts: [],
      dialogCancel: false,
      dialogSuccessCancel: false,
      dialogCreate: false,
      dialogCreateSuccess: false,
      dialogDelete: false,
      dialogSuccessDelete: false,
      allProductsShipped: false,
      allProductsQuantity: false,
      shipAllRemainingProducts: false,
      modalRangeDate: false,
      deliveryDate: '',
      selectDeliveryDate: '',
      modalTimePicker: false,
      deliveryTime: '',
      selectedTime: '',
      SelectProductName: '',
      addQuantity: '',
      deliveryList: [],
      productsDetails: [],
      selectProductsDetails: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    window.scrollTo(0, 0)
    this.orderNumber = this.$route.query.orderNumber
    this.productsDetail()
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: `/ManageDeliveryMobile?orderNumber=${this.orderNumber}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/ManageDelivery?orderNumber=${this.orderNumber}` }).catch(() => {})
      }
    },
    'deliveryList.deliveryDate': {
      handler () {
        this.isComplete = this.checkCompleteData()
      },
      immediate: true
    },
    'deliveryList.deliveryTime': {
      handler () {
        this.isComplete = this.checkCompleteData()
      },
      immediate: true
    },
    'deliveryList.productsList': {
      deep: true,
      handler () {
        this.isComplete = this.checkCompleteData()
      },
      immediate: true
    }
  },
  methods: {
    backToListDelivery () {
      this.activeTab = 'delivery'
      if (this.MobileSize) {
        this.$router.push({ path: '/ListDeliveryMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ListDelivery' })
      }
    },
    backToListDelivery2 () {
      const targetPath = this.MobileSize ? '/ListDeliveryMobile' : '/ListDelivery'
      this.$router.push({ path: targetPath, query: { tab: 'delivery' } }).catch(() => {})
    },
    checkCompleteData () {
      return (
        !!this.deliveryList.deliveryDate &&
        !!this.deliveryList.deliveryTime &&
        this.deliveryList.productsList &&
        this.deliveryList.productsList.length > 0 &&
        // this.deliveryList.productsList.every(product => product.addQuantity > 0)
        this.deliveryList.productsList.every(product =>
          product.addQuantity > 0 &&
          product.addQuantity <= product.quantity // ตรวจไม่ให้เกินจำนวนที่มี
        )
      )
    },
    async productsDetail () {
      this.$store.commit('openLoader')
      var data = {
        order_number: this.orderNumber
      }
      await this.$store.dispatch('actionListProductDelivery', data)
      var responseData = await this.$store.state.ModuleShop.stateListProductDelivery
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.productsDetails = responseData.data
      }
    },
    selectItem (item) {
      if (!this.selectedProducts.some(p => p.product_id === item.product_id)) {
        this.selectedProduct = item
        this.selectProduct()
      }

      this.closeMenu()
      this.selectedProduct = null
    },
    selectProduct () {
      if (this.selectedProduct && !this.selectedProducts.some(p => p.product_id === this.selectedProduct.product_id)) {
        this.selectedProducts.push({
          ...this.selectedProduct,
          addQuantity: 0,
          index: this.selectedProducts.length
        })

        this.$set(this.deliveryList, 'productsList', [...this.selectedProducts])
      }
      this.closeMenu()
    },
    toggleAllProducts (newValue) {
      this.allProductsShipped = newValue

      if (newValue) {
        this.selectedProducts = this.productsDetails.map((product, index) => ({
          ...product,
          addQuantity: product.quantity,
          allProductsQuantity: true,
          index
        }))
      } else {
        this.selectedProducts.forEach((item) => {
          this.$set(item, 'addQuantity', 0)
          this.$set(item, 'allProductsQuantity', false)
        })
      }

      this.$set(this.deliveryList, 'productsList', [...this.selectedProducts])
    },
    toggleSingleProduct (item) {
      this.$set(item, 'addQuantity', item.allProductsQuantity ? item.quantity : 0)
      this.$set(item, 'allProductsQuantity', item.addQuantity === item.quantity)

      this.allProductsShipped = this.selectedProducts.length === this.productsDetails.length && this.selectedProducts.every(
        p => p.addQuantity === p.quantity
      )
      // console.log(this.allProductsShipped)
    },
    updateAddQuantity (item) {
      // console.log('item', item)
      var maxQuantity = item.quantity || 0
      item.addQuantity = Math.min(Number(item.addQuantity) || 0, maxQuantity)

      this.$set(item, 'allProductsQuantity', item.addQuantity === item.quantity)

      this.allProductsShipped = this.selectedProducts.length === this.productsDetails.length && this.selectedProducts.every(
        p => p.addQuantity === p.quantity && p.addQuantity > 0
      )
      // console.log(this.allProductsShipped)
    },
    removeItem (index) {
      this.selectedProducts.splice(index, 1)
      this.allProductsShipped = false
      this.allProductsQuantity = false
    },
    closeMenu () {
      this.$nextTick(() => {
        if (this.$refs.select) {
          this.$refs.select.blur()
        }
      })
    },
    formatDate (date) {
      return new Date(date).toLocaleDateString('th-TH', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      })
    },
    setDate (date) {
      this.deliveryList.deliveryDate = this.formatDate(date)
      this.modalRangeDate = false
      this.isComplete = this.checkCompleteData()
    },
    allowedDates (date) {
      const today = new Date()
      const selected = new Date(date)

      // ปรับเวลาทั้งคู่ให้เท่ากันเพื่อเปรียบเทียบเฉพาะวันที่
      today.setHours(0, 0, 0, 0)
      selected.setHours(0, 0, 0, 0)

      return selected >= today
    },
    allowedHours (hour) {
      if (!this.selectDeliveryDate) return true

      const today = new Date()
      const selected = new Date(this.selectDeliveryDate)
      const isToday = today.toDateString() === selected.toDateString()

      if (!isToday) return true

      const currentHour = today.getHours()
      return hour >= currentHour
    },

    allowedMinutes (minute) {
      if (!this.selectDeliveryDate || !this.selectedTime) return true

      const today = new Date()
      const selected = new Date(this.selectDeliveryDate)
      const isToday = today.toDateString() === selected.toDateString()

      const selectedHour = parseInt(this.selectedTime.split(':')[0])
      const currentHour = today.getHours()
      const currentMinute = today.getMinutes()

      // ถ้าเลือกวันนี้ และอยู่ในชั่วโมงปัจจุบัน → ต้องเลือกนาทีถัดจากเวลาปัจจุบัน
      if (isToday && selectedHour === currentHour) {
        return minute > currentMinute
      }

      return true
    },
    setTime (time) {
      // สร้าง Date object จากวันที่และเวลาที่ผู้ใช้เลือก
      const [hours, minutes] = time.split(':')
      const selectedDateTime = new Date(this.selectDeliveryDate)
      selectedDateTime.setHours(hours, minutes, 0, 0)

      const now = new Date()
      // ถ้าเป็นวันเดียวกันกับวันนี้ และเวลาที่เลือก < เวลาปัจจุบัน
      const isSameDate = selectedDateTime.toDateString() === now.toDateString()

      if (isSameDate && selectedDateTime < now) {
        this.$swal.fire({
          icon: 'warning',
          text: 'ไม่สามารถเลือกเวลาที่ผ่านไปแล้วได้',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
        return
      }

      this.deliveryList.deliveryTime = time
      this.modalTimePicker = false
      this.isComplete = this.checkCompleteData()
    },
    confirmShipping () {
      this.dialogCreate = true
    },
    async createShipping () {
      this.$store.commit('openLoader')
      this.dialogCreate = false
      var data = {
        order_number: this.orderNumber,
        deliveryDate: this.selectDeliveryDate,
        deliveryTime: this.deliveryList.deliveryTime,
        productList: this.deliveryList.productsList.map((product, index) => ({
          productId: product.product_id,
          productName: product.product_info,
          quantity: product.addQuantity,
          image: product.image,
          attribute: product.color,
          index: index
        }))
      }
      await this.$store.dispatch('actionCreateProductDelivery', data)
      var responseData = await this.$store.state.ModuleShop.stateCreateProductDelivery
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.dialogCreateSuccess = true
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: responseData.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    },
    cancelShipping () {
      this.dialogCancel = true
    },
    clearShippingData () {
      this.deliveryList = []
      this.dialogCancel = false
      this.dialogSuccessCancel = true
      // this.backToListDelivery()
    },
    removeDeliveryList () {
      this.dialogDelete = true
    },
    removeDelivery () {
      this.dialogDelete = false
      this.dialogSuccessDelete = true
      this.deliveryList = []
      this.selectedProducts = []
      this.deliveryList.productsList = []
      this.allProductsShipped = false
      this.allProductsQuantity = false
    }
  }
}
</script>

<style scoped>
.head {
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 400;
  color: black;
  text-align: center;
}

.divider {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.v-divider {
  background-color: #000;
}
.custom-card {
  border: 1px solid #ccc;
  border-radius: 8px;
}

.card-header {
  background-color: #f5f5f5;
  color: #333;
  font-weight: bold;
  padding: 16px;
  border-bottom: 1px solid #ccc;
  font-size: 18px;
}

.disabled-text {
  color: gray !important;
  pointer-events: none; /* ❌ ปิดการคลิก */
}
</style>
