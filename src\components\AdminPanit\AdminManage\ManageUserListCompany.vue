<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0">
      <v-card-title v-if="!MobileSize && !IpadSize" style="font-weight: 700; font-size: 24px;" ><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>จัดการผู้ใช้งานบริษัท <span style="color: #27AB9C; margin-left: .5vw;">   {{ this.$route.query.name }}</span></v-card-title>
      <v-card-title v-else-if="IpadSize" style="font-weight: 700; font-size: 20px;" ><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>จัดการผู้ใช้งานบริษัท <span style="color: #27AB9C; margin-left: .5vw;">   {{ this.$route.query.name }}</span></v-card-title>
      <v-card-title v-else style="font-weight: 700;" ><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>จัดการผู้ใช้งานบริษัท <span style="color: #27AB9C; margin-left: .5vw;"> {{ this.$route.query.name }}</span></v-card-title>
      <v-card-text>
        <v-row dense no-gutters class="mt-2">
          <v-col cols="12" class="py-0 mb-0">
            <a-tabs @change="selectTab">
              <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
              <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ listUser.length }}</a-tag></span></a-tab-pane>
              <a-tab-pane :key="1"><span slot="tab">ใช้งาน <a-tag color="#1AB759" style="border-radius: 8px;">{{ listUserActive.length }}</a-tag></span></a-tab-pane>
              <a-tab-pane :key="2"><span slot="tab">ยกเลิก <a-tag color="#FF0000" style="border-radius: 8px;">{{ listUserInactive.length }}</a-tag></span></a-tab-pane>
            </a-tabs>
          </v-col>
        </v-row>
      </v-card-text>
      <v-row class="pt-0">
        <v-col cols="12">
          <v-col cols="12" md="6" sm="12">
             <v-text-field v-model="search" placeholder="ค้นหาจากผู้ใช้บริษัทในระบบ" outlined rounded dense hide-details>
               <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
             </v-text-field>
          </v-col>
          <v-col :style="MobileSize ? { display: 'flex', flexDirection: 'column', gap: '2.5vw' } : { display: 'flex', justifyContent: 'space-between', alignItems: 'center' }" :cols="MobileSize ? 12 : ''" :class="IpadSize ? 'pt-2 pb-1' : ''">
            <span
              v-if="IpadSize"
              style="
              font-size: 16px;
              line-height: 20px;
              align-items: center;
              color: #333333;
              font-weight: 600;">
              รายการทั้งหมด {{ showCountRequest }} รายการ
            </span>
            <span
              v-else
              style="
              font-size: 18px;
              line-height: 24px;
              align-items: center;
              color: #333333;
              font-weight: 600;" >
              <span v-if="tab === 0">รายชื่อผู้ใช้งานในระบบทั้งหมด {{ showCountRequest }} รายการ</span>
              <span v-if="tab === 1">รายชื่อผู้ใช้งานที่ใช้งานทั้งหมด {{ showCountRequest }} รายการ</span>
              <span v-if="tab === 2">รายชื่อผู้ใช้งานที่ยกเลิกทั้งหมด {{ showCountRequest }} รายการ</span>
            </span>
            <v-btn
              @click="OpenDialogAddUser"
              style="
              border-radius: 2vw;
              color: #fff;
              background-color: #27AB9C;">
              <v-icon>mdi-plus-circle</v-icon><br>
              <span>เพิ่มผู้ใช้งานบริษัท</span>
            </v-btn>
          </v-col>
          <v-col cols="12" class="pt-0">
            <v-data-table
              :headers="headers"
              :search="search"
              :items="tab === 0 ? listUser : tab === 1 ? listUserActive : listUserInactive"
              style="width:100%; text-align: center; white-space: nowrap;"
              height="100%"
              @pagination="countRequest"
              no-results-text="ไม่พบรายการบริษัทในระบบ"
              no-data-text="ไม่พบรายการบริษัทในระบบ"
              :items-per-page="10"
              :class="IpadSize ? 'elevation-1' : 'elevation-1 mt-4'"
              :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            >
              <template v-slot:[`item.indexOfUser`]="{ index }">
                  {{ index + 1 }}
              </template>
              <template v-slot:[`item.action`]="{ item }">
                <v-btn v-if="item.id !== null" text color="primary" @click="OpenDialogEditUser(item)">
                  <v-icon>mdi-square-edit-outline</v-icon><br>
                  <span>แก้ไข</span>
                </v-btn>
                <v-btn v-if="item.id === null" text color="primary" disabled>
                  <v-icon>mdi-square-edit-outline</v-icon><br>
                  <span>แก้ไข</span>
                </v-btn>
                <v-btn v-if="item.id !== null" text color="error" @click="DeleteUserShop(item.id)">
                  <v-icon>mdi-delete-outline</v-icon><br>
                  <span>ลบ</span>
                </v-btn>
                <v-btn v-if="item.id === null" text color="error" disabled>
                  <v-icon>mdi-delete-outline</v-icon><br>
                  <span>ลบ</span>
                </v-btn>
              </template>
              <template v-slot:[`item.status`]="{ item }">
                <span v-if="item.status === 'active'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">กำลังใช้งาน</v-chip>
                </span>
                <span v-else-if="item.status === 'inactive'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#f7c5ad" text-color="#f50">ยกเลิก</v-chip>
                </span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.email`]="{ item }">
                <span v-if="item.email !== null" color="#27AB9C" > {{ item.email }}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.first_name_th`]="{ item }">
                <span v-if="item.first_name_th !== null" color="#27AB9C" > {{ item.first_name_th + ' ' + item.last_name_th }}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.positions`]="{ item }">
                <span v-if="item.positions.length !== 0">
                  <span v-for="(role, index) in item.positions" :key="index">
                    {{ role.name }}
                    <span v-if="index < item.positions.length - 1">, </span>
                  </span>
                </span>
                <span v-else>-</span>
              </template>
            </v-data-table>
          </v-col>
          <!-- diglog add user -->
          <v-dialog content-class="elevation-0" v-model="modalAddUser" :width="MobileSize ? '100%' : '35%'">
            <v-card style="border-radius: 1.5vw;">
              <v-card-title class="backgroundHead">
                <v-row>
                  <v-col style="text-align: center;" class="pt-4">
                    <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>เพิ่มข้อมูลผู้ใช้งานบริษัท</b></span>
                  </v-col>
                  <v-btn fab small @click="cancel()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
                </v-row>
              </v-card-title>

              <v-card-text class="pt-4">
                <div>
                  <span>เพิ่มผู้ใช้งานจาก Email : </span>
                  <div style="display: flex; gap: .5vw; margin-top: .5vw;">
                    <v-text-field @keyup.enter="SearchUserCompany" style="border-radius: 8px;" class="input_text namedoc_input" v-model="email" placeholder="อีเมลผู้ใช้" outlined dense></v-text-field>
                    <v-btn v-if="email !== '' || null" color="primary" @click="SearchUserCompany()">ค้นหา</v-btn>
                    <v-btn v-if="email === ''" disabled color="primary" @click="SearchUserCompany()">ค้นหา</v-btn>
                  </div>
                </div>
                <div v-if="showForm">
                  <div>
                    <span>ชื่อ-นามสกุล : </span>
                    <v-text-field disabled style="border-radius: 8px;" class="input_text namedoc_input" v-model="name" placeholder="ชื่อ-นามสกุล" outlined dense></v-text-field>
                  </div>
                  <!-- <div>
                    <span>สถานะการใช้งาน : </span>
                    <v-select :items="listStatus" label="สถานะการใช้งานบริษัท" solo></v-select>
                  </div> -->
                  <div>
                    <span>สิทธิ์ผู้ใช้งาน : </span>
                    <v-select
                      v-model="selectedPositions"
                      :items="listPosition"
                      item-text="position_name"
                      item-value="position_id"
                      label="สิทธิ์ผู้ใช้งาน"
                      no-results-text="ไม่พบตำแหน่งในบริษัท"
                      no-data-text="ไม่พบตำแหน่งในบริษัท"
                      multiple
                      solo
                      chips
                      style="border-radius: 8px; max-height: 100px; overflow-y: auto;"
                      placeholder="เลือกสิทธิ์ผู้ใช้งาน"
                      :menu-props="{ maxHeight: '230px' }"
                    ></v-select>
                    <!-- <v-select :items="listPosition" label="สิทธิ์ผู้ใช้งานบริษัท" solo></v-select> -->
                  </div>
                </div>
              </v-card-text>

              <v-card-actions style="display: flex; justify-content: center; gap: 1vw; padding-bottom: 2vw;">
              <!-- <v-spacer></v-spacer> -->
                <v-btn
                  color="primary"
                  outlined
                  rounded
                  width="8vw"
                  height="40"
                  @click="cancel()"
                >
                  ยกเลิก
                </v-btn>
                <v-btn
                  color="primary"
                  width="8vw"
                  height="40"
                  rounded
                  :disabled="toggleConfirm"
                  @click="AddUserComapny()"
                >
                  ตกลง
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
          <v-dialog content-class="elevation-0" v-model="modalEditUser" :width="MobileSize ? '100%' : '35%'">
            <v-card style="border-radius: 1.5vw;">
              <v-card-title class="backgroundHead">
                <v-row>
                  <v-col style="text-align: center;" class="pt-4">
                    <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>แก้ไขข้อมูลผู้ใช้งานบริษัท</b></span>
                  </v-col>
                  <v-btn fab small @click="cancel()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
                </v-row>
              </v-card-title>

              <v-card-text class="pt-4">
                <div>
                  <span>อีเมล : </span>
                  <v-text-field disabled style="border-radius: 8px;" class="input_text namedoc_input" v-model="email" placeholder="อีเมลผู้ใช้" outlined dense>
                    <v-icon slot="append" color="#c6c6c6">mdi-square-edit-outline</v-icon>
                  </v-text-field>
                </div>
                <div>
                  <span>ชื่อ-นามสกุล : </span>
                  <v-text-field disabled style="border-radius: 8px;" class="input_text namedoc_input" v-model="name" placeholder="ชื่อ-นามสกุล" outlined dense>
                    <v-icon slot="append" color="#c6c6c6">mdi-square-edit-outline</v-icon>
                  </v-text-field>
                </div>
                <div>
                  <span>สิทธิ์ผู้ใช้งาน : </span>
                  <v-select
                    v-model="selectedRoleID"
                    :items="listPosition"
                    item-text="position_name"
                    item-value="position_id"
                    label="สิทธิ์ผู้ใช้งาน"
                    multiple
                    solo
                    chips
                    style="border-radius: 8px; max-height: 100px; overflow-y: auto;"
                    placeholder="เลือกสิทธิ์ผู้ใช้งาน"
                    :menu-props="{ maxHeight: '230px' }"
                  ></v-select>
                </div>
              </v-card-text>

              <v-card-actions style="display: flex; justify-content: center; gap: 1vw; padding-bottom: 2vw;">
                <v-btn
                  color="primary"
                  outlined
                  rounded
                  width="8vw"
                  height="40"
                  @click="cancel()"
                >
                  ยกเลิก
                </v-btn>
                <v-btn
                  color="primary"
                  width="8vw"
                  height="40"
                  rounded
                  @click="EditUserCompany()"
                >
                  ตกลง
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </v-col>
      </v-row>
    </v-card>
  </v-container>
</template>

<script>
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data () {
    return {
      tab: 0,
      status: '',
      selectedRoles: [],
      selectedRoleID: [],
      userID: null,
      modalEditUser: false,
      selectedPositions: [],
      listPosition: [],
      toggleConfirm: true,
      listUserEmail: [],
      email: '',
      name: '',
      showForm: false,
      modalAddUser: false,
      showCountRequest: 0,
      search: '',
      headers: [
        { text: 'ลำดับ', value: 'indexOfUser', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'อีเมล', value: 'email', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ-นามสกุล', value: 'first_name_th', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะผู้ใช้งาน', value: 'status', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สิทธิ์ผู้ใช้งาน', value: 'positions', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'action', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      listUser: [],
      listUserActive: [],
      listUserInactive: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ManageUserListCompanyMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'ManageUserListCompany')
        this.$router.push({ path: '/ManageUserListCompany' }).catch(() => {})
      }
    }
  },
  created () {
    var id = Number(this.$route.query.company_id)
    var companyName = this.$route.query.name
    localStorage.setItem('paramID', id)
    localStorage.setItem('paramName', companyName)
    if (localStorage.getItem('oneData') !== null) {
      this.listUserCompany()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    selectTab (val) {
      // console.log(this.listPositionInActive, 'inactive')
      this.search = ''
      this.tab = val
      // if (this.tab === 0 && this.listPosition.length !== 0) {
      //   this.disableTable = true
      // } else if (this.tab === 1 && this.listPositionActive.length !== 0) {
      //   this.disableTable = true
      // } else if (this.tab === 2 && this.listPositionInActive.length !== 0) {
      //   this.disableTable = true
      // } else {
      //   this.disableTable = false
      // }
    },
    backtoPage () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/manageUserCompany' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageUserCompanyMobile' }).catch(() => {})
      }
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async listUserCompany () {
      this.$store.commit('openLoader')
      var body = { company_id: Number(this.$route.query.company_id) }
      await this.$store.dispatch('actionsGetUserComapany', body)
      var response = await this.$store.state.ModuleAdminManage.stateGetUserComapany
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.listUser = response.data.users.filter(id => id.id !== null)
        this.listUserActive = response.data.users.filter(position => position.status === 'active')
        this.listUserInactive = response.data.users.filter(position => position.status === 'inactive')
      } else if (response.code === 400) {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'ไม่มีผู้ใช้งานบริษัท' })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    OpenDialogAddUser () {
      this.modalAddUser = true
      if (this.listUserEmail) {
        this.email = this.listUserEmail.email || ''
        this.name = this.listUserEmail.first_name_th + ' ' + this.listUserEmail.last_name_th || ''
      } else {
        this.email = ''
        this.name = ''
      }
      this.status = ''
      // this.toggleConfirm = false
    },
    OpenDialogEditUser (item) {
      this.email = item.email
      this.name = item.first_name_th + ' ' + item.last_name_th
      this.status = item.status
      this.selectedRoles = item.positions.map(role => role.name)
      this.selectedRoleID = item.positions.map(role => role.id)
      this.userID = item.id
      this.modalEditUser = true
      this.ManageListPosition()
      this.listUserCompany()
    },
    async SearchUserCompany () {
      this.$store.commit('openLoader')
      var companyId = Number(this.$route.query.company_id)
      var email = this.email
      var sendSearch = { email, company_id: companyId }
      await this.$store.dispatch('actionsSearchUserCompany', sendSearch)
      var response = await this.$store.state.ModuleAdminManage.stateSearchUserCompany
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.showForm = true
        this.toggleConfirm = false
        this.listUserEmail = response.data
        this.OpenDialogAddUser()
        this.ManageListPosition()
        // this.OpenDialogEditUser()
      } else if (response.message === "You don't enter email.") {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'ไม่ได้ใส่ email' })
      } else if (response.message === 'This User Have Company.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'User อยู่ในบริษัทแล้ว' })
      } else if (response.message === 'Not Found Email.') {
        this.$store.commit('closeLoader')
        this.showForm = false
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'ไม่พบ email' })
      } else if (response.message === 'You are not an admin.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'คุณไม่ใช่ Admin' })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    async ManageListPosition () {
      this.$store.commit('openLoader')
      var companyId = { company_id: Number(this.$route.query.company_id) }
      await this.$store.dispatch('actionsListPositionCompany', companyId)
      var response = await this.$store.state.ModuleAdminManage.stateListPositionCompany
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.listPosition = response.data.filter(item => item.status === 'active').map(item => ({
          position_id: item.id,
          position_name: item.name
        }))
      } else if (response.message === 'Not Found Position In Company.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'ไม่พบตำแหน่งในบริษัท'
        })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${response.message}`
        })
      }
    },
    cancel () {
      this.modalEditUser = false
      this.modalAddUser = false
      this.email = ''
      this.name = ''
      this.listUserEmail = null
      this.showForm = false
      this.toggleConfirm = true
    },
    // add user
    async AddUserComapny () {
      this.$store.commit('openLoader')
      var userID = this.listUserEmail.id
      var companyId = Number(this.$route.query.company_id)
      var data = { user_id: userID, company_id: companyId, positions: this.selectedPositions.length === 0 ? '' : this.selectedPositions }
      await this.$store.dispatch('actionsAddUserCompany', data)
      var response = await this.$store.state.ModuleAdminManage.stateAddUserCompany
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        await this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'success', text: 'เพิ่มผู้ใช้บริษัทสำเร็จ' })
        this.modalAddUser = false
        this.cancel()
        this.listUserCompany()
      } else if (response.message === "'user_id' parameter is required.") {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'ไม่ได้ใส่ user id' })
      } else if (response.message === "'company_id' parameter is required.") {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'ไม่ได้ใส่ company id' })
      } else if (response.message === 'Some key or value are empty. [positions]') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'ไม่ได้ใส่สิทธิ์การใช้งาน' })
      } else if (response.message === 'Position is already assigned to the user') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'มี position ใน user แล้ว' })
      } else if (response.message === "This user didn't have permissions'") {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'คุุณไม่ใช่ Admin' })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    async EditUserCompany () {
      this.$store.commit('openLoader')
      var companyId = Number(this.$route.query.company_id)
      var data = { user_id: this.userID, company_id: companyId, positions: this.selectedRoleID.length === 0 ? '' : this.selectedRoleID }
      await this.$store.dispatch('actionsEditUserCompany', data)
      var response = await this.$store.state.ModuleAdminManage.stateEditUserCompany
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        await this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'success', text: 'แก้ไขข้อมูลผู้ใช้งานบริษัทสำเร็จ' })
        this.modalEditUser = false
        this.listUserCompany()
      } else if (response.message === "You Don't Enter User Shop Id.") {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'ไม่ได้ใส่ user_shop_id' })
      } else if (response.message === 'Some key or value are empty. [positions]') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'ไม่ได้ใส่ position' })
      } else if (response.message === 'You are not an admin.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'คุุณไม่ใช่ Admin' })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    async DeleteUserShop (id) {
      var companyId = Number(this.$route.query.company_id)
      var datas = { user_id: id, company_id: companyId }
      this.$swal.fire({
        title: 'คุณต้องการลบผู้ใช้งานนี้หรือไม่ ?',
        icon: 'warning',
        text: 'ผู้ใช้งานจะถูกลบออกจากบริษัทนี้',
        showCancelButton: true,
        confirmButtonText: 'ตกลง',
        cancelButtonText: 'ยกเลิก',
        confirmButtonColor: '#38b2a4',
        reverseButtons: true
      }).then(async (result) => {
        if (result.isConfirmed) {
          await this.$store.dispatch('actionsDeleteUserCompany', datas)
          var data = await this.$store.state.ModuleAdminManage.stateDeleteUserCompany
          if (data.result === 'SUCCESS') {
            this.$swal.fire({
              title: 'ลบผู้ใช้งานสำเร็จ',
              text: 'ผู้ใช้งานถูกลบออกจากบริษัทเรียบร้อย',
              icon: 'success',
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true
            }).then(() => {
              this.listUserCompany()
            })
          } else if (data.message === "User In Company Can't Delete.") {
            this.$swal.fire({
              title: 'ลบผู้ใช้งานไม่สำเร็จ',
              text: 'ผู้ใช้เป็นเจ้าของนิติบุคคลไม่สามารถลบได้',
              icon: 'error',
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true
            })
          } else {
            this.$swal.fire({
              title: 'ลบผู้ใช้งานไม่สำเร็จ',
              text: 'ผู้ใช้เป็นเจ้าของนิติบุคคลไม่สามารถลบได้',
              icon: 'error',
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true
            })
          }
        }
      })
    }
  }

}
</script>

<style scoped>
  .theme--light.v-data-table > .v-data-table__wrapper > table > thead > tr:last-child > th {
    white-space: nowrap !important;
    text-align: center !important;
  }
  .v-data-table > .v-data-table__wrapper > table > tbody > tr > td {
    text-align: center !important;
  }
  .swal2-popup {
    border-radius: 2vw !important;
  }
  .swal2-cancel,
  .swal2-confirm {
    border-radius: 2vw !important;
  }
</style>

<style lang="scss" scoped>
::v-deep table {
    tbody {
    tr {
        td:nth-child(6) {
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        z-index: 10;
        background: white;
        }
    }
    }
    thead {
    tr {
        th:nth-child(1) {
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        z-index: 10;
        background: white;
        }
    }
    }
    thead {
    tr {
        th:nth-child(6) {
        z-index: 11;
        background: white;
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        }
    }
    }
}
.v-dialog > .v-card > .v-card__text {
      padding: 0px 29px 0px;
    }
</style>
