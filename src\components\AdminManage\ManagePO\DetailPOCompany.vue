<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-dialog
      v-model="ModalPaymentQU"
      :style="MobileSize ? 'z-index: 16000004' : ''"
      persistent
      width="464"
    >
      <v-card style="background: #ffffff; border-radius: 4px">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title
                ><span
                  style="color: #27ab9c"
                  :class="MobileSize ? 'title-mobile' : ''"
                  ><b>วิธีการชำระเงิน</b></span
                ></v-toolbar-title
              >
            </v-col>
          </v-row>
          <v-btn fab small @click="ModalPaymentQU = !ModalPaymentQU" icon
            ><v-icon color="#27AB9C">mdi-close</v-icon></v-btn
          >
        </v-toolbar>
        <v-container>
          <v-card-text style="text-align: center">
            <v-row dense justify="center">
              <v-col cols="12">
                <p
                  style="
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 22px;
                    color: #333333;
                  "
                >
                  เลือกวิธีการชำระเงิน
                </p>
              </v-col>
              <v-col cols="12">
                <v-row dense justify="center">
                  <v-col
                    cols="12"
                    md="12"
                    align="center"
                    v-if="DetailQU.payment_method !== 'เครดิตเทอม'"
                  >
                    <v-card
                      outlined
                      width="124"
                      height="148"
                      style="border: 1px solid #d8efe4; border-radius: 10px"
                      @click="openSelectTaxInvoice('ชำระเงินทันที')"
                    >
                      <v-card-text class="pt-8" style="cursor: pointer">
                        <v-img
                          src="@/assets/ImageINET-Marketplace/Shop/money.png"
                          contain
                          max-height="40"
                          max-width="40"
                        ></v-img>
                        <p
                          style="
                            font-weight: 500;
                            font-size: 10px;
                            line-height: 14px;
                            color: #333333;
                          "
                          class="pt-4"
                        >
                          ชำระเงินทันที
                        </p>
                      </v-card-text>
                    </v-card>
                  </v-col>
                  <v-col cols="12" md="12" align="center" v-else>
                    <v-card
                      outlined
                      width="124"
                      height="148"
                      style="border: 1px solid #d8efe4; border-radius: 10px"
                      @click="openSelectTaxInvoice('เครดิตเทอม')"
                    >
                      <v-card-text class="pt-8">
                        <v-img
                          src="@/assets/ImageINET-Marketplace/Shop/term-loan.png"
                          contain
                          max-height="40"
                          max-width="40"
                        ></v-img>
                        <p
                          style="
                            font-weight: 500;
                            font-size: 10px;
                            line-height: 14px;
                            color: #333333;
                          "
                          class="pt-4"
                        >
                          ใช้เครดิตเทอม
                        </p>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="modalSelectTaxInvoice"
      persistent
      :style="MobileSize ? 'z-index: 16000004' : ''"
      width="464"
    >
      <v-card style="background: #ffffff; border-radius: 4px">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title
                ><span
                  style="color: #27ab9c"
                  :class="MobileSize ? 'title-mobile' : ''"
                  ><b>ขอใบกำกับภาษี</b></span
                ></v-toolbar-title
              >
            </v-col>
          </v-row>
          <v-btn
            fab
            small
            @click="modalSelectTaxInvoice = !modalSelectTaxInvoice"
            icon
            ><v-icon color="#27AB9C">mdi-close</v-icon></v-btn
          >
        </v-toolbar>
        <v-container>
          <v-card-text style="text-align: center">
            <v-row dense justify="center">
              <v-col cols="12">
                <p
                  style="
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 22px;
                    color: #333333;
                  "
                >
                  เลือกการขอใบกำกับภาษี
                </p>
              </v-col>
              <v-col cols="12">
                <v-row dense justify="center">
                  <v-col cols="12" md="12">
                    <v-row dense justify="center" class="ml-10 pt-0">
                      <v-col cols="12" align="center">
                        <v-radio-group v-model="taxRoles" row>
                          <v-radio
                            color="#27AB9C"
                            label="นิติบุคคล"
                            value="Business"
                          ></v-radio>
                          <v-radio
                            color="#27AB9C"
                            label="ไม่รับใบกำกับภาษี"
                            value="no"
                          ></v-radio>
                        </v-radio-group>
                      </v-col>
                      <v-col
                        v-if="taxAddress === '' && taxRoles !== 'no'"
                        cols="12"
                        align="left"
                        class="pt-2 pl-0"
                      >
                        <v-btn
                          outlined
                          icon
                          small
                          color="#A1A1A1"
                          @click="openTaxAddress()"
                          ><v-icon color="#A1A1A1" small
                            >mdi-plus</v-icon
                          ></v-btn
                        >
                        <span class="pl-2"
                          ><b>เพิ่มที่อยู่ในการจัดส่งใบกำกับภาษี</b></span
                        >
                      </v-col>
                      <v-col
                        v-else-if="taxAddress !== '' && taxRoles !== 'no'"
                        cols="12"
                        align="left"
                        class="pt-0 pl-0"
                      >
                        <p style="font-weight: bold">
                          ที่อยู่ในการจัดส่งใบกำกับภาษี :
                        </p>
                        {{ companyName }} {{ companyTaxID }} {{ taxAddress }}
                      </v-col>
                    </v-row>
                    <v-row justify="end">
                      <v-btn
                        @click="GotoPayment()"
                        color="#27AB9C"
                        dark
                        :disabled="
                          taxAddress === '' && taxRoles !== 'no' ? true : false
                        "
                        >ชำระเงิน</v-btn
                      >
                    </v-row>
                    <!-- <v-card outlined width="124" height="148" style="border: 1px solid #D8EFE4; border-radius: 10px;" @click="GotoPayment()">
                      <v-card-text class="pt-8" style="cursor: pointer;">
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/money.png" contain max-height="40" max-width="40"></v-img>
                        <p style="font-weight: 500; font-size: 10px; line-height: 14px; color: #333333;" class="pt-4">ชำระเงินทันที</p>
                      </v-card-text>
                    </v-card> -->
                  </v-col>
                  <!-- <v-col cols="12" md="6"  align="center">
                    <v-card outlined width="124" height="148" style="border: 1px solid #D8EFE4; border-radius: 10px;">
                      <v-card-text class="pt-8">
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/term-loan.png" contain max-height="40" max-width="40"></v-img>
                        <p style="font-weight: 500; font-size: 10px; line-height: 14px; color: #333333;" class="pt-4">ใช้เครดิตเทอม</p>
                      </v-card-text>
                    </v-card>
                  </v-col> -->
                </v-row>
              </v-col>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-card
      width="100%"
      height="100%"
      elevation="0"
      :class="[MobileSize ? 'mb-12 mt-4' : 'mb-4']"
    >
      <v-card-title>
        <v-row dense>
          <v-col cols="12" md="6">
            <span
              style="
                font-weight: bold;
                font-size: 18px;
                line-height: 32px;
                color: #333333;
              "
              ><v-icon
                color="#27AB9C"
                class="mr-2"
                @click="backtoListPoCompany()"
                >mdi-chevron-left</v-icon
              >ใบเสนอราคา</span
            >
          </v-col>
          <!-- <v-col
            cols="12"
            md="6"
            align="end"
            v-if="DetailQU.status === 'waiting'"
          >
            <v-btn
              outlined
              color="#F5222D"
              class="mr-4"
              @click="changeStatus('inactive')"
              ><v-icon color="#F5222D" class="pr-2">mdi-delete-outline</v-icon>
              ยกเลิก</v-btn
            >
            <v-btn color="#27AB9C" dark @click="changeStatus('active')"
              ><v-icon class="pr-2">mdi-check</v-icon> ยอมรับ</v-btn
            >
          </v-col> -->
          <!-- <v-col
            cols="12"
            md="6"
            align="end"
            v-else-if="DetailQU.status === 'create'"
          >
            <v-btn
              v-if="this.status_approve === 'Approve'"
              outlined
              color="#F5222D"
              class="mr-4"
              @click="cancelApprove()"
              ><v-icon color="#F5222D" class="pr-2">mdi-delete-outline</v-icon>
              ยกเลิก</v-btn
            >
          </v-col> -->
          <v-col
            cols="12"
            md="6"
            align="end"
            v-if="(((DetailQU.status === 'approve' && DetailQU.use_PR) || DetailQU.status === 'waiting' || DetailQU.status === 'check_doc') && !editQT)"
          >
            <!-- <v-btn color="#27AB9C" class="mr-2 px-6" outlined dark @click="changeStatus('edit')" v-if="DetailQU.status_use_discount === 'yes'"><v-icon class="pr-2">mdi-ticket-percent-outline</v-icon> ใช้สิทธิพิเศษ</v-btn> -->
            <!-- <v-btn color="#27AB9C" class="mr-0 px-6" dark @click="ShowModalSelect()"><v-icon class="pr-2">mdi-cart</v-icon> สั่งซื้อ</v-btn> -->
            <!-- <v-btn  v-if="this.status_approve_detail === 'Approve'"  outlined color="#F5222D" class="mr-4" @click="cancelApprove()"><v-icon color="#F5222D" class="pr-2">mdi-delete-outline</v-icon> ยกเลิก</v-btn> -->
            <!-- <v-btn
            v-if="DetailQU.status === 'waiting_approve'"
              @click="editQTCompany()"
              text
              class="px-2 mr-2"
              color="#27AB9C"
            >
              <v-icon left>
                mdi-pencil-outline
              </v-icon>
              แก้ไข
            </v-btn> -->
            <v-btn
            v-if="DetailQU.status === 'check_doc'"
              @click="editQTCompany()"
              text
              class="px-2 mr-2"
              color="#27AB9C"
            >
              <v-icon left>
                mdi-pencil-outline
              </v-icon>
              แก้ไข
            </v-btn>
            <v-btn
              outlined
              color="#27AB9C"
              class="mr-2"
              width="135"
              height="40"
              rounded
              @click="openAwaitReject()"
              >
              <!-- <v-icon color="#F5222D" class="pr-2">mdi-delete-outline</v-icon> -->
              ยกเลิกคำขอซื้อ
            </v-btn>
            <v-btn
              color="#27AB9C"
              class="mr-0"
              width="125"
              height="40"
              rounded
              style="color: #ffffff"
              @click="openModalAwaitConfirm()"
              :disabled="disableButton || ((DetailQU.use_PR && costcenter === '' && DetailQU.status === 'approve') || (!DetailQU.use_PR && (DetailQU.status !== 'waiting' && DetailQU.status !== 'check_doc')))"
            >
              ยืนยัน</v-btn
            >
          </v-col>
          <v-col
            cols="12"
            md="6"
            align="end"
            v-if="(!DetailQU.use_PR && (DetailQU.status === 'approve' || DetailQU.status === 'success')) || (DetailQU.use_PR && DetailQU.status === 'success')"
          >
            <v-btn
              color="#27AB9C"
              class="mr-0"
              width="125"
              height="40"
              rounded
              style="color: #ffffff"
              @click="GoToOrderCompany()"
            >
              ดูรายการสั่งซื้อ</v-btn
            >
          </v-col>
          <v-col
            cols="12"
            md="6"
            align="end"
            v-if="editQT"
          >
            <!-- <v-btn color="#27AB9C" class="mr-2 px-6" outlined dark @click="changeStatus('edit')" v-if="DetailQU.status_use_discount === 'yes'"><v-icon class="pr-2">mdi-ticket-percent-outline</v-icon> ใช้สิทธิพิเศษ</v-btn> -->
            <!-- <v-btn color="#27AB9C" class="mr-0 px-6" dark @click="ShowModalSelect()"><v-icon class="pr-2">mdi-cart</v-icon> สั่งซื้อ</v-btn> -->
            <!-- <v-btn  v-if="this.status_approve_detail === 'Approve'"  outlined color="#F5222D" class="mr-4" @click="cancelApprove()"><v-icon color="#F5222D" class="pr-2">mdi-delete-outline</v-icon> ยกเลิก</v-btn> -->
            <!-- <v-btn
              @click="reviewQTCompany()"
              text
              class="px-2 mr-2"
              color="#27AB9C"
            >
              <v-icon left>
                mdi-eye
              </v-icon>
              ดูตัวอย่าง
            </v-btn> -->
            <v-btn
              outlined
              color="#27AB9C"
              class="mr-2"
              width="135"
              height="40"
              rounded
              @click="RejectQT()"
              >
              <!-- <v-icon color="#F5222D" class="pr-2">mdi-delete-outline</v-icon> -->
              ยกเลิก
            </v-btn>
            <v-btn
              color="#27AB9C"
              class="mr-0"
              width="125"
              height="40"
              rounded
              style="color: #ffffff"
              @click="editOrderQT()"

            >
              ยืนยัน</v-btn
            >
          </v-col>
        </v-row>
      </v-card-title>
      <v-card-text>
        <v-row class="pt-3 px-2" dense style="background: #F9FAFD; border-radius: 8px 8px 0px 0px;">
          <v-col cols="12" md="4" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">วันที่สร้าง : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{
                new Date(DetailQU.created_at).toLocaleDateString('th-TH', {
                  year: 'numeric',
                  month: 'numeric',
                  day: 'numeric'
                })
              }}</b></span>
          </v-col>
          <v-col cols="12" md="4" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">หมายเลขใบเสนอราคา : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ DetailQU.QT_number }}</b></span>
          </v-col>
          <v-col cols="12" md="4" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">สถานะ : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><v-chip :text-color="textcolorChip(DetailQU.status)" :color="colorChip(DetailQU.status)">{{
                returnStringStatus(DetailQU.status)
              }}</v-chip></span>
              <!-- <b>{{
                DetailQU.status_approve_detail
              }}</b></span> -->
          </v-col>
        </v-row>
        <v-row class="px-2" dense style="background: #F9FAFD; border-radius: 0px 0px 8px 8px;">
          <v-col cols="12" md="4" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">วันที่อัปเดตล่าสุด : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{
                new Date(DetailQU.updated_at).toLocaleDateString('th-TH', {
                  year: 'numeric',
                  month: 'numeric',
                  day: 'numeric'
                })
              }}</b></span>
          </v-col>
          <v-col cols="12" md="4"  :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">ส่งคำขอโดย : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ DetailQU.buyer_name }}</b></span>
          </v-col>
          <v-col cols="12" md="4" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">หมายเหตุ : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{
               DetailQU.remark === null ? '-' : DetailQU.remark
              }}</b></span>
          </v-col>
          <v-col cols="12" md="4" v-if="DetailQU.qu_status === 'reject'" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">เหตุผลในการปฏิเสธ : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ DetailQU.reason }}</b></span>
          </v-col>
          <v-col v-if="DetailQU.status === 'approve' && DetailQU.use_PR" cols="12" md="4" class="pt-2">
            <div style="color: #333333; font-size: 16px;">
              Cost Center<span style="color: red;"> *</span>
            </div>
            <v-autocomplete
              v-model="costcenter"
              :items="CostCenterItem"
              hide-details
              :item-value="item => item"
              :item-text="item => item.cost_center_code ? `${item.department_name} (${item.cost_center_code})` : item.department_name"
              :item-disabled="item => !item.cost_center_code"
              label="เลือก Cost Center"
              solo
              dense
              no-data-text="ไม่มีข้อมูล Cost Center">
            </v-autocomplete>
          </v-col>
          <v-col cols="12">
            <div>
              <v-col class="pa-0">
                <v-col v-if="DetailQU.old_installment_method.length !== 0" cols="12" md="4" sm="4" class="px-0">
                  <span style="color: #333333; font-size: 16px;">งวดชำระเงิน : </span>
                  <span style="color: #333333; font-size: 16px;"><b>{{ DetailQU.old_installment_method.length }} งวด</b></span>
                  <!-- <v-select v-model="selectinstallment" hide-details :items="installmentOptions" item-text="formattedMonth" item-value="month" style="border-radius: 8px;" append-icon="mdi-chevron-down" placeholder="กรุณาเลือกงวดชำระเงิน" outlined dense></v-select> -->
                </v-col>
                <v-col v-if="DetailQU.old_installment_method.length !== 0" cols="12" class="pa-0">
                  <v-card style="border-radius: 8px;" elevation="0">
                    <v-card-text>
                      <v-row>
                        <v-col class="text-start pb-2" cols="12">
                          <v-row dense>
                            <v-img class="mr-2" src="@/assets/Layer_1.png" style="max-height: 25px; max-width: 25px;"></v-img>
                            <span style="color: #333333; font-size: 16px;">ยอดเงินที่ต้องการชำระแต่ละงวด</span>
                          </v-row>
                        </v-col>
                        <v-col v-for="(amount, index) in DetailQU.old_installment_method" :key="index" class="text-start py-2 px-1" :cols="MobileSize ? '6' : '3'">
                          <span style="color: #333333; font-size: 16px;">เดือนที่ {{ index + 1 }}</span><br>
                          <span style="color: #333333; font-size: 16px;"><b>{{ amount.price }} บาท</b></span>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
                <v-col v-if="DetailQU.credit_day !== ''" cols="12" md="4" sm="4" class="px-0">
                  <span style="color: #333333; font-size: 16px;">Credit term : </span>
                  <span style="color: #333333; font-size: 16px;"><b>{{ DetailQU.credit_day }} วัน</b></span>
                  <!-- <v-autocomplete v-model="selectedCreditTerm" :items="creditTermOptions" item-text="label" item-value="value" style="border-radius: 8px;" hide-details append-icon="mdi-chevron-down" placeholder="เลือก Credit term" outlined dense></v-autocomplete> -->
                </v-col>
              </v-col>
            </div>
            <v-col class="py-6" v-if="!discountBahtB2B && discountPercentB2B || discountBahtB2B && !discountPercentB2B">
              <v-row style="align-items: center;">
                <span class="pr-2" style="color: #333333; font-size: 16px;">ขอใช้ส่วนลด : </span>
                <span v-if="discountBahtB2B" style="color: #333333; font-size: 16px;"><b>ส่วนลดรูปแบบระบุยอด</b></span>
                <span v-if="discountPercentB2B" style="color: #333333; font-size: 16px;"><b>ส่วนลดรูปแบบเปอร์เซ็นต์</b></span>
              </v-row>
            </v-col>
            <v-col v-if="!discountBahtB2B && discountPercentB2B || discountBahtB2B && !discountPercentB2B" class="px-0">
              <v-row>
                <v-col v-if="discountBahtB2B" cols="12">
                  <span style="color: #333333; font-size: 16px;">ส่วนลด : </span>
                  <span style="color: #333333; font-size: 16px;"><b>{{ DetailQU.total_b2b_discount }} บาท</b></span>
                </v-col>
                <v-col v-if="discountPercentB2B" cols="4">
                  <span style="color: #333333; font-size: 16px;">ส่วนลด : </span>
                  <span style="color: #333333; font-size: 16px;"><b>{{ DetailQU.percent_b2b_discount }} %</b></span>
                </v-col>
                <v-col v-if="discountPercentB2B" cols="4">
                  <span style="color: #333333; font-size: 16px;">ยอดส่วนลด : </span>
                  <span style="color: #333333; font-size: 16px;"><b>{{ DetailQU.total_b2b_discount }} บาท</b></span>
                </v-col>
              </v-row>
            </v-col>
          </v-col>
        </v-row>
        <v-row dense>
          <!-- ใส่ iframe -->
          <v-card
            v-if="!editQT"
            width="100%"
            height="100%"
            outlined
            style="background: #c4c4c4; border-radius: 8px"
            class="mt-4"
          >
            <v-card-text :class="MobileSize ? 'pa-0' : ''">
              <!-- {{ DetailQU.pdf_path_qt }} -->
              <!-- <vuePdf :src="DetailQU.pdf_path_qt"></vuePdf> -->
              <!-- <vue-pdf></vue-pdf> -->
              <!-- {{ currentPage }} / {{ pageCount }} -->
              <!-- <span v-if="DetailQU.QT_path !== '-'">
                <vue-pdf
                  v-for="i in numPages"
                  :key="i"
                  :page="i"
                   @num-pages="pageCount = $event"
                  @page-loaded="currentPage = $event"
                  width="100%"
                  :src="pdfPath"
                ></vue-pdf>
                <vue-pdf-embed :source="DetailQU.QT_path" />
              </span> -->
              <iframe v-if="DetailQU.QT_path !== '-'"  :src="DetailQU.QT_path" width="100%" :height="MobileSize ? '500' : IpadSize ? '600' : '1200'"></iframe>
              <!-- <iframe src="https://digitalflow-dev.one.th/api/public/v1/download/0107544000094/64afbdd5359dbf0012c2253b?is_doc_id=true" width="100%" :height="MobileSize ? '500' : IpadSize ? '600' : '1200'"></iframe> -->
            </v-card-text>
          </v-card>
          <QTCheckoutV2 v-else class="mt-4" ref="QTCheckoutV2" />
        </v-row>
      </v-card-text>
    </v-card>
    <!-- Await Confirm QT -->
    <v-dialog v-model="dialogAwaitConfirmQT" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeModalAwaitConfirm()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ยืนยันคำขอซื้อ</b></p>
            <!-- <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูลใบเสนอราคา</span><br/> -->
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeModalAwaitConfirm()">ยกเลิก</v-btn>
              <v-btn v-if="DetailQU.status === 'approve'" width="156" height="38" class="white--text" rounded color="#27AB9C" @click="confirmApprove()">ตกลง</v-btn>
              <v-btn v-else width="156" height="38" class="white--text" rounded color="#27AB9C" @click="confirmQT('approve')">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Confirm OT -->
    <v-dialog v-model="dialogSuccessConfirmQT" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeModalSuccess()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ยืนยันคำขอซื้อเสร็จสิ้น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการยืนยันคำขอซื้อเรียบร้อย</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeModalSuccess()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Await Reject QT -->
    <v-dialog v-model="dialogAwaitRejectQT" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogAwaitRejectQT = !dialogAwaitRejectQT"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ยกเลิกคำขอซื้อ</b></p>
            <!-- <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูลใบเสนอราคา</span><br/> -->
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogAwaitRejectQT = !dialogAwaitRejectQT">ยกเลิก</v-btn>
              <v-btn v-if="DetailQU.status === 'approve'" width="156" height="38" class="white--text" rounded color="#27AB9C" @click="cancelApprove()">ตกลง</v-btn>
              <v-btn v-else width="156" height="38" class="white--text" rounded color="#27AB9C" @click="confirmQT('reject')">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Reject OT -->
    <v-dialog v-model="dialogSuccessRejectQT" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeRejectSuccess()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ยกเลิกคำขอซื้อเสร็จสิ้น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการยกเลิกคำขอซื้อเรียบร้อย</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeRejectSuccess()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- dialog เลือกตำแหน่งก่อนชำระเงิน -->
    <v-dialog
      v-model="modalSetPositionCompany"
      width="732"
      :style="MobileSize ? 'z-index: 16000004' : ''"
      persistent
    >
      <v-card align="center" class="rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size: 20px">
            <font color="#27AB9C">ข้อมูลตำแหน่งของบริษัท</font>
          </span>
          <v-btn
            icon
            dark
            @click="modalSetPositionCompany = !modalSetPositionCompany"
          >
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-title
          class="mt-4 my-3"
          style="font-size: 14px; color: #333333; font-weight: 700"
          v-if="MobileSize"
          >รายชื่อตำแหน่งของบริษัทของฉัน (
          {{ list_position.length }} รายชื่อ)</v-card-title
        >
        <v-card-title
          class="mt-9 my-3"
          style="font-size: 20px; color: #333333; font-weight: 700"
          v-else
          >รายชื่อตำแหน่งของบริษัทของฉัน (
          {{ list_position.length }} รายชื่อ)</v-card-title
        >
        <v-card-text>
          <v-row dense no-gutters>
            <v-col
              cols="12"
              md="12"
              sm="12"
              xs="12"
              v-for="(item, index) in list_position"
              :key="index"
              class="mb-4"
            >
              <v-card
                width="100%"
                height="100%"
                outlined
                style="
                  background: #ffffff;
                  border: 1px solid #ebebeb;
                  border-radius: 8px;
                "
              >
                <v-card-text>
                  <v-row dense no-gutters>
                    <v-col cols="3" md="2" sm="2" class="pr-0">
                      <v-avatar
                        size="60"
                        style="
                          background: #f2f2f2;
                          border: 1px solid #e6e6e6;
                          border-radius: 8px;
                        "
                        rounded
                      >
                        <v-img
                          src="@/assets/ImageINET-Marketplace/Shop/office.png"
                          contain
                          width="36"
                          height="36"
                        >
                        </v-img>
                      </v-avatar>
                    </v-col>
                    <v-col
                      cols="9"
                      md="7"
                      sm="7"
                      :class="MobileSize ? 'pt-4 pl-4' : 'pt-4 pl-0'"
                    >
                      <div
                        style="
                          font-weight: 700;
                          font-size: 18px;
                          line-height: 26px;
                          color: #333333;
                          display: flex;
                          align-items: center;
                        "
                      >
                        {{ item.role_name }}
                      </div>
                    </v-col>
                    <v-col
                      cols="12"
                      md="2"
                      sm="2"
                      class="pt-3 pl-6"
                      :align="MobileSize ? 'end' : ''"
                    >
                      <v-btn
                        text
                        color="#27AB9C"
                        @click="setPositionCompany(item)"
                        >เลือกตำแหน่ง<v-icon color="#27AB9C">
                          mdi-chevron-right</v-icon
                        >
                      </v-btn>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <ModalTaxInvoice
      ref="ModalTaxAddress"
      :taxType="taxRoles"
      :frompage="QUPage"
      :ShippingType="QUPage"
    />
  </v-container>
</template>

<script>
// import VuePdf from 'vue-pdf'
import { Encode, Decode } from '@/services'
// import VuePdfEmbed from 'vue-pdf-embed/dist/vue2-pdf-embed'
export default {
  components: {
    // VuePdf,
    // VuePdfEmbed,
    ModalTaxInvoice: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalAddress/TaxInvoiceAddress'),
    QTCheckoutV2: () => import(/* webpackPrefetch: true */ '@/components/Quotation/QTCheckoutV2')
  },
  data () {
    return {
      discountBahtB2B: false,
      discountPercentB2B: false,
      editQT: false,
      CostCenterItem: [],
      costcenter: '',
      dialogAwaitConfirmQT: false,
      dialogSuccessConfirmQT: false,
      dialogAwaitRejectQT: false,
      dialogSuccessRejectQT: false,
      disableButton: false,
      pdfPath: '',
      numPages: undefined,
      currentPage: 0,
      pageCount: 0,
      QUID: '',
      ID: '',
      ShopID: '',
      DetailQU: [],
      ModalPaymentQU: false,
      modalSelectTaxInvoice: false,
      modalSetPositionCompany: false,
      dataToEdit: [],
      dataRole: '',
      taxRoles: 'no',
      taxAddress: '',
      companyName: '',
      companyTaxID: '',
      QUPage: 'QUPage',
      selectPayment: '',
      list_position: [],
      list_company: [],
      companyData: [],
      companyId: '',
      comPerID: '',
      orderNumber: '',
      selectedCompany: '',
      com_perm_id: '',
      detailItemQu: '',
      status_approve_detail: ''
    }
  },
  created () {
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$emit('checkPathCompany')
    // this.$EventBus.$on('checkAddressTaxinvoice', this.checkAddressTaxinvoice)
    if (
      localStorage.getItem('oneData') !== null &&
      (localStorage.getItem('SetRowCompany') !== null ||
        localStorage.getItem('CompanyData') !== null)
    ) {
      this.companyData = JSON.parse(
        Decode.decode(localStorage.getItem('SetRowCompany'))
      )
      this.companyId = this.companyData.company.company_id
      this.comPerID = this.companyData.position.com_perm_id
      this.orderNumber = this.$route.query.ordernumber
      this.QUID = this.$route.query.QU_ID
      this.ID = this.$route.query.id
      // this.ShopID = this.$route.query.shopID
      if (localStorage.getItem('detailItemQU') !== null) {
        this.detailItemQu = JSON.parse(
          Decode.decode(localStorage.getItem('detailItemQU'))
        )
      }
      this.GetDetailQU()
      this.AuthorityUser()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  watch: {
    MobileSize (val) {
      var itemsQU = JSON.parse(
        Decode.decode(localStorage.getItem('detailItemQU'))
      )
      if (val === false) {
        this.$router
          .push({
            path: `/QUCompanyDetail?ordernumber=${itemsQU.qu_number}`
          })
          .catch(() => {})
      } else {
        this.$router
          .push({
            path: `/QUCompanyDetailMobile?ordernumber=${itemsQU.qu_number}`
          })
          .catch(() => {})
      }
    },
    taxRoles (val) {
      if (val === 'Business') {
        this.taxAddress = ''
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    this.$EventBus.$on('successEditQT', this.successEditQT)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('successEditQT')
    })
    window.scrollTo(0, 0)
    // this.pdfPath.promise.then(pdf => {
    //   console.log(pdf)
    //   this.numPages = pdf.numPages
    // })
    // console.log('numPages', this.numPages)
  },
  methods: {
    // ConfirmQT () {
    //   window.alert('ยืนยันแก้ไขใบเสนอราคา')
    // },
    GoToOrderCompany () {
      this.$router.push({
        path: this.MobileSize ? '/orderCompanyMobile' : '/orderCompany',
        query: { searchOrder: this.DetailQU.order_number }
      })
    },
    RejectQT () {
      this.editQT = false
    },
    reviewQTCompany () {
      window.alert('ดูตัวอย่างใบเสนอราคา')
    },
    editQTCompany () {
      this.editQT = true
    },
    successEditQT () {
      this.editQT = false
      this.GetDetailQU()
    },
    async getCostCenter () {
      this.$store.commit('openLoader')
      var data = {
        company_id: this.companyId
      }
      await this.$store.dispatch('actionsListCostCenter', data)
      var response = await this.$store.state.ModuleAdminManage.stateListCostCenter
      if (response.result === 'SUCCESS') {
        this.CostCenterItem = response.data
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: response.message })
      }
    },
    returnStringStatus (quStatus) {
      if (quStatus === 'waiting') {
        return 'รออนุมัติ'
      } else if (quStatus === 'approve') {
        return 'อนุมัติแล้ว'
      } else if (quStatus === 'reject') {
        return 'ไม่อนุมัติ'
      } else if (quStatus === 'success') {
        return 'สร้างรายการสั่งซื้อ'
      } else if (quStatus === 'waiting_approve') {
        return 'รออนุมัติฝั่งผู้ซื้อ'
      } else if (quStatus === 'waiting_shop_approve') {
        return 'รอผู้ขายอนุมัติ'
      } else if (quStatus === 'check_doc') {
        return 'รอตรวจสอบเอกสาร'
      } else if (quStatus === 'waiting_dwf') {
        return 'รอตรวจสอบเอกสาร'
      } else {
        return 'ยกเลิกขอซื้อ'
      }
    },
    getErrorMsg (msg) {
      if (msg === 'This user is unauthorized.') {
        return 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง'
      } else if (msg === 'The user was not found in the company.') {
        return 'คุณไม่ได้อยู่ในบริษัทใดเลย'
      } else if (msg === 'Company not found.') {
        return 'ไม่พบข้อมูลบริษัทในระบบ'
      } else if (msg === 'You are not in this Company.') {
        return 'คุณไม่ได้อยู่ในบริษัทนี้'
      } else if (
        msg ===
        'Data missing. Please check your [" company_id "] and try again.'
      ) {
        return 'ข้อมูลขาดหาย โปรดเช็ค [ รหัสบริษัท ] แล้วลองใหม่อีกครั้ง'
      } else if (msg === 'Order not found.') {
        return 'ไม่พบข้อมูลออเดอร์'
      } else if (
        msg ===
        'Data missing. Please check your [" order_number "] and try again.'
      ) {
        return 'ข้อมูลขาดหาย โปรดเช็ค [ เลขออเดอร์ ] แล้วลองใหม่อีกครั้ง'
      } else if (msg === 'User Has PDF not found.') {
        return 'ไม่พบข้อมูลใบเสนอราคา'
      } else if (msg === 'Token not found.') {
        return 'ไม่พบข้อมูลโทเค่น'
      } else if (msg === 'You not Have Permission Purchaser.') {
        return 'คุณไม่มีสิทธิ์ผู้ซื้อองค์กร'
      } else if (msg === 'Approve Position Purchaser Not Found.') {
        return 'ไม่พบรูปแบบการอนุมัติ'
      } else if (msg === 'Approve Position Budget Less Than Order Net Price.') {
        return 'วงเงินของรูปแบบการอนุมัติน้อยกว่าเงินสุทธิออเดอร์'
      } else if (msg === 'Your Company is not Partner With This Shop.') {
        return 'บริษัทของคุณไม่ได้เป็นคู่ค้ากับร้านค้า'
      } else if (
        msg ===
        'Company Credit With This Seller Shop Less Than Order Net Price.'
      ) {
        return 'เงินขององค์กรน้อยกว่าเงินสุทธิของออเดอร์'
      } else if (msg === 'Not Have Approver In Approve Position.') {
        return 'ไม่พบผู้อนุมัติในลำดับอนุมัติ โปรดเพิ่มผู้อนุมัติก่อน'
      } else if (
        msg === 'Sum Approve Position Budget Less Than Order Net Price.'
      ) {
        return 'ผลรวมของวงเงินแต่ละลำดับน้อยกว่าเงินสุทธิของออเดอร์'
      } else if (msg === 'Wrong Approve Position Type.') {
        return 'ประเภทการอนุมัติผิด'
      } else if (msg === 'Some User not Have Permission to Approve Order.') {
        return 'มีผู้ใช้ในลำดับที่ไม่มีสิทธิ์การอนุมัติ กรุณาตรวจสอบรูปแบบการอนุมัติ'
      } else if (msg === '') {
        return ''
      } else {
        // return 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่'
        return msg
      }
    },
    async checkAddressTaxinvoice () {
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var dataCompany = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(
          Decode.decode(localStorage.getItem('SetRowCompany'))
        )
        dataCompany = companyDataSet.company.company_id
      } else {
        dataCompany = ''
      }
      var data = {
        user_id: onedata.user.user_id,
        role: 'purchaser',
        company_id: dataCompany,
        tax_type: this.taxRoles
      }
      await this.$store.dispatch('actionsGetInvoice', data)
      this.taxinvoiceAddress = await this.$store.state.ModuleUser
        .stategetInvoice
      // console.log('taxinvoiceAddress', this.taxinvoiceAddress)
      if (this.taxinvoiceAddress.data.length !== 0) {
        this.companyName = this.taxinvoiceAddress.data[0].name
        this.companyTaxID =
          'เลขประจำตัวผู้เสียภาษี :' +
          ' ' +
          this.taxinvoiceAddress.data[0].tax_id
        this.taxAddress =
          this.taxinvoiceAddress.data[0].address +
          ' ' +
          'แขวง/ตำบล' +
          ' ' +
          this.taxinvoiceAddress.data[0].sub_district +
          ' ' +
          'เขต/อำเภอ' +
          ' ' +
          this.taxinvoiceAddress.data[0].district +
          ' ' +
          'จังหวัด' +
          ' ' +
          this.taxinvoiceAddress.data[0].province +
          ' ' +
          this.taxinvoiceAddress.data[0].postal_code
      }
      // this.CreateOrderSyncTaxaddress()
    },
    openTaxAddress () {
      this.$refs.ModalTaxAddress.open()
      // มีการยิง api get ข้อมูลใบกำกับภาษี
      // localStorage.setItem('AddressData', Encode.encode(this.propsAddress[0].address_data))
    },
    openSelectTaxInvoice (val) {
      this.selectPayment = val
      this.ModalPaymentQU = !this.ModalPaymentQU
      this.modalSetPositionCompany = !this.modalSetPositionCompany
    },
    openModalAwaitConfirm () {
      this.disableButton = true
      this.dialogAwaitConfirmQT = true
    },
    closeModalAwaitConfirm () {
      this.disableButton = false
      this.dialogAwaitConfirmQT = false
    },
    closeModalSuccess () {
      this.dialogSuccessConfirmQT = false
      this.$router.push({ path: '/QUCompany' }).catch(() => {})
    },
    async confirmQT (val) {
      this.dialogAwaitConfirmQT = false
      this.dialogAwaitRejectQT = false
      this.disableButton = true
      this.$store.commit('openLoader')
      var data = {
        order_number: this.DetailQU.order_number,
        status: val,
        reason: this.DetailQU.reason
      }
      await this.$store.dispatch('BuyerUpdateStatusQT', data)
      var response = await this.$store.state.ModuleShop.stateBuyerUpdateStatusQT
      if (response) {
        this.$store.commit('closeLoader')
        this.disableButton = false
        this.dialogSuccessConfirmQT = true
      } else {
        this.$store.commit('closeLoader')
        this.disableButton = false
        await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่</h3>' })
      }
    },
    async confirmApprove () {
      // var dataCreateJsonQT = {
      //   order_number: this.DetailQU.qu_number.slice(3)
      // }
      // await this.$store.dispatch('Create_JSON_QT', dataCreateJsonQT)
      // var resCreateJsonQT = await this.$store.state.ModuleShop.stateCreateJSON_QT
      // if (resCreateJsonQT.code === 200) {
      //   var dataConfirmApprove = {
      //     order_number: this.DetailQU.qu_number.slice(3),
      //     company_id: this.ID,
      //     com_perm_id: this.com_perm_id
      //   }
      //   console.log(dataConfirmApprove)
      //   await this.$store.dispatch('Confirm_Approve_QT', dataConfirmApprove)
      //   var response = await this.$store.state.ModuleShop.stateConfirmAppoverQT
      //   if (response.code === 200) {
      //     await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ดำเนินการสำเร็จ</h3>' })
      //     this.$router.push({ path: '/QUCompany' })
      //   }
      // } else {
      //   this.$swal.fire({
      //     icon: 'error',
      //     text: 'เกิดข้อผิดพลาด',
      //     showConfirmButton: false,
      //     timer: 1500
      //   })
      // }
      this.dialogAwaitConfirmQT = false
      this.disableButton = true
      this.$store.commit('openLoader')
      // console.log('this.costcenter', this.costcenter)
      var dataConfirmApprove = {
        order_number: this.DetailQU.order_number,
        department_name: this.costcenter.department_name === undefined ? '' : this.costcenter.department_name,
        cost_center_code: this.costcenter.cost_center_code === undefined ? '' : this.costcenter.cost_center_code,
        cost_center_description: this.costcenter.cost_center_description === undefined ? '' : this.costcenter.cost_center_description
      }
      if (this.DetailQU.is_order_JV === 'yes') {
        await this.$store.dispatch('Confirm_Approve_QT', dataConfirmApprove)
        var responseJV = await this.$store.state.ModuleShop.stateConfirmAppoverQT
        if (responseJV.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          // await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: response.message })
          this.disableButton = false
          this.dialogSuccessConfirmQT = true
          // this.$router.push({ path: '/QUCompany' }).catch(() => {})
        } else {
          if (responseJV.message === 'This user is Unauthorized' || responseJV.message === 'This user is unauthorized.' || responseJV.message === 'กรุณากรอก token ให้ถูกต้อง') {
            this.$store.commit('closeLoader')
            this.$EventBus.$emit('refreshToken')
          } else {
            this.$store.commit('closeLoader')
            this.disableButton = false
            await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: responseJV.message })
          }
        }
      } else {
        await this.$store.dispatch('actionsApprovePRDocument', dataConfirmApprove)
        var response = await this.$store.state.ModuleShop.stateApprovePRDocument
        if (response.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          // await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: response.message })
          this.disableButton = false
          this.dialogSuccessConfirmQT = true
          // this.$router.push({ path: '/QUCompany' }).catch(() => {})
        } else {
          if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
            this.$store.commit('closeLoader')
            this.$EventBus.$emit('refreshToken')
          } else {
            this.$store.commit('closeLoader')
            this.disableButton = false
            await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: response.message })
          }
        }
      }
    },
    async GotoPayment () {
      this.$store.commit('openLoader')
      this.modalSelectTaxInvoice = !this.modalSelectTaxInvoice
      this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        etax: this.taxRoles,
        role_user: this.dataRole.role,
        qu_id: this.QUID,
        com_perm_id: this.com_perm_id
      }
      // console.log(data)
      await this.$store.dispatch('actionsGetPayment', data)
      var response = await this.$store.state.ModuleAdminManage.stateGetPayment
      // console.log(response)
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        if (response.data.payment_method !== 'เครดิตเทอม') {
          if (response.data.have_approve === 'no') {
            if (response.data.can_pay === 'yes') {
              await this.CreateOrder(response.data)
              this.$store.commit('openLoader')
            } else {
              if (this.MobileSize) {
                this.$router
                  .push({
                    path: `/orderDetailCompanyMobile?orderNumber=${response.data.payment_transaction_number}`
                  })
                  .catch(() => {})
              } else {
                this.$router
                  .push({
                    path: `/orderDetailCompany?orderNumber=${response.data.payment_transaction_number}`
                  })
                  .catch(() => {})
              }
            }
          } else {
            if (this.MobileSize) {
              this.$router
                .push({ path: '/listApproveOrderMobile' })
                .catch(() => {})
            } else {
              this.$router.push({ path: '/listApproveOrder' }).catch(() => {})
            }
          }
        } else {
          const data = {
            company_id: response.data.company_id
          }
          await this.$store.dispatch('actionsDetailCompany', data)
          var responseCompany = await this.$store.state.ModuleAdminManage
            .stateDetailCompany
          localStorage.setItem(
            'CompanyData',
            Encode.encode(responseCompany.data)
          )
          if (response.data.have_approve === 'no') {
            if (this.MobileSize) {
              this.$router
                .push({ path: '/companyListCreditOrderMobile' })
                .catch(() => {})
            } else {
              this.$router
                .push({ path: '/companyListCreditOrder' })
                .catch(() => {})
            }
          } else {
            if (this.MobileSize) {
              this.$router
                .push({ path: '/listApproveOrderMobile' })
                .catch(() => {})
            } else {
              this.$router.push({ path: '/listApproveOrder' }).catch(() => {})
            }
          }
        }
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          var msg = this.getErrorMsg(response.message)
          this.$swal.fire({
            icon: 'error',
            text: msg,
            showConfirmButton: false,
            timer: 1500
          })
          if (this.MobileSize) {
            this.$router.push({ path: '/QUCompanyMobile' }).catch(() => {})
          } else {
            this.$router.push({ path: '/QUCompany' }).catch(() => {})
          }
        }
      }
    },
    async CreateOrder (val) {
      // console.log(val)
      // this.$store.commit('openLoader')
      // var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // var data = {
      //   role_user: dataRole.role
      // }
      // await this.$store.dispatch('ActionCreateOrder', data)
      // var res = await this.$store.state.ModuleCart.stateCreateOrder
      // if (res.message === 'Create Order success.') {
      this.$store.commit('closeLoader')
      var dataPayment = {
        payment_transaction_number: val.payment_transaction_number
      }
      await this.$store.dispatch('ActionGetPaymentPage', dataPayment)
      var resRedirect = this.$store.state.ModuleCart.stateGetPaymentPage
      localStorage.setItem('PaymentData', Encode.encode(resRedirect))
      this.$router.push('/RedirectPaymentPage')
      // } else {
      //   this.$store.commit('closeLoader')
      //   this.$swal.fire({
      //     icon: 'error',
      //     text: `${response.message}`,
      //     showConfirmButton: false,
      //     timer: 1500
      //   })
      // }
    },
    async GetDetailQU () {
      var data = {
        company_id: this.companyId,
        com_perm_id: this.comPerID,
        order_number: this.orderNumber
      }
      await this.$store.dispatch('actionsDetailQTBuyer', data)
      var response = await this.$store.state.ModuleAdminManage.stateDetailQTBuyer
      if (response.result === 'Success') {
        this.DetailQU = response.data
        if (this.DetailQU.type_b2b_discount === 'baht') {
          this.discountBahtB2B = true
          this.discountPercentB2B = false
        } else if (this.DetailQU.type_b2b_discount === 'percent') {
          this.discountBahtB2B = false
          this.discountPercentB2B = true
        } else {
          this.discountBahtB2B = false
          this.discountPercentB2B = false
        }
        this.status_approve = this.DetailQU.status_approve
        if (this.DetailQU.status === 'approve' && this.DetailQU.use_PR) {
          this.getCostCenter()
        }
        this.getPagePDF(
          this.DetailQU.dwf_pdf_path_qt
        )
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.DetailQU = []
          this.$swal.fire({
            icon: 'error',
            text: 'ไม่มีใบเสนอราคานี้ในบริษัท',
            showConfirmButton: false,
            timer: 1500
          })
          this.$router.push({ path: '/QUCompany' }).catch(() => {})
        }
      }
    },
    async getPagePDF (path) {
      // var loadingTask = VuePdf.createLoadingTask(path)
      // this.pdfPath = loadingTask
      // await this.pdfPath.promise.then(pdf => {
      //   console.log(pdf)
      //   this.numPages = pdf.numPages
      // })
      // console.log(this.numPages)
    },
    textcolorChip (quStatus) {
      // console.log(quStatus, statusApprove)
      if (quStatus === 'waiting') {
        return '#FF710B'
      } else if (quStatus === 'waiting_dwf') {
        return '#FF710B'
      } else if (quStatus === 'waiting_approve') {
        return '#FF710B'
      } else if (quStatus === 'check_doc') {
        return '#FF710B'
      } else if (quStatus === 'waiting_shop_approve') {
        return '#FF710B'
      } else if (quStatus === 'approve') {
        return '#52C41A'
      } else if (quStatus === 'reject') {
        return '#F5222D'
      } else if (quStatus === 'success') {
        return '#52C41A'
      } else {
        return '#636363'
      }
    },
    colorChip (quStatus) {
      // console.log(quStatus, statusApprove)
      if (quStatus === 'waiting') {
        return '#FEF6E6'
      } else if (quStatus === 'waiting_dwf') {
        return '#FBECE1'
      } else if (quStatus === 'waiting_approve') {
        return '#FBECE1'
      } else if (quStatus === 'check_doc') {
        return '#FBECE1'
      } else if (quStatus === 'waiting_shop_approve') {
        return '#FBECE1'
      } else if (quStatus === 'approve') {
        return '#F0FEE8'
      } else if (quStatus === 'reject') {
        return 'rgba(245, 34, 45, 0.10)'
      } else if (quStatus === 'success') {
        return '#F0FEE8'
      } else {
        return '#E6E6E6'
      }
    },
    async changeStatus (val) {
      var data
      // console.log(val)
      if (val === 'active' || val === 'inactive') {
        data = {
          company_id: this.ID,
          qu_id: this.QUID,
          status: val
        }
        // console.log(data)
        await this.$store.dispatch('actionsUpdateStatusBuyer', data)
        var response = await this.$store.state.ModuleAdminManage
          .stateUpdateStatusBuyer
        if (response.result === 'SUCCESS') {
          this.$swal.fire({
            icon: 'success',
            text: 'อัปเดตสถานะใบเสนอราคาสำเร็จ',
            showConfirmButton: false,
            timer: 1500
          })
          this.$EventBus.$emit('ListDataTable')
          this.$router.push({ path: '/QUCompany' }).catch(() => {})
        } else {
          this.$swal.fire({
            icon: 'error',
            text: `${response.message}`,
            showConfirmButton: false,
            timer: 1500
          })
          this.$router.push({ path: '/QUCompany' }).catch(() => {})
        }
      } else {
        var dataEdit = {
          company_id: this.ID,
          qu_id: this.QUID,
          role_user: 'purchaser',
          seller_shop_id: this.ShopID,
          id: this.ID,
          total_discount: this.detailItemQu.total_discount
            ? this.detailItemQu.total_discount
            : '',
          coupon_id: this.detailItemQu.coupon_id
            ? this.detailItemQu.coupon_id
            : '',
          point: this.detailItemQu.point ? this.detailItemQu.point : ''
        }
        const pathBack = window.location.href.split('/')
        this.$store.state.ModuleAdminManage.stateURLQu = `/${pathBack[3]}`
        // console.log('URL', this.$store.state.ModuleAdminManage.stateURLQu)
        // console.log(dataEdit)
        await this.$store.dispatch('actionsEditQU', dataEdit)
        const { result = '', data = {} } = await this.$store.state
          .ModuleAdminManage.stateEditQU
        if (result === 'SUCCESS') {
          this.$store.state.ModuleAdminManage.QuotationformData = await data
          this.$router
            .push({ path: `/Quotation?role=purchaser&&qu_id=${this.QUID}` })
            .catch(() => {})
          // this.$router.push({ name: 'QuotationCompany', path: '/Quotation', query: { role: 'purchaser', qu_id: this.QUID } }).catch(() => {})
        } else {
          this.$swal.fire({
            icon: 'error',
            text: 'ไม่มีข้อมูลแก้ไขใบเสนอราคานี้',
            showConfirmButton: false,
            timer: 1500
          })
          this.$router.push({ path: '/QUCompany' }).catch(() => {})
        }
      }
    },
    backtoListPoCompany () {
      if (this.MobileSize) {
        this.$router.push({ path: '/QUCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/QUCompany' }).catch(() => {})
      }
    },
    ShowModalSelect () {
      this.ModalPaymentQU = !this.ModalPaymentQU
    },
    async AuthorityUser () {
      this.list_position = []
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      this.list_company = response.data.list_company
      this.list_company.forEach(element => {
        if (element.company_id === this.companyId) {
          this.selectedCompany = element
          element.array_position.forEach(position => {
            if (position.purchaser === true) {
              this.list_position.push(position)
            }
          })
        }
      })
    },
    openAwaitReject () {
      this.dialogAwaitRejectQT = true
    },
    async cancelApprove () {
      this.dialogAwaitRejectQT = false
      this.$store.commit('openLoader')
      // var dataConfirmApprove = {
      //   order_number: this.DetailQU.qu_number.slice(3),
      //   company_id: this.ID,
      //   com_perm_id: this.com_perm_id,
      //   terminate_subject: 'อื่นๆ',
      //   terminate_reason: '',
      //   terminate_date: ''
      // }
      // console.log(dataConfirmApprove)
      // await this.$store.dispatch('Cancel_Approve_QT', dataConfirmApprove)
      // var response = await this.$store.state.ModuleShop.stateCancelAppoverQT
      // if (response.code === 200) {
      //   await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ดำเนินการยกเลิกสำเร็จ</h3>' })
      //   this.$router.push({ path: '/QUCompany' })
      // }
      var dataCancelApprove = {
        order_number: this.DetailQU.order_number
      }
      await this.$store.dispatch('Cancel_Approve_QT', dataCancelApprove)
      var response = await this.$store.state.ModuleShop.stateCancelAppoverQT
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        // await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ดำเนินการยกเลิกสำเร็จ</h3>' })
        this.dialogSuccessRejectQT = true
        // this.$router.push({ path: '/QUCompany' }).catch(() => {})
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          if (response.message === 'Order Already Action.') {
            await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>เอกสารนี้ทำรายการไปแล้ว</h3>' })
          } else {
            await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' })
          }
        }
      }
    },
    closeRejectSuccess () {
      this.dialogSuccessRejectQT = false
      this.$router.push({ path: '/QUCompany' }).catch(() => {})
    },
    // เลือกตำแหน่งก่อนชำระเงิน
    setPositionCompany (positionData) {
      var KepData = {
        company: {
          compant_name_en: this.selectedCompany.compant_name_en,
          compant_name_th: this.selectedCompany.compant_name_th,
          company_id: this.selectedCompany.company_id
        },
        position: positionData
      }
      this.com_perm_id = positionData.com_perm_id
      localStorage.setItem('SetRowCompany', Encode.encode(KepData))
      this.selectCompanyName =
        'ตำแหน่ง : ' +
        positionData.role_name +
        '   บริษัท : ' +
        this.selectedCompany.compant_name_th
      localStorage.setItem('selectCompanyName', this.selectCompanyName)
      this.modalSelectTaxInvoice = !this.modalSelectTaxInvoice
      this.modalSetPositionCompany = !this.modalSetPositionCompany
    },
    async editOrderQT  () {
      if (this.$refs.QTCheckoutV2.hasInstallmentError) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาระบุยอดเงินของแต่ละเดือนให้ครบทุกช่อง</h3>'
        })
      } else {
        if (this.$refs.QTCheckoutV2.remainingAmount !== '0.00') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: '<h3>กรุณาระบุยอดเงินให้เป็น 0.00 ก่อนดำเนินการต่อ</h3>'
          })
        } else {
          this.$refs.QTCheckoutV2.confirm()
        }
      }
    }
  }
}
</script>

<style>
.annotationLayer {
    position: inherit !important ;
}
</style>
<style lang="css">
 .vue-pdf-embed canvas {
  display: initial;
  position: inherit!important;
}
</style>
