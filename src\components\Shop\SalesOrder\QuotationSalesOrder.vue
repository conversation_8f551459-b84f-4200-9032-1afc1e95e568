<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">ใบเสนอราคา</v-card-title>
      <v-card-title style="font-weight: 700;" v-else>
          <v-icon color="#1AB759" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> ใบเสนอราคา
      </v-card-title>
      <v-row no-gutters>
        <v-col cols="12" class="py-0 px-4">
          <a-tabs @change="selectOrder">
            <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <v-chip small text-color="#27AB9C" color="rgba(39, 171, 156, 0.10)" style="border-radius: 40px;">{{ countPOAll }}</v-chip></span></a-tab-pane>
            <!-- <a-tab-pane :key="1"><span slot="tab">สร้างรายการสั่งซื้อแล้ว <v-chip small text-color="#52C41A" color="#F0FEE8" style="border-radius: 40px;">{{ countPOSuccess }}</v-chip></span></a-tab-pane> -->
            <a-tab-pane :key="1"><span slot="tab">อนุมัติแล้ว <v-chip small text-color="#52C41A" color="#F0FEE8" style="border-radius: 40px;">{{ countPOActive }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="2"><span slot="tab">รออนุมัติ <v-chip small text-color="#E9A016" color="#FEF6E6" style="border-radius: 40px;">{{ countPOWaitingApprove }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="3"><span slot="tab">รออนุมัติใบเสนอราคา <v-chip small text-color="#FF710B" color="#FBECE1" style="border-radius: 40px;">{{ countPOWaitingDWF }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="4"><span slot="tab">ปฏิเสธอนุมัติ <v-chip small text-color="#F5222D" color="rgba(245, 34, 45, 0.10)" style="border-radius: 40px;">{{ countPOReject }}</v-chip></span></a-tab-pane>
            <!-- <a-tab-pane :key="6"><span slot="tab">ยกเลิก <v-chip small text-color="#636363" color="#E6E6E6" style="border-radius: 40px;">{{ countPOCancel }}</v-chip></span></a-tab-pane> -->
          </a-tabs>
        </v-col>
        <v-row dense>
          <v-col v-if="disableTable === true" cols="12" md="8" sm="12" :class="!MobileSize ? 'pl-3 pr-3 mb-3' : 'px-4 pb-4'">
            <v-text-field v-model="search" style="border-radius: 8px;" dense hide-details outlined placeholder="ค้นหาจากชื่อลูกค้าหรือหมายเลขใบเสนอราคา">
              <v-icon slot="append">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <!-- <v-col v-if="disableTable === true" cols="12" md="8" sm="12" :class="MobileSize ? 'px-3 pb-4' : ''">
            <v-row dense>
              <v-col cols="4" md="3" sm="3" class="mt-2">
                <span style="font-size: 16px;" :class="MobileSize ? 'pl-2' : 'pt-5'">
                  Pay Type :
                </span>
              </v-col>
              <v-col cols="8" md="8" sm="3" :class="MobileSize ? 'pr-2' : 'ml-2 mr-4'">
                <v-select
                  outlined
                  dense
                  v-model="statePayType"
                  :items="['ทั้งหมด','recurring','onetime']"
                  @change="selectType()"
                  style="border-radius: 8px;"
                  placeholder="ทั้งหมด"
                  hide-details
                ></v-select>
              </v-col>
            </v-row>
          </v-col> -->
        </v-row>
        <v-col v-if="disableTable === true" cols="12" md="12" sm="12" :class="!MobileSize ? 'pl-4 pr-3 mb-2 mt-2' : 'pl-4 pr-2 mb-0 mt-3'">
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;" v-if="StateStatus === 0">รายการใบเสนอราคาทั้งหมด {{ DatatableFilter.length }} รายการ</span>
          <!-- <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;" v-else-if="StateStatus === 1">รายการใบเสนอราคาที่สำเร็จทั้งหมด {{ DatatableFilter.length }} รายการ</span> -->
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;" v-else-if="StateStatus === 1">รายการใบเสนอราคาที่อนุมัติแล้วทั้งหมด {{ DatatableFilter.length }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;" v-else-if="StateStatus === 2">รายการใบเสนอราคาที่รออนุมัติทั้งหมด {{ DatatableFilter.length }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;" v-else-if="StateStatus === 3">รายการใบเสนอราคาที่รออนุมัติเอกสาร/รอผู้ซื้ออนุมัติทั้งหมด {{ DatatableFilter.length }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;" v-else-if="StateStatus === 4">รายการใบเสนอราคาที่ปฏิเสธทั้งหมด {{ DatatableFilter.length }} รายการ</span>
          <!-- <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;" v-else-if="StateStatus === 6">รายการใบเสนอราคาที่ยกเลิกทั้งหมด {{ DatatableFilter.length }} รายการ</span> -->
        </v-col>
        <v-col v-if="disableTable === false" cols="12" md="12" sm="6" :class="!MobileSize ? 'pl-4 pr-3 mb-2 mt-2' : 'pl-4 pr-2 mb-0 mt-3'">
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="StateStatus === 0">รายการใบเสนอราคาทั้งหมด {{ DatatableFilter.length }} รายการ</span>
          <!-- <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 1">รายการใบเสนอราคาที่สำเร็จทั้งหมด {{ DatatableFilter.length }} รายการ</span> -->
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 1">รายการใบเสนอราคาที่อนุมัติแล้วทั้งหมด {{ DatatableFilter.length }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 2">รายการใบเสนอราคาที่รออนุมัติทั้งหมด {{ DatatableFilter.length }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 3">รายการใบเสนอราคาที่รออนุมัติเอกสาร/รอผู้ซื้ออนุมัติทั้งหมด {{ DatatableFilter.length }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 4">รายการใบเสนอราคาที่ปฏิเสธทั้งหมด {{ DatatableFilter.length }} รายการ</span>
          <!-- <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 6">รายการใบเสนอราคาที่ยกเลิกทั้งหมด {{ DatatableFilter.length }} รายการ</span> -->
        </v-col>
        <v-col cols="12">
          <v-card v-if="disableTable === true" outlined class="small-card mx-4 my-5" min-height="436">
            <v-data-table
              :headers="headers"
              :items="DatatableFilter"
              :search="search"
              :page.sync="page"
              style="width:100%;"
              height="100%"
              :footer-props="{'items-per-page-text':'จำนวนแถว'}"
              :items-per-page="10" @pagination="countRequest" no-results-text="ไม่พบบริษัทผู้ซื้อหรือหมายเลขใบเสนอราคาที่ค้นหา" no-data-text="ไม่มีรายการสินค้าในตาราง"
            >
              <template v-slot:[`item.status`]="{ item }">
                <span v-if="item.status === 'success'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" color="#F0FEE8" text-color="#52C41A">สร้างรายการสั่งซื้อ</v-chip>
                </span>
                <span v-else-if="item.status === 'approve'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" color="#F0FEE8" text-color="#52C41A">อนุมัติแล้ว</v-chip>
                </span>
                <span v-else-if="item.status === 'waiting_approve'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" color="#FBECE1" text-color="#FF710B">รออนุมัติ</v-chip>
                </span>
                <span v-else-if="item.status === 'waiting_dwf'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" color="#FBECE1" text-color="#FF710B">รออนุมัติใบเสนอราคา</v-chip>
                </span>
                <span v-else>
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" class="ma-2" text-color="#F5222D" color="rgba(245, 34, 45, 0.10)">ปฏิเสธอนุมัติ</v-chip>
                </span>
              </template>
              <template v-slot:[`item.payment_transaction_number`]="{ item }">
                {{ item.payment_transaction_number === null ? '-' : item.payment_transaction_number}}
              </template>
              <template v-slot:[`item.buyer_name`]="{ item }">
                {{ item.buyer_name !== '' ? item.buyer_name : '-'}}
              </template>
              <template v-slot:[`item.pay_type`]="{ item }">
                <v-chip v-if="item.pay_type === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">One Time</v-chip>
                <v-chip v-else-if="item.pay_type === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.created_at`]="{ item }">
                {{new Date(item.created_at).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}
              </template>
              <template v-slot:[`item.start_date_contract`]="{ item }">
                {{ item.start_date_contract === null ? '-' :  new Date(item.start_date_contract).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}
              </template>
              <template v-slot:[`item.end_date_contract`]="{ item }">
                {{ item.end_date_contract === null ? '-' : new Date(item.end_date_contract).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}
              </template>
              <template v-slot:[`item.QT_path`]="{ item }">
                <v-btn style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"  outlined small @click="goPDFQU(item)">
                  <v-icon color="#27AB9C" class="">mdi-file-document-outline</v-icon>
                </v-btn>
              </template>
              <template v-slot:[`item.action`]="{ item }">
                <v-btn
                  width="24"
                  height="24"
                  style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                  outlined icon small @click="goDetailQU(item)">
                  <v-icon color="#27AB9C" class="" size="18">mdi-file-document-outline</v-icon>
                </v-btn>
                <v-btn small text rounded color="#27AB9C" @click="goDetailQU(item)">
                  <b style="text-decoration: underline;">รายละเอียด</b>
                  <!-- <v-icon small>mdi-chevron-right</v-icon> -->
                </v-btn>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
        <v-col cols="12" v-if="disableTable === false" align="center">
          <div class="my-5">
            <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
          </div>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 0">
            <b>คุณยังไม่มีรายการใบเสนอราคา</b>
          </h2>
          <!-- <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 1">
            <b>คุณยังไม่มีรายการใบเสนอราคาที่สร้างรายการสั่งซื้อแล้ว</b>
          </h2> -->
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 1">
            <b>คุณยังไม่มีรายการใบเสนอราคาที่อนุมัติแล้ว</b>
          </h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 2">
            <b>คุณยังไม่มีรายการใบเสนอราคาที่รออนุมัติ</b>
          </h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 3">
            <b>คุณยังไม่มีรายการใบเสนอราคาที่รออนุมัติเอกสาร</b>
          </h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 4">
            <b>คุณยังไม่มีรายการใบเสนอราคาที่ปฏิเสธ</b>
          </h2>
          <!-- <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 6">
            <b>คุณยังไม่มีรายการใบเสนอราคาที่ยกเลิก</b>
          </h2> -->
        </v-col>
      </v-row>
    </v-card>
  </v-container>
</template>

<script>
import { Encode, Decode } from '@/services'
import { Tabs } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane
  },
  data () {
    return {
      ShopDetailSale: '',
      countPOAll: 0,
      countPOSuccess: 0,
      countPOActive: 0,
      countPOWaitingApprove: 0,
      countPOWaitingDWF: 0,
      countPOReject: 0,
      countPOCancel: 0,
      dialogDetail: false,
      name: 'รายละเอียดตำแหน่งและสิทธิ์การใช้งาน',
      orderList: [],
      StateStatus: 0,
      showCountOrder: 0,
      showCountRequest: 0,
      disableTable: false,
      companyData: [],
      seller_shop_id: null,
      dataRole: '',
      search: '',
      page: 1,
      keyCheckHead: 0,
      dialog_rank: false,
      pay_type: 'all',
      statePayType: '',
      headers: [
        { text: 'หมายเลขใบเสนอราคา', value: 'QT_number', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '230' },
        { text: 'ชื่อลูกค้า', value: 'customer_name', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '230' },
        { text: 'รหัสลูกค้า', value: 'customer_code', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '100' },
        { text: 'ใบเสนอราคา', value: 'QT_path', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '120' },
        { text: 'ชื่อฝ่ายขาย', value: 'sale_name', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '230' },
        { text: 'รหัสฝ่ายขาย', filterable: false, value: 'sale_code', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '155' },
        { text: 'Pay Type', value: 'pay_type', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '100' },
        { text: 'วันที่สร้างรายการ', filterable: false, value: 'created_at', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '155' },
        { text: 'สถานะ', filterable: false, value: 'status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', filterable: false, value: 'action', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' }
      ],
      receive_items: [
        { text: 'ยังไม่รับ', value: 'not_received' },
        { text: 'รับของแล้ว', value: 'received' }
      ],
      statusSend: { text: 'ยังไม่รับ', value: 'not_received' },
      DataTable: [],
      orderListMap: [],
      data: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    desktopSize () {
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    },
    DatatableFilter () {
      if (this.search === '') {
        return this.DataTable
      } else {
        return this.DataTable.filter(element => {
          return element.QT_number.toLowerCase().includes(this.search.toLowerCase()) || element.company_name.includes(this.search)
        })
      }
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/listQuotationSalesMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/listQuotationSales' }).catch(() => { })
      }
    },
    overlay (val) {
      val && setTimeout(() => {
        this.overlay = false
      }, 500)
    },
    StateStatus (val) {
      if (val === 0) {
        this.DataTable = this.orderList.data.all.map(x => {
          return {
            created_at: x.created_at,
            QT_number: x.QT_number,
            QT_path: x.QT_path,
            order_number: x.order_number,
            status: x.status,
            customer_name: x.customer_name,
            customer_code: x.customer_code,
            sale_name: x.sale_name,
            sale_code: x.sale_code,
            pay_type: x.pay_type,
            trtransaction_status: x.trtransaction_status,
            qt_approve_date: x.qt_approve_date
          }
        })
        this.keyCheckHead = 0
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 1) {
        this.DataTable = this.orderList.data.approve.map(x => {
          return {
            created_at: x.created_at,
            QT_number: x.QT_number,
            QT_path: x.QT_path,
            order_number: x.order_number,
            status: x.status,
            customer_name: x.customer_name,
            customer_code: x.customer_code,
            sale_name: x.sale_name,
            sale_code: x.sale_code,
            pay_type: x.pay_type,
            trtransaction_status: x.trtransaction_status,
            qt_approve_date: x.qt_approve_date
          }
        })
        this.keyCheckHead = 1
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 2) {
        this.DataTable = this.orderList.data.waiting_approve.map(x => {
          return {
            created_at: x.created_at,
            QT_number: x.QT_number,
            QT_path: x.QT_path,
            order_number: x.order_number,
            status: x.status,
            customer_name: x.customer_name,
            customer_code: x.customer_code,
            sale_name: x.sale_name,
            sale_code: x.sale_code,
            pay_type: x.pay_type,
            trtransaction_status: x.trtransaction_status,
            qt_approve_date: x.qt_approve_date
          }
        })
        this.keyCheckHead = 2
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 3) {
        this.DataTable = this.orderList.data.reject.map(x => {
          return {
            created_at: x.created_at,
            QT_number: x.QT_number,
            QT_path: x.QT_path,
            order_number: x.order_number,
            status: x.status,
            customer_name: x.customer_name,
            customer_code: x.customer_code,
            sale_name: x.sale_name,
            sale_code: x.sale_code,
            pay_type: x.pay_type,
            trtransaction_status: x.trtransaction_status,
            qt_approve_date: x.qt_approve_date
          }
        })
        this.keyCheckHead = 3
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.$EventBus.$on('getPOBuyer', this.SwitchRole)
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    // if (this.dataRole.role !== 'sale_order') {
    //   this.seller_shop_id = JSON.parse(localStorage.getItem('shopSellerID'))
    // } else {
    //   this.ShopDetailSale = JSON.parse(Decode.decode(localStorage.getItem('ShopDetailSale')))
    // }
    this.seller_shop_id = JSON.parse(localStorage.getItem('shopSellerID'))
    // var dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
    if (localStorage.getItem('list_shop_detail') !== null) {
      // if (dataDetail.can_use_function_in_shop.manage_order === '1') {
      this.getListData()
      // } else {
      //   this.$router.push({ path: '/' })
      // }
    } else {
      this.$router.push({ path: '/' })
    }
    window.scrollTo(0, 0)
  },
  methods: {
    selectType () {
      if (this.statePayType === 'ทั้งหมด' || this.statePayType === '') {
        this.pay_type = 'all'
      } else if (this.statePayType === 'recurring') {
        this.pay_type = 'recurring'
      } else if (this.statePayType === 'onetime') {
        this.pay_type = 'onetime'
      }
      this.getListData()
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    goDetailQU (item) {
      var QUNumber = item.order_number
      var id
      // if (this.dataRole.role !== 'sale_order') {
      //   id = this.seller_shop_id.id
      // } else {
      //   id = this.ShopDetailSale.seller_shop_id
      // }
      id = JSON.parse(localStorage.getItem('shopSellerID'))
      // console.log('data ====>', item)
      localStorage.setItem('detailItemQUSale', Encode.encode(item))
      if (!this.MobileSize) {
        this.$router.push({ path: `/DetailQuotationSales?QUNumber=${QUNumber}&id=${id}` }).catch(() => { })
      } else {
        this.$router.push({ path: `/DetailQuotationSalesMobile?QUNumber=${QUNumber}&id=${id}` }).catch(() => { })
      }
    },
    selectOrder (item) {
      this.StateStatus = item
      this.page = 1
      this.getListData()
    },
    goPDFQU (item) {
      window.open(`${item.QT_path}`)
    },
    async getListData () {
      var sellerShopID
      this.$store.commit('openLoader')
      // if (this.dataRole.role !== 'sale_order') {
      //   sellerShopID = this.seller_shop_id.id
      // } else {
      //   sellerShopID = this.ShopDetailSale.seller_shop_id
      // }
      if (localStorage.getItem('list_shop_detail') !== null) {
        var dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
      }
      sellerShopID = JSON.parse(localStorage.getItem('shopSellerID'))
      var dataSeller = {
        seller_shop_id: sellerShopID,
        sale_id: dataDetail.can_use_function_in_shop.manage_sale_order === '1' ? '' : dataDetail.sale_id
        // pay_type: this.pay_type === 'all' ? null : this.pay_type
      }
      await this.$store.dispatch('actionsListQTSale', dataSeller)
      this.orderList = await this.$store.state.ModuleSaleOrder.stateListQTSale
      // console.log(this.orderList)
      if (this.orderList.result === 'Success') {
        if (this.orderList.message === 'List QT order sales success.') {
          this.countPOAll = this.orderList.data.all.length
          this.countPOReject = this.orderList.data.reject.length
          // this.countPOSuccess = this.orderList.data.success.length
          this.countPOActive = this.orderList.data.approve.length
          this.countPOWaitingApprove = this.orderList.data.waiting_approve.length
          this.countPOWaitingDWF = this.orderList.data.waiting_dwf.length
          // this.countPOCancel = this.orderList.data.cancel.length
          if (this.StateStatus === 0) {
            this.DataTable = this.orderList.data.all.map(x => {
              return {
                created_at: x.created_at,
                QT_number: x.QT_number,
                QT_path: x.QT_path,
                order_number: x.order_number,
                status: x.status,
                customer_name: x.customer_name,
                customer_code: x.customer_code,
                sale_name: x.sale_name,
                sale_code: x.sale_code,
                pay_type: x.pay_type,
                trtransaction_status: x.trtransaction_status,
                qt_approve_date: x.qt_approve_date
              }
            })
            this.showCountOrder = this.orderList.data.all.length
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 1) {
            this.DataTable = this.orderList.data.approve.map(x => {
              return {
                created_at: x.created_at,
                QT_number: x.QT_number,
                QT_path: x.QT_path,
                order_number: x.order_number,
                status: x.status,
                customer_name: x.customer_name,
                customer_code: x.customer_code,
                sale_name: x.sale_name,
                sale_code: x.sale_code,
                pay_type: x.pay_type,
                trtransaction_status: x.trtransaction_status,
                qt_approve_date: x.qt_approve_date
              }
            })
            this.showCountOrder = this.orderList.data.approve.length
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 2) {
            this.DataTable = this.orderList.data.waiting_approve.map(x => {
              return {
                created_at: x.created_at,
                QT_number: x.QT_number,
                QT_path: x.QT_path,
                order_number: x.order_number,
                status: x.status,
                customer_name: x.customer_name,
                customer_code: x.customer_code,
                sale_name: x.sale_name,
                sale_code: x.sale_code,
                pay_type: x.pay_type,
                trtransaction_status: x.trtransaction_status,
                qt_approve_date: x.qt_approve_date
              }
            })
            this.showCountOrder = this.orderList.data.waiting_approve.length
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 4) {
            this.DataTable = this.orderList.data.reject.map(x => {
              return {
                created_at: x.created_at,
                QT_number: x.QT_number,
                QT_path: x.QT_path,
                order_number: x.order_number,
                status: x.status,
                customer_name: x.customer_name,
                customer_code: x.customer_code,
                sale_name: x.sale_name,
                sale_code: x.sale_code,
                pay_type: x.pay_type,
                trtransaction_status: x.trtransaction_status,
                qt_approve_date: x.qt_approve_date
              }
            })
            this.showCountOrder = this.orderList.data.reject.length
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 3) {
            this.DataTable = this.orderList.data.waiting_dwf.map(x => {
              return {
                created_at: x.created_at,
                QT_number: x.QT_number,
                QT_path: x.QT_path,
                order_number: x.order_number,
                status: x.status,
                customer_name: x.customer_name,
                customer_code: x.customer_code,
                sale_name: x.sale_name,
                sale_code: x.sale_code,
                pay_type: x.pay_type,
                trtransaction_status: x.trtransaction_status,
                qt_approve_date: x.qt_approve_date
              }
            })
            this.showCountOrder = this.orderList.data.waiting_dwf.length
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          }
          this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            text: 'ผู้ใช้งานนี้ไม่มีสิทธิ์การเข้าถึงใบเสนอราคาฝ่ายขาย',
            showConfirmButton: false,
            timer: 1500
          })
          // this.$router.push({ path: '/detailCompany' }).catch(() => { })
        }
      } else {
        this.$store.commit('closeLoader')
        if (this.orderList.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({ text: 'เกิดข้อผิดพลาด', icon: 'error', timer: 2500, showConfirmButton: false })
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(10) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(10) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
<style scoped>
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
.v-text-field--box .v-input__slot, .v-text-field--outline .v-input__slot{
  min-height:36px;
}
</style>
