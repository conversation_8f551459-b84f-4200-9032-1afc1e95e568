<template>
  <v-container class="pa-2">
    <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4 px-0' : 'mb-4' ]">
      <v-col class="pt-6">
        <span v-if="MobileSize" class="f-left" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 24px;">
          <v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>จัดการ Flash Sale ร้านค้า</span>
        <span v-else class="f-left" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 24px;">จัดการ Flash Sale ร้านค้า</span>
      </v-col>

      <v-row class="py-2 px-4" style="display: flex; flex-wrap: wrap; align-items: center;">
        <v-col cols="12" md="auto">
          <span>ตั้งค่าการใช้ส่วนลด :</span>
        </v-col>

        <v-col cols="6" sm="auto" md="auto" class="d-flex align-center">
          <v-switch
            v-model="isSystemFlashSale"
            :disabled="isSystemFlashSale"
            color="#3eb3a6"
            inset
            class="my-0 ml-2"
            hide-details
            @change="handleSystemFlashSale"
          ></v-switch>
          <span class="ml-2">เปิด (ส่วนลดระบบ)</span>
        </v-col>

        <v-col cols="6" sm="auto" md="auto" class="d-flex align-center">
          <v-switch
            v-model="isShopFlashSale"
            :disabled="isShopFlashSale"
            color="#3eb3a6"
            inset
            class="my-0 ml-2"
            hide-details
            @change="handleShopFlashSale"
          ></v-switch>
          <span class="ml-2">เปิด (ส่วนลดร้านค้า)</span>
        </v-col>

        <v-col cols="12" md="auto" class="ml-md-auto text-md-right">
          <span>สถานะ : {{ setstatus() }}</span>
        </v-col>
      </v-row>

      <v-row class="py-2 px-4">
        <v-col cols="12" style="display: flex; justify-content: flex-end;">
          <v-btn rounded color="#27ab9c" dark @click="editImage()">
            แก้ไขภาพ Flash Sale
          </v-btn>
        </v-col>
      </v-row>

      <CarouselFlashSell />

    </v-card>
  </v-container>
</template>

<script>
export default {
  components: {
    CarouselFlashSell: () => import('@/components/Carousel/FlashProductUI')
  },
  data () {
    return {
      statusFlashSale: '',
      isSystemFlashSale: false,
      isShopFlashSale: false
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/adminFlashsaleManageMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'adminFlashsaleManage')
        this.$router.push({ path: '/adminFlashsaleManage' }).catch(() => {})
      }
    }
  },
  mounted () {
    this.StatusFlashSale()
    this.setstatus()
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    this.StatusFlashSale()
    window.scrollTo(0, 0)
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    editImage () {
      if (this.MobileSize) {
        this.$router.push({ path: '/EditImagesFlashSaleMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/EditImagesFlashSale' }).catch(() => { })
      }
    },
    async handleSystemFlashSale () {
      if (this.isSystemFlashSale) {
        this.isShopFlashSale = false
        await this.updateFlashSaleStatus('yes')
      } else {
        await this.updateFlashSaleStatus('no')
      }
    },
    async handleShopFlashSale () {
      if (this.isShopFlashSale) {
        this.isSystemFlashSale = false
        await this.updateFlashSaleStatus('no')
      } else {
        await this.updateFlashSaleStatus('yes')
      }
    },
    setstatus () {
      if (this.isSystemFlashSale) {
        return 'กำลังใช้งานส่วนลดระบบ'
      } else if (this.isShopFlashSale) {
        return 'กำลังใช้งานส่วนลดร้านค้า'
      } else {
        return 'สถานะส่วนลดไม่ถูกต้อง'
      }
    },
    async StatusFlashSale () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsStatusFlashSale')
      const responseStatusFlashSale = await this.$store.state.ModuleAdminManage.stateStatusFlashSale

      if (responseStatusFlashSale.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.statusFlashSale = responseStatusFlashSale.data
        this.isSystemFlashSale = this.statusFlashSale === 'yes'
        this.isShopFlashSale = this.statusFlashSale === 'no'
      }
    },
    async updateFlashSaleStatus (status) {
      this.$store.commit('openLoader')
      const data = {
        status_flash_sale: status
      }

      await this.$store.dispatch('actionsSetFlashSale', data)
      const responseUpdateSetFlashSale = await this.$store.state.ModuleAdminManage.stateSetFlashSale

      if (responseUpdateSetFlashSale.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: status === 'yes'
            ? 'เปิดการใช้งาน Flash Sale สำเร็จ (ส่วนลดระบบ)'
            : 'เปิดการใช้งาน Flash Sale สำเร็จ (ส่วนลดร้านค้า)'
        })
        this.$EventBus.$emit('getAllProductFlashSale')
      } else {
        this.$swal.fire({
          icon: 'error',
          title: 'ไม่สามารถเปลี่ยนสถานะ Flash Sale ได้',
          text: responseUpdateSetFlashSale.message
        })
      }
    }
  }
}
</script>

<style>

</style>
