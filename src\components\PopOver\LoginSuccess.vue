<template>
  <a-popover v-model="visible" trigger="click" placement="bottom" style="width: 150px;">
    <!-- <template slot="content">
      <v-list dense style="width: 200px;">
        <v-list-item v-for="(item, index) in ListNavbar" :key="index" @click="Gopage(item)">
          <a-icon large :type="item.icon" class="pd-link" style="padding-right: 5px;"/><span>{{item.name}}</span>
        </v-list-item>
      </v-list>
    </template> -->
    <template slot="content">
      <a-menu style="width: 256px" mode="inline"  :default-selected-keys="['0']" :selected-keys="['0']">
        <a-menu-item @click="LinkPage('userprofile')"><a-icon type="user" />บัญชีของฉัน</a-menu-item>
        <a-sub-menu v-if="$router.currentRoute.name !== 'Checkout'">
          <span slot="title"><a-icon type="team" />เลือกสิทธื์ผู้ใช้งาน</span>
          <a-menu-item v-if="userdetail.admin === '1'" @click="LinkPage('admin')">ผู้ดูแลระบบ</a-menu-item>
          <a-menu-item v-if="userdetail.seller_shop_admin === '1'" @click="LinkPage('admin')">ผู้ดูแลระบบผู้ขาย</a-menu-item>
          <a-menu-item v-if="userdetail.purchaser === '1'" @click="LinkPage('purchaser')">ผู้ค้าองค์กร</a-menu-item>
          <a-menu-item v-if="userdetail.ext_buyer === '1'" @click="LinkPage('ext_buyer')">ผู้ซื้อทั่วไป</a-menu-item>
        </a-sub-menu>
        <a-menu-item  @click="LinkPage('pobuyer')"><a-icon type="shopping-cart" />การซื้อของฉัน</a-menu-item>
        <a-menu-item  @click="LinkPage('createbusinesssid')" v-if="haveBusinessID === null && userdetail.one_id !== null"><a-icon type="shop" />สร้างบริษัท</a-menu-item>
        <a-menu-item  @click="LinkPage('detailbusinesssid')" v-else-if="userdetail.one_id !== null && haveBusinessID !== null"><a-icon type="shop" />ดูข้อมูลบริษัท</a-menu-item>
        <a-menu-item  @click="LinkPage('seller')" v-if="role.seller_admin === true"><a-icon type="shop" />ร้านค้า</a-menu-item>
        <a-menu-item  @click="LinkPage('logout')"><a-icon type="export" />ออกจากระบบ</a-menu-item>
      </a-menu>
    </template>
    <v-btn class="py-4 mr-2 mt-3" rounded color="black" text>
      <v-avatar size="30">
        <img :src="`${path}${imagePath}`" v-if="imagePath !== ''"/>
        <v-img  src="@/assets/noprofile.png" v-else />
      </v-avatar>
      <span style="color:white" class="subtitle-2 pt-6 pl-1 pr-1 pb-2">{{ name }}<p style="font-size: 12px;">({{ changeRole === 'ext_buyer' ? 'ผู้ซื้อทั่วไป' :  'ผู้ค้าองค์กร' }})</p></span>
    </v-btn>
  </a-popover>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      ListNavbar: [
        { key: 'sub1', icon: 'user', name: 'บัญชีของฉัน', path: 'userprofile', child: [] },
        { key: 'sub2', icon: 'team', name: 'เลือกสิทธื์ผู้ใช้งาน', path: '', child: [] },
        { key: 'sub3', icon: 'shopping-cart', name: 'การซื้อของฉัน', path: 'pobuyer', child: [] },
        { key: 'sub4', icon: 'shop', name: 'ร้านค้า', path: 'seller', child: [] },
        { key: 'sub5', icon: 'export', name: 'ออกจากระบบ', path: 'logout', child: [] }
      ],
      name: '',
      imagePath: '',
      visible: false,
      superadmin: false,
      selleradmin: false,
      purchaser: false,
      ext_buyer: false,
      onedata: [],
      role: '',
      disableRole: true,
      userdetail: [],
      changeRole: 'ext_buyer',
      haveBusinessID: ''
    }
  },
  created () {
    this.$EventBus.$on('closeModalSuccess', this.closeModalSuccess)
    this.$EventBus.$on('openModalSuccess', this.openModalSuccess)
    this.$EventBus.$on('getUserDetail', this.getUserDetail)
    // this.$EventBus.$on('reloadPage', this.reloadPage)
    this.$EventBus.$on('CheckPermission', this.CheckPermission)
    this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    // console.log(this.onedata)
    if (this.onedata.user !== undefined) {
      // this.name = this.onedata.user.username
      // this.role = this.onedata.user.current_role_user
      // var roleCheck = JSON.parse(localStorage.getItem('roleUser'))
      // console.log(roleCheck)
      // roleCheck.role === 'ext_buyer' ? this.changeRole = 'ext_buyer' : this.changeRole = 'purchaser'
      this.CheckPermission()
    } else {
      this.name = ''
    }
    this.getUserDetail()
  },
  methods: {
    CheckPermission () {
      this.name = this.onedata.user.username
      this.role = this.onedata.user.current_role_user
      var roleCheck = JSON.parse(localStorage.getItem('roleUser'))
      // console.log(roleCheck)
      roleCheck.role === 'ext_buyer' ? this.changeRole = 'ext_buyer' : this.changeRole = 'purchaser'
    },
    LinkPage (val) {
      // console.log(val)
      // console.log(this.$router.currentRoute.name)
      if (val === 'logout') {
        this.Logout()
      } else if (val === 'purchaser' || val === 'ext_buyer') {
        var data = {
          role: val === 'purchaser' ? 'purchaser' : 'ext_buyer'
        }
        val === 'ext_buyer' ? this.changeRole = 'ext_buyer' : this.changeRole = 'purchaser'
        localStorage.setItem('roleUser', JSON.stringify(data))
        this.$EventBus.$emit('getCartPopOver')
        this.$EventBus.$emit('getCart')
        this.$EventBus.$emit('getHomepageItems')
        this.$EventBus.$emit('getProductDetail')
        this.$EventBus.$emit('getAllNewProduct')
        this.$EventBus.$emit('getAllBestSeller')
        this.$EventBus.$emit('getResultSearch')
        this.$EventBus.$emit('getSellerShopPage')
        // this.reloadPage()
      } else if (val === 'admin') {
        if (this.userdetail.business_id !== null && this.userdetail.seller_shop_admin !== '0') {
          window.location.assign(`http://203.151.19.240:8088/?business=${this.userdetail.business_id}&token=${this.onedata.user.access_token}&role=seller_admin`)
        } else if (this.userdetail.business_id === null && this.userdetail.admin !== '0') {
          window.location.assign(`http://203.151.19.240:8088/?company=${this.userdetail.company_id}&token=${this.onedata.user.access_token}&role=admin`)
        } else {
          const Toast = this.$swal.mixin({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true
          })
          Toast.fire({
            icon: 'warning',
            title: 'ไม่มี Business ID',
            text: 'กรุณาสร้างบริษัท'
          })
        }
      } else if (val !== this.$router.currentRoute.name) {
        this.$router.push({ path: `/${val}` }).catch(() => {})
      }
      // else if (val.name === 'ร้านค้า') {
      //   window.open(`/${val.path}`)
      // }
    },
    // reloadPage () {
    //   window.location.reload()
    // },
    Logout () {
      localStorage.removeItem('roleUser')
      localStorage.removeItem('oneData')
      window.location.assign(`${process.env.VUE_APP_DOMAIN}`)
    },
    closeModalSuccess () {
      this.visible = false
    },
    openModalSuccess () {
      this.visible = true
    },
    async getUserDetail () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('actionsUserDetailPage', data)
      var response = await this.$store.state.ModuleUser.stateUserDetailPage
      // console.log('user Detail====>', response.data[0])
      this.userdetail = response.data[0].permissions
      this.haveBusinessID = this.userdetail.business_id
      // console.log(this.userdetail, this.haveBusinessID)
    }
    // checkPermission () {
    //   if (this.onedata.user.current_role_user !== undefined) {
    //     var role = this.onedata.user.current_role_user
    //     if (role.seller_admin) {
    //       this.ListNavbar = ListNavBar.ListNavbar_3
    //     } else if (role.purchaser) {
    //       this.ListNavbar = ListNavBar.ListNavbar_2
    //     } else if (role.ext_buyer) {
    //       this.ListNavbar = ListNavBar.ListNavbar_1
    //     }
    //   }
    // }
  }
}
</script>

<style scoped>
.ant-popover-inner-content{
padding: 0;
}
</style>
