<template>
  <div>
    <v-breadcrumbs :items="items" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
      <template v-slot:divider>
        <v-icon color="#3EC6B6">mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }" >
        <v-breadcrumbs-item
          href=""
          :disabled="item.disabled"
          @click="gotoBannerPage(item)"
        >
          <span :style="{ color: item.color, cursor: item.disabled !== true ? 'pointer' : 'none', fontSize: '16px' }">{{ item.category_name }}</span>
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <v-container :style="MobileSize ? 'max-width: 100% !important;' : 'max-width: 1400px !important;'" :class="IpadSize ? 'px-2' : ''">
      <v-overlay :value="overlay2">
        <v-progress-circular indeterminate size="64"></v-progress-circular>
      </v-overlay>
      <v-row dense align-content="center" justify="center" v-if="this.typeProduct === 'flash_sale'" class="mt-5 mb-3" :class="MobileSize ? 'px-0' : ''">
        <v-img
         v-if="bannerFlashSale === ''"
         :src="!MobileSize ? require('@/assets/defaultFlashSale.png') : require('@/assets/defaultFlashSaleMobile.png')"
         :style="MobileSize ? 'border-radius: 12px;' : 'border-radius: 16px;'"
         :max-height="MobileSize ? '48px' : '121px'"
         :max-width="IpadProSize ? '100%' : MobileSize ? '100%' : '1400px'"
         :width="IpadProSize ? '100%' : MobileSize ? '100%' : '1400px'"
         height="100%"
        >
          <div class="d-flex" style="align-content: center;" :style="MobileSize ? 'max-height: 48px; height: 48px;' : 'max-height: 121px; height: 121px;'">
            <!-- <img v-if="logoImage === ''" src="@/assets/ImageINET-Marketplace/ICONShop/flashSaleIcon.png" :width="MobileSize ? '80px' : '232px'" height="100%" :style="MobileSize ? 'top: 0px;' : 'top: 5px; left: 20px;'" style="max-height: 111px; display: flex; justify-content: center; text-align: center; position: absolute;" alt="flashsalelogo">
            <img v-if="logoImage !== ''" :src="logoImage" :width="MobileSize ? '80px' : '232px'" height="100%" :style="MobileSize ? 'top: 0px;' : 'top: 5px; left: 20px;'" style="max-height: 111px; display: flex; justify-content: center; text-align: center; position: absolute;" alt="flashsalelogo"> -->
            <img v-if="logoImage === ''" src="@/assets/ImageINET-Marketplace/ICONShop/flashSaleIcon.png" class="mr-auto my-auto" :width="MobileSize ? '75px' : '232px'" :height="MobileSize ? '42px' : '111px'" :style="MobileSize ? 'max-height: 48px;' : 'max-height: 111px;'" style="display: flex; align-content: center;" alt="flashsalelogo">
            <img v-if="logoImage !== ''" :src="logoImage" class="mr-auto" :width="MobileSize ? '75px' : '232px'" :height="MobileSize ? '42px' : '111px'" :style="MobileSize ? 'max-height: 48px;' : 'max-height: 111px;'" style="display: flex;" alt="flashsalelogo">
            <!-- <div :class="MobileSize ? 'countdown-wrapper-mobile' : 'countdown-wrapper'" class="ml-auto">
              <span :class="MobileSize ? 'label-mobile' : 'label'">สิ้นสุดใน</span>
              <div :class="MobileSize ? 'time-box-mobile' : 'time-box'">{{ hours }}</div>
              <span :class="MobileSize ? 'colon-mobile' : 'colon'">:</span>
              <div :class="MobileSize ? 'time-box-mobile' : 'time-box'">{{ minutes }}</div>
              <span :class="MobileSize ? 'colon-mobile' : 'colon'">:</span>
              <div :class="MobileSize ? 'time-box-mobile' : 'time-box'">{{ seconds }}</div>
            </div> -->
          </div>
        </v-img>
        <v-img
         v-else
         loading="lazy"
         :src="bannerFlashSale"
         :style="MobileSize ? 'border-radius: 12px;' : 'border-radius: 16px;'"
         :max-height="MobileSize ? '48px' : '121px'"
         :max-width="IpadProSize ? '100%' : MobileSize ? '100%' : '1400px'"
         :width="IpadProSize ? '100%' : MobileSize ? '100%' : '1400px'"
         height="100%"
        >
          <!-- <img v-if="logoImage === ''" src="@/assets/ImageINET-Marketplace/ICONShop/flashSaleIcon.png" :width="MobileSize ? '140px' : '232px'" height="100%" style="max-height: 111px; display: flex; justify-content: center; text-align: center; position: absolute; top: 5px; left: 20px;" alt="flashsalelogo">
          <img v-if="logoImage !== ''" :src="logoImage" :width="MobileSize ? '140px' : '232px'" height="100%" style="max-height: 111px; display: flex; justify-content: center; text-align: center; position: absolute; top: 5px; left: 20px;" alt="flashsalelogo"> -->
          <div class="d-flex" style="align-content: center;" :style="MobileSize ? 'max-height: 48px; height: 48px;' : 'max-height: 121px; height: 121px;'">
            <!-- <img v-if="logoImage === ''" src="@/assets/ImageINET-Marketplace/ICONShop/flashSaleIcon.png" :width="MobileSize ? '80px' : '232px'" height="100%" :style="MobileSize ? 'top: 0px;' : 'top: 5px; left: 20px;'" style="max-height: 111px; display: flex; justify-content: center; text-align: center; position: absolute;" alt="flashsalelogo">
            <img v-if="logoImage !== ''" :src="logoImage" :width="MobileSize ? '80px' : '232px'" height="100%" :style="MobileSize ? 'top: 0px;' : 'top: 5px; left: 20px;'" style="max-height: 111px; display: flex; justify-content: center; text-align: center; position: absolute;" alt="flashsalelogo"> -->
            <img v-if="logoImage === ''" src="@/assets/ImageINET-Marketplace/ICONShop/flashSaleIcon.png" class="mr-auto my-auto" :width="MobileSize ? '75px' : '232px'" :height="MobileSize ? '42px' : '111px'" :style="MobileSize ? 'max-height: 48px;' : 'max-height: 111px;'" style="display: flex; align-content: center;" alt="flashsalelogo">
            <img v-if="logoImage !== ''" :src="logoImage" class="mr-auto" :width="MobileSize ? '75px' : '232px'" :height="MobileSize ? '42px' : '111px'" :style="MobileSize ? 'max-height: 48px;' : 'max-height: 111px;'" style="display: flex;" alt="flashsalelogo">
            <div :class="MobileSize ? 'countdown-wrapper-mobile' : 'countdown-wrapper'" class="ml-auto">
              <span :class="MobileSize ? 'label-mobile' : 'label'">สิ้นสุดใน</span>
              <div :class="MobileSize ? 'time-box-mobile' : 'time-box'">{{ hours }}</div>
              <span :class="MobileSize ? 'colon-mobile' : 'colon'">:</span>
              <div :class="MobileSize ? 'time-box-mobile' : 'time-box'">{{ minutes }}</div>
              <span :class="MobileSize ? 'colon-mobile' : 'colon'">:</span>
              <div :class="MobileSize ? 'time-box-mobile' : 'time-box'">{{ seconds }}</div>
            </div>
          </div>
        </v-img>
      </v-row>
      <v-row dense align-content="center" justify="center" v-if="typeProduct !== 'flash_sale'">
        <v-col cols="12" md="12" xs="12">
          <v-row dense justify="center">
            <h2 v-if="header === this.$t('Headers.ProductText.Popular')" class="pt-3 fontHeaderListProduct gradient-underline" :style="MobileSize ? 'font-size: 24px !important;' : ''">{{headerCategory}}</h2>
            <h2 v-else class="pt-3 fontHeaderListProduct gradient-underline" :style="MobileSize ? 'font-size: 20px !important;' : ''">{{header}}</h2>
          </v-row>
        </v-col>
      </v-row>
      <!-- filter หมวดหมู่ และ ราคา -->
      <v-row dense class="mt-3" v-if="MobileSize">
        <v-col cols="12" :md="IpadProSize ? '4' : '3'" sm="auto" class="d-flex align-center">
          <!-- หมวดหมู่ -->
          <v-col cols="4">
            <span class="fontFilter" style="white-space: nowrap;">{{ $t('Headers.Detail.Categories') }} : </span>
          </v-col>
          <v-col cols="8">
            <v-select v-model="filterCategory" :items="itemsCategory" item-text="category_name" item-value="hierachy" @change="changeCategory(itemsCategory, filterCategory)" outlined dense hide-details style="border: 1px solid #EBEEF2 !important; border-radius: 8px;" :style="MobileSize ? '' : 'max-width: 200px; max-height: 48px;'" :no-data-text="this.$t('Headers.ProductText.NoCategory')" append-icon="mdi-chevron-down" class="custom-select-icon"></v-select>
          </v-col>
        </v-col>
        <v-col cols="12" md="3" sm="auto" class="d-flex align-center" style="margin-top: -2vh;">
          <!-- ราคา -->
          <v-col cols="4">
            <span class="fontFilter">{{ $t('Headers.Detail.Price') }} : </span>
          </v-col>
          <v-col cols="8">
            <v-select v-model="filterPrice" :items="itemPrice" item-text="text" item-value="value" @change="ADCDESC(filterCategory)" outlined dense hide-details style="border: 1px solid #EBEEF2 !important; border-radius: 8px;" :style="MobileSize ? '' : 'max-width: 140px; max-height: 48px;'" no-data-text="ไม่มีช่วงราคา" append-icon="mdi-chevron-down" class="custom-select-icon"></v-select>
          </v-col>
        </v-col>
      </v-row>
      <v-row dense :class="!MobileSize ? 'mt-3' : ''" v-else>
        <v-col cols="12" :md="IpadProSize ? '4' : '3'" sm="auto" class="d-flex">
          <!-- หมวดหมู่ -->
          <span class="pr-4 fontFilter" style="white-space: nowrap">{{ $t('Headers.Detail.Categories') }} : </span>
          <v-select v-model="filterCategory" :items="itemsCategory" item-text="category_name" item-value="hierachy" @change="changeCategory(itemsCategory, filterCategory)" outlined dense hide-details style="border: 1px solid #EBEEF2 !important; border-radius: 8px;" :style="MobileSize ? '' : 'max-width: 200px; max-height: 48px;'" :no-data-text="this.$t('Headers.ProductText.NoCategory')" append-icon="mdi-chevron-down" class="custom-select-icon"></v-select>
        </v-col>
        <v-col cols="12" md="3" sm="auto" class="d-flex">
          <!-- ราคา -->
          <span class="px-4 fontFilter">{{ $t('Headers.Detail.Price') }} : </span>
          <v-select v-model="filterPrice" :items="itemPrice" item-text="text" item-value="value" @change="ADCDESC(filterCategory)" outlined dense hide-details style="border: 1px solid #EBEEF2 !important; border-radius: 8px;" :style="MobileSize ? '' : 'max-width: 140px; max-height: 48px;'" no-data-text="ไม่มีช่วงราคา" append-icon="mdi-chevron-down" class="custom-select-icon"></v-select>
        </v-col>
      </v-row>
      <div v-if="showSkeletonLoader === true">
        <v-row dense class="pt-12">
          <v-col cols="6" :md="IpadProSize ? '3' : '2'" sm="3" xs="4" v-for="item in 6" :key="item">
            <v-skeleton-loader
              type="image, list-item-two-line"
            ></v-skeleton-loader>
          </v-col>
        </v-row>
      </div>
      <div v-else>
        <v-row class="pt-10" no-gutters justify="center" v-if="AllProduct.length === 0">{{ $t('Headers.ProductText.NoProduct') }}</v-row>
        <v-row justify="start" class="pt-12" v-if="AllProduct.length !== 0 && !MobileSize && !IpadSize">
          <v-col cols="6" style="display: flex; justify-content: center;" :md="IpadProSize ? '3' : '2'" sm="3" v-for="(item, index) in paginated" :key="index">
            <CardProducts :itemProduct='item' v-if="typeProduct !== 'flash_sale'" />
            <CardProductsFlashSale :itemProduct='item' v-else />
          </v-col>
        </v-row>
        <v-row justify="start" dense class="pt-12 px-0" :class="typeProduct !== 'flash_sale' ? '' : 'setStyleListFlashSale'"  v-if="AllProduct.length !== 0 && !MobileSize && IpadSize">
          <v-col cols="12" :md="IpadProSize ? '3' : '2'" sm="3" xs="6" v-for="(item, index) in paginated" :key="index" style="display: flex; justify-content: center;" class="px-0">
            <CardProductsResponsive :itemProduct='item' v-if="typeProduct !== 'flash_sale'" />
            <CardProductsFlashSale :itemProduct='item' v-else />
          </v-col>
        </v-row>
        <v-row justify="start" class="pt-4" v-if="AllProduct.length !== 0 && MobileSize && !IpadSize" :class="MobileSize ? 'px-1' : ''" >
          <v-col cols="6" md="6" sm="6" xs="6" v-for="(item, index) in paginated" :key="index" :class="MobileSize ? 'px-0' : ''" style="display: flex; justify-content: center;">
            <CardProductsResponsive :itemProduct='item' v-if="typeProduct !== 'flash_sale'" />
            <CardProductsFlashSale :itemProduct='item' v-else />
          </v-col>
        </v-row>
        <v-row justify="center" class="my-6">
          <v-pagination
          color="#3EC6B6"
          v-model="pageNumber"
          :length="pageMax"
          :total-visible="MobileSize ? 5 : 7"
          class="paginationStyle"
          @input="pageChange($event)"
          ></v-pagination>
        </v-row>
      </div>
      <!-- <a-row type="flex" :gutter="[16, 8]">
        <a-col :span="24" style="text-align:center">
          <span class="display-1">{{header}}</span>
        </a-col>
        <a-col :span="24">
          <span style="font-weight: bold;">ค้นพบสินค้า {{ productCount }} รายการสำหรับ "{{ header }}"</span>
        </a-col>
        <a-col :span="24" style="padding: 0; margin-top: -20px;">
          <a-divider></a-divider>
        </a-col>
        <div v-if="AllProduct.length !== 0">
          <a-col :span="24" :md="4" :sm="12" :xs="24" v-for="(item,index) in AllProduct" :key="index">
            <CardProducts :itemProduct="item" />
          </a-col>
        </div>
        <div v-else>
          <h2>ยังไม่มีรายการสินค้า{{ header }}</h2>
        </div>
      </a-row> -->
    </v-container>
  </div>
</template>

<script>
import { Decode } from '@/services'
const FakeData = []
for (let i = 0; i < 48; i++) {
  FakeData.push({
    product_id: i,
    name: `Data Title newArrivals ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
export default {
  components: {
    CardProducts: () => import(/* webpackPrefetch: true */ '@/components/Card/ProductCardUI'),
    CardProductsResponsive: () => import(/* webpackPrefetch: true */ '@/components/Card/ProductCardResponsive'),
    CardProductsFlashSale: () => import(/* webpackPrefetch: true */ '@/components/Shop/ManageFlashSale/FlashSaleItem/CardFlashSaleHome')
  },
  data () {
    return {
      limit: 48,
      category: false,
      // headerId: 1,
      checkPageCategory: false,
      RowUserData: '',
      pathShopSale: '',
      header: '',
      headerCategory: '',
      valueID: '',
      FakeData,
      overlay2: false,
      productCount: null,
      AllProduct: [],
      pageMax: null,
      current: 1,
      pageSize: 48,
      shopID: null,
      hierachy: '',
      typeproduct: '',
      dataRole: '',
      editPage: '',
      items: [
        {
          category_name: this.$t('Headers.Home'),
          disabled: false,
          color: '#636363',
          href: '/'
        }
      ],
      showSkeletonLoader: false,
      bannerFlashSale: '',
      logoImage: '',
      statusFlashSale: '',
      filterCategory: '',
      filterPrice: '',
      itemPrice: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'ราคา: จากน้อยไปมาก', value: 'ASC' },
        { text: 'ราคา: จากมากไปน้อย', value: 'DESC' }
      ],
      hours: '00',
      minutes: '00',
      seconds: '00',
      // targetTime: new Date(new Date().getTime() + 2 * 3600 * 1000 + 12 * 60 * 1000 + 42 * 1000),
      targetTime: '',
      timer: null
    }
  },
  watch: {
    async $route (to, from) {
      var getIDToParams = to.query.page
      var getIDFromParams = from.query.page

      if (getIDFromParams !== undefined && getIDToParams !== undefined) {
        if (parseInt(getIDToParams) !== parseInt(getIDFromParams)) {
          const newPage = parseInt(getIDToParams)

          // ตรวจสอบว่า pageNumber ถูกต้องก่อนเรียก pageChange()
          if (newPage !== this.pageNumber) {
            this.pageNumber = newPage
            this.pageChange(newPage)
          }
        }
      }
    }
  },
  async created () {
    // console.log('header------>', this.header)
    // if (this.header === '') {
    //   console.log('header ว่าง')
    // }
    // var value = this.$router.currentRoute.params.data
    var val = this.$route.query.id
    this.valueID = val
    if (this.$route.query.page !== undefined) {
      this.pageNumber = parseInt(this.$route.query.page)
    }
    // console.log('$route5555', this.limit)
    // console.log('value', val, value.id, value.split('&')[0], value.split('=')[1])
    this.showSkeletonLoader = true
    this.oneData = []
    if (localStorage.getItem('roleUser') !== null) {
      this.dataR = JSON.parse(localStorage.getItem('roleUser')).role
    } else {
      this.dataR = 'ext_buyer'
    }
    if (this.dataR === 'sale_order' || this.dataR === 'sale_order_no_JV') {
      this.pathShopSale = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale'))).path
      // console.log('pathShop', this.pathShopSale)
    }
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (localStorage.getItem('roleUser') !== null) {
        this.RowUserData = JSON.parse(localStorage.getItem('roleUser')).role
      }
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.companyId = localStorage.getItem('PartnerID')
        this.pathShopSale = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale'))).path
      } else {
        this.companyId = ''
      }
    }
    this.$EventBus.$emit('getPath')
    // console.log(this.$router.currentRoute.params)
    // console.log('test=======>3', this.$route)
    // this.headerId = this.$route.query.page
    this.typeProduct = this.$route.params.data
    // console.log('this.typeProduct', this.typeProduct)
    // this.pageNumber = parseInt(this.$route.query.pageNumber)
    await this.getAllCategory()
    if (this.$router.currentRoute.query.id === undefined) {
      if (this.typeProduct === 'new_product' | this.typeProduct === 'new_product_landing') {
        this.header = this.$t('Headers.ProductText.NewProducts')
        if (this.RowUserData === 'sale_order') {
          this.items.push({
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          })
        } else {
          this.items.push({
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          })
        }
        // this.$EventBus.$on('getAllNewProduct', this.getAllNewProduct)
        this.getAllNewProduct()
      } else if (this.typeProduct === 'best_seller' || this.typeProduct === 'best_seller_landing') {
        this.header = this.$t('Headers.ProductText.BestSeller')
        if (this.RowUserData === 'sale_order') {
          this.items.push({
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          })
        } else {
          this.items.push({
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          })
        }
        await this.getAllBestSeller()
      } else if (this.typeProduct === 'recommended_product' || this.typeProduct === 'recommended_product_for_you' || this.typeProduct === 'recommended_product_landing') {
        if (this.typeProduct === 'recommended_product_for_you') {
          this.header = this.$t('Headers.RecommendedForYou')
        } else {
          this.header = this.$t('Headers.RecommendedProducts')
        }
        if (this.RowUserData === 'sale_order') {
          this.items.push({
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          })
        } else {
          this.items.push({
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          })
        }
        // this.$EventBus.$on('getAllRecProductShop', this.getAllRecProductShop)
        this.shopID = localStorage.getItem('shopID')
        this.getAllRecProductShop()
      } else if (this.typeProduct === 'flash_sale') {
        this.header = this.$t('Headers.ProductText.FlashSale')
        if (this.RowUserData === 'sale_order') {
          this.items.push({
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          })
        } else {
          this.items.push({
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          })
        }
        // console.log('สินค้าแนะนำ')
        // this.$EventBus.$on('getAllRecProductShop', this.getAllRecProductShop)
        await this.StatusFlashSale()
        this.GetBanner()
        if (this.statusFlashSale === 'yes') {
          this.getAllFlashSaleSystem()
        } else if (this.statusFlashSale === 'no') {
          this.getAllFlashSale()
        }
        // await this.getAllFlashSaleSystem()
        this.$EventBus.$on('getAllProductFlashSale', this.getAllFlashSaleSystem)
      } else if (this.typeProduct === 'all_product' || this.typeProduct === 'same_shop' || this.typeProduct === 'all_product_cat') {
        if (this.typeProduct === 'same_shop') {
          this.header = this.$t('Headers.ProductText.SameShop')
        } else {
          this.header = this.$t('Headers.ProductText.All')
        }
        if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
          // console.log('sale_order', this.pathShopSale)
          // this.items.push({
          //   category_name: this.header,
          //   disabled: true,
          //   color: '#3EC6B6',
          //   href: this.pathShopSale
          // })
          this.items = [
            {
              category_name: this.$t('Headers.Home'),
              disabled: false,
              color: '#636363',
              href: this.pathShopSale
            },
            {
              category_name: this.header,
              disabled: true,
              color: '#3EC6B6',
              href: '/'
            }
          ]
        } else {
          this.items.push({
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          })
        }
        // this.$EventBus.$on('getProductSameShop', this.getProductSameShop)
        this.getProductSameShop()
      } else if (this.typeProduct === this.$t('Headers.ProductText.SameCategory') || this.typeProduct === 'similar_products') {
        if (this.typeProduct === 'similar_products') {
          this.header = this.$t('Headers.ProductText.RelatedCategory')
        } else {
          this.header = this.$t('Headers.ProductText.SameCategory')
        }
        this.hierachy = localStorage.getItem('hierachy')
        if (this.RowUserData === 'sale_order') {
          this.items.push({
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          })
        } else {
          this.items.push({
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          })
        }
        // this.$EventBus.$on('getAllProductCategoryDetail', this.getAllProductCategoryDetail)
        this.getAllProductCategoryDetail()
      // } else if (this.typeProduct === 'all_product') {
      //   this.header = 'สินค้าทั้งหมด'
      //   this.items.push({
      //     category_name: this.header,
      //     disabled: true,
      //     color: '#3EC6B6',
      //     href: '/'
      //   })
      //   this.getAllProductInWeb()
      } else if (this.typeProduct === 'groupshop') {
        var checkGroupshopName = localStorage.getItem('groupshop_name')
        var checkGroupshopID = localStorage.getItem('groupshop_id')
        if (checkGroupshopName === null || checkGroupshopID === null) {
          this.$router.push('/allGroupShop?page=1').catch(() => {})
        } else {
          this.header = this.$t('Headers.ProductText.Product') + JSON.parse(Decode.decode(localStorage.getItem('groupshop_name')))
          if (this.RowUserData === 'sale_order') {
            this.items.push({
              category_name: this.header,
              disabled: true,
              color: '#3EC6B6',
              href: this.pathShopSale
            })
          } else {
            this.items.push({
              category_name: this.header,
              disabled: true,
              color: '#3EC6B6',
              href: '/'
            })
          }
          this.getProductGroupShop()
          this.$EventBus.$on('getProductGroupShop', this.getProductGroupShop)
        }
      } else {
        if (this.RowUserData === 'sale_order') {
          this.items.push(
            {
              category_name: this.$t('Headers.Detail.Category'),
              disabled: true,
              color: '#636363',
              href: this.pathShopSale
            },
            {
              category_name: this.header,
              disabled: true,
              color: '#3EC6B6',
              href: this.pathShopSale
            })
        } else {
          this.items.push(
            {
              category_name: this.$t('Headers.Detail.Category'),
              disabled: true,
              color: '#636363',
              href: '/'
            },
            {
              category_name: this.header,
              disabled: true,
              color: '#3EC6B6',
              href: '/'
            }
          )
        }
        // this.$EventBus.$on('getAllProductCategory', this.getAllProductCategory)
        this.getAllProductCategory()
      }
    } else {
      // console.log('this.$route.params.data', this.$route)
      if (this.typeProduct === 'popular_product') {
        this.idCat = this.$route.query.id
        this.category = true
        this.header = this.$t('Headers.ProductText.Popular')
        if (this.RowUserData === 'sale_order') {
          this.items.push({
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          })
        } else {
          this.items.push({
            category_name: this.header,
            disabled: true,
            color: '#636363',
            href: '/'
          })
        }
        // this.$EventBus.$on('getAllPopularProduct', this.getAllPopularProduct)
        this.getAllPopularProduct()
      } else {
        this.idCat = this.$route.query.id
        this.category = true
        var decodeURL = decodeURIComponent(this.$route.params.data)
        this.header = decodeURL
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: this.pathShopSale
        })
        // this.$EventBus.$on('getProductCategoryByID', this.getProductCategoryByID)
        this.getProductCategoryByID()
      }
    }
  },
  // mounted () {
  //   this.$EventBus.$on('getAllNewProduct', this.getAllNewProduct)
  //   this.$EventBus.$on('getAllBestSeller', this.getAllBestSeller)
  //   this.$on('hook:beforeDestroy', () => {
  //     this.$EventBus.$off('getAllNewProduct')
  //     this.$EventBus.$off('getAllBestSeller')
  //   })
  // },
  mounted () {
    window.scrollTo(0, 0)
    this.updateTimer()
    this.timer = setInterval(this.updateTimer, 1000)
  },
  beforeDestroy () {
    clearInterval(this.timer)
  },
  // computed: {
  //   pageNumber: {
  //     get () {
  //       return parseInt(this.$route.query.pageNumber) || 1
  //     },
  //     set (newPage) {
  //       this.$router.push(`/ListProductUI/${this.header}?pageNumber=${newPage}`).catch(() => {})
  //     }
  //   }
  // },
  computed: {
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      // console.log('this.AllProduct', this.AllProduct.slice(this.indexStart, this.indexEnd))
      // return this.AllProduct.slice(this.indexStart, this.indexEnd)
      return this.AllProduct
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    updateTimer () {
      const now = new Date()
      const diff = Math.max(0, this.targetTime - now)

      const h = Math.floor(diff / (1000 * 60 * 60))
      const m = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
      const s = Math.floor((diff % (1000 * 60)) / 1000)

      this.hours = String(h).padStart(2, '0')
      this.minutes = String(m).padStart(2, '0')
      this.seconds = String(s).padStart(2, '0')
    },
    async ADCDESC (hierachy) {
      if (this.typeProduct === 'all_product' || this.typeProduct === 'same_shop' || this.typeProduct === 'all_product_cat') {
        this.getProductSameShop()
      } else if (this.typeProduct === 'new_product' | this.typeProduct === 'new_product_landing') {
        this.getAllNewProduct()
      } else if (this.typeProduct === 'best_seller' || this.typeProduct === 'best_seller_landing') {
        this.getAllBestSeller()
      } else if (this.typeProduct === 'similar_products') {
        this.getAllProductCategoryDetail()
      } else if (this.typeProduct === 'popular_product') {
        this.getAllPopularProduct()
      } else if (this.typeProduct === 'recommended_product' || this.typeProduct === 'recommended_product_for_you' || this.typeProduct === 'recommended_product_landing') {
        this.getAllRecProductShop()
      } else if (this.typeProduct === 'groupshop') {
        this.getProductGroupShop()
      } else if (this.typeProduct === 'flash_sale') {
        this.GetBanner()
        if (this.statusFlashSale === 'yes') {
          this.getAllFlashSaleSystem()
        } else if (this.statusFlashSale === 'no') {
          this.getAllFlashSale()
        }
      } else {
        this.getProductCategoryByID()
      }
    },
    async changeCategory (item, hierachy) {
      if (this.typeProduct === 'flash_sale') {
        if (this.statusFlashSale === 'yes') {
          await this.getAllFlashSaleSystem()
        } else if (this.statusFlashSale === 'no') {
          await this.getAllFlashSale()
        }
      } else if (this.typeProduct === 'new_product' | this.typeProduct === 'new_product_landing') {
        this.header = this.$t('Headers.ProductText.NewProducts')
        if (this.RowUserData === 'sale_order') {
          this.items = [
            {
              category_name: this.$t('Headers.Home'),
              disabled: false,
              color: '#636363',
              href: this.pathShopSale
            },
            {
              category_name: this.header,
              disabled: true,
              color: '#3EC6B6',
              href: '/'
            }
          ]
        } else {
          this.items = [
            {
              category_name: this.$t('Headers.Home'),
              disabled: false,
              color: '#636363',
              href: '/'
            },
            {
              category_name: this.header,
              disabled: true,
              color: '#3EC6B6',
              href: '/'
            }
          ]
        }
        await this.getAllNewProduct()
      } else if (this.typeProduct === 'best_seller' || this.typeProduct === 'best_seller_landing') {
        this.header = this.$t('Headers.ProductText.BestSeller')
        if (this.RowUserData === 'sale_order') {
          this.items = [
            {
              category_name: this.$t('Headers.Home'),
              disabled: false,
              color: '#636363',
              href: this.pathShopSale
            },
            {
              category_name: this.header,
              disabled: true,
              color: '#3EC6B6',
              href: '/'
            }
          ]
        } else {
          this.items = [
            {
              category_name: this.$t('Headers.Home'),
              disabled: false,
              color: '#636363',
              href: '/'
            },
            {
              category_name: this.header,
              disabled: true,
              color: '#3EC6B6',
              href: '/'
            }
          ]
        }
        await this.getAllBestSeller()
      } else if (this.typeProduct === 'recommended_product' || this.typeProduct === 'recommended_product_for_you' || this.typeProduct === 'recommended_product_landing') {
        if (this.typeProduct === 'recommended_product_for_you') {
          this.header = this.$t('Headers.RecommendedForYou')
        } else {
          var nameCat = item.filter(item => item.hierachy === hierachy)
          this.header = nameCat[0].category_name
        }
        if (this.RowUserData === 'sale_order') {
          this.items = [
            {
              category_name: this.$t('Headers.Home'),
              disabled: false,
              color: '#636363',
              href: this.pathShopSale
            },
            {
              category_name: this.header,
              disabled: true,
              color: '#3EC6B6',
              href: '/'
            }
          ]
        } else {
          this.items = [
            {
              category_name: this.$t('Headers.Home'),
              disabled: false,
              color: '#636363',
              href: '/'
            },
            {
              category_name: this.header,
              disabled: true,
              color: '#3EC6B6',
              href: '/'
            }
          ]
        }
        this.shopID = localStorage.getItem('shopID')
        await this.getAllRecProductShop()
      } else if (this.typeProduct === this.$t('Headers.ProductText.SameCategory') || this.typeProduct === 'similar_products') {
        if (this.typeProduct === 'similar_products') {
          this.header = this.$t('Headers.ProductText.RelatedCategory')
        } else {
          this.header = this.$t('Headers.ProductText.SameCategory')
        }
        this.hierachy = localStorage.getItem('hierachy')
        if (this.RowUserData === 'sale_order') {
          this.items = [
            {
              category_name: this.$t('Headers.Home'),
              disabled: false,
              color: '#636363',
              href: this.pathShopSale
            },
            {
              category_name: this.header,
              disabled: true,
              color: '#3EC6B6',
              href: '/'
            }
          ]
        } else {
          this.items = [
            {
              category_name: this.$t('Headers.Home'),
              disabled: false,
              color: '#636363',
              href: '/'
            },
            {
              category_name: this.header,
              disabled: true,
              color: '#3EC6B6',
              href: '/'
            }
          ]
        }
        await this.getAllProductCategoryDetail()
      } else if (this.typeProduct === 'groupshop') {
      } else if (this.typeProduct === 'same_shop') {
        this.header = this.$t('Headers.ProductText.SameShop')
        if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
          this.items = [
            {
              category_name: this.$t('Headers.Home'),
              disabled: false,
              color: '#636363',
              href: this.pathShopSale
            },
            {
              category_name: this.header,
              disabled: true,
              color: '#3EC6B6',
              href: '/'
            }
          ]
        } else {
          this.items = [
            {
              category_name: this.$t('Headers.Home'),
              disabled: false,
              color: '#636363',
              href: '/'
            },
            {
              category_name: this.header,
              disabled: true,
              color: '#3EC6B6',
              href: '/'
            }
          ]
        }
        await this.getProductSameShop()
      } else {
        var nameCategory = ''
        var fullPath = ''
        if (hierachy !== '') {
          nameCategory = item.filter(item => item.hierachy === hierachy)
          const encodedEvent = encodeURIComponent(nameCategory[0].category_name)
          fullPath = `/ListProduct/${encodedEvent}?id=${hierachy}&page=1`
          this.$router.push({ path: `${fullPath}` }).catch(() => {})
          this.idCat = this.$route.query.id
          this.category = true
          var decodeURL = decodeURIComponent(this.$route.params.data)
          this.header = decodeURL
          if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
            this.items = [
              {
                category_name: this.$t('Headers.Home'),
                disabled: false,
                color: '#636363',
                href: this.pathShopSale
              },
              {
                category_name: this.header,
                disabled: true,
                color: '#3EC6B6',
                href: '/'
              }
            ]
          } else {
            this.items = [
              {
                category_name: this.$t('Headers.Home'),
                disabled: false,
                color: '#636363',
                href: '/'
              },
              {
                category_name: this.header,
                disabled: true,
                color: '#3EC6B6',
                href: '/'
              }
            ]
          }
          await this.getProductCategoryByID()
        } else {
          nameCategory = 'all_product_cat'
          fullPath = `/ListProduct/${nameCategory}?page=1`
          this.$router.push({ path: `${fullPath}` }).catch(() => {})
          this.header = this.$t('Headers.ProductText.All')
          this.items = []
          if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
            this.items = [
              {
                category_name: this.$t('Headers.Home'),
                disabled: false,
                color: '#636363',
                href: this.pathShopSale
              },
              {
                category_name: this.header,
                disabled: true,
                color: '#3EC6B6',
                href: '/'
              }
            ]
          } else {
            this.items = [
              {
                category_name: this.$t('Headers.Home'),
                disabled: false,
                color: '#636363',
                href: '/'
              },
              {
                category_name: this.header,
                disabled: true,
                color: '#3EC6B6',
                href: '/'
              }
            ]
          }
          await this.getProductSameShop()
        }
      }
    },
    async getAllCategory () {
      var data = 'all'
      this.itemsCategory = []
      await this.$store.dispatch('actionsGetCategory', data)
      var response = await this.$store.state.ModuleHompage.stateGetCategory
      if (response.message === 'Get all category success.') {
        this.itemsCategory = response.data
        this.itemsCategory.unshift({
          category_logo_path: '',
          category_name: 'หมวดหมู่ทั้งหมด',
          hierachy: '',
          id: ''
        })
      }
      await this.getProductSameShop()
    },
    async pageChange (val) {
      // console.log('===>33', this.typeProduct)
      val = this.pageNumber
      var encodeURI = encodeURIComponent(this.typeProduct)
      // console.log('val===>', this.$route)
      // console.log('val===>', this.$route)
      if (this.category === true) {
        // console.log('')
        this.$router.push(`/ListProduct/${encodeURI}?id=${this.idCat}&page=${val}`).catch(() => {})
      } else {
        this.$router.push(`/ListProduct/${this.typeProduct}?page=${val}`).catch(() => {})
      }
      if (this.typeProduct === 'all_product' || this.typeProduct === 'same_shop' || this.typeProduct === 'all_product_cat') {
        // if (val === this.pageNumber) {
        //   if (this.editPage === '') {
        //     this.getProductSameShop()
        //   } else if (this.editPage === 'api success') {
        //     this.editPage = ''
        //   }
        // } else {
        //   this.getProductSameShop()
        // }
        this.getProductSameShop()
      } else if (this.typeProduct === 'new_product' | this.typeProduct === 'new_product_landing') {
        this.getAllNewProduct()
      } else if (this.typeProduct === 'best_seller' || this.typeProduct === 'best_seller_landing') {
        this.getAllBestSeller()
      } else if (this.typeProduct === 'similar_products') {
        this.getAllProductCategoryDetail()
      } else if (this.typeProduct === 'popular_product') {
        this.getAllPopularProduct()
      } else if (this.typeProduct === 'recommended_product' || this.typeProduct === 'recommended_product_for_you' || this.typeProduct === 'recommended_product_landing') {
        this.getAllRecProductShop()
      } else if (this.typeProduct === 'groupshop') {
        this.getProductGroupShop()
      } else if (this.typeProduct === 'flash_sale') {
        this.GetBanner()
        if (this.statusFlashSale === 'yes') {
          this.getAllFlashSaleSystem()
        } else if (this.statusFlashSale === 'no') {
          this.getAllFlashSale()
        }
      } else {
        this.getProductCategoryByID()
      }
      // console.log('pageChange2', this.$route)
      // var data
      this.pageNumber = val
    },
    async getAllProductInWeb () {
      // console.log('getAllProductInWeb')
      var dataRole
      if (localStorage.getItem('roleUser') !== null) {
        dataRole = JSON.parse(localStorage.getItem('roleUser')).role
      } else {
        dataRole = 'ext_buyer'
      }
      var companyID
      if (localStorage.getItem('SetRowCompany') !== null) {
        companyID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany'))).company.company_id
      } else {
        companyID = '-1'
      }
      var data = {
        role_user: dataRole,
        company_id: dataRole === 'purchaser' ? companyID : '-1',
        category: '',
        seller_shop_id: '-1',
        orderBy: '',
        status_product: '',
        limit: 48
      }
      await this.$store.dispatch('actionsSelectCategoryShopList', data)
      var res = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      if (res.ok === 'y') {
        if (res.query_result.length !== 0) {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = await [...res.query_result]
          this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
          // console.log('pageMax ===>', this.pageMax, this.AllProduct.length)
          this.productCount = res.query_result.length
          this.showSkeletonLoader = false
        }
      } else {
        this.overlay2 = false
        this.showSkeletonLoader = false
        this.$store.commit('closeLoader')
        this.$swal.fire({ html: 'ไม่สามารถดูสินค้าทั้งหมดได้ <br/> กรุณาลองใหม่อีกครั้งในภายหลัง', icon: 'warning', timer: 2500, showConfirmButton: false })
      }
    },
    gotoBannerPage (val) {
      var dataR = ''
      if (localStorage.getItem('roleUser') !== null) {
        dataR = JSON.parse(localStorage.getItem('roleUser')).role
      } else {
        dataR = 'ext_buyer'
      }
      if (dataR === 'sale_order' || dataR === 'sale_order_no_JV') {
        this.$router.push({ path: this.pathShopSale }).catch(() => {})
      } else {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    },
    async getAllNewProduct () {
      // console.log('getAllNewProduct')
      this.showSkeletonLoader = true
      var companyID = ''
      var data = ''
      var ShopID
      if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
        this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
        this.tokenstatus = this.oneData.user.access_token
      } else {
        this.dataRole = ''
        this.tokenstatus = ''
      }
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
      }
      var hier = ''
      var idCat = ''
      if (this.filterCategory !== '') {
        hier = this.filterCategory.split('_')
        idCat = hier[1]
      }
      if (this.dataRole.role !== 'sale_order') {
        ShopID = JSON.parse(localStorage.getItem('shopID'))
        data = {
          orderBy: this.filterPrice,
          category: idCat,
          status_product: 'new',
          limit: this.limit,
          page: this.pageNumber,
          seller_shop_id: this.typeProduct === 'new_product_landing' ? -1 : ShopID,
          role_user: this.dataRole !== '' ? this.dataRole.role : 'ext_buyer',
          company_id: this.dataRole.role !== 'ext_buyer' ? companyID : '-1'
        }
      } else {
        companyID = JSON.parse(localStorage.getItem('PartnerID'))
        ShopID = JSON.parse(localStorage.getItem('ShopID'))
        data = {
          orderBy: this.filterPrice,
          category: idCat,
          status_product: '',
          limit: this.limit,
          page: this.pageNumber,
          role_user: this.dataRole.role,
          company_id: companyID,
          seller_shop_id: this.typeProduct === 'new_product_landing' ? -1 : ShopID
        }
      }
      await this.$store.dispatch('actionsSelectCategoryShopList', data)
      var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      // console.log('response all New product=======>', response)
      if (response.ok === 'y') {
        if (response.query_result.length !== 0) {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = await [...response.query_result]
          // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
          this.pageMax = parseInt(response.pagination.max_page)
          this.pageNumber = parseInt(this.$route.query.page)
          // console.log('pageMax ===>', this.pageMax, this.AllProduct.length)
          this.productCount = response.query_result.length
          this.showSkeletonLoader = false
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
          this.showSkeletonLoader = false
        }
      } else {
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
        this.showSkeletonLoader = false
      }
    },
    async getAllBestSeller () {
      // console.log('getAllBestSeller')
      this.showSkeletonLoader = true
      var companyID = ''
      var dataBest = ''
      var ShopID = JSON.parse(localStorage.getItem('shopID'))
      if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
        this.dataRole = JSON.parse(localStorage.getItem('roleUser')).role
        this.tokenstatus = this.oneData.user.access_token
      } else {
        this.dataRole = 'ext_buyer'
        this.tokenstatus = ''
      }
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
      }
      var hier = ''
      var idCat = ''
      if (this.filterCategory !== '') {
        hier = this.filterCategory.split('_')
        idCat = hier[1]
      }
      if (this.dataRole.role !== 'sale_order') {
        dataBest = {
          orderBy: this.filterPrice,
          category: idCat,
          status_product: 'best-seller',
          limit: this.limit,
          page: this.pageNumber,
          role_user: this.dataRole,
          company_id: companyID,
          seller_shop_id: this.typeProduct === 'best_seller_landing' ? -1 : ShopID
        }
      } else {
        ShopID = JSON.parse(localStorage.getItem('ShopID'))
        companyID = JSON.parse(localStorage.getItem('PartnerID'))
        dataBest = {
          orderBy: this.filterPrice,
          category: idCat,
          status_product: 'best-seller',
          limit: this.limit,
          page: this.pageNumber,
          role_user: this.dataRole,
          company_id: companyID,
          seller_shop_id: this.typeProduct === 'best_seller_landing' ? -1 : ShopID
        }
      }
      // console.log('getAllBestSeller dataBest', dataBest)
      await this.$store.dispatch('actionsSelectCategoryShopList', dataBest)
      var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      // console.log('response all Best Seller =======>', response)
      if (response.ok === 'y') {
        if (response.query_result !== 'No products ready to sell.') {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = await [...response.query_result]
          // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
          this.pageMax = parseInt(response.pagination.max_page)
          this.pageNumber = parseInt(this.$route.query.page)
          // console.log('pageMax ===>', this.pageMax, this.AllProduct.length)
          this.productCount = response.query_result.length
          this.showSkeletonLoader = false
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
          this.showSkeletonLoader = false
        }
      } else {
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
        this.showSkeletonLoader = false
      }
    },
    async getAllRecProductShop () {
      // console.log('getAllRecProductShop')
      this.showSkeletonLoader = true
      var companyID = ''
      var dataRec = ''
      var ShopID = JSON.parse(localStorage.getItem('shopID'))
      if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
        this.dataRole = JSON.parse(localStorage.getItem('roleUser')).role
        this.tokenstatus = this.oneData.user.access_token
      } else {
        this.dataRole = 'ext_buyer'
        this.tokenstatus = ''
      }
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
      }
      var hier = ''
      var idCat = ''
      if (this.filterCategory !== '') {
        hier = this.filterCategory.split('_')
        idCat = hier[1]
      }
      if (this.dataRole.role !== 'sale_order') {
        dataRec = {
          orderBy: this.filterPrice,
          category: idCat,
          status_product: 'recommend',
          limit: this.limit,
          page: this.pageNumber,
          seller_shop_id: this.typeProduct === 'recommended_product_landing' || this.typeProduct === 'recommended_product_for_you' ? -1 : ShopID,
          role_user: this.dataRole,
          company_id: this.dataRole.role !== 'ext_buyer' ? companyID : '-1'
        }
      } else {
        companyID = JSON.parse(localStorage.getItem('PartnerID'))
        ShopID = JSON.parse(localStorage.getItem('ShopID'))
        dataRec = {
          orderBy: this.filterPrice,
          category: idCat,
          status_product: 'recommend',
          limit: this.limit,
          page: this.pageNumber,
          seller_shop_id: this.typeProduct === 'recommended_product_landing' || this.typeProduct === 'recommended_product_for_you' ? -1 : ShopID,
          role_user: this.dataRole,
          company_id: this.dataRole.role !== 'ext_buyer' ? companyID : '-1'
        }
      }
      // console.log('dataRec', dataRec, this.typeProduct)
      await this.$store.dispatch('actionsSelectCategoryShopList', dataRec)
      var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      // console.log('response all Best Seller =======>', response)
      if (response.ok === 'y') {
        this.overlay2 = false
        this.AllProduct = []
        this.AllProduct = await [...response.query_result]
        // this.AllProduct = response.data.list_new_products
        // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
        this.pageMax = parseInt(response.pagination.max_page)
        this.pageNumber = parseInt(this.$route.query.page)
        // console.log('pageMax ===>', this.pageMax, this.AllProduct.length)
        this.productCount = response.query_result.length
        this.showSkeletonLoader = false
      } else {
        this.overlay2 = false
        this.AllProduct = []
        this.productCount = 0
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
        this.showSkeletonLoader = false
      }
    },
    async getAllProductShop () {
      var data
      var shopID = localStorage.getItem('IDForShop')
      var ShopID
      var companyID = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
      }
      if (this.dataRole.role === 'sale_order') {
        ShopID = JSON.parse(localStorage.getItem('ShopID'))
        companyID = JSON.parse(localStorage.getItem('PartnerID'))
      } else {
        companyID = '-1'
      }
      if (localStorage.getItem('roleUser') !== null) {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        // data = {
        //   token: this.oneData.user.access_token,
        //   role_user: dataRole.role,
        //   begin_search_type: 'component',
        //   begin_search_details: {
        //     custom_user_ID: '1',
        //     what_component: 'all_product',
        //     component_id: ''
        //   },
        //   user_detail: {
        //     company_id: companyID
        //   },
        //   seller_shop_id: shopID
        // }
        data = {
          role_user: dataRole.role,
          company_id: companyID,
          seller_shop_id: this.dataRole.role === 'sale_order' ? ShopID : shopID
        }
      } else {
        if (this.oneData.length !== 0) {
          // data = {
          //   token: this.oneData.user.access_token,
          //   role_user: 'ext_buyer',
          //   begin_search_type: 'component',
          //   begin_search_details: {
          //     custom_user_ID: '1',
          //     what_component: 'all_product',
          //     component_id: ''
          //   },
          //   user_detail: {
          //     company_id: companyID
          //   },
          //   seller_shop_id: shopID
          // }
          data = {
            role_user: 'ext_buyer',
            company_id: companyID,
            seller_shop_id: shopID
          }
        } else {
          // data = {
          //   token: '',
          //   role_user: 'ext_buyer',
          //   begin_search_type: 'component',
          //   begin_search_details: {
          //     custom_user_ID: '1',
          //     what_component: 'all_product',
          //     component_id: ''
          //   },
          //   user_detail: {
          //     company_id: companyID
          //   },
          //   seller_shop_id: shopID
          // }
          data = {
            role_user: 'ext_buyer',
            company_id: companyID,
            seller_shop_id: shopID
          }
        }
      }
      // console.log('actionsAllProduct', data)
      await this.$store.dispatch('actionsAllProduct', data)
      var response = await this.$store.state.ModuleProductNode.stateAllProduct
      // console.log('response Shop Detail Page =======>', response)
      if (response.ok === 'y') {
        if (response.query_result.length !== 0) {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = await [...response.query_result]
          // this.AllProduct = response.data.list_new_products
          this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
          // console.log('pageMax ===>', this.pageMax, this.AllProduct.length)
          this.productCount = response.query_result.length
          this.showSkeletonLoader = false
        }
      } else {
        this.overlay2 = false
        this.AllProduct = []
        this.productCount = 0
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
        this.showSkeletonLoader = false
      }
    },
    async getProductGroupShop () {
      this.showSkeletonLoader = true
      var companyID = ''
      var dataBest = ''
      var groupshopID = JSON.parse(Decode.decode(localStorage.getItem('groupshop_id')))
      if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
        this.dataRole = JSON.parse(localStorage.getItem('roleUser')).role
        this.tokenstatus = this.oneData.user.access_token
      } else {
        this.dataRole = 'ext_buyer'
        this.tokenstatus = ''
      }
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
      }
      if (this.dataRole.role !== 'sale_order') {
        dataBest = {
          orderBy: '',
          category: -1,
          status_product: -1,
          filter_group_seller_shop: groupshopID,
          limit: this.limit,
          page: this.pageNumber,
          role_user: this.dataRole,
          company_id: companyID,
          seller_shop_id: -1
        }
      } else {
        companyID = JSON.parse(localStorage.getItem('PartnerID'))
        dataBest = {
          orderBy: '',
          category: -1,
          status_product: -1,
          filter_group_seller_shop: groupshopID,
          limit: this.limit,
          page: this.pageNumber,
          role_user: this.dataRole,
          company_id: companyID,
          seller_shop_id: -1
        }
      }
      await this.$store.dispatch('actionsSelectCategoryShopList', dataBest)
      var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      if (response.ok === 'y') {
        if (response.query_result !== 'No products ready to sell.') {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = await [...response.query_result]
          this.pageMax = parseInt(response.pagination.max_page)
          this.pageNumber = parseInt(this.$route.query.page)
          this.productCount = response.query_result.length
          this.showSkeletonLoader = false
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
          this.showSkeletonLoader = false
        }
      } else {
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
        this.showSkeletonLoader = false
      }
    },
    async getAllProductCategory () {
      // console.log('getAllProductCategory')
      let data = ''
      var Category = JSON.parse(Decode.decode(localStorage.getItem('category')))
      let companyID = ''
      let ShopID
      // if (localStorage.getItem('SetRowCompany') !== null) {
      //   var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      //   companyID = companyDataSet.company.company_id
      // } else {
      //   companyID = '-1'
      // }
      if (localStorage.getItem('roleUser') !== null) {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        if (localStorage.getItem('SetRowCompany') !== null) {
          var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
          companyID = companyDataSet.company.company_id
        }
        if (this.dataRole.role !== 'sale_order') {
          data = {
            role_user: dataRole.role,
            company_id: this.dataRole.role !== 'ext_buyer' ? companyID : '-1',
            hierachy_category: Category.hierachy
          }
        } else {
          companyID = JSON.parse(localStorage.getItem('PartnerID'))
          ShopID = JSON.parse(localStorage.getItem('ShopID'))
          data = {
            role_user: dataRole.role,
            company_id: companyID,
            hierachy_category: Category.hierachy,
            seller_shop_id: ShopID
          }
        }
        // data = {
        //   token: this.oneData.user.access_token,
        //   role_user: dataRole.role,
        //   begin_search_type: 'component',
        //   begin_search_details: {
        //     custom_user_ID: '1',
        //     what_component: 'all_product',
        //     component_id: ''
        //   },
        //   user_detail: {
        //     company_id: companyID
        //   },
        //   seller_shop_id: shopID
        // }
      } else {
        if (this.oneData.length !== 0) {
          // data = {
          //   token: this.oneData.user.access_token,
          //   role_user: 'ext_buyer',
          //   begin_search_type: 'component',
          //   begin_search_details: {
          //     custom_user_ID: '1',
          //     what_component: 'all_product',
          //     component_id: ''
          //   },
          //   user_detail: {
          //     company_id: companyID
          //   },
          //   seller_shop_id: shopID
          // }
          data = {
            role_user: 'ext_buyer',
            company_id: companyID,
            hierachy_category: Category.hierachy
          }
        } else {
          // data = {
          //   token: '',
          //   role_user: 'ext_buyer',
          //   begin_search_type: 'component',
          //   begin_search_details: {
          //     custom_user_ID: '1',
          //     what_component: 'all_product',
          //     component_id: ''
          //   },
          //   user_detail: {
          //     company_id: companyID
          //   },
          //   seller_shop_id: shopID
          // }
          data = {
            role_user: 'ext_buyer',
            company_id: companyID,
            hierachy_category: Category.hierachy
          }
        }
      }
      // console.log('getAllProductCategorydata', data)
      await this.$store.dispatch('actionsGetAllProductCategory', data)
      var response = await this.$store.state.ModuleProductNode.stateAllProductCategory
      // console.log('response Shop Detail Page =======>', response)
      if (response.ok === 'y') {
        if (response.query_result.length !== 0) {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = await [...response.query_result]
          // this.AllProduct = response.data.list_new_products
          this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
          // console.log('pageMax ===>', this.pageMax, this.AllProduct.length)
          this.productCount = response.query_result.length
          this.showSkeletonLoader = false
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
          this.showSkeletonLoader = false
        }
      } else {
        this.overlay2 = false
        this.AllProduct = []
        this.productCount = 0
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
        this.showSkeletonLoader = false
      }
    },
    async getAllProductCategoryDetail () {
      // console.log('getAllProductCategoryDetail')
      this.showSkeletonLoader = true
      var dataRole = 'ext_buyer'
      if (this.filterCategory !== '') {
        var catid = this.filterCategory.split('_')
        var CatID = catid[1]
      }
      // console.log('CatID', CatID)
      if (localStorage.getItem('roleUser') !== null) {
        dataRole = JSON.parse(localStorage.getItem('roleUser')).role
      } else {
        dataRole = 'ext_buyer'
      }
      let data = ''
      let companyID = ''
      var ShopID
      if (localStorage.getItem('SetRowCompany') !== null && dataRole === 'pur') {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
        ShopID = JSON.parse(localStorage.getItem('shopID'))
      } else if (dataRole === 'sale_order' || dataRole === 'sale_order_no_JV') {
        companyID = JSON.parse(localStorage.getItem('PartnerID'))
        ShopID = JSON.parse(localStorage.getItem('ShopID'))
      } else {
        companyID = '-1'
        ShopID = JSON.parse(localStorage.getItem('shopID'))
      }
      data = {
        orderBy: this.filterPrice,
        category: CatID,
        status_product: '',
        limit: this.limit,
        page: this.pageNumber,
        seller_shop_id: this.typeProduct === 'recommended_product_landing' || this.typeProduct === 'similar_products' ? -1 : ShopID,
        role_user: dataRole,
        company_id: companyID
      }
      // console.log('data', data)
      await this.$store.dispatch('actionsSelectCategoryShopList', data)
      var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      // console.log('response Shop Detail Page =======>', response)
      if (response.ok === 'y') {
        if (response.query_result.length !== 0) {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = await [...response.query_result]
          // this.AllProduct = response.data.list_new_products
          // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
          this.pageMax = parseInt(response.pagination.max_page)
          this.pageNumber = parseInt(this.$route.query.page)
          // console.log('pageMax ===>', this.pageMax, this.AllProduct.length)
          this.productCount = response.query_result.length
          this.showSkeletonLoader = false
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
          this.showSkeletonLoader = false
        }
      } else {
        this.overlay2 = false
        this.AllProduct = []
        this.productCount = 0
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
        this.showSkeletonLoader = false
      }
    },
    async getAllPopularProduct () {
      // console.log('getAllPopularProduct')
      this.showSkeletonLoader = true
      var roleUser = 'ext_buyer'
      if (localStorage.getItem('roleUser') !== null) {
        roleUser = JSON.parse(localStorage.getItem('roleUser'))
      }
      var data
      var response
      var companyId = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      }
      if (localStorage.getItem('roleUser') !== null) {
        if (roleUser.role === 'purchaser') {
          data = {
            role_user: roleUser.role,
            company_id: companyId.company.company_id,
            category: this.valueID,
            seller_shop_id: -1,
            orderBy: '',
            status_product: '',
            page: this.pageNumber,
            limit: this.limit
          }
        } else {
          data = {
            role_user: roleUser.role,
            company_id: -1,
            category: this.valueID,
            seller_shop_id: -1,
            orderBy: '',
            status_product: '',
            page: this.pageNumber,
            limit: this.limit
          }
        }
      } else {
        data = {
          role_user: 'ext_buyer',
          company_id: companyId !== '' ? companyId.company.company_id : -1,
          category: this.valueID,
          seller_shop_id: -1,
          orderBy: '',
          status_product: '',
          page: this.pageNumber,
          limit: this.limit
        }
      }
      await this.$store.dispatch('actionsSelectCategoryShopList', data)
      response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      // console.log('response_PopularProductDetail----->', response)
      var dataRes = response.categoryName
      this.headerCategory = []
      this.headerCategory = dataRes
      if (this.items.length < 3) {
        this.items.push({
          category_name: this.headerCategory,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // console.log('dataRes', dataRes)
      if (response.ok === 'y') {
        if (response.query_result.length !== 0) {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = await [...response.query_result]
          // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
          this.pageMax = parseInt(response.pagination.max_page)
          this.pageNumber = parseInt(this.$route.query.page)
          // console.log('pageMax ===>', this.pageMax, this.AllProduct.length)
          this.productCount = response.query_result.length
          this.showSkeletonLoader = false
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
          this.showSkeletonLoader = false
        }
      } else {
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
        this.showSkeletonLoader = false
      }
    },
    async getProductCategoryByID () {
      // console.log('getProductCategoryByID=')
      this.showSkeletonLoader = true
      var hier = this.$route.query.id.split('_')
      var idCat = hier[1]
      var data
      var roleUser = ''
      if (localStorage.getItem('roleUser')) {
        roleUser = JSON.parse(localStorage.getItem('roleUser')).role
      } else {
        roleUser = 'ext_buyer'
      }
      // var ShopID = JSON.parse(localStorage.getItem('shopID'))
      var response
      var companyId = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      }
      data = {
        role_user: roleUser,
        company_id: roleUser !== 'ext_buyer' ? companyId.company.company_id : '-1',
        seller_shop_id: this.sellerShopID,
        orderBy: this.filterPrice,
        status_product: '-1',
        limit: this.limit,
        category: idCat,
        page: this.pageNumber
        // seller_shop_id: ShopID
      }
      await this.$store.dispatch('actionsSelectCategoryShopList', data)
      response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      // console.log('getProductCategoryByID=======>', response)
      if (response.ok === 'y') {
        if (response.query_result.length !== 0) {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = await [...response.query_result]
          // this.AllProduct = response.data.list_new_products
          // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
          this.pageMax = parseInt(response.pagination.max_page)
          this.pageNumber = parseInt(this.$route.query.page)
          // console.log('pageMax ===>', this.pageMax, this.AllProduct.length)
          this.productCount = response.query_result.length
          this.showSkeletonLoader = false
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
          this.showSkeletonLoader = false
        }
      } else {
        this.overlay2 = false
        this.AllProduct = []
        this.productCount = 0
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
        this.showSkeletonLoader = false
      }
    },
    async getProductSameShop () {
      // console.log('test====>2222')
      this.showSkeletonLoader = true
      var data
      var roleUser = ''
      if (localStorage.getItem('roleUser')) {
        roleUser = JSON.parse(localStorage.getItem('roleUser')).role
      } else {
        roleUser = 'ext_buyer'
      }
      var ShopID = JSON.parse(localStorage.getItem('shopID'))
      var companyID = ''
      if (localStorage.getItem('SetRowCompany') !== null && roleUser === 'purchaser') {
        var companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyId.company.company_id
      }
      if (roleUser === 'sale_order' || roleUser === 'sale_order_no_JV') {
        companyID = JSON.parse(localStorage.getItem('PartnerID'))
        ShopID = JSON.parse(localStorage.getItem('ShopID'))
      } else {
        ShopID = JSON.parse(localStorage.getItem('shopID'))
      }
      // console.log('companyID', companyID)
      data = {
        company_id: this.dataRole.role !== 'ext_buyer' ? companyID : '-1',
        role_user: roleUser,
        category: '',
        seller_shop_id: this.typeProduct === 'all_product_cat' ? -1 : ShopID,
        orderBy: this.filterPrice,
        page: this.pageNumber,
        status_product: '',
        limit: this.typeProduct === 'all_product_cat' ? 48 : this.limit
      }
      await this.$store.dispatch('actionsSelectCategoryShopList', data)
      var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      // console.log('getProductSameShop=======>', response)
      if (response.ok === 'y') {
        // this.editPage = 'api success'
        if (response.query_result.length !== 0) {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = await [...response.query_result]
          // console.log('this.all', this.AllProduct)
          // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
          this.pageMax = response.pagination.max_page
          this.pageNumber = parseInt(this.$route.query.page)
          // window.location.reload()
          // console.log('pageMax ===>', response.query_result.length)
          this.productCount = response.query_result.length
          this.showSkeletonLoader = false
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
          this.showSkeletonLoader = false
        }
      } else {
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
        this.showSkeletonLoader = false
      }
    },
    async getAllFlashSale () {
      this.showSkeletonLoader = true
      // if (localStorage.getItem('SetRowCompany') !== null) {
      //   var companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      // }
      var data = {
        page: this.pageNumber,
        limit: this.typeProduct === 'flash_sale' ? 48 : this.limit
      }
      await this.$store.dispatch('actionsFlashSaleProductatShop', data)
      var response = await this.$store.state.ModuleHompage.stateFlashSaleProduct
      // this.bannerFlashSale = response.data.img_banner
      // console.log('response_PopularProductDetail----->', response)
      var dataRes = response.categoryName
      this.headerCategory = []
      this.headerCategory = dataRes
      if (this.items.length < 2) {
        this.items.push({
          category_name: this.headerCategory,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // console.log('dataRes', dataRes)
      if (response.code === 200) {
        if (response.data.products !== 0) {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = await [...response.data.products]
          // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
          // this.pageMax = parseInt(response.pagination.max_page)
          this.pageMax = response.data.max_page
          this.pageNumber = parseInt(this.$route.query.page)
          // console.log('pageMax ===>', this.pageMax, this.AllProduct.length)
          this.productCount = response.data.length
          this.showSkeletonLoader = false
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
          this.showSkeletonLoader = false
        }
      } else {
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
        this.showSkeletonLoader = false
      }
    },
    async getAllFlashSaleSystem () {
      var hier = ''
      var idCat = ''
      if (this.filterCategory !== '') {
        hier = this.filterCategory.split('_')
        idCat = hier[1]
      }
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        page: this.pageNumber,
        limit: 48,
        company_id: -1,
        category: idCat,
        seller_shop_id: -1,
        orderBy: this.filterPrice,
        status_product: 'discount',
        role_user: dataRole.role
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsFlashSaleProductSystem', data)
      var response = await this.$store.state.ModuleHompage.stateFlashSaleProductSystem
      if (response.ok === 'y') {
        this.flashSaleProduct = response.query_result
        // this.backgroundImage = response.data.img_background
        // this.logoImage = response.data.img_logo
        this.isLoading = true
        this.$store.commit('closeLoader')
      } else {
        this.flashSaleProduct = []
        this.$store.commit('closeLoader')
      }

      if (response.ok === 'y') {
        if (response.query_result.length !== 0) {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = await [...response.query_result]
          // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
          // this.pageMax = parseInt(response.pagination.max_page)
          this.pageMax = response.pagination.max_page
          this.pageNumber = parseInt(this.$route.query.page)
          // console.log('pageMax ===>', this.pageMax, this.AllProduct.length)
          this.productCount = response.query_result.length
          this.showSkeletonLoader = false
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
          this.showSkeletonLoader = false
        }
      } else {
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
        this.showSkeletonLoader = false
      }
    },
    async StatusFlashSale () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsStatusFlashSale')
      const responseStatusFlashSale = await this.$store.state.ModuleAdminManage.stateStatusFlashSale

      if (responseStatusFlashSale.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.statusFlashSale = responseStatusFlashSale.data
      }
    },
    async GetBanner () {
      await this.$store.dispatch('actionsGetBanner')
      var response = await this.$store.state.ModuleHompage.stateGetBanner
      // console.log(response.data.img_flash_sale_banner.length, '555555')
      if (response.data.img_flash_sale_banner.length !== 0) {
        this.bannerFlashSale = response.data.img_flash_sale_banner[0].path
        this.logoImage = response.data.img_flash_sale_logo[0].path
      } else {
        this.bannerFlashSale = ''
        this.logoImage = ''
      }
    }
  }
}
</script>

<style scoped>
.fontFilter {
  font-size: 16px;
  font-weight: 400;
  color: #333333;
  display: flex;
  align-items: center;
}
.setStyleListFlashSale {
  display: flex;
  align-items: center;
}
.paginationStyle /deep/ .v-pagination__item {
  background: transparent;
  border-radius: 6px !important;
  border: 1px solid #E6E6E6 !important;
  font-size: 1rem;
  height: 40px;
  margin: 0.3rem;
  min-width: 40px;
  padding: 0 5px;
  text-decoration: none;
  transition: 0.3s cubic-bezier(0, 0, 0.2, 1);
  width: auto;
  box-shadow: none !important;
}
.paginationStyle /deep/ .v-pagination__navigation {
  box-shadow: none !important;
  border-radius: 6px !important;
  border: 1px solid #E6E6E6 !important;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  height: 40px;
  width: 40px;
  margin: 0.3rem 10px;
}
.v-application a {
  color: #636363 !important;
}
.v-breadcrumbs__item  {
  color: #3EC6B6 !important;
}
.v-breadcrumbs li .v-icon {
  color: #3EC6B6 !important;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 12px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}

.custom-select-icon >>> .v-input__append-inner .v-icon {
  color: #3EC6B6 !important; /* Change this to any color */
}

.custom-select-icon >>> .v-input__append-inner {
  margin-top: 12px !important;
}

.custom-select-icon >>> .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 48px;
  font-size: 14px !important;
  color: #333333 !important;
}
</style>
