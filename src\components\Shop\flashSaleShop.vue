<template>
  <div v-if="itemListFlashSale.length !== 0 && itemListFlashSale[0].product_list.length !== 0">
    <v-col v-for="(item, index) in itemListFlashSale" :key="index" cols="12" md="12" class="pa-0 mt-10 d-flex justify-center">
      <v-card
        width="1376px"
        elevation="0"
        :height="MobileSize ? '260px' : '350px'"
        :style="imgBackground === '' ? {background: 'linear-gradient(0deg, #ea9348, #f3c249)', borderRadius: '10px'} : {backgroundImage: `url('${imgBackground}')`, backgroundSize: 'cover', backgroundPosition: 'center', backgroundRepeat: 'no-repeat', backgroundSize: 'cover', borderRadius: '10px'}">
        <img v-if="imgLogo === ''" src="@/assets/ImageINET-Marketplace/ICONShop/flashSaleIcon.png" :width="MobileSize ? '140px' : '180px'" :style="MobileSize ? 'position: absolute; top: -20px; left: 20px' : 'position: absolute; top: -20px; left: 20px;'">
        <img v-else :src="imgLogo" :width="MobileSize ? '140px' : '180px'" :style="MobileSize ? 'position: absolute; top: -20px; left: 20px' : 'position: absolute; top: -20px; left: 20px;'">
        <v-row class="justify-end" no-gutters>
          <v-btn elevation="0" @click="GetAllProductsFlashSale(item.product_list, 'flashSaleShop', sellerShopID)" class="mt-3 mr-3 productAll" :style="MobileSize ? 'font-size: 14px; color: #a55918; font-weight: bold;' : 'font-size: 16px; color: #a55918; font-weight: bold;'">ดูทั้งหมด</v-btn>
        </v-row>
        <vue-horizontal-list :class="MobileSize || IpadSize ? 'mx-4' : 'mx-10'"  :items='item.product_list' :options='optionsItems'>
        <template v-slot:nav-prev>
          <div><v-icon color="#008E00" size="32">mdi-chevron-left</v-icon></div>
        </template>
        <template v-slot:nav-next>
          <div><v-icon color="#008E00" size="32">mdi-chevron-right</v-icon></div>
        </template>
        <template v-slot:default="{ item }">
          <v-col style="display: flex; height: 100%;" class="pr-2">
            <component :is="MobileSize || IpadSize ? 'CardProductsFlashSale' : 'CardProductsFlashSale'" :itemProduct="item"/>
          </v-col>
        </template>
        </vue-horizontal-list>
      </v-card>
    </v-col>
  </div>
</template>

<script>
import VueHorizontalList from 'vue-horizontal-list'
// import { Encode } from '@/services'
export default {
  components: {
    VueHorizontalList,
    CardProductsFlashSale: () => import('@/components/Shop/ManageFlashSale/FlashSaleItem/CardFlashSaleHome')
  },
  data () {
    return {
      sellerShopID: '',
      imgBackground: '',
      imgLogo: '',
      itemListFlashSale: [],
      optionsItems: {
        responsive: [
          { end: 576, size: 3 },
          { start: 576, end: 768, size: 4 },
          { start: 768, end: 1024, size: 4 },
          { start: 1024, end: 1200, size: 6 },
          { start: 1200, size: 6 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1300,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 5
        }
      }
    }
  },
  async created () {
    var sellershop = this.$route.params.data.split('-')
    this.sellerShopID = sellershop[sellershop.length - 1]
    if (this.sellerShopID !== null && this.sellerShopID !== '' && this.sellerShopID !== undefined) {
      this.getProductFlashSale()
    } else {
      this.$router.push({ path: '/' })
    }
    // console.log(this.sellerShopID, 'id')
    // if (localStorage.getItem('shopID') !== null) {
    //   this.sellerShopID = JSON.parse(localStorage.getItem('shopID'))
    // } else if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
    //   this.sellerShopID = JSON.parse(localStorage.getItem('ShopID'))
    // } else {
    //   // console.log(this.$router.currentRoute.params.data)
    //   this.sellerShopID = sellershop
    // }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    async getProductFlashSale () {
      var RowUserData = ''
      if (localStorage.getItem('roleUser') !== null) {
        RowUserData = JSON.parse(localStorage.getItem('roleUser')).role
        // this.RoleUser = 'sale_order_no_JV'
      } else {
        RowUserData = 'ext_buyer'
      }
      var data = {
        seller_shop_id: this.sellerShopID,
        page: 1,
        limit: 18,
        role: RowUserData
      }
      await this.$store.dispatch('actionsGetFlashSale', data)
      var response = await this.$store.state.ModuleManageFlashSale.stateGetFlashSale
      if (response.result === 'SUCCESS') {
        // console.log('response', response)
        // console.log('response itemListDetailArticle', response.data.data)
        this.itemListFlashSale = response.data.data
        this.imgLogo = response.data.img_logo
        this.imgBackground = response.data.img_background

        // if (response.data.length === 0) {
        //   this.disabledArticle = false
        // } else {
        //   this.disabledArticle = trues
        // }
      } else {
        this.itemListFlashSale = []
      }
    },
    GetAllProductsFlashSale (allItem, header, id) {
      if (allItem.length !== 0) {
        // localStorage.setItem('flashSaleId', Encode.encode(id))
        this.$router.push(`/ListShopProduct/${header}?&ID=${id}&page=1`).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>
::v-deep .vhl-list>[data-v-8b923bbc] {
  flex-shrink: 1 !important;
}
::v-deep .vhl-item[data-v-8b923bbc] {
    box-sizing: content-box;
    padding-top: 25px;
    padding-bottom: 24px;
    z-index: 1;
    min-height: 1px;
}
@media (max-width: 600px) {
    ::v-deep .vhl-item[data-v-8b923bbc] {
        padding-top: 10px;
    }
}
.productAll {
  cursor: pointer;
}
</style>
