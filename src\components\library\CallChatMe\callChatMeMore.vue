<template>
  <div id="create" class="showChat">
    <!-- กรณี login -->
    <v-speed-dial
      v-model="fab"
      :bottom="true"
      :right="true"
      direction="top"
      :open-on-hover="false"
      fixed
      transition="slide-y-reverse-transition"
      :style="bottomStyle"
      v-if="status"
    >
      <template v-slot:activator>
        <v-badge
          color="red"
          :value="countAllNotRead"
          :content="countAllNotRead"
          top
          overlap
        >
          <v-btn
            @click.stop="changeStatusShowChat()"
            color="blue darken-2"
            dark
            fab
          >
              <v-icon v-if="changeChat">
                mdi-chat-processing-outline
              </v-icon>
              <v-icon v-else>
                mdi-chat-outline
              </v-icon>
          </v-btn>
        </v-badge>
      </template>
      <div id="chatMeB2B" class="dots">
        <div v-if="listChat.length !== 0 && (changeChat === true && fab)">
          <div v-for="(item, index) in listChat" :key="index" class="chatMainSub" style="display: inline;">
            <a>
              <span class="">
                <v-btn
                  :style="item.ImgShop !== '' && item.ImgShop !== null ? '' : `background-color: ${getRandomColor()};`"
                  max-height="65"
                  max-width="65"
                  fab
                  @click.stop="sentChatMore(item)"
                >
                  <v-badge :content="item.number_not_read" :value="item.number_not_read" color="red" top bordered overlap>
                    <v-avatar  v-if="item.ImgShop !== '' && item.ImgShop !== null">
                      <v-img
                        :alt="item.botName"
                        :src="item.ImgShop"
                        width="100%"
                        contain
                      ></v-img>
                    </v-avatar>
                    <v-avatar v-else>
                      <span class="white--text text-avatar font-weight-black mt-1" style="font-size:25px;">{{ !['ไ', 'แ', 'ใ', 'เ', 'โ'].includes(item.botName[0]) ? item.botName[0].toUpperCase() : item.botName[1].toUpperCase() }}</span>
                    </v-avatar>
                  </v-badge>
                </v-btn>
              </span>
            </a>
          </div>
          <div v-if="this.countBot >= 5" class="chatMainSub" style="display: inline;">
            <!-- <a href="/ChatAll">
              <v-btn
                max-height="65"
                max-width="65"
                fab
              >
                <v-icon>mdi-page-next</v-icon>
              </v-btn>
            </a> -->
            <v-menu
              left
              offset-x
              transition="slide-x-transition"
              :nudge-left="10"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  max-height="65"
                  max-width="65"
                  fab
                  v-bind="attrs"
                  v-on="on"
                >
                  <v-badge
                    color="red"
                    dot
                    :value="listChatOter.countOtherBot === 0 ? false : true"
                    top
                    overlap
                  >
                    <v-avatar>
                      <v-img
                        :alt="imagesOtherChat"
                        :src="imagesOtherChat"
                        width="100%"
                        contain
                      ></v-img>
                    </v-avatar>
                    <div class="textShowCss">
                      <span style="position: relative; text-align: center; top: 35%;">
                        +{{ listChatOter.length }}
                      </span>
                    </div>
                  </v-badge>
                </v-btn>
              </template>
              <v-list dense>
                <v-list-item
                  v-for="(item, index) in listChatOter"
                  :key="index"
                  link
                >
                  <v-list-item-title @click.stop="sentChatMore(item)">{{ item.botName }} <span :class="item.number_not_read !== 0 ? 'bridge' : ''">{{ item.number_not_read === 0 ? '' : item.number_not_read }}</span></v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
          <!-- <div class="chatMainSub">
            <v-btn
              max-height="65"
              max-width="65"
              fab
              @click="sentChatMore(chatBotAdmin)"
            >
              <button aria-label="แชทกับแอดมิน" tooltip-position="left">
                <v-avatar class="">
                  <v-img
                  alt="ngc"
                  src="@/assets/ngc_logo.webp"
                  width="100%"
                  ></v-img>
                </v-avatar>
              </button>
            </v-btn>
          </div> -->
        </div>
        <div v-else-if="changeChat === false && fab === true" class="chatMainSub" style="display: block;">
          <div>
            <v-btn
              max-height="65"
              max-width="65"
              fab
              @click.stop="sentChatMore(chatBotAdmin)"
            >
              <v-badge
                color="red"
                :content="countNotiAdminLogin"
                :value="countNotiAdminLogin"
                top
                overlap
              >
                <button aria-label="แชทกับแอดมิน" tooltip-position="left">
                  <v-avatar class="">
                    <v-img
                    alt="ngc"
                    src="@/assets/ngc_logo.webp"
                    width="100%"
                    contain
                    ></v-img>
                  </v-avatar>
                </button>
              </v-badge>
            </v-btn>
          </div>
        </div>
      </div>
    </v-speed-dial>
    <!-- กรณี ไม่ login -->
    <v-speed-dial
      v-model="fab"
      :bottom="true"
      :right="true"
      direction="top"
      fixed
      transition="slide-y-reverse-transition"
      :style="bottomStyle"
      v-else
    >
      <template v-slot:activator>
        <v-btn
          @click.stop="changeStatusShowChat()"
          color="blue darken-2"
          dark
          fab
        >
          <v-icon v-if="changeChat === true">
            mdi-chat-processing-outline
          </v-icon>
          <v-icon v-else>
            mdi-chat-outline
          </v-icon>
        </v-btn>
      </template>
      <div id="chatMeB2B" class="dots">
        <div v-if="listChat.length !== 0 && (changeChat === true && fab)">
          <div v-for="(item, index) in listChatRealTime" :key="index" class="chatMainSub" style="display: inline;">
            <a>
              <span class="">
                <v-btn
                  :style="item.ImgShop !== '' && item.ImgShop !== null ? '' : `background-color: ${getRandomColor()};`"
                  max-height="65"
                  max-width="65"
                  fab
                  @click.stop="sentChatMore(item)"
                >
                  <v-avatar
                  v-if="item.ImgShop !== '' && item.ImgShop !== null"
                  >
                    <v-img
                      :alt="item.botName"
                      :src="item.ImgShop"
                      width="100%"
                      contain
                    ></v-img>
                  </v-avatar>
                  <v-avatar v-else >
                    <span class="white--text text-avatar font-weight-black mt-1" style="font-size:25px;">{{ !['ไ', 'แ', 'ใ', 'เ', 'โ'].includes(item.botName[0]) ? item.botName[0].toUpperCase() : item.botName[1].toUpperCase() }}</span>
                  </v-avatar>
                </v-btn>
              </span>
            </a>
          </div>
          <div v-if="this.countBot >= 5" class="chatMainSub" style="display: inline;">
            <v-menu
              left
              offset-x
              transition="slide-x-transition"
              :nudge-left="10"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  max-height="65"
                  max-width="65"
                  fab
                  v-bind="attrs"
                  v-on="on"
                >
                  <v-avatar>
                    <v-img
                      :alt="imagesOtherChat"
                      :src="imagesOtherChat"
                      width="100%"
                      contain
                    ></v-img>
                  </v-avatar>
                  <div class="textShowCss">
                    <span style="position: relative; text-align: center; top: 25%;">
                      +{{ listChatOter.length }}
                    </span>
                  </div>
                </v-btn>
              </template>
              <v-list dense>
                <v-list-item
                  v-for="(item, index) in listChatOter"
                  :key="index"
                  link
                >
                  <v-list-item-title @click.stop="sentChatMore(item)">{{ item.botName }}</v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </div>
        <div v-else-if="listChat.length !== 0 && changeChat === false && fab === true" class="chatMainSub" style="display: block;">
          <div>
            <v-btn
              max-height="65"
              max-width="65"
              fab
              @click.stop="sentChatMore(chatBotAdmin)"
            >
              <button aria-label="แชทกับแอดมิน" tooltip-position="left">
                <v-avatar class="">
                  <v-img
                  alt="ngc"
                  src="@/assets/ngc_logo.webp"
                  width="100%"
                  contain
                  ></v-img>
                </v-avatar>
              </button>
            </v-btn>
          </div>
        </div>
      </div>
    </v-speed-dial>
    <v-fab-transition v-if="!status && listChatRealTime.length === 0">
      <v-btn
        v-show="fabNoLogin"
        max-height="65"
        max-width="65"
        fab
        fixed
        bottom
        right
        :style="bottomStyle"
        @click="sentChatMore(chatBotAdmin)"
      >
        <button aria-label="แชทกับแอดมิน" tooltip-position="left">
          <v-badge
            color="red"
            :content="countNotiAdmin"
            :value="countNotiAdmin"
            top
            overlap
          >
            <v-avatar>
              <v-img
                alt="ngc"
                src="@/assets/ngc_logo.webp"
                width="100%"
              ></v-img>
            </v-avatar>
          </v-badge>
        </button>
      </v-btn>
    </v-fab-transition>
  </div>
  <!-- <div class="showChat">
    <ul v-if="status" id="chatMeB2B" class="dots">
      <span v-if="listChat.length !== 0">
        <li v-for="(item, index) in listChat" :key="index" class="chatMainSub" style="display: none;">
          <a >
            <span class="">
              <v-avatar style="margin-top: -9px; margin-left: -9px;">
                <v-img
                 @click="sentChatMore(item)"
                 :alt="item.botName"
                 :src="item.ImgShop"
                 width="100%"
                ></v-img>
              </v-avatar>
              <mark v-if="item.noti !== '0' && item.noti !== undefined && item.noti !== null" class="red tada">{{ item.noti }}</mark>
            </span>
          </a>
        </li>
        <li v-if="this.countBot >= 5" class="chatMainSub" style="display: none;">
          <a href="/ChatAll">
            <span class="mdi mdi-page-next" ></span>
          </a>
        </li>
        <li class="chatMainSub" style="display: block;">
          <button aria-label="แชทกับแอดมิน" tooltip-position="left">
            <span class="">
              <v-avatar class="" style="margin-top: -9px; margin-left: -9px;">
                <v-img
                 @click="sentChatMore(chatBotAdmin)"
                 alt="ngc"
                 src="@/assets/ngc_logo.webp"
                 width="100%"
                ></v-img>
              </v-avatar>
            </span>
          </button>
        </li>
        <li style="margin-bottom: 45px; ">
          <a aria-label="แชทกับร้านค้า" tooltip-position="left" >
            <span id="chatMeAll" class="mdi mdi-chat-outline" style="color: #ffffff;background-color: #18ace3;">
            </span>
          </a>
        </li>
      </span>
      <span v-else>
        <li class="chatMainSub" style="display: block;">
          <button aria-label="แชทกับแอดมิน" tooltip-position="left">
            <span class="">
              <v-avatar class="" style="margin-top: -9px; margin-left: -9px;">
                <v-img
                 @click="sentChatMore(chatBotAdmin)"
                 alt="ngc"
                 src="@/assets/ngc_logo.webp"
                 width="100%"
                ></v-img>
              </v-avatar>
            </span>
          </button>
        </li>
      </span>
    </ul>
    <ul v-else id="chatMeB2B" class="dots">
      <li class="chatMainSub" style="display: block;">
        <button aria-label="แชทกับแอดมิน" tooltip-position="left">
          <span class="">
            <v-avatar class="" style="margin-top: -9px; margin-left: -9px;">
              <v-img
               @click="sentChatMore(chatBotAdmin)"
               alt="ngc"
               src="@/assets/ngc_logo.webp"
               width="100%"
              ></v-img>
            </v-avatar>
          </span>
        </button>
      </li>
      <span v-if="listChat.length !== 0">
        <li v-for="(item, index) in listChat" :key="index" class="chatMainSub" style="display: none;">
          <a >
            <span class="">
              <v-avatar class="" style="margin-top: -9px; margin-left: -9px;">
                <v-img
                  @click="sentChatMore(item)"
                  :alt="item.botName"
                  :src="item.ImgShop"
                  width="100%"
                ></v-img>
              </v-avatar>
              <mark v-if="item.noti !== '0' && item.noti !== undefined && item.noti !== null" class="red tada">{{ item.noti }}</mark>
            </span>
          </a>
        </li>
        <li class="chatMainSub" style="display: none;">
        </li>
        <li style="margin-bottom: 45px; ">
          <a aria-label="แชทกับร้านค้า" tooltip-position="left" >
            <span id="chatMeAll" class="mdi mdi-chat-outline" style="color: #ffffff;background-color: #18ace3;">
            </span>
          </a>
        </li>
      </span>
    </ul>
  </div> -->
</template>
<script>
import axios from 'axios'
import { createChat } from './callChatMe'
import { Decode } from '@/services'
// import '@fortawesome/fontawesome-free/css/all.css'
// import '@fortawesome/fontawesome-free/js/all.js'
export default {
  data () {
    return {
      renderData: true,
      fab: true,
      changeChat: false,
      fabNoLogin: true,
      oneData: null,
      status: false,
      countBot: 0,
      countBotOther: 0,
      hover: false,
      listChat: [],
      listChatOter: [],
      chatBotAdmin: {
        ImgShop: '@/assets/ngc_logo.webp',
        botID: 'afdc57f4-159e-4afd-a7cb-d5a1cb6329b4',
        botName: 'ADMIN-NGC',
        botToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJib3RfaWQiOiJhZmRjNTdmNC0xNTllLTRhZmQtYTdjYi1kNWExY2I2MzI5YjQiLCJlbnYiOiJwcm8ifQ.AmSXqCpfWUAPVnBBDqBzwt69-PwCPwBOjBvxYjy-FGI',
        shopNGSID: '1',
        shopNGSName: 'ADMIN-NGC',
        shop_name_en: 'ADMIN-NGC'
      },
      imagesOtherChat: '',
      one_id: '',
      centrifuge: null,
      sub: null,
      listAllChat: [],
      countNotiAdmin: 0,
      countNotiAdminLogin: 0,
      countAllNotRead: 0
    }
  },
  computed: {
    bottomStyle () {
      return {
        bottom: ((this.$route.name === 'DetailProduct' || this.$route.name === 'checkoutExt' || this.$route.name === 'shoppingcartui') && this.MobileSize)
          ? '22vw'
          : '16px'
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    listChatRealTime () {
      return this.listChat
    }
  },
  created () {
    // console.log('pathname', window.location.pathname)
    this.$EventBus.$on('initChat', this.initChat)
    this.checkChatMe()
    // this.status = localStorage.getItem('oneData') !== null
    // if (this.status) {
    //   this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    //   // console.log('oneData', this.oneData)
    //   this.initChat()
    // } else {
    //   this.listChatNotLogin()
    // }
  },
  mounted () {
    this.$EventBus.$on('checkChatMe', this.checkChatMe)
    this.$el.querySelector('.v-speed-dial__list').addEventListener('click', (e) => {
      e.stopPropagation()
    })
  },
  watch: {
    // changeChat (val) {
    //   console.log('changeChat', val)
    // },
    // fab (val) {
    //   console.log('fab', val)
    // }
  },
  methods: {
    async checkChatMe () {
      this.status = localStorage.getItem('oneData') !== null
      if (this.status) {
        this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        this.one_id = this.oneData.user.one_id
        // console.log('oneData', this.oneData.user.one_id)
        this.initChat()
        this.getAllNotReadChat()
        this.getChatAdmin(this.chatBotAdmin)
        await this.getToken(this.oneData.user.one_id)
      } else {
        // this.listChatNotLogin(this.chatBotAdmin)
        // await this.getTokenNoLogin(20)
      }
    },
    async getTokenNoLogin (length, characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
      var randomString = ''
      const charactersLength = characters.length
      for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * charactersLength)
        randomString += characters.charAt(randomIndex)
      }
      this.one_id = randomString
      await this.getToken(randomString)
    },
    async getToken (id) {
      try {
        const response = await axios.get(`https://chat-plugin.one.th/backend/api/v1/onechat/jwt-centriful?user_id=${id}`)
        if (response.data.status === 200) {
          this.connectCentrifuge(response.data.data) // ส่ง token ที่ได้
        }
      } catch (err) {
        console.error('Token fetch error:', err)
      }
      // await this.axios.get(`https://chat-plugin.one.th/backend/api/v1/onechat/jwt-centriful?user_id=${id}`).then((response) => {
      //   // console.log(response)
      //   if (response.data.status === 200) {
      //     this.connectCentrifuge(response.data.data)
      //   }
      // }).catch(() => {
      //   // console.log(err.response)
      // })
    },
    connectCentrifuge (token) {
      this.centrifuge = new window.Centrifuge('wss://chat-plugin.one.th/connection/websocket')

      // ✅ ตั้งค่า Token แยกต่างหากใน v2
      this.centrifuge.setToken(token)

      // ✅ Event connect/disconnect
      this.centrifuge.on('connect', (msg) => {
        // console.log('✅ Connected:', msg)
      })

      this.centrifuge.on('disconnect', (msg) => {
        // console.log('❌ Disconnected:', msg.reason)
      })

      // ✅ Subscribe แบบ v2
      this.centrifuge.subscribe(`update-noti-chat-${this.one_id}`, (message) => {
        if (this.status) {
          setTimeout(() => {
            this.initChat()
            this.getAllNotReadChat()
            this.getChatAdmin(this.chatBotAdmin)
          }, 100)
        } else {
          // setTimeout(() => {
          //   this.listChatNotLogin(this.chatBotAdmin)
          // }, 100)
        }
        // console.log('📩 New message:', message.data) // ใช้ message.data สำหรับ payload
      })

      // ✅ Connect ตอนจบ
      this.centrifuge.connect()
    },
    getRandomColor () {
      var letters = '0123456789ABCDEF'
      var color = '#'
      for (var i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)]
      }
      // console.log('color***', color)
      return color
    },
    syncUpdate () {
      // const syncDatas = () => { document.querySelector('.chatMainSub') }
      var cretaes = document.querySelector('#cretae')
      var initial = cretaes.innerHTML
      cretaes.innerHTML = ''
      cretaes.innerHTML = initial
    },
    changeStatusShowChat () {
      // this.changeChat = !this.changeChat
      if (this.fab === false) {
        if (this.changeChat === true) {
          this.changeChat = true
        } else {
          this.changeChat = false
        }
      } else {
        if (this.fab === true) {
          this.changeChat = !this.changeChat
          // this.syncUpdate()
        } else {
          this.changeChat = false
        }
      }
      this.fab = true
      // this.$forceUpdate()
    },
    async getAllNotReadChat () {
      const sharetoken = await createChat.sharetoken()
      var data = {
        shared_token: sharetoken.data.shared_token
      }
      var response = await axios.post('https://chat-plugin.one.th/backend/api/v1/oneid/count-not-read-room-by-shared-token', data, '')
      if (response.data.status === 200) {
        this.countAllNotRead = response.data.data.number_not_read
      } else {
        this.countAllNotRead = 0
      }
    },
    async listChatNotLogin (item) {
      this.listChat = await JSON.parse(localStorage.getItem('userChat') || '[]')
      // console.log('vvvv', this.listChat)
      // const sharetoken = this.encodeToken(item.botToken)
      // var data = {
      //   shared_token: sharetoken
      // }
      // var response = await axios.post(`https://chat-plugin.one.th/backend/api/v1/oneid/list-bot-by-shared-token?bot_id=${item.botID}`, data, '')
      // if (response.data.status === 200) {
      //   this.countNotiAdmin = response.data.data[0].number_not_read
      // }
    },
    encodeToken (data) {
      data = data + this.randomString(5)
      return this.reverse(btoa(unescape(data))) + this.randomString(10)
    },
    reverse (s) {
      return s.split('').reverse().join('')
    },
    randomString (num) {
      var text = ''
      var possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
      for (var i = 0; i < num; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length))
      }
      return text
    },
    async initChat () {
      const req = {
        user_id: this.oneData.user.user_id
      }
      await this.$store.dispatch('actionslistBotChatWithUser', req)
      var res = await this.$store.state.ModuleHompage.statelistBotChatWithUser
      if (res.result === 'SUCCESS') {
        this.listAllChat = res.data.listChat
        await this.getCountNotReadEachChat()
        if (this.listAllChat.length < 5) {
          this.listChat = await this.listAllChat
          this.countBot = await res.data.count_bot_with_user
        } else {
          this.listChat = await this.listAllChat.slice(0, 4)
          if (this.listAllChat.length > 5) {
            this.listChatOter = await this.listAllChat.slice(this.listChat.length, this.listAllChat.length)
            this.listChatOter.countOtherBot = 0
            for (var i = 0; i < this.listChatOter.length; i++) {
              this.listChatOter.countOtherBot += this.listChatOter[i].number_not_read
            }
            this.imagesOtherChat = this.listChatOter[0].ImgShop
          }
          this.countBot = await res.data.count_bot_with_user
        }
      }
      // console.log('this.listChat', this.listChatOter)
      // console.log(res, 'res')
    },
    async getCountNotReadEachChat () {
      const sharetoken = await createChat.sharetoken()
      var data = {
        shared_token: sharetoken.data.shared_token
      }
      var response = await axios.post('https://chat-plugin.one.th/backend/api/v1/oneid/list-bot-by-shared-token?bot_id', data, '')
      if (response.data.status === 200) {
        this.listAllChat = this.listAllChat.map(item1 => {
          const match = response.data.data.find(item2 => item2.bot_id === item1.botID)
          return {
            ...item1,
            number_not_read: match ? match.number_not_read : 0 // ถ้าไม่เจอให้ใส่ 0
          }
        })
      }
    },
    async getChatAdmin (item) {
      const sharetoken = await createChat.sharetoken()
      var data = {
        shared_token: sharetoken.data.shared_token
      }
      var response = await axios.post(`https://chat-plugin.one.th/backend/api/v1/oneid/list-bot-by-shared-token?bot_id=${item.botID}`, data, '')
      if (response.data.status === 200) {
        this.countNotiAdminLogin = response.data.data[0].number_not_read
      }
    },
    async sentChatMore (item) {
      if (this.status) {
        // this.changeStatusShowChat()
        await createChat.sharetoken()
        await createChat.chatMe2(item)
        await this.initChat()
      } else {
        // const data = await {
        //   botID: item.botID,
        //   botToken: item.botToken
        // }
        // await createChat.chatMe(data)
        // ต้อง login ถึงจะ chat ได้
        this.$swal.fire({ text: 'กรุณาเข้าสู่ระบบก่อนแชทกับแอดมิน', icon: 'warning', timer: 2500, showConfirmButton: false })
        setTimeout(() => {
          this.$router.push({ path: '/Login' }).catch(() => {})
        }, 100)
        // this.$forceUpdate()
      }
    }
  }
}
</script>

<style>
.textShowCss {
  text-align:center;
  position: absolute;
  top:50%;
  right:50%;
  border-radius: 25px;
  width: 56px;
  height: 56px;
  transform:translate(50%, -50%);
  background: rgb(0, 0, 0, 0.35);
  color: white;
  font-size: 18px;
}
#create .v-speed-dial {
  position: absolute;
}

#create .v-btn--floating {
  position: relative;
}
</style>
<style scoped>
.bridge {
  background-color: red;
  border-radius: 10px;
  color: #FFFFFF;
  display: inline-block;
  font-size: 12px;
  height: 20px;
  letter-spacing: 0;
  line-height: 1;
  min-width: 20px;
  padding: 4px 6px;
  pointer-events: auto;
  text-align: center;
  text-indent: 0;
  top: auto;
  transition: 0.3s cubic-bezier(0.25, 0.8, 0.5, 1);
  white-space: nowrap;
}
.classChat {
  display: inline;
}
/* @import url("https://fonts.googleapis.com/css?family=Roboto:400,700");
@import url("https://fonts.googleapis.com/css?family=Merriweather+Sans:400,400italic");
@import url("//netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"); */

/* main {
  margin-left: 100px;
  padding: 20px;
} */

/* h1 {
  font-family: 'Merriweather Sans', sans-serif;
  font-weight: 400;
  font-size: 30px;
}
nav#sidebar {
  background-color: #34495e;
  position: fixed;
  width: 100px;
  height: 100%;
} */
.container {
  display: flex;
  gap: 30px;
  padding: 50px;
  padding-top: 100px;
  padding-bottom: 100px;

  width: 600px;
  margin: auto;
}

[aria-label][tooltip-position] {
  position: relative;
  letter-spacing: 0.1rem;
}

[aria-label][tooltip-position]::before,
[aria-label][tooltip-position]::after {
  --scale: 0;
  position: absolute;
  font-size: 1rem;
  transition: transform 100ms;
  transition-timing-function: linear;
}

[aria-label][tooltip-position]:hover::before,
[aria-label][tooltip-position]:hover::after {
  --scale: 1;
  color: #ffffff;
  width: 180px;
  transition-timing-function: cubic-bezier(0.25, 0.1, 0.45, 1.93);
}

[aria-label][tooltip-position]::before {
  content: attr(aria-label);
  background-color: var(--tooltip-color);
  padding: 0.5em;
  border-radius: 0.3em;
  /* width: max-content;
  max-width: 100%; */
  text-align: center;
  transform: scale(0);
}

[aria-label][tooltip-position]::after {
  content: "";
  border: var(--arrow-size) solid transparent;
}

[aria-label][tooltip-position]::before,
[aria-label][tooltip-position]::after {
  --tooltip-color: #18ace3;
  --arrow-size: 0.5rem;
  --scale: 0;

  z-index: 1;
  font-size: 1rem;
  transform: translate(var(--translate-x), var(--translate-y))
    scale(var(--scale));
}

/** Right  */

[aria-label][tooltip-position="right"]::before {
  --translate-x: calc(100% + var(--arrow-size));
  --translate-y: -50%;
  right: 0px;
  top: 50%;
  transform-origin: left center;
}

[aria-label][tooltip-position="right"]::after {
  --translate-x: calc(var(--arrow-size));
  --translate-y: -50%;
  right: 0px;
  top: 50%;
  border-right-color: var(--tooltip-color);
  transform-origin: left center;
}

/** Bottom  */

[aria-label][tooltip-position="bottom"]::before {
  --translate-x: -50%;
  --translate-y: calc(100% + var(--arrow-size));
  bottom: 0px;
  left: 50%;
  transform-origin: top center;
}

[aria-label][tooltip-position="bottom"]::after {
  --translate-x: -50%;
  --translate-y: calc(var(--arrow-size));
  left: 50%;
  bottom: 0px;
  border-bottom-color: var(--tooltip-color);
  transform-origin: top center;
}

/** Top */

[aria-label][tooltip-position="top"]::before {
  position: absolute;
  --translate-x: -50%;
  --translate-y: calc(-100% - var(--arrow-size));
  top: 0px;
  left: 50%;
  transform-origin: bottom center;
}

[aria-label][tooltip-position="top"]::after {
  --translate-x: -50%;
  --translate-y: calc(-1 * var(--arrow-size));
  top: 0px;
  left: 50%;
  border-top-color: var(--tooltip-color);
  transform-origin: bottom center;
}

/** Left */

[aria-label][tooltip-position="left"]::before {
  --translate-x: calc(-100% - var(--arrow-size));
  --translate-y: -50%;
  left: 0px;
  top: 50%;
  transform-origin: right center;
}

[aria-label][tooltip-position="left"]::after {
  --translate-x: calc(-1 * var(--arrow-size));
  --translate-y: -50%;
  left: 0px;
  top: 50%;
  border-left-color: var(--tooltip-color);
  transform-origin: right center;
}

/** Top */

[aria-label][tooltip-position="top"]::before {
  position: absolute;
  --translate-x: -50%;
  --translate-y: calc(-100% - var(--arrow-size));
  top: 0px;
  left: 50%;
  transform-origin: bottom center;
}

[aria-label][tooltip-position="top"]::after {
  --translate-x: -50%;
  --translate-y: calc(-1 * var(--arrow-size));
  top: 0px;
  left: 50%;
  border-top-color: var(--tooltip-color);
  transform-origin: bottom center;
}

/** Left */

[aria-label][tooltip-position="left"]::before {
  --translate-x: calc(-100% - var(--arrow-size));
  --translate-y: -50%;
  left: 0px;
  top: 50%;
  transform-origin: right center;
}

[aria-label][tooltip-position="left"]::after {
  --translate-x: calc(-1 * var(--arrow-size));
  --translate-y: -50%;
  left: 0px;
  top: 50%;
  border-left-color: var(--tooltip-color);
  transform-origin: right center;
}

.showChat {
  border-radius: 10px;
  margin-top: 0px;
  margin-right: 0px;
  margin-bottom: 0px !important;
  padding: 0px;
  border: 0px;
  background: transparent;
  position: fixed;
  z-index: 16000002;
  width: 50px;
  height: auto;
  right: 0px;
  bottom: 0%;
}
ul.dots * {
  /* disable border-box from bootstrap */
  box-sizing: content-box;
  list-style-type: none;
  /* เพิ่ม */
  z-index: 16000003;
  height: auto;
  width: 90px;
  /* position: relative; */
  cursor: pointer;
}
ul.dots a {
  text-decoration: none;
  font-size: 20px;
  color: #34495e;
}
ul.dots a:hover {
  text-decoration: none;
  font-size: 20px;
  color: #bdc3c7;
}
ul.dots li {
  display: box;
  position: relative;
  width: 100%;
  padding: 10px 0;
}
ul.dots li:hover {
  /* background: #fefeff; */
}
ul.dots li span {
  display: block;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  border-radius: 30px;
  background-color: #FFF;
  -webkit-box-shadow: 1px 1px 5px #808080;
  -moz-box-shadow: 1px 1px 5px #808080;
  box-shadow: 1px 1px 5px #808080;
  padding: 10px;
  width: 30px;
  height: 30px;
  margin: 0 auto;
  line-height: 30px;
  text-align: center;
  position: relative;
}
ul.dots li mark {
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  border-radius: 20px;
  border: 2px solid #FFF;
  width: 20px;
  height: 20px;
  background-color: #FF6B6B;
  position: absolute;
  top: -5px;
  left: 30px;
  font-size: 10px;
  line-height: 20px;
  font-family: 'Roboto', sans-serif;
  font-weight: 400;
  color: #FFF;
  font-weight: 700;
}
ul.dots li mark.green {
  background-color: #27ae60;
}
ul.dots li mark.blue {
  background-color: #3498db;
}
ul.dots li mark.red {
  background-color: #e44040;
}
ul.dots > li > a > span > mark {
  -webkit-animation-name: bounceIn;
          animation-name: bounceIn;
  -webkit-transform-origin: center bottom;
      -ms-transform-origin: center bottom;
          transform-origin: center bottom;
  -webkit-animation-duration: 1s;
          animation-duration: 1s;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
-webkit-animation-iteration-count: 1;
        animation-iteration-count: 1;
}
ul.dots > li:hover > a > span > mark {
  -webkit-animation-name: bounce;
          animation-name: bounce;
}
ul.dots > li:hover > a > span > mark.tada {
  -webkit-animation-name: tada;
          animation-name: tada;
}

li {
  list-style-type: circle;
}

code {
  background: #ecf0f1;
}

@-webkit-keyframes tada {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
            transform: scale3d(1, 1, 1);
  }

  10%, 20% {
    -webkit-transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
            transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
  }

  30%, 50%, 70%, 90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
            transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  40%, 60%, 80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
            transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  100% {
    -webkit-transform: scale3d(1, 1, 1);
            transform: scale3d(1, 1, 1);
  }
}

@keyframes tada {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
            transform: scale3d(1, 1, 1);
  }

  10%, 20% {
    -webkit-transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
            transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
  }

  30%, 50%, 70%, 90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
            transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  40%, 60%, 80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
            transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  100% {
    -webkit-transform: scale3d(1, 1, 1);
            transform: scale3d(1, 1, 1);
  }
}

</style>
