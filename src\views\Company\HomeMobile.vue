<template>
  <v-container :class="MobileSize ? 'mt-3' : 'mx-0'">
    <!-- Mobile -->
    <v-row class="mb-6" v-if="MobileSize">
      <v-col cols="12" md="12" sm="12" xs="12">
        <v-card class="mt-6" max-height="100%" height="100%" style="border-radius: 8px; border: 1px solid #F2F2F2;" elevation="0">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-storefront</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="font-size: 16px; line-height: 24px;">กลับไปหน้าการซื้อขาย</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <v-list-item-group color="#27AB9C">
              <v-list-item v-for="(item, i) in items" :key="i" :disabled="item.disable" @click="changePage(item.path)">
                <v-list-item-icon>
                  <v-icon>{{ item.action }}</v-icon>
                </v-list-item-icon>
                <v-list-item-content>
                  <v-list-item-title style="font-size: 16px; line-height: 24px;">
                    {{ item.title }}
                  </v-list-item-title>
                </v-list-item-content>
                <!-- <v-list-item-action v-if="item.title !== 'บริษัท'"> -->
                <v-list-item-action v-if="item.disable !== true">
                  <v-icon>mdi-chevron-right</v-icon>
                </v-list-item-action>
              </v-list-item>
            </v-list-item-group>
          </v-list>
          <div v-if="itemsCompany.length !== 0">
            <v-list nav>
              <v-list-item-group color="#27AB9C">
                <v-list-item v-for="(item, i) in itemsCompany" :key="i" :disabled="item.disable" @click="changePage(item.path)">
                  <v-list-item-icon>
                    <v-icon>{{ item.action }}</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title style="font-size: 16px; line-height: 24px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action v-if="item.title !== 'จัดการรายการสั่งซื้อ'">
                    <v-icon>mdi-chevron-right</v-icon>
                  </v-list-item-action>
                </v-list-item>
              </v-list-item-group>
            </v-list>
          </div>
        </v-card>
      </v-col>
      <!-- </v-navigation-drawer> -->
      <!-- <v-col cols="12" md="9">
        <v-main style="padding: 0px;">
          <v-container>
            <v-card max-height="100%" height="100%" width="100%" class="mt-3">
              <router-view></router-view>
            </v-card>
          </v-container>
        </v-main>
      </v-col> -->
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      dataDetail: [],
      items: [],
      itemsCompany: []
      // activeMenu: true,
      // defaultSelect: 0,
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/companyMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => { })
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    // this.$on('hook:beforeDestroy', () => {
    //   this.$EventBus.$off('chackAuthorityCompanyMenuMobile')
    // })
  },
  created () {
    this.$EventBus.$emit('resetSearch')
    this.$EventBus.$on('chackAuthorityCompanyMenuMobile', this.chackAuthorityCompanyMenuMobile)
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' })
    } else {
      this.$EventBus.$emit('getPath')
      this.$EventBus.$emit('CheckFooter')
      this.$EventBus.$on('ChangeActiveMenu', this.ChangeActiveMenu)
      this.chackAuthorityCompanyMenuMobile()
    }
  },
  methods: {
    async chackAuthorityCompanyMenuMobile () {
      this.dataDetail = ''
      if (localStorage.getItem('list_Company_detail') !== null) {
        this.dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_Company_detail')))
      }
      // console.log('dataDetail', this.dataDetail.can_use_function_in_company)
      var item1 = [
        { key: 0, action: 'mdi-domain', title: 'บริษัท', disable: true },
        { key: 1, title: this.dataDetail.compant_name_th, disable: true }
      ]
      var item2 = [{ key: 0, action: 'mdi-file-document-outline', title: 'จัดการรายการสั่งซื้อ', disable: true }]
      if (this.dataDetail.can_use_function_in_company.set_company === '1') {
        item1.push({ key: 2, title: 'จัดการข้อมูลบริษัท', path: '/detailCompanyMobile', disable: false })
      }
      if (this.dataDetail.can_use_function_in_company.set_company === '1') {
        item1.push({ key: 3, title: 'จัดการที่อยู่บริษัท', path: '/ManageAddressCompanyMobile', disable: false })
      }
      if (this.dataDetail.can_use_function_in_company.set_permission === '1') {
        item1.push({ key: 4, title: 'จัดการตำแหน่งภายในบริษัท', path: '/ManagePositionMobile', isDisabled: false })
      }
      if (this.dataDetail.can_use_function_in_company.set_permission === '1') {
        item1.push({ key: 5, title: 'จัดการผู้ใช้งานภายในบริษัท', path: '/ManageCompanyPostionUserMobile', isDisabled: false })
      }
      if (this.dataDetail.can_use_function_in_company.set_permission === '1') {
        item1.push({ key: 6, title: 'ร้านค้าคู่ค้า', path: '/PartnerMobile', isDisabled: false })
      }
      if (this.dataDetail.can_use_function_in_company.report === '1') {
        item1.push({ key: 7, title: 'รายงานรายจ่าย', path: '/ReportMobile', isDisabled: false })
      }
      if (this.dataDetail.can_use_function_in_company.set_company === '1') {
        item1.push({ key: 8, title: 'คูปองและคะแนน', path: '/CompanyCouposAndPointsMobile', isDisabled: false })
      }
      if (this.dataDetail.can_use_function_in_company.set_company === '1') {
        item1.push({ key: 9, title: 'e-Withholding Tax', path: '/eWHTCompanyMobile', isDisabled: false })
      }
      // item2
      if (this.dataDetail.can_use_function_in_company.order === '1') {
        item2.push({ key: 1, title: 'รายการสั่งซื้อ', path: '/orderCompanyMobile', isDisabled: false })
      }
      if (this.dataDetail.can_use_function_in_company.order === '1') {
        item2.push({ key: 2, title: 'ประวัติรายการสั่งซื้อ', path: '/orderRecordCompanyMobile', isDisabled: false })
      }
      if (this.dataDetail.can_use_function_in_company.approve_order === '1') {
        item2.push({ key: 3, title: 'จัดการรูปแบบการอนุมัติ', path: '/listApprovePositionMobile', isDisabled: false })
      }
      if (this.dataDetail.can_use_function_in_company.approve_order === '1') {
        item2.push({ key: 4, title: 'จัดการลำดับการอนุมัติ', path: '/listApproveSequenceMobile', isDisabled: false })
      }
      // if (this.dataDetail.can_use_function_in_company.approve_order === '1') {
      //   item2.push({ key: 3, title: 'จัดการผู้ซื้อองค์กร', path: '/ManageBuyerApproveMobile', isDisabled: false })
      // }
      // if (this.dataDetail.can_use_function_in_company.approve_order === '1') {
      //   item2.push({ key: 4, title: 'รายการอนุมัติ', path: '/listApproveOrderMobile', isDisabled: false })
      // }
      // if (this.dataDetail.can_use_function_in_company.approve_order === '1') {
      //   item2.push({ key: 5, title: 'รายการอนุมัติการสั่งซื้อ', path: '/ListApproveMobile', isDisabled: false })
      // }
      // if (this.dataDetail.can_use_function_in_company.order === '1') {
      //   item2.push({ key: 6, title: 'การร้องขอราคาพิเศษ', path: '/specialPriceBuyerRequestMobile', isDisabled: false })
      // }
      if (this.dataDetail.can_use_function_in_company.order === '1') {
        item2.push({ key: 7, title: 'ใบเสนอราคา', path: '/QUCompanyMobile', isDisabled: false })
      }
      if (this.dataDetail.can_use_function_in_company.order === '1') {
        item2.push({ key: 8, title: 'รายการอนุมัติ', path: '/listApproveCompanyMobile', isDisabled: false })
      }
      if (this.dataDetail.can_use_function_in_company.order === '1' || this.dataDetail.can_use_function_in_company.payment === '1') {
        item2.push({ key: 9, title: 'รับของ', path: '/ReceiveItemsMobile', isDisabled: false })
      }
      // if (this.dataDetail.can_use_function_in_company.order === '1') {
      //   item2.push({ key: 8, title: 'การชำระเงินแบบเครดิตเทอม', path: '/companyListCreditOrderMobile', isDisabled: false })
      // }
      // if (this.dataDetail.can_use_function_in_company.tracking === '1') {
      //   item2.push({ key: 9, title: 'ติดตามสถานะสินค้า', path: '/TackingCompanyMobile', isDisabled: false })
      // }
      // if (this.dataDetail.can_use_function_in_company.refund === '1') {
      //   item2.push({ key: 10, title: 'ติดตามการคืนสินค้า', path: '/refundCompanyMobile', isDisabled: false })
      // }
      // if (this.dataDetail.can_use_function_in_company.review === '1') {
      //   item2.push({ key: 11, title: 'การประเมินความพึงพอใจ', path: '/reviewCompanyMobile', isDisabled: false })
      // }
      this.items = []
      this.items = item1
      this.itemsCompany = []
      this.itemsCompany = item2
    },
    changePage (val) {
      // console.log(val)
      this.$EventBus.$emit('resetAdminShop')
      this.$router.push({ path: `${val}` }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.v-application ul, .v-application ol {
    padding: 0px 0px !important;
}
.v-application ol, .v-application ul {
    padding: 0px 0px !important;
}
</style>
