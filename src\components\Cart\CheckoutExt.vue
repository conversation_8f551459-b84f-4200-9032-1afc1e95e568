<template>
  <div>
    <!-- <v-overlay :value="overlay">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay> -->
    <v-breadcrumbs :items="items" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
      <template v-slot:divider>
        <v-icon>mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item :href="item.href" :disabled="item.disabled">
          <span
            :style="{
              color: item.disabled === true ? '#27AB9C' : '#636363',
              'font-size': '16px',
            }"
            >{{ item.text }}</span
          >
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <v-container>
      <v-row class="mt-1">
        <v-col cols="12" md="8">
          <v-row no-gutters>
            <!-- Desktop, ipadPro -->
            <v-col cols="12" md="12" v-if="!MobileSize" :id="MobileSize || IpadSize || IpadProSize ? '' : 'FormAddProduct'">
              <div v-if="firstValidChooseItem">
                <v-card class="pa-5"
                  elevation="0" style="border-radius: 8px; border: 1px solid #F2F2F2">
                  <div>
                    <v-img class="float-left mr-2" src="@/assets/map1.png" width="32" height="32"></v-img>
                    <p style="font-size: 22px; font-weight: 700;"><b>{{ $t('CheckOut.ShippingAddress')}}</b></p>
                  </div>
                  <v-spacer style="border-top: 1px solid #F2F2F2; margin-top: 16px;"></v-spacer>
                  <v-row dense>
                    <v-row>
                      <v-col>
                        <div v-if="CartAddress.length !== 0 && role.role === 'ext_buyer'">
                          <div v-if="userdetail.length !== 0 && (CartAddress[0].detail !== '' && CartAddress[0].detail !== null)">
                            <div v-for="(item, index) in userdetail" :key="index">
                              <div style="margin-top: 16px;" v-if="radios === 'radio-2' && item.default_address === 'Y' && item.detail !== '' && role.role === 'ext_buyer'">
                                <span style="font-size: 16px; font-weight: 600;">{{ item.first_name }} {{ item.last_name
                                }}</span>
                                <span class="px-1" style="color: #EBEBEB;">|</span>
                                <span style="font-size: 16px; font-weight: 600;">{{ item.phone }}</span>
                                <v-btn class="float-end" color="#27AB9C" text dark
                                  style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="changeAddress()"><span
                                    style="text-decoration-line: underline;">{{ $t('CheckOut.ChangeAddress')}}</span></v-btn>
                                  <v-col class="pl-0 pr-10 mb-2">
                                    <span style="font-size: 16px;">{{ item.detail }} {{ item.sub_district }} {{ item.district
                                    }} {{ item.province }} {{ item.zip_code }}</span><br>
                                    <span  v-if="item.note_address" >{{ $t('CheckOut.Notation')}}: {{ item.note_address }}</span>
                                  </v-col>
                              </div>
                            </div>
                          </div>
                          <div v-else-if="userdetail.length !== 0 && (CartAddress[0].detail === '' || CartAddress[0].detail === null)">
                            <div style="margin-top: 16px;">
                              <span style="font-size: 16px; font-weight: 600;">{{ CartAddress[0].first_name }} {{ CartAddress[0].last_name
                              }}</span>
                              <span class="px-1" style="color: #EBEBEB;">|</span>
                              <span style="font-size: 16px; font-weight: 600;">{{ CartAddress[0].phone }}</span>
                              <v-btn class="float-end" color="#27AB9C" text dark
                                style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="changeAddress()"><span
                                  style="text-decoration-line: underline;">{{ $t('CheckOut.ChangeAddress')}}</span></v-btn>
                                <v-col class="pl-0 pr-10 mb-2">
                                  <span style="font-size: 14px; font-weight: 600; color: red;">* {{ $t('CheckOut.IncompleteAddress')}}</span>
                                </v-col>
                            </div>
                          </div>
                        </div>
                        <div v-if="CartAddress.length === 0 && role.role === 'ext_buyer'">
                          <div v-if="userdetail.length === 1 && ((userdetail[0].detail === '' || userdetail[0].detail === null) || userdetail[0].default_address !== 'Y') && role.role === 'ext_buyer'">
                            <v-card class="mt-7" elevation="0"
                            style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="editAddress(userdetail[0])">
                              <v-card-text class="py-2">
                                <v-row class="my-2 px-3 ">
                                  <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                                  <span class="pl-2"
                                  style="font-weight: 500; font-size: 14px; color: #1B5DD6;">{{ $t('CheckOut.AddAddress')}}</span>
                                </v-row>
                              </v-card-text>
                            </v-card>
                          </div>
                          <div v-else-if="userdetail.length === 0 && role.role === 'ext_buyer'">
                            <v-card class="mt-7" elevation="0"
                            style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="addAddress()">
                              <v-card-text class="py-2">
                                <v-row class="my-2 px-3 ">
                                  <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                                  <span class="pl-2"
                                  style="font-weight: 500; font-size: 14px; color: #1B5DD6;">{{ $t('CheckOut.AddAddress')}}</span>
                                </v-row>
                              </v-card-text>
                            </v-card>
                          </div>
                        </div>
                      </v-col>
                    </v-row>
                  </v-row>
                </v-card>
              </div>
              <v-card v-if="itemsCart.length !== 0" class="pa-2" elevation="0" :style="firstValidChooseItem ? 'border-radius: 8px; border: 1px solid #F2F2F2; margin-bottom: 24px; margin-top: 24px;' : 'border-radius: 8px; border: 1px solid #F2F2F2; margin-bottom: 24px;'">
                <v-col cols="12" class="mt-2 pb-0">
                  <v-row dense>
                    <v-img class="float-left mr-2" src="@/assets/boxbox.png" max-width="32" max-height="32"></v-img>
                    <p style="font-size: 24px; font-weight: 700;" class="mb-0"><b>{{ $t('CheckOut.OrderList')}}</b></p>
                  </v-row>
                </v-col>
                <v-container v-for="(item, index) in itemsCart.choose_list" :key="index" grid-list-xs class="pt-0">
                  <div class="pa-0">
                    <a-table :data-source="item.product_list"
                      :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                      :columns="headers" :pagination="false">
                      <template slot="title">
                        <v-row class="text-left">
                          <v-col justify="center">
                            <b class=""
                              style="display: inline-block;  max-width: 450px; line-height: 35px; cursor: pointer; font-weight: 600; white-space: nowrap; font-size: 18px; color: #333333; text-overflow: ellipsis; overflow: hidden;"
                              @click="gotoShopDetail(item.seller_shop_name, item.seller_shop_id)">{{ $t('CheckOut.ShopName') }} : {{ item.seller_shop_name }}</b>
                            <v-btn class="float-end" color="#636363" text dark style="font-size:16px; font-weight: 500;"
                              @click="GetReviewQuotation(item)"><v-img class="mr-1" src="@/assets/preview_qt.png" max-width="24" max-height="24"></v-img><span
                                style="text-decoration-line: underline;">{{ $t('CheckOut.QuotationSample') }}</span></v-btn>
                            <v-spacer style="border-top: 1px solid #F2F2F2; margin-top: 6px;"></v-spacer>
                          </v-col>
                        </v-row>
                      </template>
                      <template slot="sku" slot-scope="text, record">
                        <v-col cols="12" class="pl-0">
                          <span style="font-size: 14px; font-weight: 600;">{{ record.sku }}</span>
                        </v-col>
                      </template>
                      <template slot="productdetails" slot-scope="text, record">
                        <v-row no-gutters>
                          <v-col cols="12" md="4" sm="4" class="px-0" style="text-align: -webkit-center;">
                            <v-img :src="`${record.product_image}`" contain style="border-radius: 4px; padding-right: 8px;"
                              :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                              @click="goProductDetail(record)" />
                            <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                              style="border-radius: 4px; padding-right: 8px;" v-else @click="goProductDetail(record)" />
                          </v-col>
                          <v-col cols="12" md="8" sm="8" class="pt-0">
                            <p class="mb-0" style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_name }}</p>
                            <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                              style="font-size: 16px; color: #989898;">{{ record.product_attribute_detail.key_1_value }} : <span class="mb-0"
                                style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                            </p>
                            <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                              style="font-size: 16px; color: #989898;">{{ record.product_attribute_detail.key_2_value }} : <span class="mb-0"
                                style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                            </p>
                            <v-chip :color="record.product_type === 'general' ? '#3EC6B6' : '#FAC352'" class="prodcutsType" label small style="border-radius: 6px !important; height: 20px;">{{record.product_type === 'general'? `${$t('CheckOut.GeneralProduct')}`:`${$t('CheckOut.ServiceProduct')}`}}</v-chip>
                          </v-col>
                        </v-row>
                      </template>
                      <template slot="revenue_default" slot-scope="text, record">
                        <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ Number(record.vat_default === 'yes' ? parseFloat(record.revenue_default) + parseFloat(record.vat_include):record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </template>
                      <template slot="quantity" slot-scope="text, record">
                        <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ Number(record.quantity).toLocaleString() }}</span>
                      </template>
                      <template slot="revenue_vat" slot-scope="text, record">
                        <span v-if="record.vat_default === 'no'" style="font-size: 16px; font-weight: 400; color: #333333;">{{
                          Number(record.show_price).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span>
                        <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">{{
                          Number(record.show_price).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span>
                      </template>
                      <template slot="item_code_pr" slot-scope="text, record">
                        <v-row>
                          <v-col>
                            <v-text-field v-model="record.item_code_pr_buyer" outlined dense style="height: 50px;" ></v-text-field>
                          </v-col>
                        </v-row>
                      </template>
                      <template slot="footer">
                        <v-row class="align-center">
                          <v-col class="px-1" :cols="MobileSize ? '12' : '6'">
                            <v-btn style="padding: 0;" text v-if="CouponData.filter(coupon => coupon.seller_shop_id === item.seller_shop_id).length === 0"
                                    color="#F56E22"
                                    @click="clickCoupon('', PointData, XBaht, item)"
                                    class="pr-5">
                                    <v-img src="@/assets/TicketSale.png" width="26" height="26">
                                    </v-img>
                              <span style="font-size: 16px; font-weight: 500;" class="px-2 text-decoration-underline">{{ $t('CheckOut.UseCouponShop') }}</span>
                            </v-btn>
                            <v-chip v-else
                                    color="#FFF6F1"
                                    style="color: #464646;">
                              <v-img src="@/assets/seclectTicket.png" width="26" height="26">
                              </v-img>
                              <span style="font-size: 16px; font-weight: 500;" v-if="CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id).coupon_type === 'discount'" class="px-2">
                                {{MobileSize ? `${$t('CheckOut.ApplyStoreVoucherMobile')}` : `${$t('CheckOut.ApplyStoreVoucher')}`}} {{ CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id).discount_type === 'baht' ?
                                          `${CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id).discount_amount}฿` :
                                          `${CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id).discount_amount}%` }}
                              </span>
                              <span style="font-size: 16px; font-weight: 500;" v-else-if="CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id).coupon_type === 'free_shipping'" class="px-2">
                                {{MobileSize ? `${$t('CheckOut.FreeShippingMobile')}` : `${$t('CheckOut.FreeShipping')}`}} {{ CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id).discount_type === 'baht' ?
                                                `${CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id).discount_amount}฿` :
                                                `${CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id).discount_amount}%` }}
                              </span>
                              <span style="font-size: 16px; font-weight: 500;" v-else class="px-2">{{$t('CheckOut.Free')}}</span>
                              <v-icon
                                      @click.stop="closeCoupon(item)"
                                      style="color: #A1A1A1;">
                                mdi-close
                              </v-icon>
                            </v-chip>
                          </v-col>
                        <v-col :class="MobileSize ? 'px-1' : 'px-1 text-end'" :cols="MobileSize ? '12' : '6'" v-if="PointData.filter(point => point.seller_shop_id === item.seller_shop_id).length === 0">
                          <v-btn style="padding: 0;" text :disabled="item.seller_shop_point_data[0].seller_use_point === 'no' || item.seller_shop_point_data[0].seller_use_point === null" :class="item.seller_shop_point_data[0].seller_use_point === 'no' || item.seller_shop_point_data[0].seller_use_point === null ? 'disChipClickCoupon' : ''" class="mr-3" v-if="CouponData.length !== 0" color="#FFC107" @click="clickPoint(getCouponId(), PointData, parseFloat(item.seller_shop_point_data[0].x_baht) / parseFloat(item.seller_shop_point_data[0].x_point), item.total_coupon_discount, item)">
                            <v-img v-if="item.seller_shop_point_data[0].seller_use_point === 'no' || item.seller_shop_point_data[0].seller_use_point === null" src="@/assets/Rubledis.png" width="18" height="18">
                            </v-img>
                            <v-img v-else src="@/assets/Ruble.png" width="18" height="18">
                            </v-img>
                            <span style="font-size: 16px; font-weight: 500;" class="px-2 text-decoration-underline">{{$t('CheckOut.UseStorePoint')}}</span>
                          </v-btn>
                          <v-btn style="padding: 0;" text :disabled="item.seller_shop_point_data[0].seller_use_point === 'no' || item.seller_shop_point_data[0].seller_use_point === null" :class="item.seller_shop_point_data[0].seller_use_point === 'no' || item.seller_shop_point_data[0].seller_use_point === null ? 'disChipClickCoupon' : ''" class="mr-3" v-else color="#FFC107" @click="clickPoint('', PointData, parseFloat(item.seller_shop_point_data[0].x_baht) / parseFloat(item.seller_shop_point_data[0].x_point), item.total_coupon_discount, item)">
                            <v-img v-if="item.seller_shop_point_data[0].seller_use_point === 'no' || item.seller_shop_point_data[0].seller_use_point === null" src="@/assets/Rubledis.png" width="18" height="18">
                            </v-img>
                            <v-img v-else src="@/assets/Ruble.png" width="18" height="18">
                            </v-img>
                            <span style="font-size: 16px; font-weight: 500;" class="px-2 text-decoration-underline">{{$t('CheckOut.UseStorePoint')}}</span>
                          </v-btn>
                        </v-col>
                        <v-col :class="MobileSize ? 'px-1' : 'px-1 text-end'" :cols="MobileSize ? '12' : '6'" v-if="PointData.length !== 0 && PointData.find(p => p.seller_shop_id === item.seller_shop_id) !== undefined">
                          <v-chip color="#FFF6F1" style="color: #464646;">
                          <v-img src="@/assets/seclectPoint.png" width="26" height="26">
                          </v-img>
                          <span style="font-size: 16px; font-weight: 500;" class="px-2">{{$t('CheckOut.Discount')}} {{PointData.find(p => p.seller_shop_id === item.seller_shop_id).total_point.toFixed(2)}}฿</span>
                            <v-icon @click.stop="closePoint(item)" style="color: #A1A1A1;">mdi-close</v-icon>
                          </v-chip>
                        </v-col>
                        </v-row>
                      <!-- <v-card v-if="item.isEtax === 'yes'" class="pa-0 mt-6">
                      </v-card> -->
                      <v-row dense v-if="item.isEtax === 'yes'" class="pa-0 mt-5">
                        <v-row>
                          <v-col cols="12" :class="item.radiostax === 'radiotax-1' ? 'py-0 px-0' : 'pt-0 px-0'" style="border-top: 1px solid #60617029;">
                            <v-radio-group v-model="item.radiostax" row class="mt-0" hide-details>
                              <v-col cols="6">
                                <v-row dense class="">
                                  <v-img class="mx-2" src="@/assets/Capa_1.png" max-width="24" max-height="24"></v-img>
                                  <span style="font-size: 18px; font-weight: 600;">{{$t('CheckOut.AddressTaxInvoiceIssuance')}}</span>
                                </v-row>
                              </v-col>
                              <v-col cols="6" style="align-content: center; justify-items: end;">
                                <v-row dense>
                                  <v-radio class="pr-4 custom-radio-checkout" value="radiotax-1" @click="getCart()">
                                    <template v-slot:label>
                                      <span style="font-size: 16px;">{{$t('CheckOut.ReceiveTaxInvoice')}}</span>
                                    </template>
                                  </v-radio>
                                  <v-radio class="custom-radio-checkout" value="radiotax-2" @click="getCart()"><template v-slot:label>
                                      <span style="font-size: 16px;">{{$t('CheckOut.NotReceiveTaxInvoice')}}</span>
                                    </template>
                                  </v-radio>
                                </v-row>
                              </v-col>
                            </v-radio-group>
                          </v-col>
                          <v-col cols="12" class="pt-0" v-if="invoicedetailDefault.length !== 0 && item.invoice_id === '' && item.radiostax === 'radiotax-1'">
                            <v-spacer style="border-top: 1px solid #F2F2F2;"></v-spacer>
                            <div class="pt-5 px-2">
                              <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                                <div style="display: flex; align-items: center; gap: 4px;">
                                  <span class="text-truncate" style="font-size: 16px; font-weight: 600; max-width: 250px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                                    {{ invoicedetailDefault[0].name }}
                                  </span>
                                  <span style="color: #EBEBEB;">|</span>
                                  <v-chip
                                    :color="invoicedetailDefault[0].tax_type === 'Personal' ? '#E9F5FF' : '#FFF6F1'"
                                    small
                                    style="height: 20px;"
                                  >
                                    <span
                                      style="font-size: 14px;"
                                      :style="invoicedetailDefault[0].tax_type === 'Personal' ? 'color: #269AFD;' : 'color: #EF6C00;'"
                                    >
                                      {{ invoicedetailDefault[0].tax_type === 'Personal' ? `${$t('CheckOut.Individual')}` : `${$t('CheckOut.JuristicPerson')}` }}
                                    </span>
                                  </v-chip>
                                </div>
                                <v-btn color="#27AB9C" text dark small
                                       style="font-size: 14px; font-weight: 500; text-decoration: underline;"
                                       @click="openListTaxAddress(item)">
                                  {{$t('CheckOut.ChangeAddress')}}
                                </v-btn>
                              </div>
                              <v-col class="pa-0 pt-2" v-if="invoicedetailDefault[0].tax_type !== 'Personal'">
                                <span style="font-size: 16px;">{{$t('CheckOut.BranchCode')}}: <b>{{ invoicedetailDefault[0].branch_id }}</b></span>
                              </v-col>
                              <v-col class="pa-0">
                                <span style="font-size: 16px;">{{$t('CheckOut.TaxNumber')}}: <b>{{ invoicedetailDefault[0].tax_id }}</b></span>
                              </v-col>
                              <v-col class="pl-0 pt-1 pr-14">
                                <span style="font-size: 16px;">{{$t('CheckOut.Address')}}: {{ invoicedetailDefault[0].address }} {{ invoicedetailDefault[0].sub_district }} {{ invoicedetailDefault[0].district }} {{ invoicedetailDefault[0].province }} {{ invoicedetailDefault[0].postal_code }}</span>
                              </v-col>
                            </div>
                          </v-col>
                          <v-col cols="12" class="pt-0" v-else-if="item.invoice_id !== '' && item.radiostax === 'radiotax-1'">
                            <v-spacer style="border-top: 1px solid #F2F2F2;"></v-spacer>
                            <div v-for="(invoice, idx) in invoicedetail" :key="idx">
                              <div v-if="item.invoice_id === invoice.id" class="pt-5 px-2">
                              <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                                <div style="display: flex; align-items: center; gap: 4px;">
                                  <span class="text-truncate" style="font-size: 16px; font-weight: 600; max-width: 250px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                                    {{ invoicedetailDefault[0].name }}
                                  </span>
                                  <span style="color: #EBEBEB;">|</span>
                                  <v-chip
                                    :color="invoicedetailDefault[0].tax_type === 'Personal' ? '#E9F5FF' : '#FFF6F1'"
                                    small
                                    style="height: 20px;"
                                  >
                                    <span
                                      style="font-size: 14px;"
                                      :style="invoicedetailDefault[0].tax_type === 'Personal' ? 'color: #269AFD;' : 'color: #EF6C00;'"
                                    >
                                      {{ invoicedetailDefault[0].tax_type === 'Personal' ? `${$t('CheckOut.Individual')}` : `${$t('CheckOut.JuristicPerson')}` }}
                                    </span>
                                  </v-chip>
                                </div>
                                <v-btn color="#27AB9C" text dark small
                                       style="font-size: 14px; font-weight: 500; text-decoration: underline;"
                                       @click="openListTaxAddress(item)">
                                  {{$t('CheckOut.ChangeAddress')}}
                                </v-btn>
                              </div>
                              <v-col class="pa-0 pt-2" v-if="invoicedetailDefault[0].tax_type !== 'Personal'">
                                <span style="font-size: 16px;">{{$t('CheckOut.BranchCode')}}: <b>{{ invoicedetailDefault[0].branch_id }}</b></span>
                              </v-col>
                              <v-col class="pa-0">
                                <span style="font-size: 16px;">{{$t('CheckOut.TaxNumber')}}: <b>{{ invoicedetailDefault[0].tax_id }}</b></span>
                              </v-col>
                              <v-col class="pl-0 pt-1 pr-14">
                                <span style="font-size: 16px;">{{$t('CheckOut.Address')}}: {{ invoicedetailDefault[0].address }} {{ invoicedetailDefault[0].sub_district }} {{ invoicedetailDefault[0].district }} {{ invoicedetailDefault[0].province }} {{ invoicedetailDefault[0].postal_code }}</span>
                              </v-col>
                            </div>
                            </div>
                          </v-col>
                          <v-col cols="12" class="pt-0 pb-6 pt-0 px-6" v-if="invoicedetailDefault.length === 0 && invoicedetail.length === 0 && item.radiostax === 'radiotax-1'">
                            <v-spacer style="border-top: 1px solid #F2F2F2;"></v-spacer>
                            <v-card elevation="0" class="mt-4"
                              style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="openModalTaxAddress()">
                              <v-card-text class="py-2">
                                <v-row class="my-2 px-3 ">
                                  <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                                  <span class="pl-2" style="font-weight: 500; font-size: 14px; color: #1B5DD6;">{{$t('CheckOut.AddAddress')}}</span>
                                </v-row>
                              </v-card-text>
                            </v-card>
                          </v-col>
                        </v-row>
                      </v-row>
                      <v-row dense class="pa-0 mt-4">
                        <v-row>
                          <v-col class="pt-0 px-0 pb-2 d-flex" style="border-top: 1px solid #60617029;">
                              <v-col :cols="IpadProSize ? '3' : '2'" class="align-content-center">
                                <v-row dense class="">
                                  <v-img class="mx-2" src="@/assets/USER_INTERFACE.png" max-width="26" max-height="32" contain></v-img>
                                  <span style="font-size: 16px; font-weight: 600; ">{{$t('CheckOut.Notation')}}</span>
                                </v-row>
                              </v-col>
                              <v-col :cols="IpadProSize ? '9' : '10'" class="align-content-center">
                                <v-row dense class="">
                                  <v-text-field @keypress="CheckSpacebar($event)" v-model="item.remark_to_shop" :placeholder="$t('CheckOut.NoteToStore')" style="border-radius: 8px;" outlined dense hide-details clearable></v-text-field>
                                </v-row>
                              </v-col>
                          </v-col>
                        </v-row>
                      </v-row>
                      <v-row dense v-if="item.product_list.some(productitem => (productitem.product_type === 'general'))" class="pa-0 mt-4">
                        <v-row>
                          <v-col class="pt-0 px-0 pb-2" style="border-top: 1px solid #60617029;">
                            <v-radio-group v-model="item.radios" row class="" hide-details>
                              <v-col cols="4">
                                <v-row dense class="">
                                  <v-img class="mx-2" src="@/assets/box_2.png" max-width="28" max-height="28" contain></v-img>
                                  <span style="font-size: 16px; font-weight: 600; ">{{$t('CheckOut.ShippingType')}}</span>
                                </v-row>
                              </v-col>
                              <v-col cols="8" style="align-content: center; justify-items: end;">
                                <v-row dense>
                                  <v-radio v-if="item.store_front === 'yes'" value="radio-1" class="pr-4 custom-radio-checkout" :disabled="item.radios === 'radio-1'" @click="ClearRadio(item)"><template v-slot:label>
                                    <span style="font-size: 16px;">{{$t('CheckOut.PickUpAStore')}}</span>
                                  </template>
                                </v-radio>
                                <v-radio value="radio-2" class="custom-radio-checkout" :disabled="item.radios === 'radio-2'" @click="ClearRadio(item)"><template v-slot:label>
                                    <span style="font-size: 16px;">{{$t('CheckOut.ProductDelivery')}}</span>
                                  </template>
                                </v-radio>
                                </v-row>
                              </v-col>
                            </v-radio-group>
                            <div v-if="item.radios === 'radio-2' && item.shipping_method.length !== 0" class="pa-0" style="border-top: 1px solid #60617029;">
                              <div v-if="item.product_list.some(productitem => (role.role === 'ext_buyer' && productitem.product_type === 'general' && item.radios !== 'radio-1' && item.shipping_method.length !== 0))">
                                <v-col cols="12" class="py-2">
                                  <v-chip v-if="item.shipping_data.length === 0 && item.shipping_data !== undefined" color="#3EC6B6" text :disabled="btnEstimateCost === false" :class="btnEstimateCost === false ? '' : 'theme--dark'" outlined
                                    style="font-size: 16px; font-weight: 500;" @click="EstimateCost(item)">
                                    <v-img src="@/assets/star--filled.png" width="18" height="18">
                                    </v-img>
                                    <span class="pl-2">{{$t('CheckOut.ChooseShipping')}}</span>
                                  </v-chip>
                                  <v-chip v-else-if="item.shipping_data.length !== 0 && item.shipping_data !== undefined" text outlined
                                    style="font-size: 16px; font-weight: 500; background-color: rgb(243, 246, 255) !important; border: 0px !important;">
                                    <v-img src="@/assets/star--filled.png" width="18" height="18">
                                    </v-img>
                                    <span class="pl-2" style="color: #464646;">{{item.shipping_data.tpl_name}}<span> {{item.shipping_data.netEstimatePrice}}฿</span></span>
                                  </v-chip>
                                  <v-btn v-if="item.shipping_data.length !== 0 && item.shipping_data !== undefined" small text style="color: #1B5DD6; font-size: 16px; font-weight: 500;" @click="EstimateCost(item)">{{$t('CheckOut.ChooseOtherShipping')}}</v-btn>
                                </v-col>
                              </div>
                            </div>
                            <div v-if="item.radios === 'radio-2' && item.shipping_method.length === 0" class="pa-0" style="border-top: 1px solid #60617029;">
                              <div v-if="item.product_list.some(productitem => (role.role === 'ext_buyer' && productitem.product_type === 'general' && item.radios !== 'radio-1' && item.shipping_method.length === 0))">
                                <v-col cols="12" class="py-2">
                                  <v-chip text outlined
                                    style="font-size: 16px; font-weight: 500; background-color: rgb(243, 246, 255) !important; border: 0px !important;">
                                    <v-img src="@/assets/star--filled.png" width="18" height="18">
                                    </v-img>
                                    <span class="pl-2" style="color: #464646;">{{$t('CheckOut.SentByStore')}}</span>
                                  </v-chip>
                                </v-col>
                              </div>
                            </div>
                            <div v-if="item.radios === 'radio-1'" class="px-4" style="border-top: 1px solid #60617029;">
                              <div style="margin-top: 16px;">
                                <span style="font-size: 16px; font-weight: 600;">{{ item.product_list[0].seller_shop_name
                                }}</span>
                                <span class="px-1" style="color: #EBEBEB;">|</span>
                                <span style="font-size: 16px; font-weight: 600;">{{ item.shipping_detail.data_seller_address[0].phone !== undefined ? item.shipping_detail.data_seller_address[0].phone : '-' }}</span>
                                  <v-col class="pl-0 pr-10 mb-2">
                                    <span style="font-size: 16px;"> {{$t('CheckOut.HouseNumber')}} {{ item.shipping_detail.data_seller_address[0].house_no}} {{ item.shipping_detail.data_seller_address[0].detail }} {{$t('CheckOut.SubDistrict')}} {{ item.shipping_detail.data_seller_address[0].sub_district }}
                                      {{$t('CheckOut.District')}} {{ item.shipping_detail.data_seller_address[0].district }} {{$t('CheckOut.Province')}} {{ item.shipping_detail.data_seller_address[0].province }} {{ item.shipping_detail.data_seller_address[0].zipcode }}
                                      </span>
                                  </v-col>
                                  <v-row>
                                    <v-col cols="6">
                                      <span style="line-height: 24px; font-size: 16px; color: #333333;">
                                        {{$t('CheckOut.ReceivingDate')}} <span style="color: red;">*</span>
                                      </span>
                                      <v-menu
                                        v-model="item.menu"
                                        :close-on-content-click="false"
                                        :nudge-right="40"
                                        transition="scale-transition"
                                        offset-y
                                        min-width="auto"
                                      >
                                        <template v-slot:activator="{ on, attrs }">
                                          <v-text-field
                                            v-model="item.contractDate"
                                            style="border-radius: 8px;"
                                            outlined
                                            dense
                                            :placeholder="$i18n.locale === 'th' ? 'วว/ดด/ปปปป' : 'DD/MM/YYYY'"
                                            readonly
                                            v-bind="attrs"
                                            v-on="on"
                                          >
                                            <v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon>
                                          </v-text-field>
                                        </template>
                                        <v-date-picker
                                          v-model="item.dates"
                                          @input="setValueDate(index, item.dates)"
                                          :locale="$i18n.locale === 'th' ? 'th' : 'en'"
                                          scrollable
                                          no-title
                                          :min="today"
                                          :max="futureDate"
                                          @change="updateTime()"
                                        ></v-date-picker>
                                      </v-menu>
                                    </v-col>
                                    <v-col cols="6" :class="{ 'is-disabled': item.dates === '' }">
                                      <span style="line-height: 24px; font-size: 16px; color: #333333;">
                                        {{$t('CheckOut.ReceivingTime')}} <span style="color: red;">*</span>
                                      </span>
                                      <v-col class="pt-0 pl-0" v-if="item.dates === today">
                                        <template>
                                          <a-space direction="vertical" style="width: 100%;">
                                            <a-time-picker
                                              v-model="item.timeselecttoday"
                                              :bordered="false"
                                              style="width: 100%;"
                                              :format="$i18n.locale === 'th' ? 'HH:mm น.' : 'HH:mm'"
                                              size="large"
                                              :placeholder="$i18n.locale === 'th' ? '00.00 น.' : '00.00'"
                                              :disabled="!item.dates"
                                              :disabledHours="disabledHours"
                                              :disabledMinutes="disabledMinutes"
                                              @change="updateTime()"
                                            />
                                            <a-time-range-picker
                                              :bordered="false"
                                              style="width: 100%;"
                                              :disabled="!item.dates"
                                            />
                                          </a-space>
                                        </template>
                                      </v-col>
                                      <v-col class="pt-0 pl-0" v-else>
                                        <template>
                                          <a-space direction="vertical" style="width: 100%;">
                                            <a-time-picker
                                              v-model="item.timeselect"
                                              :bordered="false"
                                              style="width: 100%;"
                                              :format="$i18n.locale === 'th' ? 'HH:mm น.' : 'HH:mm'"
                                              size="large"
                                              :placeholder="$i18n.locale === 'th' ? '00.00 น.' : '00.00'"
                                              :disabled="!item.dates"
                                              @change="updateTime()"
                                            />
                                            <a-time-range-picker
                                              :bordered="false"
                                              style="width: 100%;"
                                              :disabled="!item.dates"
                                            />
                                          </a-space>
                                        </template>
                                      </v-col>
                                    </v-col>
                                  </v-row>
                              </div>
                            </div>
                          </v-col>
                        </v-row>
                      </v-row>
                      </template>
                    </a-table>
                    <a-table v-if="item.product_free.length !== 0" :data-source="item.product_free"
                      :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                      :columns="headers" :pagination="false">
                      <template slot="title">
                        <v-row class="text-left">
                          <v-col justify="center">
                            <v-img class="float-left" src="@/assets/icons/nullproduct.png" width="30" height="30" contain></v-img>
                            <b class="ml-2"
                              style="line-height: 35px; font-size: 18px; font-weight: 600; color: #F4BC5F;">{{$t('CheckOut.Free')}}</b>
                          </v-col>
                        </v-row>
                      </template>
                      <template slot="sku" slot-scope="text, record">
                        <v-col cols="12" class="pl-0">
                          <span style="font-size: 14px; font-weight: 600;">{{ record.sku }}</span>
                        </v-col>
                      </template>
                      <template slot="productdetails" slot-scope="text, record">
                        <v-row no-gutters>
                          <v-col cols="12" md="4" sm="4" class="px-0" style="text-align: -webkit-center;">
                            <v-img :src="`${record.product_image}`" contain style="border-radius: 4px; padding-right: 8px;"
                              :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                              @click="goProductDetail(record)" />
                            <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                              style="border-radius: 4px; padding-right: 8px;" v-else @click="goProductDetail(record)" />
                          </v-col>
                          <v-col cols="12" md="8" sm="8" class="pt-0">
                            <p class="mb-0" style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_name }}</p>
                            <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                              style="font-size: 16px; font-weight: 400; color: #989898;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                            </p>
                            <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                              style="font-size: 16px; font-weight: 400; color: #989898;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                            </p>
                            <v-chip :color="record.product_type === 'general' ? '#3EC6B6' : '#FAC352'" class="prodcutsType" label small style="border-radius: 6px !important; height: 20px;">{{record.product_type === 'general'? `${$t('CheckOut.GeneralProduct')}`:`${$t('CheckOut.ServiceProduct')}`}}</v-chip>
                          </v-col>
                        </v-row>
                      </template>
                      <template slot="revenue_default">
                          <span style="font-size: 16px; font-weight: 400; color: #333333;">0</span>
                      </template>
                      <template slot="quantity" slot-scope="text, record">
                        <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ Number(record.quantity).toLocaleString() }}</span>
                      </template>
                      <template slot="revenue_vat" slot-scope="text, record">
                        <span v-if="record.vat_default === 'no'" style="font-size: 16px; font-weight: 400; color: #333333;">0
                          </span>
                        <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">
                          0</span>
                      </template>
                    </a-table>
                  </div>
                </v-container>
              </v-card>
            </v-col>
            <!-- Mobile-->
            <v-col cols="12" md="12" v-if="MobileSize">
              <div v-if="firstValidChooseItem">
                <v-card class="pa-5"
                  elevation="0" style="border-radius: 8px; border: 1px solid #F2F2F2" >
                  <div>
                    <v-img class="float-left mr-2" src="@/assets/map1.png" width="22" height="22"></v-img>
                    <p style="font-size: 16px; font-weight: 700;"><b>{{$t('CheckOut.ShippingAddress')}}</b></p>
                  </div>
                  <v-spacer style="border-top: 1px solid #F2F2F2; margin-top: 16px;"></v-spacer>
                  <v-row dense>
                    <v-row>
                      <v-col>
                        <div v-if="CartAddress.length !== 0 && role.role === 'ext_buyer'">
                          <div v-if="userdetail.length !== 0 && (CartAddress[0].detail !== '' && CartAddress[0].detail !== null)">
                            <div v-for="(item, index) in userdetail" :key="index">
                              <div style="margin-top: 16px;" v-if="radios === 'radio-2' && item.default_address === 'Y' && item.detail !== '' && role.role === 'ext_buyer'">
                                <span style="font-size: 14px; font-weight: 600;">{{ item.first_name }} {{ item.last_name
                                }}</span>
                                <span class="px-1" style="color: #EBEBEB;">|</span>
                                <span style="font-size: 14px; font-weight: 600;">{{ item.phone }}</span>
                                <v-btn class="float-end" color="#27AB9C" text dark
                                  style="font-size: 12px; font-weight: 500; margin-top: -5px;" @click="changeAddress()"><span
                                    style="text-decoration-line: underline;">{{$t('CheckOut.ChangeAddress')}}</span></v-btn>
                                  <v-col class="pl-0 pr-10 mb-2">
                                    <span style="font-size: 14px;">{{ item.detail }} {{ item.sub_district }} {{ item.district
                                    }} {{ item.province }} {{ item.zip_code }}</span><br>
                                    <span  v-if="item.note_address" >{{$t('CheckOut.Notation')}} : {{ item.note_address }}</span>
                                  </v-col>
                              </div>
                            </div>
                          </div>
                          <div v-else-if="userdetail.length !== 0 && (CartAddress[0].detail === '' || CartAddress[0].detail === null)">
                            <div style="margin-top: 16px;">
                              <span style="font-size: 14px; font-weight: 600;">{{ CartAddress[0].first_name }} {{ CartAddress[0].last_name
                              }}</span>
                              <span class="px-1" style="color: #EBEBEB;">|</span>
                              <span style="font-size: 14px; font-weight: 600;">{{ CartAddress[0].phone }}</span>
                              <v-btn class="float-end" color="#27AB9C" text dark
                                style="font-size: 12px; font-weight: 500; margin-top: -5px;" @click="changeAddress()"><span
                                  style="text-decoration-line: underline;">{{$t('CheckOut.ChangeAddress')}}</span></v-btn>
                                <v-col class="pl-0 pr-10 mb-2">
                                  <span style="font-size: 14px; font-weight: 600; color: red;">* {{$t('CheckOut.IncompleteAddress')}}</span>
                                </v-col>
                            </div>
                          </div>
                        </div>
                        <div v-if="CartAddress.length === 0 && role.role === 'ext_buyer'">
                          <div v-if="userdetail.length === 1 && ((userdetail[0].detail === '' || userdetail[0].detail === null) || userdetail[0].default_address !== 'Y') && role.role === 'ext_buyer'">
                            <v-card class="mt-7" elevation="0"
                            style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="editAddress(userdetail[0])">
                              <v-card-text class="py-1">
                                <v-row class="my-2 px-3 ">
                                  <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                                  <span class="pl-2"
                                  style="font-weight: 500; font-size: 14px; color: #1B5DD6;">{{$t('CheckOut.AddAddress')}}</span>
                                </v-row>
                              </v-card-text>
                            </v-card>
                          </div>
                          <div v-else-if="userdetail.length === 0 && role.role === 'ext_buyer'">
                            <v-card class="mt-7" elevation="0"
                            style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="addAddress()">
                              <v-card-text class="py-1">
                                <v-row class="my-2 px-3 ">
                                  <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                                  <span class="pl-2"
                                  style="font-weight: 500; font-size: 14px; color: #1B5DD6;">{{$t('CheckOut.AddAddress')}}</span>
                                </v-row>
                              </v-card-text>
                            </v-card>
                          </div>
                        </div>
                      </v-col>
                    </v-row>
                  </v-row>
                </v-card>
              </div>
              <v-card v-if="itemsCart.length !== 0" class="px-1 py-2" elevation="0" style="border-radius: 8px; border: 1px solid #F2F2F2; margin-bottom: 8px; margin-top: 24px;">
                <v-col cols="12" class="mt-2 pb-0">
                  <v-row dense>
                    <v-img class="float-left mr-2" src="@/assets/boxbox.png" max-width="22" max-height="22"></v-img>
                    <p style="font-size: 16px; font-weight: 700;" class="mb-0"><b>{{$t('CheckOut.OrderList')}}</b></p>
                  </v-row>
                </v-col>
                <v-container v-for="(item, index) in itemsCart.choose_list" :key="index" grid-list-xs class="pt-0">
                  <a-table :data-source="item.product_list"
                    :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                    :columns="headersMobile" :pagination="false">
                    <template slot="title">
                      <v-row class="text-left">
                        <v-col align="center">
                          <b class="float-left d-inline-block text-truncate"
                            style="line-height: 35px; cursor: pointer; font-size: 14px; font-weight: 600; color: #333333; max-width: 150px"
                            @click="gotoShopDetail(item.seller_shop_name, item.seller_shop_id)">{{$t('CheckOut.ShopName')}} : {{ item.seller_shop_name }}</b>
                          <v-btn class="float-end" color="#636363" text dark style="font-size:12px; font-weight: 500;"
                            @click="GetReviewQuotation(item)"><v-img class="mr-1" src="@/assets/preview_qt.png" max-width="14" max-height="14"></v-img><span
                              style="text-decoration-line: underline;">{{$t('CheckOut.QuotationSample')}}</span></v-btn>
                            </v-col>
                          </v-row>
                          <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 6px;"></v-spacer>
                    </template>
                    <template slot="productdetails" slot-scope="text, record">
                      <v-row >
                        <v-col cols="4" md="4" class="pr-2">
                          <v-img :src="`${record.product_image}`" contain
                            :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                            @click="goProductDetail(record)" />
                          <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-else
                            @click="goProductDetail(record)" />
                        </v-col>
                        <v-col cols="8" md="8">
                          <!-- <p class="mb-0 captionSku">รหัสสินค้า: <b style="font-size: 14px;">{{ record.sku }}</b></p> -->
                          <p class="mb-0" style="font-size: 16px; font-weight: 400;">{{ record.product_name }}</p>
                          <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                            style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                          </p>
                          <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                            style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                          </p>
                          <v-chip :color="record.product_type === 'general' ? '#3EC6B6' : '#FAC352'" class="prodcutsType" label small style="border-radius: 6px !important; height: 20px;">{{record.product_type === 'general'? 'สินค้าทั่วไป':'สินค้าบริการ'}}</v-chip>
                          <p class="mb-0 captionSku">{{$t('CheckOut.UnitPrice')}}: <b style="font-size: 14px;"> {{ Number(record.vat_default === 'yes' ? parseFloat(record.revenue_default) + parseFloat(record.vat_include):record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</b></p>
                          <p class="mb-0 captionSku">{{$t('CheckOut.Quantity')}}: <b style="font-size: 14px;">{{ record.quantity }}</b></p>
                          <p v-if="record.vat_default === 'no'" class="mb-0 captionSku">{{$t('CheckOut.TotalPrice')}}: <b style="font-size: 14px;">{{Number(record.show_price).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</b>
                            <span v-if="choose_list === 'recurring'" class="ml-2">{{$t('CheckOut.PricePerMonth')}}</span>
                          </p>
                          <p v-else class="mb-0 captionSku">{{$t('CheckOut.TotalPrice')}}: <b style="font-size: 14px;">{{Number(record.show_price).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</b>
                            <span v-if="choose_list === 'recurring'" class="ml-2">{{$t('CheckOut.PricePerMonth')}}</span>
                          </p>
                        </v-col>
                      </v-row>
                    </template>
                    <template slot="footer">
                        <v-row class="align-center">
                          <v-col class="px-1" :cols="MobileSize ? '12' : '6'">
                            <v-btn style="padding: 0;" text v-if="CouponData.filter(coupon => coupon.seller_shop_id === item.seller_shop_id).length === 0"
                                    color="#F56E22"
                                    @click="clickCoupon('', PointData, XBaht, item)"
                                    class="pr-5">
                                    <v-img src="@/assets/TicketSale.png" width="26" height="26">
                                    </v-img>
                              <span style="font-size: 16px; font-weight: 500;" class="px-2 text-decoration-underline">{{$t('CheckOut.UseCouponShop')}}</span>
                            </v-btn>
                            <v-chip v-else
                                    color="#FFF6F1"
                                    style="color: #464646;">
                              <v-img src="@/assets/seclectTicket.png" width="26" height="26">
                              </v-img>
                              <span style="font-size: 16px; font-weight: 500;" v-if="CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id).coupon_type === 'discount'" class="px-2">
                                {{MobileSize ? `${$t('CheckOut.ApplyStoreVoucherMobile')}` : `${$t('CheckOut.ApplyStoreVoucher')}`}} {{ CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id).discount_type === 'baht' ?
                                          `${CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id).discount_amount}฿` :
                                          `${CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id).discount_amount}%` }}
                              </span>
                              <span style="font-size: 16px; font-weight: 500;" v-else-if="CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id).coupon_type === 'free_shipping'" class="px-2">
                                {{MobileSize ? `${$t('CheckOut.FreeShippingMobile')}` : `${$t('CheckOut.FreeShipping')}`}} {{ CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id).discount_type === 'baht' ?
                                                `${CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id).discount_amount}฿` :
                                                `${CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id).discount_amount}%` }}
                              </span>
                              <span style="font-size: 16px; font-weight: 500;" v-else class="px-2">{{$t('CheckOut.Free')}}</span>
                              <v-icon
                                      @click.stop="closeCoupon(item)"
                                      style="color: #A1A1A1;">
                                mdi-close
                              </v-icon>
                            </v-chip>
                          </v-col>
                        <v-col :class="MobileSize ? 'px-1' : 'px-1 text-end'" :cols="MobileSize ? '12' : '6'" v-if="PointData.filter(point => point.seller_shop_id === item.seller_shop_id).length === 0">
                          <v-btn style="padding: 0;" text :disabled="item.seller_shop_point_data[0].seller_use_point === 'no' || item.seller_shop_point_data[0].seller_use_point === null" :class="item.seller_shop_point_data[0].seller_use_point === 'no' || item.seller_shop_point_data[0].seller_use_point === null ? 'disChipClickCoupon' : ''" class="mr-3" v-if="CouponData.length !== 0" color="#FFC107" @click="clickPoint(getCouponId(), PointData, parseFloat(item.seller_shop_point_data[0].x_baht) / parseFloat(item.seller_shop_point_data[0].x_point), item.total_coupon_discount, item)">
                            <v-img v-if="item.seller_shop_point_data[0].seller_use_point === 'no' || item.seller_shop_point_data[0].seller_use_point === null" src="@/assets/Rubledis.png" width="18" height="18">
                            </v-img>
                            <v-img v-else src="@/assets/Ruble.png" width="18" height="18">
                            </v-img>
                            <span style="font-size: 16px; font-weight: 500;" class="px-2 text-decoration-underline">{{$t('CheckOut.UseStorePoint')}}</span>
                          </v-btn>
                          <v-btn style="padding: 0;" text :disabled="item.seller_shop_point_data[0].seller_use_point === 'no' || item.seller_shop_point_data[0].seller_use_point === null" :class="item.seller_shop_point_data[0].seller_use_point === 'no' || item.seller_shop_point_data[0].seller_use_point === null ? 'disChipClickCoupon' : ''" class="mr-3" v-else color="#FFC107" @click="clickPoint('', PointData, parseFloat(item.seller_shop_point_data[0].x_baht) / parseFloat(item.seller_shop_point_data[0].x_point), item.total_coupon_discount, item)">
                            <v-img v-if="item.seller_shop_point_data[0].seller_use_point === 'no' || item.seller_shop_point_data[0].seller_use_point === null" src="@/assets/Rubledis.png" width="18" height="18">
                            </v-img>
                            <v-img v-else src="@/assets/Ruble.png" width="18" height="18">
                            </v-img>
                            <span style="font-size: 16px; font-weight: 500;" class="px-2 text-decoration-underline">{{$t('CheckOut.UseStorePoint')}}</span>
                          </v-btn>
                        </v-col>
                        <v-col :class="MobileSize ? 'px-1' : 'px-1 text-end'" :cols="MobileSize ? '12' : '6'" v-if="PointData.length !== 0 && PointData.find(p => p.seller_shop_id === item.seller_shop_id) !== undefined">
                          <v-chip color="#FFF6F1" style="color: #464646;">
                          <v-img src="@/assets/seclectPoint.png" width="26" height="26">
                          </v-img>
                          <span style="font-size: 16px; font-weight: 500;" class="px-2">{{$t('CheckOut.Discount')}} {{PointData.find(p => p.seller_shop_id === item.seller_shop_id).total_point.toFixed(2)}}฿</span>
                            <v-icon @click.stop="closePoint(item)" style="color: #A1A1A1;">mdi-close</v-icon>
                          </v-chip>
                        </v-col>
                        </v-row>
                      <!-- <v-card v-if="item.isEtax === 'yes'" class="pa-0 mt-6">
                      </v-card> -->
                      <v-row dense v-if="item.isEtax === 'yes'" class="pa-0 mt-5">
                        <v-row>
                          <v-col cols="12" :class="item.radiostax === 'radiotax-1' ? 'py-0 px-0' : 'pt-0 px-0'" style="border-top: 1px solid #60617029;">
                            <v-radio-group v-model="item.radiostax" row class="mt-0" hide-details>
                              <v-col cols="12">
                                <v-row dense class="">
                                  <v-img class="mx-2" src="@/assets/Capa_1.png" max-width="18" max-height="18"></v-img>
                                  <span style="font-size: 16px; font-weight: 600;">{{$t('CheckOut.AddressTaxInvoiceIssuance')}}</span>
                                </v-row>
                              </v-col>
                              <v-col cols="12" style="align-content: center;">
                                <v-row dense>
                                  <v-radio class="pr-2 custom-radio-checkout" value="radiotax-1" @click="getCart()">
                                    <template v-slot:label>
                                      <span style="font-size: 14px;">{{$t('CheckOut.ReceiveTaxInvoice')}}</span>
                                    </template>
                                  </v-radio>
                                  <v-radio class="custom-radio-checkout" value="radiotax-2" @click="getCart()"><template v-slot:label>
                                      <span style="font-size: 14px;">{{$t('CheckOut.NotReceiveTaxInvoice')}}</span>
                                    </template>
                                  </v-radio>
                                </v-row>
                              </v-col>
                            </v-radio-group>
                          </v-col>
                          <v-col cols="12" class="pt-0" v-if="invoicedetailDefault.length !== 0 && item.invoice_id === '' && item.radiostax === 'radiotax-1'">
                            <v-spacer style="border-top: 1px solid #F2F2F2;"></v-spacer>
                            <div class="pt-2 px-0">
                              <span class="d-inline-block text-truncate pt-1" style="font-size: 14px; font-weight: 600; max-width: 100px;">{{ invoicedetailDefault[0].name }}</span>
                              <!-- <span class="px-1" style="color: #EBEBEB;">|</span> -->
                              <v-chip class="mb-4" :color="invoicedetailDefault[0].tax_type === 'Personal' ? '#E9F5FF' : '#FFF6F1'" small style="height: 20px;"
                              ><span style="font-size: 14px;" :style="invoicedetailDefault[0].tax_type === 'Personal' ? 'color: #269AFD;' : 'color: #EF6C00;'">{{ invoicedetailDefault[0].tax_type === 'Personal' ?  `${$t('CheckOut.Individual')}` : `${$t('CheckOut.JuristicPerson')}` }}</span></v-chip>
                              <v-btn class="float-end" color="#27AB9C" text dark small
                                  style="font-size: 12px; font-weight: 500;" @click="openListTaxAddress(item)"><span
                                    style="text-decoration-line: underline;">{{$t('CheckOut.ChangeAddress')}}</span></v-btn>
                              <v-col class="pa-0 pt-2" v-if="invoicedetailDefault[0].tax_type !== 'Personal'">
                                <span style="font-size: 14px;">{{$t('CheckOut.BranchCode')}}: <b>{{ invoicedetailDefault[0].branch_id }}</b></span>
                              </v-col>
                              <v-col class="pa-0">
                                <span style="font-size: 14px;">{{$t('CheckOut.TaxNumber')}}: <b>{{ invoicedetailDefault[0].tax_id }}</b></span>
                              </v-col>
                              <v-col class="pl-0 pt-1 pr-14">
                                <span style="font-size: 14px;">{{$t('CheckOut.Address')}}: {{ invoicedetailDefault[0].address }} {{ invoicedetailDefault[0].sub_district }} {{ invoicedetailDefault[0].district }} {{ invoicedetailDefault[0].province }} {{ invoicedetailDefault[0].postal_code }}</span>
                              </v-col>
                            </div>
                          </v-col>
                          <v-col cols="12" class="pt-0" v-else-if="item.invoice_id !== '' && item.radiostax === 'radiotax-1'">
                            <v-spacer style="border-top: 1px solid #F2F2F2;"></v-spacer>
                            <div v-for="(invoice, idx) in invoicedetail" :key="idx">
                              <div v-if="item.invoice_id === invoice.id" class="pt-2 px-0">
                              <span class="d-inline-block text-truncate pt-1" style="font-size: 14px; font-weight: 600; max-width: 100px;">{{ invoicedetailDefault[0].name }}</span>
                              <!-- <span class="px-1" style="color: #EBEBEB;">|</span> -->
                              <v-chip class="mb-4" :color="invoicedetailDefault[0].tax_type === 'Personal' ? '#E9F5FF' : '#FFF6F1'" small style="height: 20px;"
                              ><span style="font-size: 14px;" :style="invoicedetailDefault[0].tax_type === 'Personal' ? 'color: #269AFD;' : 'color: #EF6C00;'">{{ invoicedetailDefault[0].tax_type === 'Personal' ?  `${$t('CheckOut.Individual')}` : `${$t('CheckOut.JuristicPerson')}` }}</span></v-chip>
                              <v-btn class="float-end" color="#27AB9C" text dark small
                                  style="font-size: 12px; font-weight: 500;" @click="openListTaxAddress(item)"><span
                                    style="text-decoration-line: underline;">{{$t('CheckOut.ChangeAddress')}}</span></v-btn>
                              <v-col class="pa-0 pt-2" v-if="invoicedetailDefault[0].tax_type !== 'Personal'">
                                <span style="font-size: 14px;">{{$t('CheckOut.BranchCode')}}: <b>{{ invoicedetailDefault[0].branch_id }}</b></span>
                              </v-col>
                              <v-col class="pa-0">
                                <span style="font-size: 14px;">{{$t('CheckOut.TaxNumber')}}: <b>{{ invoicedetailDefault[0].tax_id }}</b></span>
                              </v-col>
                              <v-col class="pl-0 pt-1 pr-14">
                                <span style="font-size: 14px;">{{$t('CheckOut.Address')}}: {{ invoicedetailDefault[0].address }} {{ invoicedetailDefault[0].sub_district }} {{ invoicedetailDefault[0].district }} {{ invoicedetailDefault[0].province }} {{ invoicedetailDefault[0].postal_code }}</span>
                              </v-col>
                            </div>
                            </div>
                          </v-col>
                          <v-col cols="12" class="pt-0 pb-6 pt-0 px-4" v-if="invoicedetailDefault.length === 0 && invoicedetail.length === 0 && item.radiostax === 'radiotax-1'">
                            <v-card elevation="0" class="mt-4"
                              style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="openModalTaxAddress()">
                              <v-card-text class="py-1 px-2">
                                <v-row class="my-2 px-3 ">
                                  <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                                  <span class="pl-2" style="font-weight: 500; font-size: 14px; color: #1B5DD6;">{{$t('CheckOut.AddAddress')}}</span>
                                </v-row>
                              </v-card-text>
                            </v-card>
                          </v-col>
                        </v-row>
                      </v-row>
                      <v-row dense class="pa-0 mt-4">
                        <v-row>
                          <v-col class="pt-0 px-0 pb-2" style="border-top: 1px solid #60617029;">
                            <v-col cols="12" class="pb-0">
                              <v-row dense class="">
                                <v-img class="mx-2" src="@/assets/USER_INTERFACE.png" max-width="18" max-height="22" contain></v-img>
                                <span style="font-size: 16px; font-weight: 600; ">หมายเหตุ</span>
                              </v-row>
                            </v-col>
                              <v-col cols="12">
                                <v-row dense class="">
                                  <v-text-field @keypress="CheckSpacebar($event)" v-model="item.remark_to_shop" placeholder="หมายเหตุถึงร้านค้า(ไม่บังคับ)" style="border-radius: 8px;" outlined dense hide-details clearable></v-text-field>
                                </v-row>
                              </v-col>
                          </v-col>
                        </v-row>
                      </v-row>
                      <v-row dense v-if="item.product_list.some(productitem => (productitem.product_type === 'general'))" class="pa-0 mt-4">
                        <v-row>
                          <v-col class="pt-0 px-0 pb-2" style="border-top: 1px solid #60617029;">
                            <v-radio-group v-model="item.radios" row class="" hide-details>
                              <v-col cols="12">
                                <v-row dense class="">
                                  <v-img class="mx-2" src="@/assets/box_2.png" max-width="18" max-height="18" contain></v-img>
                                  <span style="font-size: 16px; font-weight: 600; ">{{$t('CheckOut.ShippingType')}}</span>
                                </v-row>
                              </v-col>
                              <v-col cols="12" style="align-content: center;">
                                <v-row dense>
                                  <v-radio v-if="item.store_front === 'yes'" value="radio-1" class="pr-4 custom-radio-checkout" :disabled="item.radios === 'radio-1'" @click="ClearRadio(item)"><template v-slot:label>
                                    <span style="font-size: 14px;">{{$t('CheckOut.PickUpAStore')}}</span>
                                  </template>
                                </v-radio>
                                <v-radio value="radio-2" class="custom-radio-checkout" :disabled="item.radios === 'radio-2'" @click="ClearRadio(item)"><template v-slot:label>
                                    <span style="font-size: 14px;">{{$t('CheckOut.ProductDelivery')}}</span>
                                  </template>
                                </v-radio>
                                </v-row>
                              </v-col>
                            </v-radio-group>
                            <div v-if="item.radios === 'radio-2' && item.shipping_method.length !== 0" class="pa-0" style="border-top: 1px solid #60617029;">
                              <div v-if="item.product_list.some(productitem => (role.role === 'ext_buyer' && productitem.product_type === 'general' && item.radios !== 'radio-1' && item.shipping_method.length !== 0))">
                                <v-col cols="12" class="py-2">
                                  <v-chip v-if="item.shipping_data.length === 0 && item.shipping_data !== undefined" color="#3EC6B6" text :disabled="btnEstimateCost === false" :class="btnEstimateCost === false ? '' : 'theme--dark'" outlined
                                    style="font-size: 16px; font-weight: 500;" @click="EstimateCost(item)">
                                    <v-img src="@/assets/star--filled.png" width="14" height="14">
                                    </v-img>
                                    <span class="pl-2">{{$t('CheckOut.ChooseShipping')}}</span>
                                  </v-chip>
                                  <v-chip v-else-if="item.shipping_data.length !== 0 && item.shipping_data !== undefined" text outlined
                                    style="font-size: 16px; font-weight: 500; background-color: rgb(243, 246, 255) !important; border: 0px !important;">
                                    <v-img src="@/assets/star--filled.png" width="18" height="18">
                                    </v-img>
                                    <span class="pl-2 d-inline-block text-truncate " style="color: #464646; font-size: 14px; max-width: 150px;">{{item.shipping_data.tpl_name}}<span> {{item.shipping_data.netEstimatePrice}}฿</span></span>
                                  </v-chip>
                                  <v-btn v-if="item.shipping_data.length !== 0 && item.shipping_data !== undefined" small text style="color: #1B5DD6; font-size: 14px; font-weight: 500;" @click="EstimateCost(item)">{{$t('CheckOut.ChooseOtherShipping')}}</v-btn>
                                </v-col>
                              </div>
                            </div>
                            <div v-if="item.radios === 'radio-2' && item.shipping_method.length === 0" class="pa-0" style="border-top: 1px solid #60617029;">
                              <div v-if="item.product_list.some(productitem => (role.role === 'ext_buyer' && productitem.product_type === 'general' && item.radios !== 'radio-1' && item.shipping_method.length === 0))">
                                <v-col cols="12" class="py-2">
                                  <v-chip text outlined
                                    style="font-size: 16px; font-weight: 500; background-color: rgb(243, 246, 255) !important; border: 0px !important;">
                                    <v-img src="@/assets/star--filled.png" width="18" height="18">
                                    </v-img>
                                    <span class="pl-2" style="color: #464646; font-size: 14px;">{{$t('CheckOut.SentByStore')}}</span>
                                  </v-chip>
                                </v-col>
                              </div>
                            </div>
                            <div v-if="item.radios === 'radio-1'" class="px-4" style="border-top: 1px solid #60617029;">
                              <div style="margin-top: 16px;">
                                <span style="font-size: 14px; font-weight: 600;">{{ item.product_list[0].seller_shop_name
                                }}</span>
                                <span class="px-1" style="color: #EBEBEB;">|</span>
                                <span style="font-size: 14px; font-weight: 600;">{{ item.shipping_detail.data_seller_address[0].phone !== undefined ? item.shipping_detail.data_seller_address[0].phone : '-' }}</span>
                                  <v-col class="pl-0 pr-10 mb-2">
                                    <span style="font-size: 14px;"> {{$t('CheckOut.HouseNumber')}} {{ item.shipping_detail.data_seller_address[0].house_no}} {{ item.shipping_detail.data_seller_address[0].detail }} {{$t('CheckOut.SubDistrict')}} {{ item.shipping_detail.data_seller_address[0].sub_district }}
                                      {{$t('CheckOut.District')}} {{ item.shipping_detail.data_seller_address[0].district }} {{$t('CheckOut.Province')}} {{ item.shipping_detail.data_seller_address[0].province }} {{ item.shipping_detail.data_seller_address[0].zipcode }}
                                      </span>
                                  </v-col>
                                  <v-row>
                                    <v-col cols="12" class="pb-0">
                                      <span style="line-height: 24px; font-size: 14px; color: #333333;">
                                        {{$t('CheckOut.ReceivingDate')}} <span style="color: red;">*</span>
                                      </span>
                                      <v-menu
                                        v-model="item.menu"
                                        :close-on-content-click="false"
                                        :nudge-right="40"
                                        transition="scale-transition"
                                        offset-y
                                        min-width="auto"
                                      >
                                        <template v-slot:activator="{ on, attrs }">
                                          <v-text-field
                                            v-model="item.contractDate"
                                            style="border-radius: 8px;"
                                            outlined
                                            dense
                                            :placeholder="$i18n.locale === 'th' ? 'วว/ดด/ปปปป' : 'DD/MM/YYYY'"
                                            readonly
                                            v-bind="attrs"
                                            v-on="on"
                                          >
                                            <v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon>
                                          </v-text-field>
                                        </template>
                                        <v-date-picker
                                          v-model="item.dates"
                                          @input="setValueDate(index, item.dates)"
                                          :locale="$i18n.locale === 'th' ? 'th' : 'en'"
                                          scrollable
                                          no-title
                                          :min="today"
                                          :max="futureDate"
                                          @change="updateTime()"
                                        ></v-date-picker>
                                      </v-menu>
                                    </v-col>
                                    <v-col cols="12"  class="pt-0" :class="{ 'is-disabled': item.dates === '' }">
                                      <span style="line-height: 24px; font-size: 14px; color: #333333;">
                                        {{$t('CheckOut.ReceivingTime')}} <span style="color: red;">*</span>
                                      </span>
                                      <v-col class="pt-0 pl-0" v-if="item.dates === today">
                                        <template>
                                          <a-space direction="vertical" style="width: 100%;">
                                            <a-time-picker
                                              v-model="item.timeselecttoday"
                                              :bordered="false"
                                              style="width: 100%;"
                                              :format="$i18n.locale === 'th' ? 'HH:mm น.' : 'HH:mm'"
                                              size="large"
                                              :placeholder="$i18n.locale === 'th' ? '00.00 น.' : '00.00'"
                                              :disabled="!item.dates"
                                              :disabledHours="disabledHours"
                                              :disabledMinutes="disabledMinutes"
                                              @change="updateTime()"
                                            />
                                            <a-time-range-picker
                                              :bordered="false"
                                              style="width: 100%;"
                                              :disabled="!item.dates"
                                            />
                                          </a-space>
                                        </template>
                                      </v-col>
                                      <v-col class="pt-0 pl-0" v-else>
                                        <template>
                                          <a-space direction="vertical" style="width: 100%;">
                                            <a-time-picker
                                              v-model="item.timeselect"
                                              :bordered="false"
                                              style="width: 100%;"
                                              :format="$i18n.locale === 'th' ? 'HH:mm น.' : 'HH:mm'"
                                              size="large"
                                              :placeholder="$i18n.locale === 'th' ? '00.00 น.' : '00.00'"
                                              :disabled="!item.dates"
                                              @change="updateTime()"
                                            />
                                            <a-time-range-picker
                                              :bordered="false"
                                              style="width: 100%;"
                                              :disabled="!item.dates"
                                            />
                                          </a-space>
                                        </template>
                                      </v-col>
                                    </v-col>
                                  </v-row>
                              </div>
                            </div>
                          </v-col>
                        </v-row>
                      </v-row>
                      </template>
                  </a-table>
                  <a-table v-if="item.product_free.length !== 0" :data-source="item.product_free"
                    :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                    :columns="headersMobile" :pagination="false">
                    <template slot="title">
                      <v-row class="text-left">
                        <v-col align="center">
                          <v-img class="float-left" src="@/assets/icons/nullproduct.png" width="24" height="24"></v-img>
                          <b class="float-left ml-2 d-inline-block text-truncate"
                            style="line-height: 35px; font-size: 14px; font-weight: 600; color: #F4BC5F; max-width: 90px">{{$t('CheckOut.Free')}}</b>
                            </v-col>
                          </v-row>
                          <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 6px;"></v-spacer>
                    </template>
                    <template slot="productdetails" slot-scope="text, record">
                      <v-row >
                        <v-col cols="4" md="4" class="pr-2">
                          <v-img :src="`${record.product_image}`" contain
                            :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                            @click="goProductDetail(record)" />
                          <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-else
                            @click="goProductDetail(record)" />
                        </v-col>
                        <v-col cols="8" md="8">
                          <p class="mb-0 captionSku">{{$t('CheckOut.SKU')}}: <b style="font-size: 14px;">{{ record.sku }}</b></p>
                          <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                          <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                            style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                          </p>
                          <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                            style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                          </p>
                          <p class="mb-0 captionSku">{{$t('CheckOut.UnitPrice')}}: <b style="font-size: 14px;">0</b></p>
                          <p class="mb-0 captionSku">{{$t('CheckOut.Quantity')}}: <b style="font-size: 14px;">{{ record.quantity }}</b></p>
                          <p v-if="record.vat_default === 'no'" class="mb-0 captionSku">{{$t('CheckOut.TotalPrice')}}: <b style="font-size: 14px;">0</b>
                            <span v-if="choose_list === 'recurring'" class="ml-2">{{$t('CheckOut.PricePerMonth')}}</span>
                          </p>
                          <p v-else class="mb-0 captionSku">{{$t('CheckOut.TotalPrice')}}: <b style="font-size: 14px;">0</b>
                            <span v-if="choose_list === 'recurring'" class="ml-2">{{$t('CheckOut.PricePerMonth')}}</span>
                          </p>
                        </v-col>
                      </v-row>
                    </template>
                  </a-table>
                </v-container>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" md="4" class="pb-6" v-if="!MobileSize">
          <affix class="pb-8" relative-element-selector="#FormAddProduct" :offset="{ top: 110, bottom: 0 }" :scroll-affix="true">
          <v-card elevation="0" style="border-radius: 8px; border: 1px solid #F2F2F2" class="mb-4" >
            <v-container grid-list-xs class="pa-5">
              <v-row class="pb-2">
                <v-col cols="12" class="my-2">
                  <v-row dense>
                    <!-- <v-icon color="#27AB9C" class="pr-2">mdi-file-document-outline</v-icon> -->
                    <span
                      :style="MobileSize ? 'font-size: 18px;' : IpadSize ? 'font-size: 20px;' : 'font-size: 24px; font-weight: 700;'"><b>{{$t('CheckOut.SystemDiscountCoupon')}}</b></span>
                  </v-row>
                </v-col>
                <v-col v-if="role.role === 'ext_buyer'" cols="6" class="Textcard">
                  <span>{{$t('CheckOut.SystemDiscountCoupon')}}</span>
                </v-col>
                <v-col v-if="role.role === 'ext_buyer'" cols="6" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                  <v-btn style="padding: 0;" text v-if="CodePlatform.length === 0"
                    color="#F56E22"
                    @click="clickSystemCode()"
                    class="">
                    <v-img src="@/assets/TicketSale.png" width="26" height="26">
                    </v-img>
                    <span style="font-size: 16px; font-weight: 500;" class="px-2 text-decoration-underline">{{MobileSize ? `${$t('CheckOut.UseSystemDiscountCoupon')}` : `${$t('CheckOut.UseSystemDiscountCoupon')}`}}</span>
                  </v-btn>
                  <v-chip v-else
                          color="#E7FDFF"
                          style="color: #1EB4EB;">
                    <v-img src="@/assets/seclectTicket.png" width="26" height="26">
                    </v-img>
                    <span style="font-size: 16px; font-weight: 500;" v-if="CodePlatform[0].coupon_type === 'discount'" class="px-2">
                      {{$t('CheckOut.DiscountCoupon')}} {{ CodePlatform[0].discount_type === 'baht' ?
                        `${CodePlatform[0].discount_amount}฿` :
                        `${CodePlatform[0].discount_amount}%` }}
                    </span>
                    <v-icon
                      @click.stop="clearCodePlatform()"
                      style="color: #A1A1A1;">
                      mdi-close
                    </v-icon>
                  </v-chip>
                </v-col>
                <v-row class="px-3" style="width: 100%;">
                  <v-col v-if="role.role === 'ext_buyer'" cols="6" class="Textcard">
                    <span>{{$t('CheckOut.DiscountShipping')}}</span>
                  </v-col>
                  <v-col v-if="role.role === 'ext_buyer'" cols="6" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                  <v-btn style="padding: 0;" text v-if="CodePlatformShipping.length === 0"
                    color="#F56E22"
                    @click="clickSystemCodeShipping()"
                    class="">
                    <v-img src="@/assets/TicketSale.png" width="26" height="26">
                    </v-img>
                    <span style="font-size: 16px; font-weight: 500;" class="px-2 text-decoration-underline">{{MobileSize ? `${$t('CheckOut.UseSystemDiscountCoupon')}` : `${$t('CheckOut.UseSystemDiscountCoupon')}`}}</span>
                  </v-btn>
                  <v-chip v-else
                          color="#E7FDFF"
                          style="color: #1EB4EB;">
                    <v-img src="@/assets/seclectTicket.png" width="26" height="26">
                    </v-img>
                    <span style="font-size: 16px; font-weight: 500;" v-if="CodePlatformShipping[0].coupon_type === 'free_shipping'" class="px-2">
                      {{$t('CheckOut.DiscountCoupon')}} {{ CodePlatformShipping[0].discount_type === 'baht' ?
                        `${CodePlatformShipping[0].discount_amount}฿` :
                        `${CodePlatformShipping[0].discount_amount}%` }}
                    </span>
                    <v-icon
                      @click.stop="clearCodePlatformShipping()"
                      style="color: #A1A1A1;">
                      mdi-close
                    </v-icon>
                  </v-chip>
                  </v-col>
                </v-row>
                <v-col v-if="itemsCart.employee_inet && role.role === 'ext_buyer'" cols="6" class="Textcard">
                  <span>{{$t('CheckOut.Relationships')}}</span>
                </v-col>
                <v-col v-if="itemsCart.employee_inet && role.role === 'ext_buyer'" cols="6" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                  <v-chip v-if="InetRelation.length === 0" color="#27AB9C" @click="clickCode()" outlined class="pr-5"><span class="px-2">{{$t('CheckOut.RelationshipsCoupon')}}</span>
                    <v-icon>mdi-ticket-percent-outline</v-icon>
                  </v-chip>
                  <v-btn v-else color="#3EC6B6" @click="clickCode()" text><span class="text-decoration-underline">{{$t('CheckOut.ChangeRelationshipsCoupon')}}</span></v-btn>
                </v-col>
                <v-col v-if="InetRelation.length !== 0" cols="12" class="pb-0" align="right" >
                  <v-chip
                    color="#E7FDFF"
                    style="color: #1EB4EB;">
                    <v-img src="@/assets/seclectTicket.png" width="26" height="26">
                    </v-img>
                    <span style="font-size: 16px; font-weight: 500;" class="px-2">
                      {{$t('CheckOut.RelationshipsCoupon')}}
                    </span>
                    <v-icon
                      @click.stop="clearUser()"
                      style="color: #A1A1A1;">
                      mdi-close
                    </v-icon>
                  </v-chip>
                </v-col>
              </v-row>
            </v-container>
          </v-card>
          <v-card class="v-Card" elevation="0" style="border-radius: 8px; border: 1px solid #F2F2F2"  >
            <v-container grid-list-xs class="pa-5">
              <v-row>
                <v-col cols="12" class="my-2">
                  <v-row dense>
                    <!-- <v-icon color="#27AB9C" class="pr-2">mdi-file-document-outline</v-icon> -->
                    <span
                      :style="MobileSize ? 'font-size: 18px;' : IpadSize ? 'font-size: 20px;' : 'font-size: 24px; font-weight: 700;'"><b>{{$t('CheckOut.SummaryOrdered')}}</b></span>
                  </v-row>
                </v-col>
                <v-col cols="6" sm="7" class="Textcard">
                  <span>{{$t('CheckOut.PriceExcludingVAT')}}</span>
                </v-col>
                <v-col cols="6" sm="5" align="right" class="TextBaht">
                  <span>{{ itemsCart.total_price_no_vat ? formatPrice(itemsCart.total_price_no_vat) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span>{{$t('CheckOut.VAT')}}</span>
                </v-col>
                <v-col cols="6" align="right" class="TextBaht">
                  <span>{{ itemsCart.total_vat ? formatPrice(itemsCart.total_vat) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span> {{$t('CheckOut.PriceIncludingVAT')}}</span>
                </v-col>
                <v-col cols="6" align="right" class="TextBaht">
                  <span>{{ itemsCart.total_price_vat ? formatPrice(itemsCart.total_price_vat) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span class="ml-2" style="color: red;">{{$t('CheckOut.CouponDiscountSeller')}}</span>
                </v-col>
                <v-col cols="6" align="right" class="TextBaht">
                  <span style="color: red;">- {{ itemsCart.total_coupon_discount ? formatPrice(itemsCart.total_coupon_discount) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span class="ml-2" style="color: red;">{{$t('CheckOut.CouponDiscountNexgen')}}</span>
                </v-col>
                <v-col cols="6" align="right" class="TextBaht">
                  <span style="color: red;">- {{ itemsCart.total_coupon_platform_discount ? formatPrice(itemsCart.total_coupon_platform_discount) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span class="ml-2" style="color: red;">{{$t('CheckOut.NexgenPointDiscount')}}</span>
                  <!-- <span style="font-size: 10px; font-weight: 400; color: #A1A1A1; line-height: 14px;">ซื้อ บาท ได้ แต้ม </span> -->
                </v-col>
                <v-col cols="6" align="right" class="TextBaht">
                  <span style="color: red;">- {{ itemsCart.total_point ? formatPrice(itemsCart.total_point) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span>{{$t('CheckOut.SubTotal')}}</span>
                </v-col>
                <v-col cols="6" align="right" class="TextBaht">
                  <span>{{ itemsCart.total_price_after_all_discount ? formatPrice(itemsCart.total_price_after_all_discount) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span>{{$t('CheckOut.ShippingFee')}}</span><br>
                  <span style="font-size: 10px; font-weight: 400; color: #A1A1A1; line-height: 14px;">{{$t('CheckOut.ShippingCostDesicription1')}} </span>
                  <span style="font-size: 10px; font-weight: 400; color: #A1A1A1; line-height: 14px;">{{$t('CheckOut.ShippingCostDesicription2')}}</span>
                </v-col>
                <!-- <v-col v-if="parseInt(itemsCart.total_shipping) !== parseInt(itemsCart.shipping_price)" cols="6" align="right" :class="MobileSize ? 'TextBahtMobileSize' : 'TextBaht'">
                  <span style="font-weight: 400; color: #BDBDBD;" class="text-decoration-line-through">{{ Number(itemsCart.shipping_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' บาท' }}</span><br>
                  <span>{{ Number(itemsCart.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' บาท'}}</span>
                </v-col>
                <v-col v-else cols="6" align="right" :class="MobileSize ? 'TextBahtMobileSize' : 'TextBaht'">
                  <span>{{ Number(itemsCart.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' บาท' }}</span><br>
                </v-col> -->
                <v-col cols="6" align="right" :class="MobileSize ? 'TextBahtMobileSize' : 'TextBaht'">
                  <span>{{ itemsCart.shipping_price ? formatPrice(itemsCart.shipping_price) : '0.00' }} {{$t('CheckOut.Baht')}}</span><br>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span class="ml-2" style="color: red;">{{$t('CheckOut.ShippingDiscountSeller')}}</span>
                </v-col>
                <v-col cols="6" align="right" class="TextBaht">
                  <span style="color: red;">- {{ itemsCart.total_coupon_shipping_discount_v2 ? formatPrice(itemsCart.total_coupon_shipping_discount_v2) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span class="ml-2" style="color: red;">{{$t('CheckOut.ShippingDiscountNexgen')}}</span>
                </v-col>
                <v-col cols="6" align="right" class="TextBaht">
                  <span style="color: red;">- {{ itemsCart.total_coupon_platform_shipping_discount_v2 ? formatPrice(itemsCart.total_coupon_platform_shipping_discount_v2) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                </v-col>
                <v-row>
                  <v-col cols="12" v-if="(role.role === 'ext_buyer')">
                  <div v-if="role.role === 'ext_buyer'" style="background: #FAFAFA; border-radius: 8px;" class="ma-2 py-2 px-4">
                    <div>
                        <v-radio-group v-model="radioPayment" hide-details row class="ma-0 pa-0 ml-3" :disabled="firstValidChooseItem && isPaymentDisabled">
                        <v-col :class="MobileSize ? 'px-1' : 'px-0'" :cols="MobileSize ? 12 : 12">
                          <v-row dense align="center">
                            <v-img class="float-left mr-2" src="@/assets/cash.png" max-width="32" max-height="32"></v-img>
                            <span class="pl-0 pb-0" :style="MobileSize ? 'font-size: 14px; color: #333333; font-weight: 600;' : 'font-size: 16px; color: #333333; font-weight: 600;'" color="#333333">{{$t('CheckOut.SelectPaymentMethod')}}</span>
                          </v-row>
                        </v-col>
                        <v-col cols="12" :class="MobileSize ? 'pt-0' : 'px-0'" >
                          <v-radio class="custom-radio-checkout" value="radio-qr" @click="setRadioCreditTermNo()"><template v-slot:label>
                              <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">QR Code</span>
                            </template>
                          </v-radio>
                        </v-col>
                        <v-col cols="12" :class="MobileSize ? 'pt-0' : 'px-0 py-0'">
                          <v-radio class="custom-radio-checkout" value="radio-credit" @click="setRadioCreditTermNo()"><template v-slot:label>
                              <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">Credit Card / Debit Card</span>
                            </template>
                          </v-radio>
                        </v-col>
                        <v-col cols="12" :class="MobileSize ? 'pt-0' : 'px-0'">
                          <v-radio class="custom-radio-checkout" value="radio-installment" @click="setRadioCreditTermNo()"><template v-slot:label>
                              <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">{{$t('CheckOut.CreditCardInstallmentOption')}}</span>
                            </template>
                          </v-radio>
                        </v-col>
                      </v-radio-group>
                    </div>
                    <div v-if="radioPayment === 'radio-installment'" class="mt-0 mb-4 mx-3">
                      <v-row align="center">
                        <v-col :cols="MobileSize ? 4 : 4" class="pt-0">
                          <!-- <span style="font-size: 16px;">ระยะเวลาผ่อนชำระ</span> -->
                          <v-img src="@/assets/ktc_logo.png" width="130" height="100" contain style="border-radius: 8px;"></v-img>
                        </v-col>
                        <v-col :cols="MobileSize ? 8 : 8" class="pb-0">
                          <span style="font-size: 16px; font-weight: 600;">{{$t('CheckOut.Krungthai')}}</span>
                          <v-select class="mt-2" outlined dense :label="$t('CheckOut.SelectInstallmentPeriod')" v-model="radioCreditTerm" :items="filteredCreditTerms" item-text="displayText" item-value="value" style="border-radius: 8px;">
                            <template v-slot:append>
                              <v-icon>mdi-chevron-down</v-icon>
                            </template>
                            <template v-slot:no-data>
                              <v-list-item>
                                <v-list-item-content class="text-center">
                                  <v-list-item-title>{{$t('CheckOut.InstallmentPaymentNotAvailableBecause')}} <span style="color: #27AB9C;">{{$t('CheckOut.Minimum')}}</span> {{$t('CheckOut.RequiredAmountHasNotBeenMet')}}</v-list-item-title>
                                </v-list-item-content>
                              </v-list-item>
                            </template>
                          </v-select>
                        </v-col>
                      </v-row>
                    </div>
                    <!-- <v-spacer class="mx-3" style="border-top: 2px solid #EBEBEB;"></v-spacer> -->
                  </div>
                </v-col>
              </v-row>
              <v-col cols="12" class="">
                <v-row v-if="hasPoints" dense class="d-flex">
                  <v-img src="@/assets/pointtoearn.png" max-width="26" max-height="26">
                  </v-img>
                  <span color="#EBA61B" :style="MobileSize ? 'font-size: 16px; font-weight: 400; color: #EBA61B;' : 'font-size: 18px; font-weight: 400; color: #EBA61B;'"
                    class="mr-auto ml-2 align-self-center">{{$t('CheckOut.PointsEarned')}} <v-icon style="color: #989898; font-size: 18px" @click="openDialogPoint()">mdi-information-outline</v-icon></span>
                  <span :style="MobileSize ? 'font-size: 16px; font-weight: 700; color: #27AB9C;' : 'font-size: 20px; font-weight: 700; color: #27AB9C;'" class="ml-auto pt-0 pr-1">{{ Number(parseInt(itemsCart.total_point_receive)).toLocaleString(undefined, {}) }}<span
                        :style="MobileSize ? 'font-size: 16px; font-weight: 700; color: #27AB9C;' : 'font-size: 20px; font-weight: 700; color: #27AB9C;'"
                        class="ml-2">{{$t('CheckOut.Point')}}</span></span>
                </v-row>
              </v-col>
                <v-col cols="12" class="py-0 pt-1" >
                  <v-row dense class="pl-1 d-flex">
                    <span :style="MobileSize ? 'font-size: 16px; font-weight: 700;' : 'font-size: 20px; font-weight: 700;'"
                      class="mr-auto align-self-center"><b>{{$t('CheckOut.TotalPriceAll')}}</b></span>
                    <span :style="MobileSize ? 'font-size: 20px; font-weight: 700; color: #27AB9C;' : 'font-size: 28px; font-weight: 700; color: #27AB9C;'" class="ml-auto pt-0 pr-1"><b>{{
                      itemsCart.net_price  ? formatPrice(itemsCart.net_price) : '0.00' }}<span v-if="choose_list === 'recurring'"
                      :style="MobileSize ? 'font-size: 16px; font-weight: 700; color: #333333' : 'font-size: 20px; font-weight: 700; color: #333333'" class="ml-2">{{$t('CheckOut.PricePerMonth')}}</span><span
                          v-else :style="MobileSize ? 'font-size: 16px; font-weight: 700; color: #333333' : 'font-size: 20px; font-weight: 700; color: #333333'"
                          class="ml-2">{{$t('CheckOut.Baht')}}</span></b></span>
                  </v-row>
                </v-col>
                <v-col cols="12" align="right" class="mt-2 mb-2">
                  <div>
                    <v-btn v-if="role.role === 'ext_buyer'" style="border-radius: 40px; height: 40px;" class="white--text"
                    block color="#27AB9C" @click="Confirm()" :disabled="isConfirmDisabled">
                    <!-- <v-btn v-if="role.role === 'ext_buyer'" style="border-radius: 40px; height: 40px;" class="white--text"
                    block color="#27AB9C" @click="GetQRCode('cashPayment')" :disabled="isConfirmDisabled"> -->
                    <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 700;' : 'font-size: 18px; font-style: normal; font-weight: 500;'">{{$t('CheckOut.ConfirmOrder')}}</span>
                  </v-btn>
                  </div>
                </v-col>
              </v-row>
            </v-container>
          </v-card>
          </affix>
        </v-col>
          <v-col v-if="role.role === 'ext_buyer' && MobileSize" cols="12" class="py-0">
          <v-card elevation="0" style="border-radius: 8px; border: 1px solid #F2F2F2" class="mb-4" >
            <v-container grid-list-xs class="pa-5">
              <v-row class="pb-2">
                <v-col cols="12" class="my-2">
                  <v-row dense>
                    <!-- <v-icon color="#27AB9C" class="pr-2">mdi-file-document-outline</v-icon> -->
                    <span
                      :style="MobileSize ? 'font-size: 18px;' : IpadSize ? 'font-size: 20px;' : 'font-size: 24px; font-weight: 700;'"><b>{{$t('CheckOut.CouponDiscountNexgen')}}</b></span>
                  </v-row>
                </v-col>
                <v-col v-if="role.role === 'ext_buyer'" cols="6" class="Textcard">
                  <span>{{$t('CheckOut.CouponDiscountNexgen')}}</span>
                </v-col>
                <v-col v-if="role.role === 'ext_buyer'" cols="6" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                  <v-btn style="padding: 0;" text v-if="CodePlatform.length === 0"
                    color="#F56E22"
                    @click="clickSystemCode()"
                    class="">
                    <v-img src="@/assets/TicketSale.png" width="26" height="26">
                    </v-img>
                    <span style="font-size: 16px; font-weight: 500;" class="px-2 text-decoration-underline">{{MobileSize ? `${$t('CheckOut.UseSystemDiscountCoupon')}` : `${$t('CheckOut.UseSystemDiscountCoupon')}`}}</span>
                  </v-btn>
                  <v-chip v-else
                          color="#E7FDFF"
                          style="color: #1EB4EB;">
                    <v-img src="@/assets/seclectTicket.png" width="26" height="26">
                    </v-img>
                    <span style="font-size: 16px; font-weight: 500;" v-if="CodePlatform[0].coupon_type === 'discount'" class="px-2">
                      ลด {{ CodePlatform[0].discount_type === 'baht' ?
                        `${CodePlatform[0].discount_amount}฿` :
                        `${CodePlatform[0].discount_amount}%` }}
                    </span>
                    <v-icon
                      @click.stop="clearCodePlatform()"
                      style="color: #A1A1A1;">
                      mdi-close
                    </v-icon>
                  </v-chip>
                </v-col>
                <v-row class="px-3" style="width: 100%;">
                  <v-col v-if="role.role === 'ext_buyer'" cols="6" class="Textcard">
                    <span>{{$t('CheckOut.CouponDiscountSeller')}}</span>
                  </v-col>
                  <v-col v-if="role.role === 'ext_buyer'" cols="6" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                  <v-btn style="padding: 0;" text v-if="CodePlatformShipping.length === 0"
                    color="#F56E22"
                    @click="clickSystemCodeShipping()"
                    class="">
                    <v-img src="@/assets/TicketSale.png" width="26" height="26">
                    </v-img>
                    <span style="font-size: 16px; font-weight: 500;" class="px-2 text-decoration-underline">{{MobileSize ? `${$t('CheckOut.UseSystemDiscountCoupon')}` : `${$t('CheckOut.UseSystemDiscountCoupon')}`}}</span>
                  </v-btn>
                  <v-chip v-else
                          color="#E7FDFF"
                          style="color: #1EB4EB;">
                    <v-img src="@/assets/seclectTicket.png" width="26" height="26">
                    </v-img>
                    <span style="font-size: 16px; font-weight: 500;" v-if="CodePlatformShipping[0].coupon_type === 'free_shipping'" class="px-2">
                      {{$t('CheckOut.DiscountCoupon')}} {{ CodePlatformShipping[0].discount_type === 'baht' ?
                        `${CodePlatformShipping[0].discount_amount}฿` :
                        `${CodePlatformShipping[0].discount_amount}%` }}
                    </span>
                    <v-icon
                      @click.stop="clearCodePlatformShipping()"
                      style="color: #A1A1A1;">
                      mdi-close
                    </v-icon>
                  </v-chip>
                  </v-col>
                </v-row>
                <v-col v-if="itemsCart.employee_inet && role.role === 'ext_buyer'" cols="6" class="Textcard">
                  <span>{{$t('CheckOut.Relationships')}}</span>
                </v-col>
                <v-col v-if="itemsCart.employee_inet && role.role === 'ext_buyer'" cols="6" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                  <v-chip v-if="InetRelation.length === 0" color="#27AB9C" @click="clickCode()" outlined class="pr-5"><span class="px-2">{{$t('CheckOut.RelationshipsCoupon')}}</span>
                    <v-icon>mdi-ticket-percent-outline</v-icon>
                  </v-chip>
                  <v-btn v-else color="#3EC6B6" @click="clickCode()" text><span class="text-decoration-underline">{{$t('CheckOut.ChangeRelationshipsCoupon')}}</span></v-btn>
                </v-col>
                <v-col v-if="InetRelation.length !== 0" cols="12" class="pb-0" align="right" >
                  <v-chip
                    color="#E7FDFF"
                    style="color: #1EB4EB;">
                    <v-img src="@/assets/seclectTicket.png" width="26" height="26">
                    </v-img>
                    <span style="font-size: 16px; font-weight: 500;" class="px-2">
                      {{$t('CheckOut.RelationshipsCoupon')}}
                    </span>
                    <v-icon
                      @click.stop="clearUser()"
                      style="color: #A1A1A1;">
                      mdi-close
                    </v-icon>
                  </v-chip>
                </v-col>
              </v-row>
            </v-container>
          </v-card>
          </v-col>
          <v-col cols="12" v-if="role.role === 'ext_buyer' && MobileSize" class="py-0">
            <v-card elevation="0" style="border-radius: 8px; border: 1px solid #F2F2F2"  >
            <div v-if="role.role === 'ext_buyer'" style="" class="ma-2 py-2 px-4">
              <div>
                  <v-radio-group v-model="radioPayment" hide-details row class="ma-0 pa-0 ml-3" :disabled="firstValidChooseItem && isPaymentDisabled">
                  <v-col :class="MobileSize ? 'px-1' : 'px-0'" :cols="MobileSize ? 12 : 12">
                    <v-row dense align="center">
                      <v-img class="float-left mr-2" src="@/assets/cash.png" max-width="32" max-height="32"></v-img>
                      <span class="pl-0 pb-0" :style="MobileSize ? 'font-size: 14px; color: #333333; font-weight: 600;' : 'font-size: 16px; color: #333333; font-weight: 600;'" color="#333333">{{$t('CheckOut.SelectPaymentMethod')}}</span>
                    </v-row>
                  </v-col>
                  <v-col cols="12" :class="MobileSize ? 'pt-0' : 'px-0'" >
                    <v-radio class="custom-radio-checkout" value="radio-qr" @click="setRadioCreditTermNo()"><template v-slot:label>
                        <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">QR Code</span>
                      </template>
                    </v-radio>
                  </v-col>
                  <v-col cols="12" :class="MobileSize ? 'pt-0' : 'px-0 py-0'">
                    <v-radio class="custom-radio-checkout" value="radio-credit" @click="setRadioCreditTermNo()"><template v-slot:label>
                        <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">Credit Card / Debit Card</span>
                      </template>
                    </v-radio>
                  </v-col>
                  <v-col cols="12" :class="MobileSize ? 'pt-0' : 'px-0'">
                    <v-radio class="custom-radio-checkout" value="radio-installment" @click="setRadioCreditTermNo()"><template v-slot:label>
                        <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">Credit Card {{$t('CheckOut.Installment')}}</span>
                      </template>
                    </v-radio>
                  </v-col>
                </v-radio-group>
              </div>
              <div v-if="radioPayment === 'radio-installment'" class="mt-0 mb-4 mx-3">
                <v-row align="center">
                  <v-col :cols="MobileSize ? 4 : 4" class="pt-0">
                    <!-- <span style="font-size: 16px;">ระยะเวลาผ่อนชำระ</span> -->
                    <v-img src="@/assets/ktc_logo.png" width="130" height="100" contain style="border-radius: 8px;"></v-img>
                  </v-col>
                  <v-col :cols="MobileSize ? 8 : 8" class="pb-0">
                    <span style="font-size: 16px; font-weight: 600;">{{$t('CheckOut.Krungthai')}}</span>
                    <v-select class="mt-2" outlined dense :label="$t('CheckOut.SelectInstallmentPeriod')" v-model="radioCreditTerm" :items="filteredCreditTerms" item-text="displayText" item-value="value" style="border-radius: 8px;">
                      <template v-slot:append>
                        <v-icon>mdi-chevron-down</v-icon>
                      </template>
                      <template v-slot:no-data>
                        <v-list-item>
                          <v-list-item-content class="text-center">
                            <v-list-item-title>{{$t('CheckOut.InstallmentPaymentNotAvailableBecause')}} <span style="color: #27AB9C;">{{$t('CheckOut.Minimum')}}</span> {{$t('CheckOut.RequiredAmountHasNotBeenMet')}}</v-list-item-title>
                          </v-list-item-content>
                        </v-list-item>
                      </template>
                    </v-select>
                  </v-col>
                </v-row>
              </div>
              <!-- <v-spacer class="mx-3" style="border-top: 2px solid #EBEBEB;"></v-spacer> -->
            </div>
            </v-card>
          </v-col>
        <!-- ส่วนที่เป็นการแสดงรายละเอียดการสั่งซื้อ mobile -->
        <div v-if="MobileSize && ShowMoreDetail" style="position: fixed; bottom: 56px; z-index: 16000100; width: 100%; background-color: #FFFFFF; max-height: 65vh; overflow-y: auto;">
          <v-card style="padding-bottom: 6vw; border-top-right-radius: 6vw; border-top-left-radius: 6vw; margin-top: 10px">
            <div class="top-container">
              <v-btn @click="ShowMoreDetail = !ShowMoreDetail" fab color="#DAF1E9" elevation="0" class="btnCart">
                <v-icon color="#3EC6B6">{{ ShowMoreDetail ? 'mdi-chevron-down' : 'mdi-chevron-up' }}</v-icon>
              </v-btn>
            </div>
            <v-card-text>
              <v-row dense class="pt-2">
                <!-- สรุปราคาสินค้าที่สั่งซื้อ -->
                <v-col cols="12">
                  <span style="font-size: 22px; font-weight: 700; color: #333333;">{{$t('CheckOut.SummaryOrdered')}}</span>
                </v-col>
                <!-- ราคาไม่รวมภาษีมูลค่าเพิ่ม -->
                <v-col cols="12">
                  <div class="d-flex">
                    <span style="font-size: 16px; font-weight: 400; color: #333333;">{{$t('CheckOut.PriceExcludingVAT')}}</span>
                    <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ itemsCart.total_price_no_vat ? formatPrice(itemsCart.total_price_no_vat) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                  </div>
                </v-col>
                <!-- ภาษีมูลค่าเพิ่ม -->
                <v-col cols="12">
                  <div class="d-flex">
                    <span style="font-size: 16px; font-weight: 400; color: #333333;">{{$t('CheckOut.VAT')}}</span>
                    <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ itemsCart.total_vat ? formatPrice(itemsCart.total_vat) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                  </div>
                </v-col>
                <!-- ราคารวมภาษีมูลค่าเพิ่ม -->
                <v-col cols="12">
                  <div class="d-flex">
                    <span style="font-size: 16px; font-weight: 400; color: #333333;"> {{$t('CheckOut.PriceIncludingVAT')}}</span>
                    <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ itemsCart.total_price_vat ? formatPrice(itemsCart.total_price_vat) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                  </div>
                </v-col>
                <!-- ส่วนลดคูปอง (ร้านค้า) -->
                <v-col cols="12">
                  <div class="d-flex">
                    <span style="font-size: 16px; font-weight: 400; color: red;" class="pl-1">{{$t('CheckOut.CouponDiscountSeller')}}</span>
                    <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: red;">- {{ itemsCart.total_coupon_discount ? formatPrice(itemsCart.total_coupon_discount) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                  </div>
                </v-col>
                <!-- ส่วนลดคูปอง (ระบบ) -->
                <v-col cols="12">
                  <div class="d-flex">
                    <span style="font-size: 16px; font-weight: 400; color: red;" class="pl-1">{{$t('CheckOut.CouponDiscountNexgen')}}</span>
                    <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: red;">- {{ itemsCart.total_coupon_platform_discount ? formatPrice(itemsCart.total_coupon_platform_discount) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                  </div>
                </v-col>
                <!-- ส่วนลดแต้ม -->
                <v-col cols="12">
                  <div class="d-flex">
                    <span style="font-size: 16px; font-weight: 400; color: red;" class="pl-1">{{$t('CheckOut.PointDiscount')}}</span>
                    <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: red;">- {{ itemsCart.total_point ? formatPrice(itemsCart.total_point) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                  </div>
                </v-col>
                <!-- ราคาหลังหักส่วนลด -->
                <v-col cols="12">
                  <div class="d-flex">
                    <span style="font-size: 16px; font-weight: 400; color: #333333;">{{$t('CheckOut.SubTotal')}}</span>
                    <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ itemsCart.total_price_after_all_discount ? formatPrice(itemsCart.total_price_after_all_discount) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                  </div>
                </v-col>
                <!-- ค่าจัดส่ง -->
                <v-col cols="12">
                  <div class="d-flex">
                    <div class="mr-auto">
                      <span style="font-size: 16px; font-weight: 400; color: #333333;">{{$t('CheckOut.ShippingCost')}}</span><br>
                      <span style="font-size: 10px; font-weight: 400; color: #A1A1A1; line-height: 14px;">{{$t('CheckOut.ShippingCostDesicription1')}} </span><br>
                      <span style="font-size: 10px; font-weight: 400; color: #A1A1A1; line-height: 14px;">{{$t('CheckOut.ShippingCostDesicription2')}}</span>
                    </div>
                    <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ itemsCart.shipping_price ? formatPrice(itemsCart.shipping_price) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                  </div>
                </v-col>
                <!-- ส่วนลดค่าจัดส่ง (ร้านค้า) -->
                <v-col cols="12">
                  <div class="d-flex">
                    <span style="font-size: 16px; font-weight: 400; color: red;" class="pl-1">{{$t('CheckOut.ShippingDiscountSeller')}}</span>
                    <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: red;">- {{ itemsCart.total_coupon_shipping_discount_v2 ? formatPrice(itemsCart.total_coupon_shipping_discount_v2) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                  </div>
                </v-col>
                <!-- ส่วนลดค่าจัดส่ง (ระบบ) -->
                <v-col cols="12">
                  <div class="d-flex">
                    <span style="font-size: 16px; font-weight: 400; color: red;" class="pl-1">{{$t('CheckOut.ShippingDiscountNexgen')}}</span>
                    <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: red;">- {{ itemsCart.total_coupon_platform_shipping_discount_v2 ? formatPrice(itemsCart.total_coupon_platform_shipping_discount_v2) : '0.00' }} {{$t('CheckOut.Baht')}}</span>
                  </div>
                </v-col>
                <!-- แต้มที่ได้ในครั้งนี้ -->
                <v-col cols="12" v-if="hasPoints">
                  <div class="d-flex">
                    <span style="font-size: 18px; font-weight: 400; color: #EBA61B;" class="pl-1">
                      {{$t('CheckOut.PointsEarned')}}
                      <v-icon style="color: #989898; font-size: 15px" @click="openDialogPoint()">mdi-information-outline</v-icon>
                    </span>
                    <span class="ml-auto" style="font-size: 18px; font-weight: 700; color: #333333;">
                      <span style="color: #EBA61B; font-size: 18px;">{{ Number(parseInt(itemsCart.total_point_receive)).toLocaleString(undefined, {}) }}</span> {{$t('CheckOut.Point')}}
                    </span>
                  </div>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </div>
        <!-- ตรง Footer แสดงข้อมูล Mobile -->
        <v-bottom-navigation v-if="MobileSize" style="height: 20vw; position: fixed; bottom: 0; z-index: 16000101; border-top-right-radius: 6vw; border-top-left-radius: 6vw;">
          <div class="top-container" v-if="ShowMoreDetail === false">
            <v-btn @click="ShowMoreDetail = !ShowMoreDetail" fab color="#DAF1E9" elevation="0" class="btnCart">
              <v-icon color="#3EC6B6">{{ ShowMoreDetail ? 'mdi-chevron-down' : 'mdi-chevron-up' }}</v-icon>
            </v-btn>
          </div>
          <div class="d-flex align-center justify-end pt-4" style="width: 100%;">
            <div class="pr-4">
              <span style="font-size: 18px; font-weight: 500; color: #464646;">{{$t('CheckOut.TotalPriceAll')}}</span><br/>
              <span :style="MobileSize ? 'font-size: 20px; font-weight: 700; color: #27AB9C;' : 'font-size: 24px; font-weight: 700; color: #27AB9C;'" class="ml-auto pt-0 pr-1">
                <b>{{ itemsCart.net_price  ? formatPrice(itemsCart.net_price) : '0.00' }}
                  <span v-if="choose_list === 'recurring'" :style="MobileSize ? 'font-size: 16px; font-weight: 400; color: #333333' : 'font-size: 20px; font-weight: 400; color: #333333'" class="ml-2">{{$t('CheckOut.PricePerMonth')}}</span>
                  <span v-else :style="MobileSize ? 'font-size: 16px; font-weight: 400; color: #333333' : 'font-size: 20px; font-weight: 400; color: #333333'" class="ml-2">{{$t('CheckOut.Baht')}}</span>
                </b>
              </span>
            </div>
            <div class="pr-4">
              <v-btn v-if="role.role === 'ext_buyer'" max-width="150px" style="border-radius: 40px; height: 48px;" class="white--text"
                block color="#3EC6B6" @click="Confirm()" :disabled="isConfirmDisabled">
                <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 500;' : 'font-size: 18px; font-style: normal; font-weight: 500;'"  class="white--text">{{$t('CheckOut.ConfirmOrder')}}</span>
              </v-btn>
            </div>
          </div>
        </v-bottom-navigation>
      </v-row>
    </v-container>
    <v-dialog v-model="dialogAwaitConfirm" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="240px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn color="#CCCCCC" icon
              @click="typeButton === 'confirm' ? CancelAwaitConfirm() : dialogAwaitConfirm = !dialogAwaitConfirm">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ typeButton
              === 'confirm' ? 'ยืนยันการสั่งซื้อ' : 'ลบที่อยู่จัดส่ง' }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{$t('CheckOut.ComfirmModal')}}</span>
          </v-card-text>
          <v-card-text>
            <v-row v-if="!MobileSize" dense justify="center">
              <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4"
                @click="CancelAwaitConfirm()">{{$t('CheckOut.Cancel')}}</v-btn>
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C"
                @click="typeButton === 'confirm' && radioPayment === '' ? Test('payment')
                  : typeButton === 'confirm' && radioPayment === 'radio-qr' ? GetQRCode('cashPayment') : typeButton === 'confirm' && (radioPayment === 'radio-credit' || radioPayment === 'radio-installment') ? GetCC('cashPayment')
                  : typeButton === 'confirm' && radioPayment === 'radio-EWTH' ? GetEWTH('cashPayment') : typeButton === 'deleteCompany' ? deleteCompanyAddress() : deleteAddress()">{{$t('CheckOut.Confirm')}}</v-btn>
            </v-row>
            <v-row v-if="MobileSize" dense>
              <v-btn outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }" @click="CancelAwaitConfirm()">{{$t('CheckOut.Cancel')}}</v-btn>
              <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }"
                @click="typeButton === 'confirm' && radioPayment === '' ? Test('payment')
                  : typeButton === 'confirm' && radioPayment === 'radio-qr' ? GetQRCode('cashPayment') : typeButton === 'confirm' && (radioPayment === 'radio-credit' || radioPayment === 'radio-installment') ? GetCC('cashPayment')
                  : typeButton === 'confirm' && radioPayment === 'radio-EWTH' ? GetEWTH('cashPayment') : typeButton === 'deleteCompany' ? deleteCompanyAddress() : deleteAddress()">{{$t('CheckOut.Confirm')}}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogSuccess" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="240px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn color="#CCCCCC" icon @click="typeButton === 'confirm' ? goHomePage() : typeButton === 'confirm' && radioPayment === 'radio-credit' ? goHomePage() : closeModalSuccess()">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ typeButton
              === 'confirm' ? `${$t('CheckOut.OrderConfirmationCompleted')}` : `${$t('CheckOut.ShippingAddressDeletedSuccessfully')}` }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ typeButton === 'confirm'
              ? `${$t('CheckOut.YourOrderHasBeenPlacedSuccessfully')}` : `${$t('CheckOut.YouHaveSuccessfullyDeletedTheShippingAddress')}` }}</span><br />
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;"
              v-if="typeButton === 'confirm' && radioPayment !== 'radio-credit'">{{$t('CheckOut.PleaseWaitSellerApproveOrder')}}</span>
          </v-card-text>
          <v-card-text :class="MobileSize ? 'px-0 pb-2' : ''">
            <v-row dense justify="center">
              <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                <v-btn :width="MobileSize ? '165' : ''" :style="{ flex: MobileSize ? '0 0 auto' : '1 1 auto' }" height="38" outlined rounded color="#27AB9C" :class="MobileSize ? 'mr-2': 'mr-4'" v-if="(typeButton === 'confirm' && radioPayment === '' && itemsCart.is_JV === 'no' && itemsCart.is_JV_buyer === 'yes') || (typeButton === 'confirm' && radioPayment === '' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'no')"
                @click="GoToOrderCompany()">{{$t('CheckOut.GoOrderListPage')}}</v-btn>
                <v-btn :style="{ flex: '1 1 auto' }" height="38" class="white--text" rounded color="#27AB9C" v-if="(typeButton === 'confirm' && radioPayment === '' && itemsCart.is_JV === 'no' && itemsCart.is_JV_buyer === 'yes') || (typeButton === 'confirm' && radioPayment === '' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'no')"
                @click="goHomePage()">{{$t('CheckOut.GoToHomePage')}}</v-btn>
                <v-btn :width="MobileSize ? '165' : ''" :style="{ flex: MobileSize ? '0 0 auto' : '1 1 auto' }" height="38" outlined rounded color="#27AB9C" :class="MobileSize ? 'mr-2': 'mr-4'" v-if="chooseitem.product_list.some(productitem => productitem.product_type === 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes' && typeButton === 'confirm' && radioPayment === '')"
                @click="GoToOrderCompany()">{{$t('CheckOut.GoOrderListPage')}}</v-btn>
                <v-btn :style="{ flex: '1 1 auto' }" height="38" class="white--text" rounded color="#27AB9C" v-if="chooseitem.product_list.some(productitem => productitem.product_type === 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes' && typeButton === 'confirm' && radioPayment === '')"
                @click="goHomePage()">{{$t('CheckOut.GoToHomePage')}}</v-btn>
                <v-btn :width="MobileSize ? '165' : ''" :style="{ flex: MobileSize ? '0 0 auto' : '1 1 auto' }" height="38" outlined rounded color="#27AB9C" :class="MobileSize ? 'mr-2': 'mr-4'" v-if="chooseitem.product_list.some(productitem => productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes' && typeButton === 'confirm' && radioPayment === '')"
                @click="goToQUCompany()">{{$t('CheckOut.GoQuotationPage')}}</v-btn>
                <v-btn :style="{ flex: '1 1 auto' }" height="38" class="white--text" rounded color="#27AB9C" v-if="chooseitem.product_list.some(productitem => productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes' && typeButton === 'confirm' && radioPayment === '')"
                @click="goHomePage()">{{$t('CheckOut.GoToHomePage')}}</v-btn>
                <v-btn :style="{ flex: '1 1 auto' }" height="38" outlined rounded color="#27AB9C" class="mr-4" v-if="typeButton === 'confirm' && radioPayment !== 'radio-credit' && radioPayment === 'radio-qr'"
                @click="goPoBuyerProfilePage()">{{$t('CheckOut.GoOrderListPage')}}</v-btn>
                <v-btn :style="{ flex: '1 1 auto' }" height="38" class="white--text" rounded color="#27AB9C" v-if="typeButton === 'confirm' && radioPayment !== 'radio-credit' && radioPayment === 'radio-qr'"
                @click="goHomePage()">{{$t('CheckOut.GoToHomePage')}}</v-btn>
                <v-btn :style="{ flex: '1 1 auto' }" height="38" outlined rounded color="#27AB9C" class="mr-4" v-if="typeButton === 'confirm' && radioPayment === 'radio-credit' && radioPayment !== 'radio-credit'"
                  @click="goPoBuyerProfilePage()">{{$t('CheckOut.GoOrderListPage')}}</v-btn>
                <v-btn :style="{ flex: '1 1 auto' }" height="38" rounded color="#27AB9C" class="white--text" v-if="typeButton === 'confirm' && radioPayment === 'radio-credit' && radioPayment !== 'radio-credit'"
                  @click="goHomePage()">{{$t('CheckOut.GoToHomePage')}}</v-btn>
                <v-btn :width="!MobileSize ? '156' : ''" :style="{ flex: !MobileSize ? '' : '1' }" height="38" class="white--text mx-2" rounded color="#27AB9C" v-if="typeButton !== 'confirm'"
                  @click="closeModalSuccess()">{{$t('CheckOut.Confirm')}}</v-btn>
              </div>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Dialog เปลี่ยนที่อยู่ -->
    <v-dialog v-model="DialogAddress" :width="MobileSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead"
            style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-6">
                <span
                  :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>{{$t('CheckOut.ChangeShippingAddress')}}</b></span>
              </v-col>
              <v-btn fab small @click="cancelChangeAddrss()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <div :class="MobileSize ? 'py-6 px-4' : 'py-12 px-10'">
                <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                <span :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'" >{{$t('CheckOut.ShippingAddress')}}</span>
                <v-card v-show="!MobileSize" class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in AddressNew"
                  :key="index"
                  style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                  <v-row>
                    <v-col cols="1" class="align-self-center mt-2">
                      <v-radio-group v-model="item.default_address" class="mt-0 pt-0">
                        <v-radio color="#27AB9C" value="Y" :disabled="item.default_address === 'Y'" @click="setDefaultAdress(item)" style="color: #333333">
                        </v-radio>
                      </v-radio-group>
                    </v-col>
                    <div class="col-11 row">
                      <v-col class="pl-0 pb-0" cols="7">
                        <span style="font-size: 16px; font-weight: 600;">{{ item.first_name }} {{ item.last_name }}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span style="font-size: 16px; font-weight: 600;">{{ item.phone }}</span>
                      </v-col>
                      <v-col class="pl-0 pb-0" cols="5">
                        <v-row no-gutters justify="end">
                          <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                            @click="editAddress(item)"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                              style="text-decoration-line: underline;">{{$t('CheckOut.Edit')}}</span></v-btn>
                          <v-btn :disabled="AddressNew.length === 1 || item.default_address === 'Y'" color="red" text
                            style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                            @click="DeleteAddress(item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                              style="text-decoration-line: underline;">{{$t('CheckOut.Delete')}}</span></v-btn>
                        </v-row>
                      </v-col>
                      <v-col class="pb-2 pl-0 pa-3 pt-0">
                        <span style="font-size: 16px;">{{ item.detail }} {{ item.sub_district }} {{ item.district }} {{
                          item.province }} {{ item.zip_code }}</span><br>
                          <span v-if="item.note_address" style="font-size: 16px; color: #333333;">{{$t('CheckOut.Notation')}} : {{ item.note_address }}</span>
                      </v-col>
                    </div>
                  </v-row>
                </v-card>
                <v-card v-show="MobileSize" class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in AddressNew"
                  :key="index"
                  style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                  <v-row>
                    <v-col  class="align-self-center mt-2">
                      <v-radio-group v-model="item.default_address" class="mt-0 pt-0">
                        <v-radio color="#27AB9C" value="Y" :disabled="item.default_address === 'Y'" @click="setDefaultAdress(item)" style="color: #333333">
                        </v-radio>
                      </v-radio-group>
                    </v-col>
                    <div class="row">
                      <v-col class="pl-0 pb-0 mt-5">
                        <v-row no-gutters justify="end">
                          <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                            @click="editAddress(item)"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                              style="text-decoration-line: underline;">{{$t('CheckOut.Edit')}}</span></v-btn>
                          <v-btn :disabled="AddressNew.length === 1 || item.default_address === 'Y'" color="red" text
                            style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                            @click="DeleteAddress(item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                              style="text-decoration-line: underline;">{{$t('CheckOut.Delete')}}</span></v-btn>
                        </v-row>
                      </v-col>
                    </div>
                    <v-col class="pt-2" cols="12">
                        <span :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 16px; font-weight: 600;'" >{{ item.first_name }} {{ item.last_name }}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 16px; font-weight: 600;'">{{ item.phone }}</span>
                      </v-col>
                    <v-col class="pb-2 px-2 pt-0">
                      <span :style="MobileSize ? 'font-size: 12px;' : 'font-size: 16px;'">{{ item.detail }} {{ item.sub_district }} {{ item.district }} {{
                        item.province }} {{ item.zip_code }}</span><br>
                        <span v-if="item.note_address" style="font-size: 12px; color: #333333;">{{$t('CheckOut.Notation')}} : {{ item.note_address }}</span>
                    </v-col>
                  </v-row>
                </v-card>
                <div v-if="AddressNew.length === 0" style="text-align: -webkit-center;">
                  <v-img src="@/assets/emptypo.png" width="500" height="100%"></v-img>
                  <v-col class="pt-10 pb-0">
                    <span  :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'">{{$t('CheckOut.NoAddress')}}</span>
                  </v-col>
                </div>
                <v-card v-if="AddressNew.length !== 10" class="mt-7" elevation="0"
                  style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="userdetail.length === 1 && (userdetail[0].zip_code === '' || userdetail[0].zip_code === null) ? editAddress(userdetail[0]) : addAddress()">
                  <v-card-text class="py-2">
                    <v-row class="my-2 px-3 ">
                      <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                      <span class="pl-2"
                        style="font-weight: 500; font-size: 14px; color: #1B5DD6;">{{$t('CheckOut.AddAddress')}}</span>
                    </v-row>
                  </v-card-text>
                </v-card>
              </div>
            </v-card>
          </div>
        </v-card-text>
        <!-- <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="cancelChangeAddrss()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" height="40" class="white--text"
            @click="confirmAddress()">ยืนยัน</v-btn>
        </v-card-actions> -->
      </v-card>
    </v-dialog>
    <!-- Dialog เปลี่ยนที่อยู่ Company -->
    <v-dialog v-model="DialogCompanyAddress" :width="MobileSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead"
            style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-6">
                <span
                  :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>{{$t('CheckOut.ChangeShippingAddress')}}</b></span>
              </v-col>
              <v-btn fab small @click="cancelChangeAddrss()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <div :class="MobileSize ? 'py-6 px-4' : 'py-12 px-10'">
                <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                <span :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'" >{{$t('CheckOut.ShippingAddress')}}</span>
                <v-card v-show="!MobileSize" class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in comAddress"
                  :key="index"
                  style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                  <v-row>
                    <v-col cols="1" class="align-self-center mt-2">
                      <v-radio-group v-model="item.default" class="mt-0 pt-0">
                        <v-radio color="#27AB9C" value="Y" :disabled="item.default === 'Y'" @click="setDefaultCompanyAddress(item)" style="color: #333333">
                        </v-radio>
                      </v-radio-group>
                    </v-col>
                    <div class="col-11 row">
                      <v-col class="pl-0 pb-0" cols="7">
                        <span style="font-size: 16px; font-weight: 600;">{{ item.name_th }}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span style="font-size: 16px; font-weight: 600;">{{ item.phone }}</span>
                      </v-col>
                      <v-col class="pl-0 pb-0" cols="5">
                        <v-row no-gutters justify="end">
                          <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                            @click="editCompanyAddress(item)"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                              style="text-decoration-line: underline;">{{$t('CheckOut.Edit')}}</span></v-btn>
                          <v-btn v-if="index !== 0" :disabled="comAddress.length === 1 || item.default === 'Y'" color="red" text
                            style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                            @click="DeleteCompanyAddress(item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                              style="text-decoration-line: underline;">{{$t('CheckOut.Delete')}}</span></v-btn>
                        </v-row>
                      </v-col>
                      <v-col cols="12" class="pt-0 pl-0 pb-0 mt-1">
                        <span style="font-size: 16px;">{{$t('CheckOut.TaxNumber')}}: <b>{{ item.tax_id }}</b></span>
                      </v-col>
                      <v-col class="pb-2 pl-0 pa-3 pt-0">
                        <span style="font-size: 16px;">{{ item.detail }} {{ item.sub_district }} {{ item.district }} {{
                          item.province }} {{ item.zip_code }}</span>
                      </v-col>
                    </div>
                  </v-row>
                </v-card>
                <v-card v-show="MobileSize" class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in comAddress"
                  :key="index"
                  style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                  <v-row>
                    <v-col  class="align-self-center mt-2">
                      <v-radio-group v-model="item.default" class="mt-0 pt-0">
                        <v-radio color="#27AB9C" value="Y" :disabled="item.default === 'Y'" @click="setDefaultCompanyAddress(item)" style="color: #333333">
                        </v-radio>
                      </v-radio-group>
                    </v-col>
                    <div class="row">
                      <v-col class="pl-0 pb-0 mt-5">
                        <v-row no-gutters justify="end">
                          <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                            @click="editCompanyAddress(item)"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                              style="text-decoration-line: underline;">{{$t('CheckOut.Edit')}}</span></v-btn>
                          <v-btn v-if="index !== 0" :disabled="comAddress.length === 1 || item.default === 'Y'" color="red" text
                            style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                            @click="DeleteCompanyAddress(item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                              style="text-decoration-line: underline;">{{$t('CheckOut.Delete')}}</span></v-btn>
                        </v-row>
                      </v-col>
                    </div>
                    <v-col class="pt-2" cols="12">
                        <span :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 16px; font-weight: 600;'" >{{ item.name_th }}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 16px; font-weight: 600;'">{{ item.phone }}</span>
                      </v-col>
                      <v-col cols="12" class="pt-0 pl-2 pb-0 mt-1">
                        <span style="font-size: 12px;">{{$t('CheckOut.TaxNumber')}}: <b>{{ item.tax_id }}</b></span>
                      </v-col>
                    <v-col class="pb-2 px-2 pt-0">
                      <span :style="MobileSize ? 'font-size: 12px;' : 'font-size: 16px;'">{{ item.detail }} {{ item.sub_district }} {{ item.district }} {{
                        item.province }} {{ item.zip_code }}</span>
                    </v-col>
                  </v-row>
                </v-card>
                <div v-if="comAddress.length === 0" style="text-align: -webkit-center;">
                  <v-img src="@/assets/emptypo.png" width="500" height="100%"></v-img>
                  <v-col class="pt-10 pb-0">
                    <span  :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'">{{$t('CheckOut.NoAddress')}}</span>
                  </v-col>
                </div>
                <v-card v-if="comAddress.length !== 10" class="mt-7" elevation="0"
                  style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="addCompanyAddress()">
                  <v-card-text class="py-2">
                    <v-row class="my-2 px-3 ">
                      <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                      <span class="pl-2"
                        style="font-weight: 500; font-size: 14px; color: #1B5DD6;">{{$t('CheckOut.AddAddress')}}</span>
                    </v-row>
                  </v-card-text>
                </v-card>
              </div>
            </v-card>
          </div>
        </v-card-text>
        <!-- <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="cancelChangeAddrss()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" height="40" class="white--text"
            @click="confirmAddress()">ยืนยัน</v-btn>
        </v-card-actions> -->
      </v-card>
    </v-dialog>
    <!-- Dialog เลือกขนส่ง -->
    <v-dialog v-model="DialogTransport" :width="MobileSize ? '100%' : '513'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 8px; overflow-x: hidden;">
        <v-card-text class="pa-0">
          <div class="backgroundContent" style="position: relative;">
            <v-card :class="MobileSize ? '' : ''" elevation="0" width="100%" height="100%" :style="MobileSize ? '' : 'max-height: 550px;'" style="background: #FFFFFF; border-radius: 20px; overflow-y: auto; overflow-x: hidden;">
              <div :class="MobileSize ? 'py-6 px-4' : 'py-6 px-4'">
                <v-row dense style="justify-content: space-between; align-items: center;">
                  <span :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 18px; font-weight: 600;'" >{{$t('CheckOut.SelectShippingMethod')}}</span>
                    <v-btn fab small @click="DialogTransport = !DialogTransport" icon :class="MobileSize ? '' : ''"><v-icon
                    >mdi-close</v-icon></v-btn>
                </v-row>
                <div v-if="!MobileSize">
                  <v-img class="mt-5" :src="$t('CheckOut.ShippingBanner')" max-width="520" max-height="120"></v-img>
                    <v-hover v-slot="{ hover }" v-for="(item, index) in estimateCostData" :key="index">
                      <v-card class="mt-5 pa-4" :elevation="hover ? 4 : 0" :class="{ 'on-hover': hover }" width="100%" height="100%"
                        style="background: #FFFFFF; border-radius: 8px; border: 1px solid #F2F2F2;"
                        @click="selectTransport(item)"
                        >
                        <v-row>
                          <v-col class="pa-0" cols="4">
                          <v-img v-if="item.media_path !== ''" class="ma-2" style="border-radius: 8px;" :src="item.media_path" contain max-width="100" max-height="100"></v-img>
                          <v-img v-else src="@/assets/NoImage.png" class="ma-2" style="border-radius: 8px;" contain max-width="100" max-height="100"></v-img>
                          </v-col>
                          <v-col  cols="8" class="align-self-center text-start pl-0 py-0">
                            <span :style="MobileSize ? 'font-size: 16px; font-weight: 700;' : 'font-size: 18px; font-weight: 600;'">{{item.tpl_name}}</span><br>
                            <span :style="MobileSize ? 'font-size: 16px; font-weight: 700; color: #3EC6B6;' : 'font-size: 16px; font-weight: 500; color: #3EC6B6;'">{{$t('CheckOut.StartingShippingFee')}} {{Math.ceil(item.netEstimatePrice)}} {{$t('CheckOut.Baht')}}</span>
                          </v-col>
                        </v-row>
                      </v-card>
                    </v-hover>
                </div>
                <div v-else>
                  <v-img class="mt-5" :src="$t('CheckOut.ModalShippingIMG')" cover style="border-radius: 8px;"></v-img>
                    <v-hover v-slot="{ hover }" v-for="(item, index) in estimateCostData" :key="index">
                      <v-card class="mt-5 pa-4" :elevation="hover ? 4 : 0" :class="{ 'on-hover': hover }" width="100%" height="100%"
                        style="background: #FFFFFF; border-radius: 8px; border: 1px solid #F2F2F2;"
                        @click="selectTransport(item)"
                        >
                        <v-row>
                          <v-col class="pa-0" cols="4">
                          <v-img v-if="item.media_path !== ''" class="ma-2" style="border-radius: 8px;" :src="item.media_path" contain max-width="80" max-height="80"></v-img>
                          <v-img v-else src="@/assets/NoImage.png" class="ma-2" style="border-radius: 8px;" contain max-width="80" max-height="80"></v-img>
                          </v-col>
                          <v-col  cols="8" class="align-self-center text-start pl-0 py-0">
                            <span :style="MobileSize ? 'font-size: 16px; font-weight: 700;' : 'font-size: 18px; font-weight: 600;'">{{item.tpl_name}}</span><br>
                            <span :style="MobileSize ? 'font-size: 14px; font-weight: 500; color: #3EC6B6;' : 'font-size: 16px; font-weight: 500; color: #3EC6B6;'">{{$t('CheckOut.StartingShippingFee')}} {{Math.ceil(item.netEstimatePrice)}} {{$t('CheckOut.Baht')}}</span>
                          </v-col>
                        </v-row>
                      </v-card>
                    </v-hover>
                </div>
              </div>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Dialog เปลี่ยนที่อยู่ Tax -->
    <v-dialog v-model="DialogTaxAddress" :width="MobileSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;" >
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead"
            style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-6">
                <span
                :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>{{$t('CheckOut.ChangeBillingAddressInvoice')}}</b></span>
              </v-col>
              <v-btn @click="DialogTaxAddress = !DialogTaxAddress" fab small icon  :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <div  :class="MobileSize ? 'py-6 px-4' : 'py-12 px-10'">
                <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                <span :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'">{{$t('CheckOut.TaxInvoiceAddress')}}</span>
                <v-card v-show="!MobileSize" class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in invoicedetail" :key="index"
                  style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                  <v-row>
                    <v-col cols="1" class="align-self-center mt-2">
                      <v-radio-group v-model="DefaultInvoice" class="mt-0 pt-0">
                        <v-radio color="#27AB9C" :value="item.id" @click="setDefaultInvoice(item)" style="color: #333333">
                        </v-radio>
                      </v-radio-group>
                    </v-col>
                    <div class="col-11 row">
                      <v-col class="pl-0 pb-0" cols="6">
                        <span style="font-size: 16px; font-weight: 600;">{{item.name}}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span class="pr-1" style="font-size: 16px; font-weight: 600; color: #27AB9C;">{{item.tax_type === 'Personal' ? `${$t('CheckOut.Individual')}` : `${$t('CheckOut.JuristicPerson')}`}}</span>
                        <span style="font-size: 16px; font-weight: 600; color: #9E9E9E;">{{item.default_invoice === 'Y' ? `(${$t('CheckOut.DefaultAddress')})` : ''}}</span>
                      </v-col>
                      <v-col class="pl-0 pb-0" cols="6">
                        <v-row no-gutters justify="end">
                          <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="openModalEditTaxAddress(item)"
                            ><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                              style="text-decoration-line: underline;">{{$t('CheckOut.Edit')}}</span></v-btn>
                          <v-btn color="red" text :disabled="invoicedetail.length === 1 || item.default_invoice === 'Y'"
                            style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="openDeleteInvoice(item)"
                            ><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                              style="text-decoration-line: underline;">{{$t('CheckOut.Delete')}}</span></v-btn>
                        </v-row>
                      </v-col>
                      <v-col class="py-0 pl-0" v-if="item.tax_type !== 'Personal'">
                        <span style="font-size: 16px;">{{$t('CheckOut.BranchCode')}}: <b>{{item.branch_id}}</b></span>
                      </v-col>
                      <v-col cols=12 class="pb-2 pl-0 pa-3 pt-0">
                        <span style="font-size: 16px;">{{$t('CheckOut.TaxNumber')}}: <b>{{item.tax_id}}</b></span>
                      </v-col>
                      <v-col cols="12" class="pb-2 pl-0 pa-3 pt-0">
                        <span style="font-size: 16px;">{{$t('CheckOut.Address')}}: {{item.address}} {{item.sub_district}} {{item.district}} {{item.province}} {{item.postal_code}}</span>
                      </v-col>
                    </div>
                  </v-row>
                </v-card>
                <v-card v-show="MobileSize" class="mt-4 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in invoicedetail" :key="index"
                  style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                  <v-row>
                    <v-col class="align-self-center mt-2">
                      <v-radio-group v-model="DefaultInvoice" class="mt-0 pt-0">
                        <v-radio color="#27AB9C" :value="item.id" @click="setDefaultInvoice(item)" style="color: #333333">
                        </v-radio>
                      </v-radio-group>
                    </v-col>
                    <div class="row">
                      <v-col class="pl-0 pb-0 mt-5">
                        <v-row no-gutters justify="end">
                          <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="openModalEditTaxAddress(item)"
                            ><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                              style="text-decoration-line: underline;">{{$t('CheckOut.Edit')}}</span></v-btn>
                          <v-btn color="red" text :disabled="invoicedetail.length === 1 || item.default_invoice === 'Y'"
                            style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="openDeleteInvoice(item)"
                            ><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                              style="text-decoration-line: underline;">{{$t('CheckOut.Delete')}}</span></v-btn>
                        </v-row>
                      </v-col>
                    </div>
                      <v-col class="pt-2" cols="12">
                        <span style="font-size: 14px; font-weight: 600;">{{item.name}}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span class="pr-1" style="font-size: 14px; font-weight: 600; color: #27AB9C;">{{item.tax_type === 'Personal' ? `${$t('CheckOut.Individual')}` : `${$t('CheckOut.JuristicPerson')}`}}</span>
                        <span style="font-size: 14px; font-weight: 600; color: #9E9E9E;">{{item.default_invoice === 'Y' ? `${$t('CheckOut.DefaultAddress')})` : ''}}</span>
                      </v-col>
                      <v-col class="py-0 pl-2" v-if="item.tax_type !== 'Personal'">
                        <span style="font-size: 14px;">{{$t('CheckOut.BranchCode')}}: <b>{{item.branch_id}}</b></span>
                      </v-col>
                      <v-col cols=12 class="pb-2 px-2 pt-0">
                        <span style="font-size: 14px;">{{$t('CheckOut.TaxNumber')}}: <b>{{item.tax_id}}</b></span>
                      </v-col>
                      <v-col cols="12" class="pb-2 px-2 pt-0">
                        <span style="font-size: 12px;">{{$t('CheckOut.Address')}}: {{item.address}} {{item.sub_district}} {{item.district}} {{item.province}} {{item.postal_code}}</span>
                      </v-col>
                  </v-row>
                </v-card>
                <div v-if="invoicedetail.length === 0" style="text-align: -webkit-center;">
                  <v-img src="@/assets/emptypo.png" width="500" height="100%"></v-img>
                  <v-col class="pt-10 pb-0">
                    <span  :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'">{{$t('CheckOut.NoAddress')}}</span>
                  </v-col>
                </div>
                <v-card v-if="invoicedetail.length !== 5" class="mt-7" elevation="0"
                  style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="openModalTaxAddress()">
                  <v-card-text class="py-2">
                    <v-row class="my-2 px-3 ">
                      <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                      <span class="pl-2"
                        style="font-weight: 500; font-size: 14px; color: #1B5DD6;">{{$t('CheckOut.AddAddress')}}</span>
                    </v-row>
                  </v-card-text>
                </v-card>
              </div>
            </v-card>
          </div>
        </v-card-text>
        <!-- <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="cancelChangeAddrss()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" height="40" class="white--text"
            @click="confirmAddress()">ยืนยัน</v-btn>
        </v-card-actions> -->
      </v-card>
    </v-dialog>
    <!-- Dialog QR -->
    <v-dialog v-model="DialogQR" :width="MobileSize ? '100%' : '640'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden; ">
        <v-card-text class="px-0">
          <div  :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 640px'" class="backgroundHead"
            style="position: absolute; height: 120px; ">
            <v-row style="height: 120px; ">
              <v-col style="text-align: center;" class="pt-6">
                <span style="margin-left: 0px"
                :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>{{ QRShop === true ? `${$t('CheckOut.ScanQRCodeMakePayment')}` : `${$t('CheckOut.ScanQRCodeToPay')}` }}</b></span>
              </v-col>
              <v-btn fab small @click="closeDialogQR()" icon class="mt-3"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '640px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card class="d-flex justify-center mt-10 mb-9" elevation="0" width="100%" height="100%"
              style="background: #FFFFFF; border-radius: 20px;">
              <div style="text-align: center;">
                <v-col class="py-0">
                  <img id="qrcode" height="280" width="280" style="margin-inline: auto;" :src="`${imageBase64}`" v-if="ImageQR !== ''"/>
                </v-col>
                <v-col class="py-0">
                  <v-btn v-if="TypeOS !== 'iOS'" color="#27AB9C" rounded width="125" height="40" class="white--text my-8" @click="saveQRCode()">{{$t('CheckOut.SaveImage')}}</v-btn>
                </v-col>
                <div>
                  <v-col>
                    <span :style="MobileSize ? 'font-size: 16px; font-weight: 700;' : 'font-size: 20px; font-weight: 700;'">{{$t('CheckOut.TotalPaymentAmount')}} : {{ netPrice ?
                      Number(netPrice).toLocaleString(undefined, { minimumFractionDigits: 2 }) : '0.00' }}
                       {{$t('CheckOut.Baht')}}</span>
                  </v-col>
                  <v-col>
                    <span :style="MobileSize ? 'font-size: 13px; font-weight: 400;' : 'font-size: 14px; font-weight: 400;'">{{ QRShop === false ? `${$t('CheckOut.RefID')}` : '' }} {{Ref1}}</span>
                  </v-col>
                  <v-col class="py-0">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 600; color: #A1A1A1;' : 'font-size: 14px; font-weight: 600; color: #A1A1A1;'">{{$t('CheckOut.CanMakePaymentByFollowingStep')}}</span>
                  </v-col>
                  <v-col class="text-left py-0">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 400; color: #A1A1A1;' : 'font-size: 14px; font-weight: 400; color: #A1A1A1;'">1. {{$t('CheckOut.SaveImageStep1')}}<br><span style="color: red; font-weight: 700;">* {{$t('CheckOut.ForIOSDevices')}}</span> {{$t('CheckOut.TakeScreenshot')}} <b>{{$t('CheckOut.TapTheButton')}}</b></span>
                  </v-col>
                  <v-col class="text-left py-0">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 400; color: #A1A1A1;' : 'font-size: 14px; font-weight: 400; color: #A1A1A1;'">2. {{$t('CheckOut.OpenYourBankingApp')}}</span>
                  </v-col>
                  <v-col class="text-left py-0">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 400; color: #A1A1A1;' : 'font-size: 14px; font-weight: 400; color: #A1A1A1;'">3. {{$t('CheckOut.SelectScreenshotScanTheQRCode')}}</span>
                  </v-col>
                </div>
              </div>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <EditModalAddress :EditAddressDetail="EditAddressDetail" :title="titleAddress" :page="page" />
    <EditModalCompanyAddress :EditAddressDetail="EditAddressDetail" :title="titleCompanyAddress" :page="page" />
    <ModalTaxInvoice ref="ModalTaxAddress" :title="titleTaxAddress" :frompage="CartPage" :ShippingType="selectTypeAddress " :page="page"/>
    <CouponCart ref="CouponCart" />
    <CodeCart ref="CodeCart" />
    <SystemCodeCart ref="SystemCodeCart" />
    <SystemCodeCartShipping ref="SystemCodeCartShipping" />
    <PointCart ref="PointCart" />
    <v-dialog v-model="dialog_Cancel_Coupon" width="436px" persistent scrollable>
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense>
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">{{$t('CheckOut.RequestQuotationEdit')}}</font>
          </span>
          <v-btn icon dark @click="dialog_Cancel_Coupon = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text align="center">
          <v-container>
            <v-row align="center" class="pa-2 mt-4">
              <span style="font-weight: 200; font-size: 18px; line-height: 26px; color: #333333;">
                {{$t('CheckOut.EditCouponDisscription')}}<br />
                <font color="#27AB9C">{{$t('CheckOut.EditCouponConfirm')}}</font>
              </span>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions align="center">
          <v-container>
            <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-1" @click="dialog_Cancel_Coupon = false">
              {{$t('CheckOut.Cancel')}}
            </v-btn>
            <v-btn dense color="#27AB9C" class="ml-4 mt-1 pl-8 pr-8 white--text" @click="openEditQU()">
              {{$t('CheckOut.Confirm')}}
            </v-btn>
          </v-container>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="deleteDialog" :style="MobileSize ? 'z-index: 16000004' : ''" persistent width="424">
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="deleteDialog = !deleteDialog"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 20px; line-height: 24px; color: #333333;" class="my-4" ><b>{{$t('CheckOut.DeleteTaxInvoiceAddress')}}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{$t('CheckOut.ConfirmDelectTaxInvoiceAddress')}}</span>
          </v-card-text>
          <v-card-text>
            <v-row v-if="!MobileSize" dense justify="center">
              <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="deleteDialog = !deleteDialog">{{$t('CheckOut.Cancel')}}</v-btn>
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="DeleteInvoice()">{{$t('CheckOut.Confirm')}}</v-btn>
            </v-row>
            <v-row v-if="MobileSize" dense justify="center">
              <v-btn height="38" outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }" @click="deleteDialog = !deleteDialog">{{$t('CheckOut.Cancel')}}</v-btn>
              <v-btn height="38" class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="DeleteInvoice()">{{$t('CheckOut.Confirm')}}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogDeleteSuccessTax" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogDeleteSuccessTax = !dialogDeleteSuccessTax"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 20px; line-height: 24px; color: #333333;" class="my-4"><b>{{$t('CheckOut.DeleteAddressSuccessfully')}}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{$t('CheckOut.YouHaveSuccessfullyDeletedTaxInvoiceAddress')}}</span><br/>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center" v-if="!MobileSize">
            <v-btn width="156" height="38" dark rounded color="#27AB9C" @click="dialogDeleteSuccessTax = !dialogDeleteSuccessTax">{{$t('CheckOut.Confirm')}}</v-btn>
            </v-row>
            <v-row dense justify="center" v-if="MobileSize">
            <v-btn height="38" dark rounded color="#27AB9C" :style="{ flex: '1' }" @click="dialogDeleteSuccessTax = !dialogDeleteSuccessTax">{{$t('CheckOut.Confirm')}}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogPoint" width="482"  persistent style="z-index: 16000103;">
        <v-card style="border-radius: 24px !important; background: #FFFFFF;">
          <v-card-text class="pa-0" height="240" style="text-align: -webkit-center; background: linear-gradient(180deg, #FFECC8 0%, #FFD689 126.86%);">
            <v-app-bar  flat color="rgba(0, 0, 0, 0)">
              <v-spacer></v-spacer>
              <v-btn class="float-end" @click="dialogPoint = false" color="#CCCCCC" icon >
                <v-icon style="font-size: 32px">mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
            <v-img height="223" max-width="340" :src="require('@/assets/showPoint.png')">
            </v-img>
          </v-card-text >
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 20px; line-height: 33.6px; color: #333333;" class="my-4"><b>{{$t('CheckOut.TotalPointsEarnedInThisTransaction')}}</b></p>
              <v-simple-table
                style="border-radius: 8px; border: 1px solid #F2F2F2"
                class="mb-3"
              >
                <template v-slot:default>
                  <thead style="background: #FAFAFA;">
                    <tr>
                      <th class="text-start" style="color: #333333; height: 42px;">
                        {{$t('CheckOut.Store')}}
                      </th>
                      <th class="text-start" style="color: #333333; height: 42px;">
                        {{$t('CheckOut.PointEarned')}}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="item in filteredItemsCart"
                      :key="item.seller_shop_name"
                    >
                      <td class="text-start" style="font-size: 16px; color: #333333; height: 80px;">{{ item.seller_shop_name }}</td>
                      <td class="text-start" style="font-size: 16px; color: #333333; height: 80px;">{{ Number(parseInt(item.seller_shop_point_data[0].total_point_receive)).toLocaleString(undefined, {}) }}</td>
                    </tr>
                  </tbody>
                      <tfoot class="custom-footer">
                      <tr>
                        <td class="text-start" style="font-size: 16px; font-weight: 700; color: #EBA61B; height: 42px;"><strong>{{$t('CheckOut.TotalPoint')}}</strong></td>
                        <td class="text-start" style="font-size: 16px; font-weight: 700; color: #EBA61B; height: 42px;">
                          <strong>
                            {{ Number(parseInt(itemsCart.total_point_receive)).toLocaleString(undefined, {}) }}
                          </strong>
                        </td>
                      </tr>
                    </tfoot>
                </template>
              </v-simple-table>
          </v-card-text>
        </v-card>
      </v-dialog>
  </div>
</template>

<script>
import { Decode, Encode } from '@/services'
import { Table, Space, TimePicker } from 'ant-design-vue'
import { Affix } from 'vue-affix'
import Vue from 'vue'
export default {
  props: ['taxType'],
  components: {
    Affix,
    'a-table': Table,
    'a-space': Space,
    'a-time-picker': TimePicker,
    'a-time-range-picker': TimePicker.RangePicker,
    EditModalAddress: () => import(/* webpackPrefetch: true */ '@/components/Modal/EditAddressProfile'),
    ModalTaxInvoice: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalAddress/TaxInvoiceAddress'),
    CouponCart: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalCoupon/ListCoupon'),
    CodeCart: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalCoupon/ListCode'),
    SystemCodeCartShipping: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalCoupon/ListSystemCodeShipping'),
    SystemCodeCart: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalCoupon/ListSystemCode'),
    PointCart: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalCoupon/ListPoint'),
    EditModalCompanyAddress: () => import(/* webpackPrefetch: true */ '@/components/Modal/AddressCompanyModal')
    // ModalQuotation: () => import('@/components/Cart/ModalQuotation/QuotationModal')
  },
  data () {
    return {
      dialogPoint: false,
      ShowMoreDetail: false,
      allPickUp: false,
      AddressNew: [],
      invoicedetailDefault: [],
      DefaultInvoice: '',
      ShopData: [],
      showAll: false,
      showUsers: 5,
      InetRelation: [],
      CodePlatform: [],
      CodePlatformShipping: [],
      TypeOS: '',
      usePointOrNot: '',
      XBaht: '',
      PointData: '',
      CouponData: [],
      btnEstimateCost: false,
      dialogDeleteSuccessTax: false,
      radioCreditTerm: 'No',
      reShippingData: false,
      DialogCompanyAddress: false,
      comAddress: [],
      CartAddress: [],
      ShippingData: [],
      costTransport: '',
      nameTransport: '',
      radioTransport: '',
      estimateCostData: [],
      SellerShopID: '',
      DialogTransport: false,
      extbuyerrole: false,
      companydata: '',
      openEtaxAddress: false,
      deleteDialog: false,
      paymenttransactionnumber: '',
      CloseDialog: false,
      RequiredInvoice: '',
      invoiceID: '',
      EtaxType: '',
      titleTaxAddress: '',
      OldId: '',
      invoicedetail: [],
      TaxAddress: false,
      select: 1,
      disabledHours () {
        const currentHour = new Date().getHours()
        // Disable current hour and hours before it
        return Array.from({ length: currentHour + 1 }, (v, k) => k)
      },
      disabledMinutes (hour) {
        if (hour === new Date().getHours()) {
          // Disable current minute and minutes before it
          const currentMinute = new Date().getMinutes()
          return Array.from({ length: currentMinute + 1 }, (v, k) => k)
        }
        // Disable all minutes for other hours
        return []
      },
      companyAddressData: [],
      DialogTaxAddress: false,
      countproductgen: false,
      todayselect: false,
      timeselecttoday: '',
      timeselect: '',
      today: new Date().toISOString().substr(0, 10),
      futureDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().substr(0, 10),
      ImageQR: '',
      netPrice: '',
      Ref1: '',
      Ref2: '',
      radioPayment: '',
      deleteAdressData: '',
      deleteCompanyAdressData: '',
      deleteTaxAdressData: '',
      typeButton: '',
      DialogAddress: false,
      DialogQR: false,
      dialogSuccess: false,
      dialogAwaitConfirm: false,
      modalContractStartDate: false,
      modalContractEndDate: false,
      CartPage: 'CartPage',
      role: '',
      items: [
        {
          text: `${this.$t('CheckOut.HomePage')}`,
          disabled: false,
          href: '/'
        },
        {
          text: `${this.$t('CheckOut.ShoppingCart')}`,
          disabled: false,
          href: '/shoppingcart'
        },
        {
          text: `${this.$t('CheckOut.OrderList')}`,
          disabled: true,
          href: '/checkout'
        }
      ],
      dates: '',
      menu: false,
      default_address: '',
      radios: 'radio-2',
      radiosinvoice: '',
      radiostax: 'radiotax-2',
      lazy: false,
      overlay: false,
      cartData: '',
      itemsCart: [],
      Address: '',
      Fullname: '',
      EditAddressDetail: '',
      titleAddress: '',
      address_data: '',
      taxAddress: '',
      companyName: '',
      companyTaxID: '',
      checkAdminQU: true,
      pplURL: '',
      pplToken: '',
      page: '',
      taxinvoiceAddress: [],
      taxinvoiceAddressNew: [],
      googleItem: [],
      taxRoles: 'No',
      selectTypeAddress: 'Normal',
      oneDataSpecial: '',
      modalPayment: false,
      responseSentDataPPL: '',
      SelectCouponOrPoint: true,
      dialog_Cancel_Coupon: false,
      checkOwnShop: 'N',
      discountBaht: '',
      discountCode: '',
      disableButtonPay: false,
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      minDate: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      date1: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      date2: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      searchContractStartDate: '',
      setMinDateContractEndDate: '',
      contractStartDate: '',
      contractDate: '',
      searchContractEndDate: '',
      contractEndDate: '',
      ActiveDiscount: false,
      selectDiscount: '',
      discount: '',
      contractSet: false,
      reason: '',
      selectBudget: '',
      choose_list: '',
      selectTypeDoc: '',
      tax_id: '',
      itemTypeDoc: [],
      selectedPr: '',
      itemCodePrList: [],
      itemBudget: [
        { text: `${this.$t('CheckOut.OperatingBudget')}`, value: 'operating_budget' },
        { text: `${this.$t('CheckOut.investmentBudget')}`, value: 'investment_budget' },
        { text: `${this.$t('CheckOut.regularExpenditureBudget')}`, value: 'regular_expenditure_budget' }
      ],
      selectCutBudget: '',
      itemCutBudget: [
        { text: `${this.$t('CheckOut.COGS')}`, value: 'COGS' },
        { text: `${this.$t('CheckOut.SGnA')}`, value: 'SG&A' },
        { text: `${this.$t('CheckOut.RnD')}`, value: 'R&D' }
      ],
      itemsCreditTerm: [
        { value: '3', label: `3 ${this.$t('CheckOut.Month')}` },
        { value: '6', label: `6 ${this.$t('CheckOut.Month')}` },
        { value: '10', label: `10 ${this.$t('CheckOut.Month')}` }
      ],
      Name_Buyer: '',
      Phone_Buyer: '',
      Position_Buyer: '',
      Email_Buyer: '',
      Name_Audit1: '',
      Phone_Audit1: '',
      Position_Audit1: '',
      Email_Audit1: '',
      Name_Audit2: '',
      Phone_Audit2: '',
      Position_Audit2: '',
      Email_Audit2: '',
      json_personal: [],
      purchasing_cheif: [],
      inspectors_one: [],
      inspectors_two: [],
      name: '',
      position: '',
      phone: '',
      email: '',
      buyer_name: '',
      buyer_phone: '',
      buyer_email: '',
      userdetail: [],
      a: '',
      Rules: {
        Name: [
          v => !!v || `${this.$t('CheckOut.PleaseEnterYourFullName')}`
        ],
        Phone: [
          v => !!v || `${this.$t('CheckOut.PleaseEnterYourPhoneNumber')}`,
          v => /^[0-9]{10}$/.test(v) || `${this.$t('CheckOut.PleaseEnterValidPhoneNumber')}`
        ],
        Position: [
          v => !!v || `${this.$t('CheckOut.PleasEnterYourPosition')}`
        ],
        Email: [
          v => !!v || `${this.$t('CheckOut.PleaseEnterYourEmailAddress')}`,
          v => /^\w+([.-]?\w+)@[a-zA-Z]+([.-]?[a-zA-Z]+)(.[a-zA-Z]{2,3})+$/.test(v) || `${this.$t('CheckOut.PleaseEnterValidEmailAddress')}`
        ]
      },
      titleCompanyAddress: '',
      imageBase64: '',
      roleCheck: '',
      QRShop: false
    }
  },
  computed: {
    filteredItemsCart () {
      return this.itemsCart.choose_list.filter(
        (item) =>
          item.seller_shop_point_data &&
          item.seller_shop_point_data.length > 0 &&
          parseInt(item.seller_shop_point_data[0].total_point_receive) > 0
      )
    },
    hasPoints () {
      return this.itemsCart &&
      Array.isArray(this.itemsCart.choose_list) &&
      this.itemsCart.choose_list.some(item =>
        item.seller_shop_point_data &&
          item.seller_shop_point_data.length > 0 &&
          parseInt(item.seller_shop_point_data[0].total_point_receive) > 0
      )
    },
    isConfirmDisabled () {
      // console.log(this.itemsCart.choose_list)
      return (
        this.radioPayment === '' || // ถ้าการเลือกวิธีชำระเงินยังไม่ได้เลือก
        this.itemsCart.choose_list.some(
          (item) =>
            item.radios === 'radio-1' && // ตรวจสอบเฉพาะกรณี radios === 'radio-1'
            (!item.dates || // วันที่ยังไม่ได้เลือก
              !item.timeselect || // เวลา timeselect ยังไม่ได้เลือก
              !item.timeselecttoday || // เวลา timeselecttoday ยังไม่ได้เลือก
              item.timeselect === null || // เวลา timeselect เป็น null
              item.timeselecttoday === null) // เวลา timeselecttoday เป็น null
        )
      )
    },
    isPaymentDisabled () {
      return this.itemsCart.choose_list.some((item) => {
        // ตรวจสอบว่า product_list มี product_type = 'general' อย่างน้อย 1 ตัว
        const hasGeneralProduct = item.product_list.some(
          (product) => product.product_type === 'general'
        )

        // หากไม่มี product_type = 'general' ใน product_list ของ item นี้ ข้ามไป
        if (!hasGeneralProduct) {
          return false
        }

        // ตรวจสอบเงื่อนไขอื่นๆ
        return (
          (item.shipping_data && item.shipping_data.length === 0) &&
          (item.shipping_method && item.shipping_method.length !== 0) &&
          item.radios === 'radio-2'
        )
      })
    },
    firstValidChooseItem () {
      // ตรวจสอบว่า choose_list มีค่าเป็น array ก่อน
      if (Array.isArray(this.itemsCart.choose_list)) {
        return this.itemsCart.choose_list.find(chooseitem =>
          chooseitem.product_list.some(productitem => productitem.product_type === 'general')
        )
      }
      return null // คืนค่า null ถ้า choose_list ไม่ใช่ array
    },
    filteredCreditTerms () {
      return this.itemsCreditTerm.map(term => {
        const correspondingInstallment = this.itemsCart.installment_method.find(installment => installment.month === term.value)
        // เพิ่ม property displayText เพื่อแสดงราคาในตัวเลือก
        return {
          ...term,
          displayText: correspondingInstallment ? `${term.label} - ${Number(correspondingInstallment.price).toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                })} ${this.$t('CheckOut.PricePerMonth')}` : term.label
        }
      }).filter(term => {
        // กรองตามเงื่อนไขที่ต้องการ
        const correspondingInstallment = this.itemsCart.installment_method.find(installment => installment.month === term.value)
        return correspondingInstallment && correspondingInstallment.price >= 500
      })
    },
    // formattedTime (item) {
    //   if (item.dates !== this.today) {
    //     const dateObject = new Date(item.timeselect)
    //     const hours = dateObject.getHours()
    //     const minutes = dateObject.getMinutes()

    //     return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`
    //   } else {
    //     const dateObject = new Date(item.timeselecttoday)
    //     const hours = dateObject.getHours()
    //     const minutes = dateObject.getMinutes()

    //     return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`
    //   }
    // },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    headers () {
      this.itemsCart.choose_list[0].product_list.forEach(element => {
        if (element.product_type.length >= 1 && element.product_type === 'general') {
          this.countproductgen = true
        }
      })
      const headers = [
        {
          title: `${this.$t('CheckOut.ProductDetails')}`,
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '25%'
        },
        {
          title: `${this.$t('CheckOut.UnitPrice')}`,
          dataIndex: 'revenue_default',
          key: 'revenue_default',
          scopedSlots: { customRender: 'revenue_default' },
          align: 'center',
          width: '20%'
        },
        {
          title: `${this.$t('CheckOut.Quantity')}`,
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          align: 'center',
          width: '10%'
        },
        {
          title: `${this.$t('CheckOut.TotalPrice')}`,
          dataIndex: 'revenue_vat',
          scopedSlots: { customRender: 'revenue_vat' },
          key: 'revenue_vat',
          align: 'center',
          width: '15%'
        }
      ]
      return headers
    },
    headersMobile () {
      const headersMobile = [
        {
          title: `${this.$t('CheckOut.ProductDetails')}`,
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '100%'
        }
      ]
      return headersMobile
    },
    checkSelectPr () {
      const checkPr = this.itemsCart.length === 0 ? true : this.itemsCart.choose_list[0].product_list.some(v => v.item_code_pr_buyer === null)
      return checkPr
    }
  },
  async created () {
    this.$EventBus.$on('SentGetCart', this.getCart)
    // this.$EventBus.$on('EditAddressComplete', (data) => { this.getCart(data) })
    this.$EventBus.$on('selectcouponorpointCheckout', this.selectcouponorpointCheckout)
    this.$EventBus.$emit('GetTaxAddress')
    localStorage.removeItem('InetRelationShip')
    this.TypeOS = this.detectOS()
    var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    this.role = JSON.parse(localStorage.getItem('roleUser'))
    this.roleCheck = this.role.role
    this.pplToken = onedata.user.access_token
    if (Object.prototype.hasOwnProperty.call(onedata, 'cartData')) {
      if (onedata.cartData.coupon.length !== 0) {
        if (onedata.cartData.coupon === undefined || onedata.cartData.coupon[0].coupon_sort === 'cancel' || onedata.cartData.coupon.length === 0) {
          if (localStorage.getItem('ClickgoToCheckOut') !== null) {
            this.SelectCouponOrPoint = true
          }
        } else {
          localStorage.setItem('ClickgoToCheckOut', true)
          if (localStorage.getItem('ClickgoToCheckOut') !== null) {
            this.SelectCouponOrPoint = false
            if (localStorage.getItem('CouponOrPoint') === 'Point') {
              this.nameCouponOrPoint = `${this.$t('CheckOut.RedeemPoints')} ` + onedata.cartData.coupon[0].point + ` ${this.$t('CheckOut.Points')}`
            } else {
              this.nameCouponOrPoint = onedata.cartData.coupon[0].coupon_name
            }
          }
        }
      }
      this.cartData = onedata.cartData
      this.oneDataSpecial = onedata.cartDataSpecialPrice
      if (onedata.cartData.coupon.length !== 0) {
        this.CouponData = onedata.cartData.coupon.filter(coupon => coupon.seller_shop_id !== -1)
      } else {
        this.CouponData = []
      }
      this.PointData = onedata.cartData.point
      this.usePointOrNot = this.cartData.usePointOrNot
      await this.getAddress()
      this.checkAddress()
      // this.getListUserPointByUser()
      this.GetAllinvoice()
      setTimeout(() => { this.btnEstimateCost = true }, 2000)
      await this.GetPersonal()
      localStorage.removeItem('ClickgoToCheckOut')
    } else {
      this.cartData = ''
      this.$router.push('/')
    }
    // await this.GetPersonal()
    window.scrollTo(0, 0)
  },
  mounted () {
    window.addEventListener('storage', this.handleStorageChange)
    this.$EventBus.$on('EditAddressComplete', (data) => { this.getAddress(data) })
    this.$EventBus.$on('EditAddressCompanyComplete', data => {
      this.getCompanyAddress(data)
    })
    this.$EventBus.$on('SelectCouponCheckout', this.getCart)
    this.$EventBus.$on('ListCode', this.getCart)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('SelectCouponCheckout')
      this.$EventBus.$off('ListCode')
    })
  },
  beforeDestroy () {
    window.removeEventListener('storage', this.handleStorageChange)
    this.$EventBus.$off('SentGetCart')
    this.$EventBus.$off('GetTaxAddress')
    this.$EventBus.$off('EditAddressComplete')
    this.$EventBus.$off('EditAddressCompanyComplete')
  },
  watch: {
    usePointOrNot (val) {
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      onedata.cartData.usePointOrNot = val
      localStorage.setItem('oneData', Encode.encode(onedata))
    },
    '$store.state.ModuleUser.stateAddInvoice': {
      handler: async function (newValue) {
        console.log('stateAddInvoice changed', newValue)
        await this.GetAllinvoice()
      },
      deep: true
    },
    '$store.state.ModuleUser.stateupsertInvoice': {
      handler: async function (newValue) {
        console.log('stateupsertInvoice changed', newValue)
        await this.GetAllinvoice()
      },
      deep: true
    },
    selectTypeAddress (val) {
      var onedata
      if (val === 'Normal') {
        this.discountCode = ''
        this.discountBaht = ''
        onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        onedata.cartData.shipping = 'yes'
        localStorage.setItem('oneData', Encode.encode(onedata))
        this.ChangeDiscount(this.itemsCart.total_price_no_vat)
      } else {
        onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        onedata.cartData.shipping = 'no'
        localStorage.setItem('oneData', Encode.encode(onedata))
        this.getCart()
      }
    },
    taxRoles (val) {
      if (val === 'Personal' || val === 'Business') {
        this.taxAddress = ''
      }
    },
    discountCode (val) {
      if (val === '') {
        this.discountBaht = ''
        this.ChangeDiscount(this.itemsCart.total_price_no_vat)
      }
    }
  },
  methods: {
    openDialogPoint () {
      this.dialogPoint = true
    },
    formatPrice (value) {
      const num = Number(value)
      return num < 0
        ? '0.00'
        : num.toLocaleString(undefined, {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })
    },
    formattedTime (item) {
      if (item.dates !== this.today) {
        const dateObject = new Date(item.timeselect)
        const hours = dateObject.getHours()
        const minutes = dateObject.getMinutes()
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`
      } else {
        const dateObject = new Date(item.timeselecttoday)
        const hours = dateObject.getHours()
        const minutes = dateObject.getMinutes()
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`
      }
    },
    updateTime () {
      this.radioPayment = ''
      this.$forceUpdate()
    },
    openListTaxAddress (item) {
      // console.log('item', item)
      this.invoicedetail.forEach(element => {
        if (element.default_invoice === 'Y') {
          this.invoiceID = element.id
        }
      })
      this.DefaultInvoice = ''
      this.DialogTaxAddress = !this.DialogTaxAddress
      this.ShopData = item
      if (this.ShopData.invoice_id === '') {
        this.ShopData.invoice_id = this.invoiceID
      }
    },
    getCouponId () {
      const foundCoupon = this.CouponData.find(coupon => coupon.seller_shop_id !== -1)
      return foundCoupon ? foundCoupon.coupon_id : ''
    },
    async handleStorageChange (event) {
      if (event.key === 'oneData') {
        var NewValue = JSON.parse(Decode.decode(event.newValue))
        var oldValue = JSON.parse(Decode.decode(event.oldValue))
        if (NewValue.cartData !== oldValue.cartData) {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 4500,
            timerProgressBar: true,
            icon: 'warning',
            text: this.$t('CheckOut.TheCartHasBeenUpdated')
          })
          this.backstep()
        }
      }
    },
    HideUsers () {
      this.showAll = false
      this.showUsers = 5
    },
    ShowUsers () {
      this.showAll = true
      this.showUsers = this.InetRelation.length
    },
    clearUser () {
      this.InetRelation = []
      localStorage.removeItem('InetRelationShip')
      this.$EventBus.$emit('ListCode')
    },
    clearCodePlatform () {
      this.CodePlatform = []
      this.CouponData = this.CouponData.filter(coupon => coupon.seller_shop_id !== -1)
      // localStorage.removeItem('CouponPlatform')
      // this.$EventBus.$emit('ListCode')
      this.$EventBus.$emit('clearCouponPlatform')
    },
    clearCodePlatformShipping () {
      this.CodePlatformShipping = []
      this.CouponData = this.CouponData.filter(coupon => coupon.seller_shop_id !== -1 || coupon.coupon_type !== 'free_shipping')
      // localStorage.removeItem('CouponPlatform')
      // this.$EventBus.$emit('ListCode')
      this.$EventBus.$emit('clearCouponPlatformShipping')
    },
    clickCode () {
      this.page = 'checkout'
      // this.$EventBus.$emit('submitStart')
      this.$refs.CodeCart.open()
    },
    clickSystemCode () {
      this.page = 'checkout'
      // this.$EventBus.$emit('submitStart')
      var priceCheckCoupon = 0
      if (parseInt(this.itemsCart.total_include_vat) === 0) {
        priceCheckCoupon = parseFloat(this.itemsCart.total_price_no_vat) - parseFloat(this.itemsCart.choose_list[0].total_point)
      } else {
        priceCheckCoupon = parseFloat(this.itemsCart.total_price_vat) + parseFloat(this.itemsCart.total_coupon_discount)
      }
      this.$refs.SystemCodeCart.open('', this.CodePlatform, '', '', priceCheckCoupon)
      this.$EventBus.$emit('getListPlatformCoupon')
    },
    clickSystemCodeShipping () {
      this.page = 'checkout'
      // this.$EventBus.$emit('submitStart')
      var priceCheckCoupon = 0
      if (parseInt(this.itemsCart.total_include_vat) === 0) {
        priceCheckCoupon = parseFloat(this.itemsCart.total_price_no_vat) - parseFloat(this.itemsCart.choose_list[0].total_point)
      } else {
        priceCheckCoupon = parseFloat(this.itemsCart.total_price_vat) + parseFloat(this.itemsCart.total_coupon_discount)
      }
      this.$refs.SystemCodeCartShipping.open('', this.CodePlatformShipping, '', '', priceCheckCoupon, this.radios)
      this.$EventBus.$emit('getListPlatformCouponShipping')
    },
    detectOS () {
      const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera
      if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        return 'iOS'
      }
      if (/android/i.test(userAgent)) {
        return 'Android'
      }
      return 'PC'
    },
    // async getListUserPointByUser () {
    //   this.XBaht = []
    //   var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    //   var RoleUser = JSON.parse(localStorage.getItem('roleUser'))
    //   var PartnerID = JSON.parse(localStorage.getItem('partner_id'))
    //   var data = {
    //     role_user: RoleUser.role,
    //     customer_id: RoleUser.role === 'sale_order_no_JV' ? PartnerID : -1,
    //     seller_shop_id: this.SellerShopID,
    //     company_id: onedata.cartData.company_id,
    //     com_perm_id: onedata.cartData.com_perm_id
    //   }
    //   await this.$store.dispatch('actionsgetDetailUserPointByUser', data)
    //   var res = await this.$store.state.ModuleManagePoint.stategetDetailUserPointByUser
    //   if (res.result === 'SUCCESS') {
    //     this.XBaht = res.data[0].x_baht
    //     this.XBaht = parseFloat(res.data[0].x_baht) / parseFloat(res.data[0].x_point)
    //     // console.log('this.XBaht', this.XBaht)
    //   } else if (res.message === 'This user is unauthorized.') {
    //     this.$store.commit('closeLoader')
    //     this.$EventBus.$emit('refreshToken')
    //   } else {
    //     this.$swal.fire({
    //       showConfirmButton: false,
    //       timer: 2500,
    //       timerProgressBar: true,
    //       icon: 'warning',
    //       html: '<h3>แต้มขัดข้อง กรุณาลองใหม่ภายหลัง</h3>'
    //     })
    //   }
    // },
    closePoint (item) {
      this.PointData = this.PointData.filter(point => point.seller_shop_id !== item.seller_shop_id)
      this.getCart(this.CouponData, this.PointData)
      this.$EventBus.$emit('clearPoint', item)
    },
    closeCoupon (item) {
      this.PointData = this.PointData.filter(point => point.seller_shop_id !== item.seller_shop_id)
      this.usePointOrNot = 'no'
      this.CouponData = this.CouponData.filter(coupon => coupon.seller_shop_id !== item.seller_shop_id)
      this.getCart(this.CouponData, this.PointData)
      this.$EventBus.$emit('clearCoupon', item)
    },
    clickCoupon (item, point, baht, data) {
      this.page = 'checkout'
      this.closePoint(data)
      this.usePointOrNot = 'no'
      this.$refs.CouponCart.open(this.page, item, point, baht, data.radios, data)
      // this.$EventBus.$emit('getListCoupon')
    },
    clickPoint (item, point, baht, discoupon, data) {
      // console.log('discoupon', discoupon)
      this.page = 'checkout'
      this.$refs.PointCart.open(this.page, item, point, baht, discoupon, data)
      this.$EventBus.$emit('getListPoint')
    },
    async GetReviewQuotation (item) {
      this.$store.commit('openLoader')
      if ((this.itemsCart.isEtax === 'yes' || this.itemsCart.isEtax === 'no') && this.userdetail.length === 0) {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: `<h3>${this.$t('CheckOut.PleaseEnterTheShippingAddress')}<br>${this.$t('CheckOut.ToViewsampleQuotation')}</h3>`
        })
        this.DialogAddress = true
      } else if (this.itemsCart.isEtax === 'yes' && this.invoicedetail.length === 0) {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: `<h3>${this.$t('CheckOut.PleaseEnterTheAddressForTheTaxTnvoice')}<br>${this.$t('CheckOut.ToViewSampleQuotation')}</h3>`
        })
        this.DialogTaxAddress = true
      } else {
        if (this.itemsCart.isEtax === 'yes') {
          this.invoicedetail.forEach(element => {
            if (element.default_invoice === 'Y') {
              this.invoiceID = element.id
            }
          })
        } else {
          this.invoiceID = ''
        }
        var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        const data = {
          invoice_id: this.itemsCart.isEtax === 'yes' ? item.invoice_id : '',
          role_user: this.role.role,
          company_id: '-1',
          com_perm_id: '-1',
          seller_shop_id: item.seller_shop_id,
          token: onedata.user.access_token,
          customer_id: '-1',
          product_free: JSON.stringify(item.product_free)
        }
        await this.$store.dispatch('actionsGetReviewQuotation', data)
        var res = await this.$store.state.ModuleCart.stateGetReviewQuotation
        if (res.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          window.open(res.data.pdf_path, '_blank')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            html: `<h3>${this.$t('CheckOut.UnavailableSampleQuotation')}</h3>`
          })
        }
      }
    },
    async GoToOrderCompany () {
      const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      var companyid = companyId.company.company_id
      const data = {
        company_id: companyid
      }
      await this.$store.dispatch('actionsDetailCompany', data)
      await this.$store.dispatch('actionsAuthorityUser')
      var responsecompany = await this.$store.state.ModuleAdminManage.stateDetailCompany
      var responseposition = await this.$store.state.ModuleUser.stateAuthorityUser
      var listcompany = responseposition.data.list_company
      for (let i = 0; i < listcompany.length; i++) {
        if (responsecompany.data.id === listcompany[i].company_id) {
          localStorage.removeItem('list_Company_detail')
          localStorage.setItem('list_Company_detail', Encode.encode(listcompany[i]))
        }
      }
      localStorage.setItem('CompanyData', Encode.encode(responsecompany.data))
      if (this.MobileSize) {
        this.$router.push({ path: '/orderCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/orderCompany' }).catch(() => {})
      }
    },
    ClearRadio (item) {
      this.ShippingData = this.ShippingData.filter(data => data.seller_shop_id !== item.seller_shop_id)
      // this.radioCreditTerm = 'No'
      // this.radioTransport = ''
      // this.radioPayment = ''
      this.getCart(this.CouponData, this.PointData, item)
    },
    setRadioCreditTermNo () {
      this.radioCreditTerm = 'No'
    },
    DeleteCompanyAddress (item) {
      this.dialogAwaitConfirm = true
      // console.log(item)
      this.deleteCompanyAdressData = item
      this.typeButton = 'deleteCompany'
    },
    async deleteCompanyAddress () {
      const data = {
        id: this.deleteCompanyAdressData.id,
        company_id: this.deleteCompanyAdressData.company_id
      }
      await this.$store.dispatch('actionsDeleteCompanyAddress', data)
      const res = await this.$store.state.ModuleCart.stateDeleteCompanyAddress
      if (res.ok === 'y') {
        // this.$swal.fire({ icon: 'success', title: 'ลบที่อยู่ในการจัดส่งสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.getCompanyAddress()
        this.deleteDialog = false
      } else {
        this.$swal.fire({ icon: 'error', title: `${this.$t('CheckOut.FailedToDeleteTheShippingAddress')}`, showConfirmButton: false, timer: 1500 })
      }
      this.dialogAwaitConfirm = false
      this.dialogSuccess = true
    },
    async setDefaultCompanyAddress (item) {
      const data = {
        id: item.id,
        company_id: item.company_id
      }
      await this.$store.dispatch('actionsSetDefaultCompanyAddress', data)
      var res = await this.$store.state.ModuleCart.stateSetDefaultCompanyAddress
      if (res.ok === 'y') {
        this.$swal.fire({ icon: 'success', title: `<h5>${this.$t('CheckOut.SetAsDefaultAddressSuccessfully')}</h5>`, showConfirmButton: false, timer: 1500 })
        // this.comAddress = [...res.data]
      } else {
        this.$swal.fire({ icon: 'error', title: `<h5>${this.$t('CheckOut.FailedToSetAsDefaultAddress')}</5>`, showConfirmButton: false, timer: 1500 })
      }
      this.reShippingData = true
      this.getCompanyAddress()
    },
    addCompanyAddress () {
      const val = {
        building_name: '',
        default: '',
        detail: '',
        district: '',
        email: '',
        floor: '',
        house_no: '',
        id: '',
        name_th: this.comAddress[0].name_th,
        moo_ban: '',
        moo_no: '',
        order_number: '',
        phone: '',
        province: '',
        room_no: '',
        soi: '',
        status: '',
        street: '',
        sub_district: '',
        company_id: this.comAddress[0].company_id,
        yaek: '',
        zipcode: ''
      }
      this.reShippingData = true
      localStorage.setItem('AddressCompanyDetail', Encode.encode(val))
      this.EditAddressDetail = val
      this.titleCompanyAddress = this.$t('CheckOut.AddShippingAddress')
      this.page = 'checkout'
      this.$EventBus.$emit('EditModalCompanyAddress')
    },
    editCompanyAddress (val) {
      this.$store.commit('openLoader')
      const editData = {
        building_name: val.building_name,
        default: val.default,
        detail: val.detail,
        district: val.district,
        email: val.email,
        floor: val.floor,
        house_no: val.house_no,
        id: val.id,
        name_th: val.name_th,
        moo_ban: val.moo_ban,
        moo_no: val.moo_no,
        order_number: val.order_number,
        phone: val.phone,
        province: val.province,
        room_no: val.room_no,
        soi: val.soi,
        status: val.status,
        street: val.street,
        sub_district: val.sub_district,
        company_id: val.company_id,
        yaek: val.yaek,
        zipcode: val.zip_code
      }
      // console.log(editData.default_address)
      if (editData.default === 'Y') {
        this.reShippingData = true
      }
      localStorage.setItem('AddressCompanyDetail', Encode.encode(editData))
      this.EditAddressDetail = editData
      this.titleCompanyAddress = this.$t('CheckOut.EditShippingAddress')
      this.page = 'checkout'
      this.$EventBus.$emit('EditModalCompanyAddress')
      this.getCompanyAddress()
    },
    async getCompanyAddress () {
      this.$store.commit('openLoader')
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyId = ''
      if (dataRole.role !== 'ext_buyer') {
        if (localStorage.getItem('SetRowCompany') !== null) {
          var companyDataID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
          companyId = companyDataID.company.company_id
        } else {
          companyId = -1
        }
      }
      var data = {
        company_id: companyId
      }
      await this.$store.dispatch('actionsGetCompanyAddress', data)
      var res = await this.$store.state.ModuleCart.stateGetCompanyAddress
      if (res.ok === 'y') {
        // console.log(res.query_result)
        this.$store.commit('closeLoader')
        this.comAddress = res.query_result
      } else {
        this.$store.commit('closeLoader')
      }
    },
    selectTransport (item) {
      this.radioTransport = item.tpl_name
      if (this.radioTransport === item.tpl_name) {
        this.nameTransport = item.tpl_name
        this.costTransport = item.netEstimatePrice
        var shippingData = item
        const index = this.ShippingData.findIndex(existingShippingData => existingShippingData.seller_shop_id === shippingData.seller_shop_id)
        if (index !== -1) {
          this.ShippingData.splice(index, 1)
        }
        this.ShippingData.push(shippingData)
        this.DialogTransport = false
        this.getCart(this.CouponData, this.PointData)
      }
    },
    async EstimateCost (item) {
      this.$store.commit('openLoader')
      // console.log(123456)
      this.radioTransport = ''
      if (item.radios === 'radio-2' && (this.userdetail.length === 0 || (this.userdetail.length === 1 && ((this.userdetail[0].detail === '' && this.userdetail[0].detail === null) || this.userdetail[0].default_address !== 'Y')))) {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: `<h3>${this.$t('CheckOut.PleaseEnterTheShippingAddress')}</h3>`
        })
        if (this.userdetail.length === 1 && (this.userdetail[0].detail === '' && this.userdetail[0].detail === null)) {
          this.editAddress(this.userdetail[0])
        } else if (this.userdetail.length === 1 && (this.userdetail[0].default_address !== 'Y')) {
          this.DialogAddress = true
        } else if (this.userdetail.length === 0) {
          this.DialogAddress = true
        }
      } else {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        // console.log(companyId)
        var conditionMet = false
        if (this.CartAddress[0].detail === '' || this.CartAddress[0].detail === null) {
          this.DialogTransport = false
          // console.log(6666)
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'warning',
            html: `<h3>${this.$t('CheckOut.SelectAddressFirst')}</h3>`
          })
          this.DialogAddress = true
          conditionMet = true
        }
        if (conditionMet) {
          this.$store.commit('closeLoader')
          return
        }
        var data = {
          address: this.CartAddress,
          role_user: dataRole.role,
          company_id: '-1',
          com_perm_id: '-1',
          seller_shop_id: item.seller_shop_id
        }
        // console.log('data', data)
        await this.$store.dispatch('actionsEstimateCost', data)
        var res = await this.$store.state.ModuleCart.stateEstimateCost
        if (res.result === 'Success') {
          this.DialogTransport = true
          this.radioTransport = item.shipping_data.tpl_name !== undefined ? item.shipping_data.tpl_name : ''
          this.estimateCostData = res.data
          this.$store.commit('closeLoader')
          if (this.estimateCostData.length === 0 && this.itemsCart.shipping_method !== 0) {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'error',
              html: '<h3>ไม่มีขนส่งรองรับ</h3>'
            })
            this.backstep()
          }
        } else if (res.code === 400) {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            html: `<h4>${res.message}</h4>`
          })
          this.backstep()
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            html: `<h3>${this.$t('CheckOut.SystemErrorHasOccurredPleaseContactSupport')}</h3>`
          })
          this.DialogTransport = false
        }
      }
    },
    saveQRCode () {
      const image = document.getElementById('qrcode')
      // console.log(image)
      const link = document.createElement('a')
      link.href = image.src
      link.download = 'image.png'

      // Simulate a click on the link
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    checkIsEtax () {
      if (this.itemsCart.isEtax === 'yes' && this.invoicedetail.length === 0) {
        this.radiostax = 'radiotax-1'
        this.DialogTaxAddress = true
      }
    },
    closeDialogTax () {
      this.radiostax = 'radiotax-1'
    },
    openModalEditTaxAddress (item) {
      const editData = {
        id: item.id,
        user_id: item.user_id,
        branch_id: item.branch_id,
        company_id: item.company_id,
        tax_type: item.tax_type,
        tax_id: item.tax_id,
        buyer_one_id: item.buyer_one_id,
        name: item.name,
        email: item.email,
        address: item.address,
        postal_code: item.postal_code,
        province: item.province,
        district: item.district,
        sub_district: item.sub_district,
        role: item.role,
        default_invoice: item.default_invoice
      }
      // console.log(editData)
      localStorage.setItem('InvoiceAddress', Encode.encode(editData))
      this.titleTaxAddress = this.$t('CheckOut.EditTaxInvoiceAddress')
      this.page = 'checkout'
      this.$refs.ModalTaxAddress.open()
    },
    openModalTaxAddress () {
      this.titleTaxAddress = this.$t('CheckOut.AddTaxInvoiceAddress')
      this.page = 'checkout'
      this.$refs.ModalTaxAddress.open()
    },
    async GetAllinvoice () {
      this.$store.commit('openLoader')
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var companyId = ''
      if (dataRole.role !== 'ext_buyer') {
        if (localStorage.getItem('SetRowCompany') !== null) {
          var companyDataID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
          companyId = companyDataID.company.company_id
        } else {
          companyId = -1
        }
      }
      if (dataRole.role !== 'ext_buyer') {
        var data = {
          user_id: onedata.user.user_id,
          company_id: companyId
        }
      } else {
        data = {
          user_id: onedata.user.user_id,
          company_id: companyId
        }
      }
      await this.$store.dispatch('actionsGetAllInvoiceAddress', data)
      var res = await this.$store.state.ModuleUser.stateGetAllInvoiceAddress
      if (res.message === 'Success') {
        this.invoicedetail = res.data
        this.invoicedetailDefault = this.invoicedetail.filter(element => element.default_invoice === 'Y')
        // console.log('test', this.invoicedetailDefault)
        this.invoicedetail.forEach(element => {
          if (element.default_invoice === 'Y') {
            this.OldId = element.id
          }
        })
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3500,
          timerProgressBar: true,
          icon: 'error',
          text: this.$t('CheckOut.SystemErrorHasOccurredPleaseContactSupport')
        })
        this.backstep()
      }
    },
    async setDefaultInvoice (item) {
      this.ShopData.invoice_id = item.id
      this.ShopData.tax_type = item.tax_type
      this.itemsCart.choose_list = this.itemsCart.choose_list.map(listItem => {
        if (listItem.seller_shop_id === this.ShopData.seller_shop_id) {
          return { ...listItem, ...this.ShopData }
        }
        return listItem
      })
      localStorage.setItem('ChooseListData', Encode.encode(this.itemsCart.choose_list))
      // localStorage.removeItem('ChooseListData')
      this.DialogTaxAddress = false
      this.$swal.fire({ icon: 'success', title: `<h5>${this.$t('CheckOut.SetAsDefaultAddressSuccessfully')}</h5>`, showConfirmButton: false, timer: 1500 })
      // console.log('this.itemsCart', this.itemsCart)
      // if (this.OldId === '') {
      //   this.OldId = 0
      // }
      // if (this.Old === item.id) {
      //   this.OldId = 0
      // }
      // var data
      // if (item.role === 'purchaser') {
      //   data = {
      //     old_id: this.OldId,
      //     new_id: item.id,
      //     company_id: item.company_id
      //   }
      // } else {
      //   data = {
      //     old_id: this.OldId,
      //     new_id: item.id
      //   }
      // }
      // await this.$store.dispatch('actionsSetDefaultInvoice', data)
      // var res = await this.$store.state.ModuleUser.stateSetDefaultInvoice
      // if (res.result === 'SUCCESS') {
      // } else {
      //   this.$swal.fire({ icon: 'error', title: '<h5>ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ</5>', showConfirmButton: false, timer: 1500 })
      // }
      this.GetAllinvoice()
    },
    openDeleteInvoice (item) {
      this.deleteDialog = true
      this.deleteTaxAdressData = item
    },
    async DeleteInvoice () {
      var data
      if (this.deleteTaxAdressData.role === 'purchaser') {
        data = {
          invoice_id: this.deleteTaxAdressData.id,
          company_id: this.deleteTaxAdressData.company_id
        }
      } else {
        data = {
          invoice_id: this.deleteTaxAdressData.id
        }
      }
      await this.$store.dispatch('actionsDeleteInvoice', data)
      var res = await this.$store.state.ModuleUser.stateDeleteInvoice
      if (res.result === 'SUCCESS') {
        this.$swal.fire({ icon: 'success', title: `${this.$t('CheckOut.DeleteAddressSuccessfully')}`, showConfirmButton: false, timer: 1500 })
        this.dialogDeleteSuccessTax = true
        this.GetAllinvoice()
        this.$store.commit('openLoader')
        setTimeout(() => { this.$store.commit('closeLoader') }, 2500)
      }
      this.deleteDialog = false
    },
    async setDefaultAdress (item) {
      const data = {
        id: item.id,
        default_address: item.default_address
      }
      await this.$store.dispatch('actionDefaultUserAddress', data)
      var res = await this.$store.state.ModuleUser.stateSetDefaultUserAddress
      if (res.message === 'Update default address success') {
        this.$swal.fire({ icon: 'success', title: `<h5>${this.$t('CheckOut.SetAsDefaultAddressSuccessfully')}</h5>`, showConfirmButton: false, timer: 1500 })
        this.ShippingData = []
        this.userdetail = [...res.data]
      } else {
        this.$swal.fire({ icon: 'error', title: `<h5>${this.$t('CheckOut.FailedToSetAsDefaultAddress')}</5>`, showConfirmButton: false, timer: 1500 })
      }
      this.reShippingData = true
      this.getAddress()
    },
    addAddress () {
      const val = {
        building_name: '',
        default_address: '',
        detail: '',
        district: '',
        email: '',
        first_name: '',
        floor: '',
        house_no: '',
        id: '',
        last_name: '',
        moo_ban: '',
        moo_no: '',
        order_number: '',
        phone: '',
        province: '',
        room_no: '',
        soi: '',
        status: '',
        street: '',
        sub_district: '',
        user_id: '',
        yaek: '',
        zipcode: ''
      }
      this.reShippingData = true
      localStorage.setItem('AddressUserDetail', Encode.encode(val))
      this.EditAddressDetail = val
      this.titleAddress = this.$t('CheckOut.AddShippingAddress')
      this.page = 'checkout'
      this.$EventBus.$emit('EditModalAddress')
    },
    editAddress (val) {
      this.$store.commit('openLoader')
      const editData = {
        building_name: val.building_name,
        default_address: val.default_address,
        detail: val.detail,
        district: val.district,
        email: val.email,
        first_name: val.first_name,
        floor: val.floor,
        house_no: val.house_no,
        id: val.id,
        last_name: val.last_name,
        moo_ban: val.moo_ban,
        moo_no: val.moo_no,
        order_number: val.order_number,
        phone: val.phone,
        province: val.province,
        room_no: val.room_no,
        soi: val.soi,
        status: val.status,
        street: val.street,
        sub_district: val.sub_district,
        user_id: val.user_id,
        yaek: val.yaek,
        zipcode: val.zip_code,
        note_address: val.note_address
      }
      // console.log(editData.default_address)
      if (editData.default_address === 'Y') {
        this.reShippingData = true
      }
      localStorage.setItem('AddressUserDetail', Encode.encode(editData))
      this.EditAddressDetail = editData
      this.titleAddress = this.$t('CheckOut.EditShippingAddress')
      this.page = 'checkout'
      this.$EventBus.$emit('EditModalAddress')
    },
    openDialogQR () {
      this.dialogAwaitConfirm = !this.dialogAwaitConfirm
      this.DialogQR = !this.DialogQR
      // console.log(this.dates)
      // console.log(this.formattedTime)
    },
    async cancelChangeAddrss () {
      if (this.role.role === 'ext_buyer') {
        this.DialogAddress = false
      } else {
        this.DialogCompanyAddress = false
      }
    },
    async changeAddress () {
      if (this.role.role === 'ext_buyer') {
        this.DialogAddress = true
      } else {
        this.DialogCompanyAddress = true
      }
    },
    async changeAddressTax () {
      this.DialogTaxAddress = true
    },
    closeModalSuccess () {
      this.dialogSuccess = false
    },
    async goToQUCompany () {
      const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      var companyid = companyId.company.company_id
      const data = {
        company_id: companyid
      }
      await this.$store.dispatch('actionsDetailCompany', data)
      await this.$store.dispatch('actionsAuthorityUser')
      var responsecompany = await this.$store.state.ModuleAdminManage.stateDetailCompany
      var responseposition = await this.$store.state.ModuleUser.stateAuthorityUser
      var listcompany = responseposition.data.list_company
      for (let i = 0; i < listcompany.length; i++) {
        if (responsecompany.data.id === listcompany[i].company_id) {
          localStorage.removeItem('list_Company_detail')
          localStorage.setItem('list_Company_detail', Encode.encode(listcompany[i]))
        }
      }
      localStorage.setItem('CompanyData', Encode.encode(responsecompany.data))
      if (this.MobileSize) {
        this.$router.push({ path: '/QUCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/QUCompany' }).catch(() => {})
      }
    },
    async Confirm () {
      this.ShowMoreDetail = false
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var dataToCheck = ''
      dataToCheck = {
        role_user: dataRole.role
      }
      var messageCheckError = ''
      var i
      this.countproductgen = this.itemsCart.choose_list.some(chooseItem =>
        chooseItem.product_list.some(element => element.product_type === 'general')
      )
      var hasRadio2 = this.itemsCart.choose_list.some(chooseItem => chooseItem.radios === 'radio-2')
      if (this.itemsCart.net_price >= 1) {
        if (this.countproductgen) {
          if (hasRadio2) {
            var shouldStop = false
            for (const item of this.itemsCart.choose_list) {
              // ตรวจสอบว่ามี product_type = 'general' ใน product_list อย่างน้อย 1 ตัว
              const hasGeneralProduct = item.product_list.some(product => product.product_type === 'general')
              // ถ้าไม่มีสินค้า product_type = 'general' ให้ข้ามรายการนี้
              if (!hasGeneralProduct) {
                continue
              }
              // เช็คว่า shipping_method.length ไม่เท่ากับ 0
              if (item.shipping_method.length !== 0 && item.radios !== 'radio-1') {
                // เช็คว่า shipping_data.length เท่ากับ 0
                if (item.shipping_data.length === 0) {
                  this.$swal.fire({
                    showConfirmButton: false,
                    timer: 3500,
                    timerProgressBar: true,
                    icon: 'warning',
                    html: `<h3>${this.$t('CheckOut.PleaseSelectShippingMethodStoreDelivery')} <b>${item.seller_shop_name}</b></h3>`
                  })
                  this.EstimateCost(item) // เรียกฟังก์ชัน EstimateCost พร้อมส่ง item
                  shouldStop = true
                  break // หยุดการวนลูปทันที
                }
              } else {
                if (item.radios !== 'radio-1') {
                  if (this.CartAddress[0].detail === '' || this.CartAddress[0].detail === null) {
                    this.dialogAwaitConfirm = false
                    this.$swal.fire({
                      showConfirmButton: false,
                      timer: 5000,
                      timerProgressBar: true,
                      icon: 'warning',
                      html: `<h3>${this.$t('CheckOut.PleaseEnterTheShippingAddress')}</h3>`
                    })
                    this.DialogAddress = true
                    shouldStop = true
                    break
                  }
                }
              }
            }
            if (shouldStop) return
            const hasRadiotax1 = this.itemsCart.choose_list.some(item => item.radiostax === 'radiotax-1')
            // const hasRadiotax2 = this.itemsCart.choose_list.some(item => item.radiostax === 'radiotax-2')
            if (hasRadiotax1) {
              // เช็คว่า this.invoicedetail.length เท่ากับ 0
              if (this.invoicedetail.length === 0) {
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 2500,
                  timerProgressBar: true,
                  icon: 'warning',
                  html: `<h3>${this.$t('CheckOut.NoTaxInvoiceAddressAvailable')}</h3>`
                })
                this.DialogTaxAddress = true
              } else {
                await this.$store.dispatch('actionsCheckStockBeforeCreateOrder', dataToCheck)
                const response = await this.$store.state.ModuleCart.stateCheckStockBeforeCreateOrder
                if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
                  this.dialogAwaitConfirm = true
                  this.typeButton = 'confirm'
                } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
                  for (i = 0; i < response.data.product_free.length; i++) {
                    messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
                  }
                  this.$swal.fire({
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    icon: 'warning',
                    text: `${this.$t('CheckOut.SomeItemsAreNotEligibleForTheFreeGiftDueToInsufficientQuantity')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveTheseItemsFromYourCart')}`
                  })
                  this.backstep()
                } else if (response.message === 'ไม่พบตะกร้า') {
                  this.$swal.fire({
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    icon: 'warning',
                    text: this.$t('CheckOut.ShoppingCartNotFound')
                  })
                  this.backstep()
                } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
                  for (i = 0; i < response.data.product_list.length; i++) {
                    messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                  }
                  this.$swal.fire({
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    icon: 'warning',
                    text: `${this.$t('CheckOut.SomeItemsAreNoLongerAvailableInTheSystem')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveThemFromYourCart')}`
                  })
                  this.backstep()
                } else {
                  for (i = 0; i < response.data.product_list.length; i++) {
                    messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                  }
                  this.$swal.fire({
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    icon: 'warning',
                    text: `${this.$t('CheckOut.SomeItemsAreNotAvailableInSufficientQuantityForPurchase')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveTheseItemsFromYourCart')}`
                  })
                  this.backstep()
                }
              }
            } else {
              await this.$store.dispatch('actionsCheckStockBeforeCreateOrder', dataToCheck)
              const response = await this.$store.state.ModuleCart.stateCheckStockBeforeCreateOrder
              if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
                this.dialogAwaitConfirm = true
                this.typeButton = 'confirm'
              } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
                for (i = 0; i < response.data.product_free.length; i++) {
                  messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: `${this.$t('CheckOut.SomeItemsAreNotEligibleForTheFreeGiftDueToInsufficientQuantity')} ` + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
                })
                this.backstep()
              } else if (response.message === 'ไม่พบตะกร้า') {
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: this.$t('CheckOut.ShoppingCartNotFound')
                })
                this.backstep()
              } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
                for (i = 0; i < response.data.product_list.length; i++) {
                  messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: `${this.$t('CheckOut.SomeItemsAreNoLongerAvailableInTheSystem')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveThemFromYourCart')}`
                })
                this.backstep()
              } else {
                for (i = 0; i < response.data.product_list.length; i++) {
                  messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: `${this.$t('CheckOut.SomeItemsAreNotAvailableInSufficientQuantityForPurchase')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveTheseItemsFromYourCart')}`
                })
                this.backstep()
              }
            }
          } else {
            const hasRadiotax1 = this.itemsCart.choose_list.some(item => item.radiostax === 'radiotax-1')
            // const hasRadiotax2 = this.itemsCart.choose_list.some(item => item.radiostax === 'radiotax-2')
            if (hasRadiotax1) {
              // เช็คว่า this.invoicedetail.length เท่ากับ 0
              if (this.invoicedetail.length === 0) {
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 2500,
                  timerProgressBar: true,
                  icon: 'warning',
                  html: `<h3>${this.$t('CheckOut.NoTaxInvoiceAddressAvailable')}</h3>`
                })
                this.DialogTaxAddress = true
              } else {
                await this.$store.dispatch('actionsCheckStockBeforeCreateOrder', dataToCheck)
                const response = await this.$store.state.ModuleCart.stateCheckStockBeforeCreateOrder
                if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
                  this.dialogAwaitConfirm = true
                  this.typeButton = 'confirm'
                } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
                  for (i = 0; i < response.data.product_free.length; i++) {
                    messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
                  }
                  this.$swal.fire({
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    icon: 'warning',
                    text: `${this.$t('CheckOut.SomeItemsAreNotEligibleForTheFreeGiftDueToInsufficientQuantity')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveTheseItemsFromYourCart')}`
                  })
                  this.backstep()
                } else if (response.message === 'ไม่พบตะกร้า') {
                  this.$swal.fire({
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    icon: 'warning',
                    text: this.$t('CheckOut.ShoppingCartNotFound')
                  })
                  this.backstep()
                } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
                  for (i = 0; i < response.data.product_list.length; i++) {
                    messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                  }
                  this.$swal.fire({
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    icon: 'warning',
                    text: `${this.$t('CheckOut.SomeItemsAreNoLongerAvailableInTheSystem')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveThemFromYourCart')}`
                  })
                  this.backstep()
                } else {
                  for (i = 0; i < response.data.product_list.length; i++) {
                    messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                  }
                  this.$swal.fire({
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    icon: 'warning',
                    text: `${this.$t('CheckOut.SomeItemsAreNotAvailableInSufficientQuantityForPurchase')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveTheseItemsFromYourCart')}`
                  })
                  this.backstep()
                }
              }
            } else {
              await this.$store.dispatch('actionsCheckStockBeforeCreateOrder', dataToCheck)
              const response = await this.$store.state.ModuleCart.stateCheckStockBeforeCreateOrder
              if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
                this.dialogAwaitConfirm = true
                this.typeButton = 'confirm'
              } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
                for (i = 0; i < response.data.product_free.length; i++) {
                  messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: `${this.$t('CheckOut.SomeItemsAreNotEligibleForTheFreeGiftDueToInsufficientQuantity')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveTheseItemsFromYourCart')}`
                })
                this.backstep()
              } else if (response.message === 'ไม่พบตะกร้า') {
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: this.$t('CheckOut.ShoppingCartNotFound')
                })
                this.backstep()
              } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
                for (i = 0; i < response.data.product_list.length; i++) {
                  messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: `${this.$t('CheckOut.SomeItemsAreNoLongerAvailableInTheSystem')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveThemFromYourCart')}`
                })
                this.backstep()
              } else {
                for (i = 0; i < response.data.product_list.length; i++) {
                  messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: `${this.$t('CheckOut.SomeItemsAreNotAvailableInSufficientQuantityForPurchase')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveTheseItemsFromYourCart')}`
                })
                this.backstep()
              }
            }
          }
        } else {
          const hasRadiotax1 = this.itemsCart.choose_list.some(item => item.radiostax === 'radiotax-1')
          if (hasRadiotax1) {
            if (this.invoicedetail.length === 0) {
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'warning',
                html: '<h3>ไม่มีที่อยู่ในการออกใบกำกับภาษี</h3>'
              })
              this.DialogTaxAddress = true
            } else {
              await this.$store.dispatch('actionsCheckStockBeforeCreateOrder', dataToCheck)
              const response = await this.$store.state.ModuleCart.stateCheckStockBeforeCreateOrder
              if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
                this.dialogAwaitConfirm = true
                this.typeButton = 'confirm'
              } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
                for (i = 0; i < response.data.product_free.length; i++) {
                  messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: `${this.$t('CheckOut.SomeItemsAreNotEligibleForTheFreeGiftDueToInsufficientQuantity')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveTheseItemsFromYourCart')}`
                })
                this.backstep()
              } else if (response.message === 'ไม่พบตะกร้า') {
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: this.$t('CheckOut.ShoppingCartNotFound')
                })
                this.backstep()
              } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
                for (i = 0; i < response.data.product_list.length; i++) {
                  messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: `${this.$t('CheckOut.SomeItemsAreNoLongerAvailableInTheSystem')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveThemFromYourCart')}`
                })
                this.backstep()
              } else {
                for (i = 0; i < response.data.product_list.length; i++) {
                  messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: `${this.$t('CheckOut.SomeItemsAreNotAvailableInSufficientQuantityForPurchase')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveTheseItemsFromYourCart')}`
                })
                this.backstep()
              }
            }
          } else {
            await this.$store.dispatch('actionsCheckStockBeforeCreateOrder', dataToCheck)
            const response = await this.$store.state.ModuleCart.stateCheckStockBeforeCreateOrder
            if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
              this.dialogAwaitConfirm = true
              this.typeButton = 'confirm'
            } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
              for (i = 0; i < response.data.product_free.length; i++) {
                messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
              }
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                icon: 'warning',
                text: `${this.$t('CheckOut.SomeItemsAreNotEligibleForTheFreeGiftDueToInsufficientQuantity')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveTheseItemsFromYourCart')}`
              })
              this.backstep()
            } else if (response.message === 'ไม่พบตะกร้า') {
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                icon: 'warning',
                text: this.$t('CheckOut.ShoppingCartNotFound')
              })
              this.backstep()
            } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
              for (i = 0; i < response.data.product_list.length; i++) {
                messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
              }
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                icon: 'warning',
                text: `${this.$t('CheckOut.SomeItemsAreNoLongerAvailableInTheSystem')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveThemFromYourCart')}`
              })
              this.backstep()
            } else {
              for (i = 0; i < response.data.product_list.length; i++) {
                messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
              }
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                icon: 'warning',
                text: `${this.$t('CheckOut.SomeItemsAreNotAvailableInSufficientQuantityForPurchase')} ` + messageCheckError + ` ${this.$t('CheckOut.PleaseRemoveTheseItemsFromYourCart')}`
              })
              this.backstep()
            }
          }
        }
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3500,
          timerProgressBar: true,
          icon: 'warning',
          html: `<h3>${this.$t('CheckOut.TheTotalPriceMustBeGreaterThanZerobaht')}</h3>`
        })
      }
    },
    async CancelAwaitConfirm () {
      this.dialogAwaitConfirm = false
      this.getCart()
    },
    async DeleteAddress (item) {
      this.dialogAwaitConfirm = true
      this.deleteAdressData = item
      this.typeButton = 'delete'
    },
    async deleteAddress () {
      const data = {
        id: this.deleteAdressData.id
      }
      await this.$store.dispatch('actionDeleteUserAddress', data)
      const res = await this.$store.state.ModuleUser.stateDeleteUserAddress
      if (res.message === 'Delete user address success') {
        // this.$swal.fire({ icon: 'success', title: 'ลบที่อยู่ในการจัดส่งสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.getAddress()
        this.deleteDialog = false
      } else {
        this.$swal.fire({ icon: 'error', title: `${this.$t('CheckOut.FailedToDeleteTheShippingAddress')}`, showConfirmButton: false, timer: 1500 })
      }
      this.dialogAwaitConfirm = false
      this.dialogSuccess = true
    },
    async getAddress (item) {
      this.$store.commit('openLoader')
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      await this.$store.dispatch('actionListUserAddress')
      this.userdetail = await this.$store.state.ModuleUser.stateListUserAddress
      this.AddressNew = this.userdetail.data
      if (item) {
        if (item.default_address === 'Y' || item.default === 'Y') {
          this.ShippingData = []
          this.radioPayment = ''
        }
      }
      if (dataRole.role === 'ext_buyer') {
        this.userdetail.data.forEach(element => {
          if (element.default_address === 'Y') {
            // console.log('get address', element)
            this.CartAddress = [
              {
                id: element.id,
                email: element.email,
                first_name: element.first_name,
                last_name: element.last_name,
                detail: element.detail,
                house_no: element.house_no,
                room_no: element.room_no,
                floor: element.floor,
                building_name: element.building_name,
                moo_ban: element.moo_ban,
                moo_no: element.moo_no,
                soi: element.soi,
                yaek: element.yaek,
                street: element.street,
                sub_district: element.sub_district,
                district: element.district,
                province: element.province,
                zip_code: element.zip_code,
                note_address: element.note_address,
                phone: element.phone,
                phone_ext: '',
                status: element.status
              }
            ]
          }
        })
        // await this.getCart()
      } else {
        await this.getCompanyAddress()
        this.comAddress.forEach(element => {
          if (element.default === 'Y') {
            this.CartAddress = [
              {
                id: element.id,
                email: element.email,
                company_id: element.company_id,
                first_name: '',
                last_name: '',
                name_th: element.name_th,
                detail: element.detail,
                house_no: element.house_no,
                room_no: element.room_no,
                floor: element.floor,
                building_name: element.building_name,
                moo_ban: element.moo_ban,
                moo_no: element.moo_no,
                soi: element.soi,
                yaek: element.yaek,
                street: element.street,
                sub_district: element.sub_district,
                district: element.district,
                province: element.province,
                zip_code: element.zip_code,
                note_address: element.note_address,
                phone: element.phone,
                phone_ext: '',
                status: element.status
              }
            ]
          }
        })
        // await this.getCart()
      }
      // console.log('เข้ามั้ย')
      // await this.itemsCart.choose_list[0].product_list.forEach(element => {
      //   this.SellerShopID = element.seller_shop_id
      //   if (element.product_type.length >= 1 && element.product_type === 'general') {
      //     this.countproductgen = true
      //   }
      // })
      await this.getCart()
      // console.log('เข้ามั้ยthis.itemsCart', this.itemsCart)
      // console.log('เข้ามั้ย2')
      // if (this.itemsCart.isEtax === 'yes') {
      //   this.radiostax = 'radiotax-1'
      //   // this.DialogTaxAddress = true
      // }
      // this.itemsCart.choose_list.forEach(element => {
      //   element.radios = 'radio-2'
      //   element.shipping_data = []
      //   element.invoice_id = ''
      //   element.tax_type = ''
      //   if (element.isEtax === 'yes') {
      //     element.radiostax = 'radiotax-1'
      //   } else {
      //     element.radiostax = 'radiotax-2'
      //   }
      // })
      // console.log('this.itemsCart', this.itemsCart)
      if (this.role.role === 'ext_buyer') {
        // this.extbuyerrole = true
        if (this.userdetail.data.length === 0 && this.countproductgen === true) {
          // this.DialogAddress = true
        }
        if (this.userdetail.data.length === 1 && this.userdetail.data[0].detail === '' && this.countproductgen === true) {
          // this.$swal.fire({
          //   showConfirmButton: false,
          //   timer: 3000,
          //   timerProgressBar: true,
          //   icon: 'warning',
          //   html: '<h3>กรุณากรอกข้อมูลที่อยู่ในการจัดการส่งสินค้าให้สมบูรณ์ </h3>'
          // })
          // this.userdetail.data[0].default_address = 'Y'
          // this.userdetail.data[0].status = 'Y'
          // this.editAddress(this.userdetail.data[0])
          // // this.getAddress()
        } else if (this.userdetail.data.length === 1 && (this.userdetail.data[0].detail !== '' || this.userdetail.data[0].detail !== null)) {
          setTimeout(() => { this.checkIsEtax() }, 3000)
        }
      }
      // this.getAddress()
      this.userdetail = [...this.userdetail.data]
      // this.userdetail.forEach(element => {
      //   var x = element.phone.replace(/\D/g, '').match(/(\d{0,3})(\d{0,3})(\d{0,4})/)
      //   this.telnumber = !x[2] ? x[1] : x[1] + '-' + x[2] + (x[3] ? '-' + x[3] : '')
      //   if (element.first_name === '' && element.last_name === '') {
      //     this.fullname = '-'
      //   } else {
      //     this.fullname = element.first_name + ' ' + element.last_name
      //   }
      // })
      // await this.getCart()
      this.$store.commit('closeLoader')
    },
    CheckSpacebar (e) {
      // console.log(e)
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '' || e.target.value === ' ')) {
        e.preventDefault()
      }
    },
    formatDateToShow (date) {
      const options = { year: 'numeric', month: '2-digit', day: '2-digit' }
      return new Date(date).toLocaleDateString('th-TH', options) // ฟอร์แมตเป็นวันที่ไทย
    },
    setValueContractStartDate (val, index) {
      this.$refs.dialogContractStartDate[index].save(val)
      this.searchContractStartDate = val
      // console.log(this.searchDateNotFormat)
      this.contractStartDate = this.formatDateToShow(val)
      // clear end date กรณีที่ start date เปลี่ยนแปลง
      var setMin = new Date(val)
      this.date1 = new Date(setMin.setDate(setMin.getDate() + 1)).toISOString().substr(0, 10)
      this.setMinDateContractEndDate = this.date1
      // console.log('date1', this.date1)
      this.contractEndDate = ''
      this.searchContractEndDate = ''
    },
    setValueContractStartDate1 (val, index) {
      // console.log('123', val)
      this.$refs.dialogContractStartDate1[index].save(val)
      this.searchContractStartDate = val
      // console.log(this.searchDateNotFormat)
      this.contractStartDate = this.formatDateToShow(val)
      // clear end date กรณีที่ start date เปลี่ยนแปลง
      var setMin = new Date(val)
      this.date1 = new Date(setMin.setDate(setMin.getDate() + 1)).toISOString().substr(0, 10)
      this.setMinDateContractEndDate = this.date1
      // console.log('date1', this.date1)
      this.contractEndDate = ''
      this.searchContractEndDate = ''
    },
    setValueContractEndDate (val, chooseindex) {
      this.$refs.dialogContractEndDate[chooseindex].save(val)
      this.searchContractEndDate = val
      // console.log(this.searchDateNotFormat)
      this.contractEndDate = this.formatDateToShow(val)
    },
    setValueDate (index, val) {
      const item = this.itemsCart.choose_list[index]
      item.contractDate = this.formatDateToShow(val) // อัปเดต contractDate
      item.dates = val // เก็บค่าวันที่ที่เลือก

      // ตั้งค่าเวลาตามวันที่
      if (item.dates !== this.today) {
        item.timeselect = ''
        item.timeselecttoday = '1'
      } else {
        item.timeselecttoday = ''
        item.timeselect = '1'
      }
    },
    async closeDialogQR () {
      this.$store.commit('openLoader')
      this.DialogQR = false
      this.CloseDialog = true
      var companyId = ''
      if (this.role.role !== 'ext_buyer') {
        if (localStorage.getItem('SetRowCompany') !== null) {
          var companyDataID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
          companyId = companyDataID.company.company_id
        } else {
          companyId = -1
        }
        const data = {
          company_id: companyId
        }
        await this.$store.dispatch('actionsDetailCompany', data)
        await this.$store.dispatch('actionsAuthorityUser')
        var responsecompany = await this.$store.state.ModuleAdminManage.stateDetailCompany
        var responseposition = await this.$store.state.ModuleUser.stateAuthorityUser
        var listcompany = responseposition.data.list_company
        for (let i = 0; i < listcompany.length; i++) {
          if (responsecompany.data.id === listcompany[i].company_id) {
            localStorage.removeItem('list_Company_detail')
            localStorage.setItem('list_Company_detail', Encode.encode(listcompany[i]))
          }
        }
        localStorage.setItem('CompanyData', Encode.encode(responsecompany.data))
      }
      if (this.role.role === 'ext_buyer') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('getCartPopOver')
        if (!this.MobileSize) {
          this.$router.push({ path: '/pobuyerProfile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/pobuyerProfileMobile' }).catch(() => { })
        }
      } else {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('getCartPopOver')
        if (!this.MobileSize) {
          this.$router.push({ path: `/orderDetailCompany?orderNumber=${this.paymenttransactionnumber}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/orderDetailCompanyMobile?orderNumber=${this.paymenttransactionnumber}` }).catch(() => {})
        }
      }
    },
    closeModalContractStartDate () {
      this.modalContractStartDate = false
      this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      this.searchContractStartDate = ''
      this.contractStartDate = ''
      this.contractEndDate = ''
      this.date1 = ''
    },
    closeModalContractEndDate () {
      this.modalContractEndDate = false
      this.date1 = this.setMinDateContractEndDate
      this.contractEndDate = ''
    },
    ChangeDiscount (price) {
      var onedata
      this.disableButtonPay = false
      if (this.discountBaht !== '') {
        if (parseFloat(this.discountBaht) < parseFloat(price)) {
          // console.log('value =====>', this.discountCode, this.discountBaht)
          onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
          onedata.cartData.note_of_code = this.discountCode
          onedata.cartData.code_discount = this.discountBaht
          localStorage.setItem('oneData', Encode.encode(onedata))
          this.getCart()
        } else {
          this.disableButtonPay = true
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: `<h3>${this.$t('CheckOut.PleaseEnterDiscountThatDoesNotExceedTheProductPricel')}</h3>`
          })
        }
      } else {
        onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        onedata.cartData.note_of_code = this.discountCode
        onedata.cartData.code_discount = this.discountBaht
        localStorage.setItem('oneData', Encode.encode(onedata))
        this.getCart()
      }
      // console.log(onedata)
    },
    openModalPayment () {
      this.modalPayment = true
    },
    async checkAddress () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('GetUserAddress', data)
      this.propsAddress = this.$store.state.ModuleManageShop.GetUserAddress
      localStorage.setItem('AddressData', Encode.encode(this.propsAddress[0].address_data))
      if (dataRole.role === 'ext_buyer') {
        if (this.propsAddress[0].address_data.length !== 0) {
          if (this.propsAddress[0].address_data.length === 1) {
            localStorage.setItem('AddressUserDetail', Encode.encode(this.propsAddress[0].address_data[0]))
            if (this.propsAddress[0].address_data[0].status === 'Y' && (this.propsAddress[0].address_data[0].default_address === 'N' || this.propsAddress[0].address_data[0].default_address === null)) {
              // this.$swal.fire({
              //   showConfirmButton: false,
              //   timer: 2500,
              //   timerProgressBar: true,
              //   icon: 'warning',
              //   html: '<h3>คุณยังไม่ได้ตั้งค่าที่อยู่เป็นค่าเริ่มต้น</h3>'
              // })
              // if (this.MobileSize) {
              //   this.$router.push({ path: '/addressProfileMobile' })
              // } else {
              //   this.$router.push({ path: '/addressProfile' })
              // }
            }
          } else {
            this.propsAddress[0].address_data.forEach(element => {
              if (element.default_address === 'Y') {
                if (element.status === 'N') {
                  localStorage.setItem('AddressUserDetail', Encode.encode(element))
                  this.EditAddressDetail = element
                  this.titleAddress = this.$t('CheckOut.EditShippingAddress')
                  this.page = 'checkout'
                  this.$EventBus.$emit('EditModalAddress')
                }
              }
            })
          }
        }
      }
    },
    async checkStatus () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('GetUserAddress', data)
      this.propsAddress = this.$store.state.ModuleManageShop.GetUserAddress
      if (this.propsAddress[0].address_data.length !== 0) {
        this.propsAddress[0].address_data.forEach(element => {
          if (element.default_address === 'Y') {
            if (element.status === 'N') {
              this.backstep()
            }
          }
        })
      }
    },
    async getCart (itemCoupon, itemPoint) {
      this.$store.commit('openLoader')
      if (localStorage.getItem('InetRelationShip') !== null) {
        this.InetRelation = JSON.parse(localStorage.getItem('InetRelationShip'))
      } else {
        this.InetRelation = []
      }
      if (localStorage.getItem('CouponPlatform') !== null) {
        this.CodePlatform = JSON.parse(localStorage.getItem('CouponPlatform'))
      } else {
        this.CodePlatform = []
      }
      if (localStorage.getItem('CouponPlatformShipping') !== null) {
        this.CodePlatformShipping = JSON.parse(localStorage.getItem('CouponPlatformShipping'))
      } else {
        this.CodePlatformShipping = []
      }
      // if (isNaN(itemPoint) && itemPoint !== undefined) {
      //   itemPoint = 0
      // }
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // console.log('onedata', onedata)
      var res
      // var CartAddress
      this.checkOwnShop = 'N'
      this.cartData = onedata.cartData
      this.cartData.address = this.CartAddress
      if (itemPoint !== null && itemPoint !== undefined) {
        // this.cartData.point = Number(itemPoint)
        // this.PointData = Number(itemPoint)
        const newPoint = itemPoint
        newPoint.forEach(newPoint => {
          const isDuplicate = this.PointData.some(existingPoint => existingPoint.seller_shop_id === newPoint.seller_shop_id)
          if (!isDuplicate) {
            // เพิ่มเฉพาะรายการที่ไม่ซ้ำ
            this.PointData.push(newPoint)
          }
        })
        this.cartData.point = this.PointData
      } else {
        if (this.PointData.length !== 0) {
          this.PointData = this.PointData.filter(coupon => coupon.seller_shop_id !== -1)
        } else {
          this.PointData = []
        }
        this.cartData.point = this.PointData
      }
      if (itemCoupon !== null && itemCoupon !== undefined) {
        this.CouponData = this.CouponData.filter(coupon => coupon.seller_shop_id !== -1)
        const newCoupons = itemCoupon.flat().filter(coupon => coupon.seller_shop_id !== -1) // แปลง nested array เป็น array ธรรมดา
        newCoupons.forEach(newCoupon => {
          const isDuplicate = this.CouponData.some(existingCoupon => existingCoupon.seller_shop_id === newCoupon.seller_shop_id)
          if (!isDuplicate) {
            this.CouponData.push(newCoupon) // เพิ่มรายการที่ไม่ซ้ำ
          }
        })
      } else {
        if (this.CouponData.length !== 0) {
          this.CouponData = this.CouponData.filter(coupon => coupon.seller_shop_id !== -1)
        } else {
          this.CouponData = []
        }
      }
      if (this.CodePlatform.length !== 0) {
        const updatedCoupons = this.CodePlatform.map(coupon => {
          const updatedCoupon = {
            ...coupon,
            coupon_id: coupon.id
          }
          delete updatedCoupon.id
          return updatedCoupon
        })
        this.CodePlatform = updatedCoupons
        // this.cartData.coupon = updatedCoupons
      }
      if (this.CodePlatformShipping.length !== 0) {
        const updatedCoupons = this.CodePlatformShipping.map(coupon => {
          const updatedCoupon = {
            ...coupon,
            coupon_id: coupon.id
          }
          delete updatedCoupon.id
          return updatedCoupon
        })
        this.CodePlatformShipping = updatedCoupons
        // this.cartData.coupon = updatedCoupons
      }
      // this.cartData.coupon = [...this.CouponData, ...this.CodePlatform]
      if (this.itemsCart.length !== 0) {
        this.cartData.array_type_shipping = this.itemsCart.choose_list.map(item => ({
          seller_shop_id: item.seller_shop_id,
          type_shipping: item.radios === 'radio-2' ? 'online' : 'pickup'
        }))
      } else {
        const uniqueMap = new Map()
        this.cartData.product_to_calculate.forEach(item => {
          if (!uniqueMap.has(item.seller_shop_id)) {
            uniqueMap.set(item.seller_shop_id, {
              seller_shop_id: item.seller_shop_id,
              type_shipping: 'online'
            })
          }
        })
        this.cartData.array_type_shipping = Array.from(uniqueMap.values())
      }
      this.CouponData = [...this.CouponData, ...this.CodePlatform, ...this.CodePlatformShipping]
      this.cartData.coupon = this.CouponData
      // this.cartData.address = CartAddress
      if (Array.isArray(this.itemsCart.choose_list) && this.itemsCart.choose_list.length > 0) {
        if (this.itemsCart.choose_list.some(item => item.radios === 'radio-2')) {
          // ถ้าใน choose_list มีค่า radios = 'radio-2'
          Vue.set(this.cartData, 'shipping_data', this.ShippingData)
        }
        if (this.itemsCart.choose_list.some(item => item.radios === 'radio-1')) {
          // ตรวจสอบและตัด coupon ตาม radios = 'radio-1'
          if (this.CouponData !== undefined && this.CouponData.length !== 0) {
            this.itemsCart.choose_list.forEach(item => {
              // ข้ามรายการที่ไม่มี radios
              if (!item.radios) return
              if (item.radios === 'radio-1') {
                const filteredChooseList = this.itemsCart.choose_list.filter(item => {
                  return this.CouponData.some(coupon =>
                    coupon.coupon_type === 'free_shipping' && coupon.seller_shop_id === item.seller_shop_id && coupon.seller_shop_id !== -1
                  )
                })
                // ส่งข้อมูล filteredChooseList ที่ตรงเงื่อนไข
                if (filteredChooseList.length > 0) {
                  this.$EventBus.$emit('clearCoupon', filteredChooseList[0]) // ส่งข้อมูลที่กรองแล้ว
                }
                this.CouponData = this.CouponData.filter(coupon => {
                  if (coupon.coupon_type === 'free_shipping' && coupon.seller_shop_id === item.seller_shop_id) {
                    return false // ตัดคูปองที่ตรงเงื่อนไข
                  }
                  return true // คูปองที่เหลืออยู่
                })
              }
            })
            // อัปเดต CouponData ใน cartData
            this.cartData.coupon = this.CouponData
          }
          // อัปเดตค่า cartData เมื่อไม่มี radio-2
          this.radioTransport = ''
          this.radioCreditTerm = 'No'
          this.radioPayment = ''
          this.reShippingData = false
        }
      }
      if (Object.prototype.hasOwnProperty.call(onedata, 'cartDataSpecialPrice')) {
        if (onedata.cartDataSpecialPrice === 'yes') {
          await this.$store.dispatch('ActionGetCartSpecialPrice', this.cartData)
          res = await this.$store.state.ModuleCart.stateGetCartSpecialPrice
        } else {
          await this.$store.dispatch('ActionGetCartV2', this.cartData)
          res = await this.$store.state.ModuleCart.stateGetCartV2
        }
      }
      if (res.message === 'Get cart success') {
        // console.log('this.itemsCart+', this.itemsCart)
        var localChooseListData
        if (this.itemsCart.length === 0) {
          this.itemsCart = res.data
          if (localStorage.getItem('ChooseListData') !== null) {
            localChooseListData = JSON.parse(Decode.decode(localStorage.getItem('ChooseListData')))
          } else {
            localChooseListData = null
          }
          this.itemsCart.choose_list.forEach(element => {
            element.radios = 'radio-2'
            element.shipping_data = []
            element.remark_to_shop = ''
            element.invoice_id = ''
            element.tax_type = ''
            if (element.isEtax === 'yes') {
              element.radiostax = 'radiotax-1'
            } else {
              element.radiostax = 'radiotax-2'
            }
            if (localChooseListData && Array.isArray(localChooseListData)) {
              const matchedLocalItem = localChooseListData.find(localItem => localItem.seller_shop_id === element.seller_shop_id)
              if (matchedLocalItem && matchedLocalItem.invoice_id) {
                element.invoice_id = matchedLocalItem.invoice_id
              }
            }
          })
        } else {
          if (localStorage.getItem('ChooseListData') !== null) {
            localChooseListData = JSON.parse(Decode.decode(localStorage.getItem('ChooseListData')))
          } else {
            localChooseListData = null
          }
          res.data.choose_list.forEach(dataItem => {
            const matchedCart = this.itemsCart.choose_list.find(cartItem => cartItem.seller_shop_id === dataItem.seller_shop_id)
            if (matchedCart) {
              dataItem.radios = matchedCart.radios
              dataItem.radiostax = matchedCart.radiostax
              dataItem.remark_to_shop = matchedCart.remark_to_shop
              dataItem.invoice_id = matchedCart.invoice_id
              dataItem.tax_type = matchedCart.tax_type
              dataItem.menu = matchedCart.menu
              dataItem.contractDate = matchedCart.contractDate
              dataItem.dates = matchedCart.dates
              dataItem.timeselect = matchedCart.timeselect
              dataItem.timeselecttoday = matchedCart.timeselecttoday
            }
            if (localChooseListData && Array.isArray(localChooseListData)) {
              const matchedLocalCart = localChooseListData.find(localItem => localItem.seller_shop_id === dataItem.seller_shop_id)
              if (matchedLocalCart && matchedLocalCart.invoice_id) {
                dataItem.invoice_id = matchedLocalCart.invoice_id
              }
            }
            const matchedShipping = this.ShippingData.find(shippingItem => shippingItem.seller_shop_id === dataItem.seller_shop_id)
            dataItem.shipping_data = matchedShipping || []
          })
          this.itemsCart = res.data
          if (this.itemsCart.choose_list.every(item => item.radios === 'radio-1')) {
            this.allPickUp = true
            if (this.CodePlatformShipping.length !== 0) {
              this.clearCodePlatformShipping()
            }
          } else {
            this.allPickUp = false
          }
        }
        if (res.data.check_own_shop === 'yes') {
          this.checkOwnShop = 'Y'
        } else {
          this.checkOwnShop = 'N'
        }
        if (this.itemsCart.total_price_no_vat >= 50000) {
          this.contractSet = true
        }
        // this.googleSentData()
        if (this.itemsCart.address_data.length !== 0) {
          if (dataRole.role === 'purchaser') {
            var addressPurchaser = ''
            this.choose_list = this.itemsCart.choose_list[0].pay_type
            this.address_data = this.itemsCart.address_data[0]
            this.Fullname = this.address_data.first_name + ' ' + this.address_data.last_name
            // addressPurchaser = this.address_data.detail + ' ' + 'แขวง/ตำบล' + ' ' + this.address_data.district + ' ' + 'จังหวัด' + ' ' + this.address_data.province + ' ' + this.address_data.zip_code + ' ' + 'เบอร์โทรศัพท์' + ' ' + this.address_data.phone
            this.Address = addressPurchaser
          } else {
            this.itemsCart.address_data.forEach(element => {
              // console.log(element)
              if (element.default_address === 'Y' || element.default_address !== undefined) {
                this.address_data = element
                this.Fullname = element.first_name + ' ' + element.last_name
                var address = ''
                address = element.detail + ' ' + `${this.$t('CheckOut.SubDistrict')}` + ' ' + element.sub_district + ' ' + `${this.$t('CheckOut.District')}` + ' ' + element.district + ' ' + `${this.$t('CheckOut.Province')}` + ' ' + element.province + ' ' + element.zip_code + ' ' + `${this.$t('CheckOut.Phone')}` + ' ' + element.phone
                this.Address = address
              } else {
                this.address_data = element
                this.Fullname = element.first_name + ' ' + element.last_name
                var address1 = ''
                address1 = element.detail + ' ' + `${this.$t('CheckOut.SubDistrict')}` + ' ' + element.sub_district + ' ' + `${this.$t('CheckOut.District')}` + ' ' + element.district + ' ' + `${this.$t('CheckOut.Province')}` + ' ' + element.province + ' ' + element.zip_code + ' ' + `${this.$t('CheckOut.Phone')}` + ' ' + element.phone
                this.Address = address1
              }
            })
          }
        } else {
          this.Fullname = ''
          this.Address = this.$t('CheckOut.YouHaveNotAddedAnAddressYet')
        }
        // get user data
        // var data = {
        //   role_user: dataRole.role
        // }
        // await this.$store.dispatch('actionsUserDetailPage', data)
        // var response = await this.$store.state.ModuleUser.stateUserDetailPage
        var response = JSON.parse(Decode.decode(localStorage.getItem('UserDetail')))
        var user = response.data[0]
        this.buyer_name = user.first_name_th + ' ' + user.last_name_th
        this.buyer_phone = user.phone
        this.buyer_email = user.email
        // get admin data
        // const sendId = { user_id: user.id }
        // await this.$store.dispatch('ActionGetAdminData', sendId)
        // var responseAdmin = await this.$store.state.ModuleCart.stateGetAdminData
        // var adminStatus = false
        // if (responseAdmin.data.length !== 0) {
        //   adminStatus = true
        // } else {
        //   adminStatus = false
        // }
        // if (adminStatus === true) {
        //   this.sentDataPPL()
        // }
        this.overlay = false
        this.$store.commit('closeLoader')
      } else if (res.message === 'Some parameter missing. [product_to_cal, shop_to_cal, address_id]') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: this.$t('CheckOut.Incompleteinformation')
        })
        this.backstep()
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'SERVER ERROR'
        })
        this.backstep()
      } else if (res.message === 'มีความผิดพลาดจากการคำนวนค่าขนส่ง Flash จากที่อยู่ของผู้ใช้งาน') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: this.$t('CheckOut.PleaseConfirmYourOrderAgain')
        })
        this.backstep()
      } else if (res.message === 'น้ำหนักของสินค้าในตระกร้ารวมกันแล้วเกิน 50 kg ที่กำหนดขนส่งของ flash') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'warning',
          text: this.$t('CheckOut.WeightOver50kilograms')
        })
        this.backstep()
      } else if (res.message === 'Get cart faild.Some products have weight equal 0.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${'รหัสสินค้า' + ' ' + res.data[0].sku + ' ' + 'มีปัญหาเรื่องน้ำหนัก กรุณาติดต่อเจ้าหน้าที่'}`
        })
        this.backstep()
      } else if (res.message === 'ขออภัยเนื่องจากที่อยู่จัดส่งอยู่นอกเขตพื้นที่บริการ หรือ ขนาดและน้ำหนักของสินค้าเกินมามาตรฐานที่จะจัดส่งได้') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          text: `${res.message}`
        })
        this.backstep()
      } else if (res.message === 'ไม่พบที่อยู่สำหรับจัดส่งสินค้า กรุุณาทำการเพิ่มข้อมูลที่อยู่ใหม่อีกครั้ง') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        if (this.MobileSize) {
          this.$router.push({ path: '/addressProfileMobile' })
        } else {
          this.$router.push({ path: '/addressProfile' })
        }
      } else if (res.message === 'This user is unauthorized.') {
        this.SelectCouponOrPoint = true
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
        // window.location.assign('/')
      } else if (res.message === 'Company User Permission Not Found.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: this.$t('CheckOut.UserPermissionsChange')
        })
        this.backstep()
      } else if (res.message === 'Not found cart') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        // this.$swal.fire({
        //   showConfirmButton: false,
        //   timer: 2500,
        //   timerProgressBar: true,
        //   icon: 'error',
        //   text: 'กรุณาตรวจสอบ เนื่องจากมีการเปลี่ยนแปลงสิทธิ์ของผู้ใช้งาน'
        // })
        this.backstep()
        this.goHomePage()
      } else if (res.message.startsWith('คูปองไม่สามารถใช้ได้')) {
        this.$store.commit('closeLoader')
        this.SelectCouponOrPoint = true
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'warning',
          text: `${res.message}`
        })
        if (res.data.seller_shop_id !== -1) {
          this.closeCoupon(res.data)
        } else {
          if (res.data.coupon_type === 'free_shipping') {
            this.clearCodePlatformShipping()
          } else if (res.data.coupon_type === 'discount') {
            this.clearCodePlatform()
          }
        }
      } else {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          text: `${res.message}`
        })
        await this.$EventBus.$emit('CancleBookCouponCheckout')
        this.backstep()
      }
      this.$store.commit('closeLoader')
    },
    backstep () {
      // this.$router.go(-1)
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (onedata.cartDataSpecialPrice === 'yes') {
        this.$router.replace({ path: '/specialPriceBuyerRequest' }).catch(() => { this.$router.go(-1) })
      } else {
        this.$router.replace({ path: '/shoppingcart' }).catch(() => { this.$router.go(-1) })
      }
    },
    async GetPersonal () {
      this.$store.commit('openLoader')
      var companyId = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        var company = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyId = company.company.company_id
      } else {
        companyId = -1
      }
      // console.log('companyId--------->', companyId.company.company_id)
      const data = {
        company_id: companyId
      }
      // console.log(data)
      await this.$store.dispatch('actionsDetailCompany', data)
      var companyData = await this.$store.state.ModuleAdminManage.stateDetailCompany
      if (companyData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        if (this.itemsCart.is_JV === 'yes' && this.itemsCart.is_JV_buyer === 'yes') {
          this.Name_Buyer = companyData.data.json_personal[0].purchasing_chief[0].name
          this.Phone_Buyer = companyData.data.json_personal[0].purchasing_chief[0].phone
          this.Position_Buyer = companyData.data.json_personal[0].purchasing_chief[0].position
          this.Email_Buyer = companyData.data.json_personal[0].purchasing_chief[0].email
          this.Name_Audit1 = companyData.data.json_personal[0].inspectors_one[0].name
          this.Phone_Audit1 = companyData.data.json_personal[0].inspectors_one[0].phone
          this.Position_Audit1 = companyData.data.json_personal[0].inspectors_one[0].position
          this.Email_Audit1 = companyData.data.json_personal[0].inspectors_one[0].email
          this.Name_Audit2 = companyData.data.json_personal[0].inspectors_two[0].name
          this.Phone_Audit2 = companyData.data.json_personal[0].inspectors_two[0].phone
          this.Position_Audit2 = companyData.data.json_personal[0].inspectors_two[0].position
          this.Email_Audit2 = companyData.data.json_personal[0].inspectors_two[0].email
          this.companydata = companyData.data
          this.companyAddressData = companyData.data.company_address
        } else {
          this.companydata = companyData.data
          this.companyAddressData = companyData.data.company_address
        }
      }
      // console.log(this.companyAddressData)
      this.$store.commit('closeLoader')
    },
    async GetCC (paymentTypeData) {
      this.$store.commit('openLoader')
      const paymentType = paymentTypeData
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (localStorage.getItem('InetRelationShip') !== null) {
        this.InetRelation = JSON.parse(localStorage.getItem('InetRelationShip'))
      } else {
        this.InetRelation = []
      }
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data
      var couponData = JSON.parse(Decode.decode(localStorage.getItem('couponData')))
      var resCoupon
      var utmMedium = ''
      if (sessionStorage.getItem('utm_medium') !== null) {
        utmMedium = sessionStorage.getItem('utm_medium')
      } else {
        utmMedium = null
      }
      if (this.CouponData.length === 0 || this.CouponData.filter(coupon => coupon.seller_shop_id !== -1).length === 0) {
        resCoupon = {
          message: this.$t('CheckOut.TheCouponCanBeUsed')
        }
      } else {
        var dataCheckCoupon = {
          coupon_id: this.CouponData.length !== 0 ? [this.CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_id] : '',
          shop_id: onedata.cartData.seller_shop_id,
          net_price: couponData.net_price,
          price_inc_vat: couponData.price_inc_vat,
          role_user: dataRole.role,
          company_id: onedata.cartData.company_id,
          customer_id: -1
        }
        await this.$store.dispatch('actionsRecheckCoupon', dataCheckCoupon)
        resCoupon = await this.$store.state.ModuleCart.stateRecheck
      }
      if (resCoupon.message === 'พบบางคูปองมีปัญหา') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: `<h3>${resCoupon.data[0].error}</h3>`
        })
        this.dialogAwaitConfirm = false
        // this.closeCoupon()
        this.$store.commit('closeLoader')
      } else {
        if (paymentType === 'cashPayment') {
          if (dataRole.role === 'ext_buyer') {
            this.invoicedetail.forEach(element => {
              if (element.default_invoice === 'Y') {
                this.invoiceID = element.id
                this.EtaxType = element.tax_type
              }
            })
            data = {
              utm_source_keyword: utmMedium === 'reach' ? sessionStorage.getItem('utm_source') : null,
              checkout_detail: this.itemsCart.choose_list.map(item => ({
                seller_shop_id: item.seller_shop_id,
                address: this.CartAddress,
                date_pickup: item.dates,
                product_free: item.product_free,
                coupon: this.CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id) !== undefined ? [this.CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id)] || [] : [],
                etax: item.tax_type !== '' && item.radiostax === 'radiotax-1' ? item.tax_type : this.EtaxType !== 'No' && item.radiostax === 'radiotax-1' ? this.EtaxType : 'No',
                invoice_id: item.invoice_id !== '' && item.radiostax === 'radiotax-1' ? item.invoice_id : this.invoiceID !== '' && item.radiostax === 'radiotax-1' ? this.invoiceID : '',
                point: this.PointData.find(point => point.seller_shop_id === item.seller_shop_id) !== undefined ? this.PointData.find(point => point.seller_shop_id === item.seller_shop_id).total_point || 0 : 0,
                point_used: this.PointData.length !== 0 && this.PointData.find(p => p.seller_shop_id === item.seller_shop_id) !== undefined ? this.PointData.find(p => p.seller_shop_id === item.seller_shop_id).total_point / (parseFloat(item.seller_shop_point_data[0].x_baht) / parseFloat(item.seller_shop_point_data[0].x_point)) : 0,
                required_invoice: item.radiostax === 'radiotax-1' ? 'yes' : 'no',
                role_user: 'ext_buyer',
                shipping_data: item.shipping_data,
                time_pickup: this.formattedTime(item),
                total_point_receive: item.seller_shop_point_data[0].total_point_receive,
                type_shipping: item.radios === 'radio-2' ? 'online' : 'pickup',
                remark_to_shop: item.remark_to_shop
              })),
              coupon_platform: [...this.CodePlatform, ...this.CodePlatformShipping],
              inet_relation_ship: this.InetRelation
            }
            // console.log('dataCreateOrederCC: ', data)
            // console.log('itemsCart: ', this.itemsCart)
          }
          await this.$store.dispatch('ActionCreateOrder', data)
          var res = await this.$store.state.ModuleCart.stateCreateOrder
          if (res.result === 'SUCCESS') {
            localStorage.removeItem('sale_order')
            var resCC
            var goLocalValue = window.location.href.startsWith('http://localhost:8080/') ? 'local' : ''
            if (this.radioPayment !== 'radio-installment') {
              this.radioCreditTerm = ''
            }
            data = {
              go_local: goLocalValue,
              payment_transaction_number: res.data.payment_transaction_number,
              term: this.radioCreditTerm
            }
            await this.$store.dispatch('actionsGetCCV2', data)
            resCC = await this.$store.state.ModuleCart.stateGetCCV2
            if (resCC.result === 'SUCCESS') {
              localStorage.setItem('PaymentData', Encode.encode(resCC.data))
              this.$router.push('/RedirectPaymentPage').catch(() => {})
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3500,
                timerProgressBar: true,
                icon: 'error',
                text: this.$t('CheckOut.SystemErrorHasOccurredPleaseContactSupport')
              })
              this.backstep()
            }
          } else if (res.message === 'ไม่สามารถใช้คูปองได้ กรุณานำออกจากรถเข็น') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3500,
              timerProgressBar: true,
              icon: 'error',
              text: this.$t('CheckOut.TheCouponCannotBeUsed')
            })
            this.$EventBus.$emit('clearPoint', res.data)
            this.$EventBus.$emit('clearCoupon', res.data)
            this.dialogAwaitConfirm = false
          } else if (res.message.startsWith('คูปองไม่สามารถใช้ได้')) {
            this.SelectCouponOrPoint = true
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 7000,
              timerProgressBar: true,
              icon: 'warning',
              text: `${res.message}`
            })
            if (res.data.seller_shop_id !== -1) {
              this.closeCoupon(res.data)
            } else {
              if (res.data.coupon_type === 'free_shipping') {
                this.clearCodePlatformShipping()
              } else if (res.data.coupon_type === 'discount') {
                this.clearCodePlatform()
              }
            }
            this.dialogAwaitConfirm = false
          } else if (res.code === 400) {
            if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
              this.$store.commit('closeLoader')
              this.$EventBus.$emit('refreshToken')
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'error',
                text: res.message
              })
            }
            this.backstep()
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3500,
              timerProgressBar: true,
              icon: 'error',
              text: this.$t('CheckOut.SystemErrorHasOccurredPleaseContactSupport')
            })
          }
          if (res.message === 'Division by zero') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3500,
              timerProgressBar: true,
              icon: 'error',
              text: this.$t('CheckOut.SystemErrorHasOccurredPleaseContactSupport')
            })
            this.backstep()
          }
        }
      }
      this.$store.commit('closeLoader')
    },
    async GetQRCode (paymentTypeData) {
      this.$store.commit('openLoader')
      const paymentType = paymentTypeData
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (localStorage.getItem('InetRelationShip') !== null) {
        this.InetRelation = JSON.parse(localStorage.getItem('InetRelationShip'))
      } else {
        this.InetRelation = []
      }
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data
      var couponData = JSON.parse(Decode.decode(localStorage.getItem('couponData')))
      var resCoupon
      var utmMedium = ''
      if (sessionStorage.getItem('utm_medium') !== null) {
        utmMedium = sessionStorage.getItem('utm_medium')
      } else {
        utmMedium = null
      }
      if (this.CouponData.length === 0 || this.CouponData.filter(coupon => coupon.seller_shop_id !== -1).length === 0) {
        resCoupon = {
          message: this.$t('CheckOut.TheCouponCanBeUsed')
        }
      } else {
        var dataCheckCoupon = {
          coupon_id: this.CouponData.length !== 0 ? [this.CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_id] : '',
          shop_id: onedata.cartData.seller_shop_id,
          net_price: couponData.net_price,
          price_inc_vat: couponData.price_inc_vat,
          role_user: dataRole.role,
          company_id: onedata.cartData.company_id,
          customer_id: -1
        }
        await this.$store.dispatch('actionsRecheckCoupon', dataCheckCoupon)
        resCoupon = await this.$store.state.ModuleCart.stateRecheck
      }
      if (resCoupon.message === 'พบบางคูปองมีปัญหา') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: `<h3>${resCoupon.data[0].error}</h3>`
        })
        this.dialogAwaitConfirm = false
        // this.closeCoupon()
        this.$store.commit('closeLoader')
      } else {
        if (paymentType === 'cashPayment') {
          if (dataRole.role === 'ext_buyer') {
            this.invoicedetail.forEach(element => {
              if (element.default_invoice === 'Y') {
                this.invoiceID = element.id
                this.EtaxType = element.tax_type
              }
            })
            data = {
              utm_source_keyword: utmMedium === 'reach' ? sessionStorage.getItem('utm_source') : null,
              checkout_detail: this.itemsCart.choose_list.map(item => ({
                seller_shop_id: item.seller_shop_id,
                address: this.CartAddress,
                date_pickup: item.dates,
                product_free: item.product_free,
                coupon: this.CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id) !== undefined ? [this.CouponData.find(coupon => coupon.seller_shop_id === item.seller_shop_id)] || [] : [],
                etax: item.tax_type !== '' && item.radiostax === 'radiotax-1' ? item.tax_type : this.EtaxType !== 'No' && item.radiostax === 'radiotax-1' ? this.EtaxType : 'No',
                invoice_id: item.invoice_id !== '' && item.radiostax === 'radiotax-1' ? item.invoice_id : this.invoiceID !== '' && item.radiostax === 'radiotax-1' ? this.invoiceID : '',
                point: this.PointData.find(point => point.seller_shop_id === item.seller_shop_id) !== undefined ? this.PointData.find(point => point.seller_shop_id === item.seller_shop_id).total_point || 0 : 0,
                point_used: this.PointData.length !== 0 && this.PointData.find(p => p.seller_shop_id === item.seller_shop_id) !== undefined ? this.PointData.find(p => p.seller_shop_id === item.seller_shop_id).total_point / (parseFloat(item.seller_shop_point_data[0].x_baht) / parseFloat(item.seller_shop_point_data[0].x_point)) : 0,
                required_invoice: item.radiostax === 'radiotax-1' ? 'yes' : 'no',
                role_user: 'ext_buyer',
                shipping_data: item.shipping_data,
                time_pickup: this.formattedTime(item),
                total_point_receive: item.seller_shop_point_data[0].total_point_receive,
                type_shipping: item.radios === 'radio-2' ? 'online' : 'pickup',
                remark_to_shop: item.remark_to_shop
              })),
              coupon_platform: [...this.CodePlatform, ...this.CodePlatformShipping],
              inet_relation_ship: this.InetRelation
            }
            // console.log('dataCreateOrederQR: ', data)
            // console.log('itemsCart: ', this.itemsCart)
          }
          await this.$store.dispatch('ActionCreateOrder', data)
          var res = await this.$store.state.ModuleCart.stateCreateOrder
          if (res.result === 'SUCCESS') {
            localStorage.removeItem('sale_order')
            var resQR = ''
            data = {
              payment_transaction_number: res.data.payment_transaction_number
            }
            this.paymenttransactionnumber = res.data.payment_transaction_number
            await this.$store.dispatch('actionsGetQRCodeV2', data)
            resQR = await this.$store.state.ModuleCart.stateGetQRCodeV2
            if (resQR.message.toLowerCase() === 'success' || resQR.result.toLowerCase() === 'success') {
              // console.log(resQR)
              if (resQR.message !== 'ดึง QR Code ของร้านค้านั้นสำเร็จ') {
                this.QRShop = false
              } else if (resQR.message === 'ดึง QR Code ของร้านค้านั้นสำเร็จ') {
                this.QRShop = true
              } else {
                this.QRShop = false
              }
              this.netPrice = await resQR.data.net_price
              this.Ref1 = await resQR.data.ref1
              this.Ref2 = await resQR.data.ref2
              var base64 = resQR.data.img_base
              this.imageBase64 = 'data:image/png;base64,' + base64
              var ImageQR = await resQR.data.img_base64
              this.ImageQR = ImageQR
              this.$store.commit('closeLoader')
              this.openDialogQR()
              data = {
                payment_transaction_number: res.data.payment_transaction_number
              }
              var value = data.payment_transaction_number
              const maxAttempts = 15
              let currentAttempt = 1
              while (currentAttempt <= maxAttempts) {
                await this.$store.dispatch('actionsCheckResultQRCodeV2', data)
                const resCheckQR = await this.$store.state.ModuleCart.stateCheckResultQRCodeV2
                if (this.CloseDialog === true) {
                  break
                }
                if (resCheckQR.result === 'SUCCESS') {
                  this.$router.push({ path: `/yourorder?id=${value}` }).catch(() => {})
                  break
                }
                await new Promise(resolve => setTimeout(resolve, 10000))
                currentAttempt++
                if (currentAttempt === 15 && resCheckQR.result !== 'SUCCESS') {
                  this.$swal.fire({
                    showConfirmButton: false,
                    timer: 5000,
                    timerProgressBar: true,
                    icon: 'error',
                    text: this.$t('CheckOut.Paymentincomplete')
                  })
                  this.$router.push({ path: '/' }).catch(() => {})
                }
              }
            } else if (res.message === 'ไม่สามารถใช้คูปองได้ กรุณานำออกจากรถเข็น') {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3500,
                timerProgressBar: true,
                icon: 'error',
                text: this.$t('CheckOut.TheCouponCannotBeUsed')
              })
              this.$EventBus.$emit('clearPoint', res.data)
              this.$EventBus.$emit('clearCoupon', res.data)
              this.dialogAwaitConfirm = false
            } else if (res.message.startsWith('คูปองไม่สามารถใช้ได้')) {
              this.SelectCouponOrPoint = true
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 7000,
                timerProgressBar: true,
                icon: 'warning',
                text: `${res.message}`
              })
              if (res.data.seller_shop_id !== -1) {
                this.closeCoupon(res.data)
              } else {
                if (res.data.coupon_type === 'free_shipping') {
                  this.clearCodePlatformShipping()
                } else if (res.data.coupon_type === 'discount') {
                  this.clearCodePlatform()
                }
              }
              this.dialogAwaitConfirm = false
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3500,
                timerProgressBar: true,
                icon: 'error',
                text: this.$t('CheckOut.SystemErrorHasOccurredPleaseContactSupport')
              })
              this.backstep()
            }
            this.$store.commit('closeLoader')
          } else if (res.code === 400) {
            if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
              this.$store.commit('closeLoader')
              this.$EventBus.$emit('refreshToken')
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'error',
                text: res.message
              })
            }
            this.backstep()
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3500,
              timerProgressBar: true,
              icon: 'error',
              text: this.$t('CheckOut.SystemErrorHasOccurredPleaseContactSupport')
            })
          }
          if (res.message === 'Division by zero') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3500,
              timerProgressBar: true,
              icon: 'error',
              text: this.$t('CheckOut.SystemErrorHasOccurredPleaseContactSupport')
            })
            this.backstep()
          }
        }
      }
      this.$store.commit('closeLoader')
    },
    // error msg form OrderPurchaserCheck
    Cancel () {
      this.$router.go(-1)
    },
    goHomePage () {
      this.$router.push({ path: '/' }).catch(() => { })
    },
    goPoBuyerProfilePage () {
      if (!this.MobileSize) {
        // console.log(1)
        this.$router.push({ path: '/pobuyerProfile' }).catch(() => { })
      } else {
        // console.log(2)
        this.$router.push({ path: '/pobuyerProfileMobile' }).catch(() => { })
      }
    },
    async goProductDetail (item) {
      const nameCleaned = item.product_name.replace(/\s/g, '-')
      const routeData = this.$router.resolve({ name: 'DetailProduct', params: { data: `${nameCleaned}-${item.product_id}` } })
      window.location.assign(routeData.href, '_blank')
    },
    gotoShopDetail (name, id) {
      const shopCleaned = encodeURIComponent(name.replace(/\s/g, '-'))
      this.$router.push({ path: `/shoppage/${shopCleaned}-${id}` }).cache(() => {})
    }
  }
}
</script>

<style scoped>
::v-deep .ant-table-tbody > tr > td {
  padding: 14px 2px !important;
}
.prodcutsType {
  font-size: 14px;
  font-weight: 400;
  color: #FFFFFF;
}
.btnCart {
  max-height: 24px !important;
  max-width: 24px !important;
  min-width: 24px !important;
  border-radius: 50% !important;
  padding: 0 !important;
}
.top-container {
  position: absolute;
  top: -10px; /* ให้ปุ่มออกมาทับเส้นขอบ */
  left: 50%;
  transform: translateX(-50%);
  background-color: #ffffff;
  border-radius: 50%;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 12px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
::v-deep .custom-radio-checkout .v-input--selection-controls__input .v-icon {
  color: #27AB9C !important;
}
::v-deep .affix {
  width: 442.66px;
  position: fixed;
}
::v-deep .affix .v-Card {
  overflow-x: hidden;
  height: 80vh; /* ความสูงเมื่อ affix ทำงาน */
  overflow-y: auto;
  transition: height 0.3s ease;
}
::v-deep .v-expansion-panel-content__wrap {
  padding: 5px 24px 0px;
  flex: 1 1 auto;
  max-width: 100%;
}
.disChipClickCoupon {
  color: #989898 !important;
  border-color: #989898 !important;
}
.custom-scroll::-webkit-scrollbar {
  width: 10px;
  height: 50%;
  -webkit-overflow-scrolling: touch;
  -webkit-appearance: none;
}

.custom-scroll::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 10px;
  margin-top: 10px;
  margin-bottom: 10px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background: #27AB9C;
  border-radius: 10px;
  margin-top: 10px;
  margin-bottom: 10px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: #23998C;
  -webkit-overflow-scrolling: touch;
}
.custom-footer {
    background: #FFF8EB;
    box-shadow: 0px 0.5px 2px 0px #60617029;
    /* box-shadow: 0px 0px 1px 0px #28293D14; */
    border-bottom: 1px solid #F3F5F7 !important;
    border-left: 1px solid #F3F5F7 !important;
    border-right: 1px solid #F3F5F7 !important;
    position: relative;
    padding: 6px 16px;
    color: rgba(0, 0, 0, 0.85);
    border-top: 0px;
    border-radius: 0 0 8px 8px;
}
::v-deep .ant-table-footer {
    background: #FFFFFF;
    box-shadow: 0px 0.5px 2px 0px #60617029;
    box-shadow: 0px 0px 1px 0px #28293D14;
    border-bottom: 1px solid #F3F5F7 !important;
    border-left: 1px solid #F3F5F7 !important;
    border-right: 1px solid #F3F5F7 !important;
    position: relative;
    padding: 6px 16px;
    color: rgba(0, 0, 0, 0.85);
    border-top: 0px;
    border-radius: 0 0 8px 8px;
}
::v-deep .ant-table-body {
  transition: opacity 0.3s;
  border: 1px solid #F2F2F2;
  border-radius: 8px 8px 0px 0px;
  box-shadow: 0px 0.5px 2px 0px #60617029;
  box-shadow: 0px 0px 1px 0px #28293D14;
}
::v-deep .setBorderTable .ant-table-tbody > tr > td {
  border-bottom: 1px solid #F3F5F7 !important;
  transition: background 0.3s;
}
</style>

<style>
.ant-table-row-cell-break-word {
    align-content: start;
}
.ant-table-thead > tr > th {
  padding: 10px 16px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  text-align: left;
  background: #F2F9FF !important;
  border-bottom: 1px solid #e8e8e8;
  transition: background 0.3s ease;
}
.ant-table-column-title {
  color: #333333 !important;
  font-weight: 600;
  font-size: 14px;
}

.ant-time-picker-large .ant-time-picker-input {
  border-radius: 8px;
  border-color: rgba(0, 0, 0, 0.42);
  height: 40px;
  padding: 6px 11px;
  font-size: 16px;
}

.ant-time-picker-large .ant-time-picker-input:hover {
  border-color: rgba(0, 0, 0, 0.87);
}

.ant-time-picker-panel-inner {
  top: 40px;
}

.ant-time-picker-panel {
  width: 418px;
}

@media (max-width: 767px) {
  .ant-time-picker-panel {
    width: 300px;
  }
}
.ant-time-picker-panel-select:first-child {
  width: 50%;
}

.ant-time-picker-panel-select:last-child {
  width: 50%;
}

.ant-time-picker-panel-select ul {
  width: auto;
}

li.ant-time-picker-panel-select-option-selected {
  color: #27AB9C;
  font-weight: 600;
  background: rgba(39, 171, 156, 0.10);
}

.ant-time-picker-panel-select li {
  text-align: center;
  padding: 0 0 0 0px;
}

li.ant-time-picker-panel-select-option-selected:hover {
  color: #27AB9C;
  background: rgba(39, 171, 156, 0.10);
}

.ant-time-picker-panel-narrow .ant-time-picker-panel-input-wrap {
  display: none;
}

.ant-time-picker-input:focus {
  border-color: #27AB9C;
  border-width: 2px !important;
  box-shadow: none;
}

.ant-time-picker-panel-input:focus {
  border-color: #27AB9C;
  border-width: 2px !important;
  box-shadow: none;
}

.anticon svg {
  font-size: larger;
  color: #27AB9C;
  display: inline-block;
}
</style>

<style lang="css" scoped>
.m-auto {
  margin: auto;
}

.captionSku {
  font-size: 13px;
  font-style: normal;
  /* font-family: 'Prompt' !important; */
  /* font-weight: 500; */
}

.captionSkuMobile {
  font-size: 12px;
  font-style: normal;
  /* font-family: 'Prompt' !important; */
  /* font-weight: 500; */
}

.imageshow {
  max-width: 75px !important;
  width: 75px;
  height: 75px;
  cursor: pointer;
}

.imageshowIpadPro {
  max-width: 60px !important;
  width: 60px;
  height: 60px;
  cursor: pointer;
}

.totalPriceFont {
  font-size: 20px;
}

::v-deep .ant-table-pagination {
  display: none;
}

::v-deep .ant-table-header-column .ant-table-selection {
  display: none;
}

::v-deep .ant-table-thead>tr>th.ant-table-selection-column {
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}

::v-deep .ant-table-tbody>tr>td.ant-table-selection-column {
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}

::v-deep .ant-table-bordered .ant-table-thead>tr>th {
  border-top: 0px solid #e8e8e8 !important;
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}

::v-deep .ant-table-bordered .ant-table-body>table {
  border: 0px solid #e8e8e8;
}

::v-deep .ant-table-bordered .ant-table-tbody>tr>td {
  border: 0px solid #e8e8e8;
}

::v-deep .ant-table.ant-table-bordered .ant-table-title {
  border: 1px transparent;
  margin-bottom: 6px;
  border-radius: 8px;
}</style>

<style>
.custom-background .v-input__slot {
  background-color: #E6E6E6 !important;
}

.v-input--selection-controls .v-input__slot,
.v-input--selection-controls .v-radio {
  margin-bottom: 0px;
  cursor: pointer;
}</style>
<style lang="css" scoped>.v-Card {
  border-radius: 8px;
}

.Textcard {
  align-content: center;
  font-size: 16px;
  font-weight: 400;
  Line-height: 22.4px;
  padding-top: 24px !important;
  padding-right: 0px;
}
.TextcardMobileSize {
  font-size: 14px;
  font-weight: 400;
  Line-height: 22.4px;
  padding-top: 24px !important;
  padding-right: 0px;
}

.TextBaht {
  font-size: 16px;
  font-weight: 700;
  Line-height: 22.4px;
  padding-top: 24px !important;
}

.TextBahtMobileSize {
  font-size: 14px;
  font-weight: 700;
  Line-height: 22.4px;
  padding-top: 34.75px !important;
}
.is-disabled {
  pointer-events: none;
  opacity: 0.5;
}
</style>

<style scoped>
.custom-radio input:disabled + .v-input--selection-controls .v-input__control::before {
  color: #27AB9C !important; /* Set your desired color */
}
::v-deep .v-btn {
  text-transform: none;
}
</style>
