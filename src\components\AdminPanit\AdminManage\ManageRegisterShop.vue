<template>
  <v-container :style="MobileSize ? 'background-color: #ffffff' : ''" :class="MobileSize ? 'pa-3' : ''">
    <v-row dense class="pt-2">
      <v-col cols="12" md="6" sm="6" style="align-content: center;">
        <v-icon v-if="MobileSize" color="#27AB9C" class="mr-2" @click="backtoMenu()">mdi-chevron-left</v-icon>
        <span style="font-weight: bold; align-content: center;" :style="IpadSize ? 'font-size: 18px;' : MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'">รายชื่อร้านค้าที่สนใจเข้าร่วม</span>
      </v-col>
      <v-col cols="12" md="6" sm="6" style="display: flex; justify-content: end; align-items: center;">
        <v-btn rounded color="#38b2a4" @click="exportExcel()" class="white--text">EXPORT รายชื่อร้านค้าที่สนใจเข้าร่วม</v-btn>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12" md="6" sm="6">
        <v-text-field
          v-model="searchShop"
          placeholder="ค้นหาร้านค้า"
          dense
          outlined
          hide-details
        ></v-text-field>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <span style="font-size: 18px; font-weight: bold;">ทั้งหมด {{items.length}} รายการ</span>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <v-card>
          <v-data-table
            no-data-text="ไม่มีร้านค้าที่สนใจเข้าร่วม"
            no-results-text="ไม่พบร้านค้าที่สนใจเข้าร่วม"
            :headers="header"
            :items="items"
            style="white-space: nowrap;"
            :items-per-page="10"
            :search="searchShop"
            height="100%"
            :custom-filter="customSearch"
            item-key="id"
          >
            <template v-slot:[`item.index`]="{ index }">
              <span>{{index + 1}}</span>
            </template>
            <template v-slot:[`item.created_at`]="{ item }">
              <span>{{new Date(item.created_at.substring(0, 10)).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric" })}}</span>
            </template>
            <template v-slot:[`item.detail`]="{ item }">
              <v-btn color="#27ab9c" @click="gotoDetailPage(item.id)">
                <span class="white--text">รายละเอียด</span>
              </v-btn>
            </template>
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      searchShop: '',
      header: [
        { text: 'ลำดับ', value: 'index', width: '50', align: 'center', filterable: false, sortable: true, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้านค้า', value: 'shop_name', width: '50', align: 'center', filterable: true, sortable: true, class: 'backgroundTable fontTable--text' },
        { text: 'บุคคลธรรมดา', value: 'business_type', width: '50', align: 'center', filterable: true, sortable: true, class: 'backgroundTable fontTable--text' },
        { text: 'หมายเลขโทรศัพท์', value: 'phone_number', width: '50', align: 'center', filterable: true, sortable: true, class: 'backgroundTable fontTable--text' },
        { text: 'วันที่บันทึกข้อมูล', value: 'created_at', width: '50', align: 'center', filterable: true, sortable: true, class: 'backgroundTable fontTable--text' },
        { text: 'รายละเอียด', value: 'detail', width: '50', align: 'center', filterable: false, sortable: true, class: 'backgroundTable fontTable--text' }
      ],
      items: [],
      lazyOne: false
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ManageRegisterShopMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'manageOrderShipping')
        this.$router.push({ path: '/ManageRegisterShop' }).catch(() => {})
      }
    }
  },
  created () {
    this.getListRegisShop()
  },
  methods: {
    async exportExcel () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}seller/export/store-registration`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET',
        responseType: 'blob'
      }).then((response) => {
        // console.log(response)
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'store-registration.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    async getListRegisShop () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsStoreRegisList')
      var response = await this.$store.state.ModuleAdminManage.stateStoreRegisList
      this.$store.commit('closeLoader')
      // console.log(response, 'response')
      if (response.code === 200) {
        this.items = response.data
      }
    },
    gotoDetailPage (val) {
      if (!this.MobileSize) {
        this.$router.push({ path: `/ManageRegisterShopDetail?shopId=${val}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/ManageRegisterShopDetailMobile?shopId=${val}` }).catch(() => {})
      }
    },
    async backtoMenu () {
      if (!this.MobileSize) {
        window.scrollTo(0, 0)
        this.$router.push({ path: '/adminPanit' }).catch(() => {})
      } else {
        window.scrollTo(0, 0)
        this.$router.push({ path: '/adminPanitMobile' }).catch(() => {})
      }
    },
    customSearch (value, search, item) {
      return (
        item.shop_name.toLowerCase().includes(search.toLowerCase()) ||
        item.business_type.toLowerCase().includes(search.toLowerCase()) ||
        item.phone_number.includes(search)
      )
    }
  }
}
</script>

<style>

</style>
