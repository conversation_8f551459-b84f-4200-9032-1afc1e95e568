<template>
  <v-hover v-slot="{ hover }" v-if="itemProduct !== undefined" >
    <v-card v-if="itemProduct.id !== ''" class="card" :elevation="hover ? 6 : 0" :class="{ 'on-hover': hover }" style="cursor: pointer; border-radius: 8px;" @click="DetailProduct(itemProduct)">
      <div class="image-container" v-if="itemProduct.images_URL !== null && itemProduct.images_URL !== '' && itemProduct.images_URL !== undefined && itemProduct.images_URL.length !== 0">
        <v-img
          loading="lazy"
          v-lazyload
          :src="itemProduct.images_URL[0]"
          style="border-radius: 8px 8px 0px 0px;"
          height="188px"
          width="188px"
          max-height="188px"
          max-width="188px"
          alt="ImageFlashSaleProduct"
          class="base-image"
        >
          <template v-slot:placeholder>
            <v-row
              class="fill-height ma-0"
              align="center"
              justify="center"
            >
              <v-progress-circular
                indeterminate
                color="grey lighten-5"
              ></v-progress-circular>
            </v-row>
          </template>
        </v-img>
        <img v-if="itemProduct.etax === 'Y' && itemProduct.otop === 'N'" :src="require('@/assets/icon_etax.png')" alt="eTaxImage" class="overlay-image" />
        <img v-if="itemProduct.etax === 'N' && itemProduct.otop === 'Y'" :src="require('@/assets/OTOP.png')" alt="otopImage" class="overlay-image" />
        <img v-if="itemProduct.etax === 'Y' && itemProduct.otop === 'Y'" :src="require('@/assets/E-TAX_OTOP.png')" alt="eTaxOtopImage" class="overlay-image" />
      </div>
      <v-img
       src="@/assets/NoImage.png"
       contain
       v-else
       @click="DetailProduct(itemProduct)"
       style="border-radius: 8px 8px 0px 0px;"
       height="188px"
       width="188px"
       max-height="188px"
       max-width="188px">
      </v-img>
      <v-card-text class="pa-2">
        <!-- ชื่อสินค้า -->
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <h1 v-bind="attrs" v-on="on" class="mb-0" style="height: 43px; max-height: 46px; color: #0B1A35; width: 178px; font-size: 16px; font-weight: 400; -webkit-line-clamp: 2; -webkit-box-orient: vertical; display: -webkit-box; overflow: hidden; text-overflow: ellipsis;">
              {{ itemProduct.name }}
            </h1>
          </template>
          <span>{{ itemProduct.name }}</span>
        </v-tooltip>
        <v-row dense style="height: 24px;"></v-row>
        <!-- ราคาสินค้า -->
        <!-- <div v-if="(parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include) === itemProduct.fake_price || itemProduct.real_price === itemProduct.fake_price) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)">
          <span style="font-size: 18px; font-weight: 700; color: #1B5DD6;">฿ {{ Number( itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {maximumFractionDigits: 2}) }}</span>
        </div> -->
        <div v-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="priceDecrese" style="font-size: 12px; font-weight: 600;">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          <span class="pr-2" style="font-size: 18px; font-weight: 700; color: #F5222D;" v-if="itemProduct.discount_percent !== '0%'">{{itemProduct.discount_percent}}</span>
          <span class="specialPrice" style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {maximumFractionDigits: 2}) }}</span>
        </div>
        <div v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="priceDecrese" style="font-size: 12px; font-weight: 600;">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          <span class="pr-2" style="font-size: 18px; font-weight: 700; color: #F5222D;" v-if="itemProduct.discount_percent !== '0%'">{{itemProduct.discount_percent}}</span>
          <span class="specialPrice" style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.special_price) + parseFloat(itemProduct.vat_include) : itemProduct.special_price).toLocaleString(undefined, {maximumFractionDigits: 2}) }}</span>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 16px;">
          ติดต่อสอบถามเจ้าหน้าที่
        </span>
        <v-row dense style="height: 24px;"></v-row>
        <!-- ขายแล้ว -->
          <span style="display: flex; font-size: 12px; font-weight: 500; color: #F5222D; width: 100%; height: 100%; border-radius: 99px; background: #F6B1B266; justify-content: center;">ขายแล้ว {{ itemProduct.sold }}</span>
      </v-card-text>
      <!-- <v-tooltip bottom>
        <template v-slot:activator="{ on }">
          <v-row dense>
            <v-col cols="12" :class="MobileSize ? 'pa-0 mt-0' : 'pl-2 mt-1'">
              <p v-on="on" :style="MobileSize ? 'font-size: 12px; font-weight: bold; display: block; -webkit-box-orient: vertical; -webkit-line-clamp: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 2; color: #000000; height: 32px;' : 'font-size: 14px; font-weight: bold; display: block; -webkit-box-orient: vertical; -webkit-line-clamp: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; color: #000000; height: 32px;'" class="mb-0 mt-1 px-2 pl-3">{{ itemProduct.name }}</p>
            </v-col>
          </v-row>
        </template>
        <span>{{ itemProduct.name }}</span>
      </v-tooltip> -->
      <!-- <v-card-text class="pt-1">
        <v-row dense>
          <v-rating
            v-model="itemProduct.stars"
            color="#FB9300"
            background-color="#C4C4C4"
            empty-icon="$ratingFull"
            half-increments
            hover
            small
            dense
            readonly
          ></v-rating>
        </v-row>
      </v-card-text> -->
      <!-- <v-card-text class="pt-0 px-2 pb-0">
        <v-row dense>
          <v-col cols="12" :class="MobileSize ? 'mt-0 d-flex justify-center' : 'mt-0 d-flex justify-center'">
            <span :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 29px; color: #F5222D;' : 'font-weight: 600; font-size: 20px; line-height: 29px; color: #F5222D;'">฿ {{ Number(itemProduct.vat_default === 'yes' ? Number(itemProduct.real_price) + Number(itemProduct.vat_include) : itemProduct.real_price ).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          </v-col>
        </v-row>
      </v-card-text> -->
    </v-card>
    <v-card v-else height="340" :width="MobileSize ? 146 : 230" :elevation="hover ? 6 : 0" :class="{ 'on-hover': hover }" style="cursor: pointer; border-radius: 6px !important; border: 1px solid #B9DAF6 !important;" outlined >
      <!-- <v-row dense>
        <v-col cols="12" md="12" sm="12" xs="12" class="ma-2" v-if="itemProduct.quantity === 0 || itemProduct.quantity === null">
          <v-img src="@/assets/Tag/newPreorder.png" height="29" width="73" contain style="margin-left: -24px; margin-top: 0px;" ></v-img>
        </v-col>
        <v-col cols="6" md="6" sm="6" xs="6" class="pt-2 pb-0" v-else>
          <v-img src="@/assets/Tag/newSale.svg" height="29" width="79" contain style="margin-left: -6px; margin-top: 0px;" v-if="itemProduct.message_status === 'sale'"></v-img>
          <v-img src="@/assets/Tag/NewNew.svg" height="29" width="76" contain style="margin-left: -6px; margin-top: 0px;" v-else-if="itemProduct.message_status === 'new'"></v-img>
          <v-img src="@/assets/Tag/Suggest.svg" height="29" width="69" contain style="margin-left: -9px; margin-top: 0px;" v-else-if="itemProduct.message_status === 'recommend'"></v-img>
          <v-img src="@/assets/Tag/newPreorder.png" height="29" width="73" contain style="margin-left: -24px; margin-top: 0px;" v-else-if="itemProduct.message_status === 'pre-order'"></v-img>
          <v-img src="@/assets/Tag/BestSellerNew.svg" height="29" width="117" contain style="margin-left: -5px; margin-top: 0px;" v-else-if="itemProduct.message_status === 'best-seller'"></v-img>
        </v-col>
      </v-row> -->
      <div class="image-container" v-if="itemProduct.images_URL !== null && itemProduct.images_URL !== '' && itemProduct.images_URL !== undefined && itemProduct.images_URL.length !== 0">
        <v-img
        loading="lazy"
        :src="itemProduct.images_URL"
        :height="MobileSize || checkWidth === 1280 ? 123 : IpadProSize || IpadSize ? 200 : checkWidth >= 1900 ? 210 : 170"
        width="100%"
        contain
        class="align-start"
        style="position: relative; margin: auto;"
        >
        </v-img>
        <img v-if="itemProduct.etax === 'Y' && itemProduct.otop === 'N'" :src="require('@/assets/icon_etax.png')" alt="eTaxImage" class="overlay-image" />
        <img v-if="itemProduct.etax === 'N' && itemProduct.otop === 'Y'" :src="require('@/assets/OTOP.png')" alt="otopImage" class="overlay-image" />
        <img v-if="itemProduct.etax === 'Y' && itemProduct.otop === 'Y'" :src="require('@/assets/E-TAX_OTOP.png')" alt="eTaxOtopImage" class="overlay-image" />
      </div>
      <v-img src="@/assets/NoImage.png" :height="MobileSize || checkWidth === 1280 ? 123 : IpadProSize || IpadSize ? 200 : checkWidth >= 1900 ? 210 : 170" width="100%" contain v-else @click="DetailProduct(itemProduct)">
      </v-img>
      <v-tooltip bottom>
        <template v-slot:activator="{ on }">
          <!-- <v-card-text v-bind="attrs" v-on="on" style="font-size: 16px; font-weight: bold; line-height: 30px; color: #333333; word-break: keep-all;" class="pb-0 px-2 truncate">{{ itemProduct.name|truncate(52, '...') }}</v-card-text> -->
          <v-row dense>
            <v-col cols="12" class="pl-0">
              <p v-on="on" style="font-size: 14px; font-weight: bold; display: block; -webkit-box-orient: vertical; -webkit-line-clamp: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; color: #000000; height: 32px;" class="mb-0 mt-1 px-2 pl-3">{{ itemProduct.name }}</p>
            </v-col>
          </v-row>
          <!-- <v-card-text v-bind="attrs" v-on="on" style="font-size: 12px; font-weight: bold; line-height: 30px; color: #333333; max-height: 90px;" class="pb-0 px-2">{{ itemProduct.name|truncate(75, '...') }}</v-card-text> -->
        </template>
        <span>{{ itemProduct.name }}</span>
      </v-tooltip>
      <!-- <p class="pt-2 px-2 mb-1" style="font-size: 14px; line-height: 26px; color: #333333; height: 57px;" v-snip="2" v-if=" itemProduct.short_description !== null">{{ itemProduct.short_description }}</p> -->
      <!-- <v-card-text class="pt-2 mb-4 px-2" style="font-size: 14px; line-height: 26px; color: #333333; height: 57px; width: 214px; width: 144px; height: 45px; word-break: keep-all;" v-if=" itemProduct.short_description !== null">{{ itemProduct.short_description|truncate(34, '...') }}</v-card-text> -->
      <!-- <v-card-text class="pt-2 mb-4 px-2" style="font-size: 14px; line-height: 26px; color: #333333; height: 57px; width: 214px; width: 144px; height: 45px; word-break: keep-all;" v-else></v-card-text> -->
      <v-card-text class="py-0 pb-0 px-2 mb-3" style="height: 46px;">
        <div v-if="itemProduct.attribute !== null">
          <p style="font-size: 12px; line-height: 16px; font-weight: 400; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden; line-height: 24px; color: #333333; height: 40px;">{{ itemProduct.attribute }}</p>
        </div>
      </v-card-text>
      <v-card-text class="pt-1">
        <v-row dense>
          <v-rating
            v-model="itemProduct.stars"
            color="#FB9300"
            background-color="#C4C4C4"
            empty-icon="$ratingFull"
            half-increments
            hover
            small
            dense
            readonly
          ></v-rating>
        </v-row>
      </v-card-text>
      <v-card-text class="pt-0">
        <v-row dense>
          <!-- <v-col cols="12">
            <v-row dense class="pl-1">
              <span style="font-size: 16px; font-weight: 600; line-height: 19px; color: #636363; text-decoration-line: line-through;" class="pt-1">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              <v-chip v-if="itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0'" color="#FBE5E4" text-color="#FF0000" style="border-radius: 4px;" :class="MobileSize ? 'px-2 ml-1' : 'ml-2'" small><span class="discountText">ส่วนลด {{ itemProduct.discount_percent }}</span></v-chip>
            </v-row>
          </v-col> -->
          <v-col cols="12" class="mt-0">
            <span style="font-weight: 600; font-size: 24px; line-height: 29px; color: #F5222D;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          </v-col>
          <v-col cols="12">
            <span style="font-weight: 700; font-size: 14px; line-height: 16px; color: #FAAD14; letter-spacing: -0.2px;" v-if="itemProduct.count !== '-'">ขายแล้ว {{ kFormatter(itemProduct.count) }} ชิ้น</span>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-hover>
</template>

<script>
export default {
  props: ['itemProduct', 'pageCheck'],
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      discription: 'หมวกนิรภัยป้องกันอุบัติหมวกนิรภัยป้องกันอุบัติ',
      rating: 5,
      favorite: false,
      priceSame: false,
      oneData: [],
      pathProductDetail: '',
      path: process.env.VUE_APP_DOMAIN
    }
  },
  created () {
    // console.log(this.itemProduct)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    checkWidth () {
      return window.screen.width
    },
    checkWidthScreen () {
      if (window.screen.width <= 567) {
        return '145'
      } else if (window.screen.width <= 768) {
        return '152'
      } else if (window.screen.width <= 834) {
        return '166'
      } else if (window.screen.width <= 992) {
        return '170'
      } else if (window.screen.width <= 1024) {
        return '151'
      } else if (window.screen.width <= 1180) {
        return '182'
      } else if (window.screen.width <= 1200) {
        return '192'
      } else if (window.screen.width <= 1280) {
        return '142'
      } else {
        if (this.IpadSize) {
          return '148'
        } else if (window.screen.width >= 1600) {
          return '207.5'
        } else {
          return '191.5'
        }
      }
    },
    checkWidthScreenDetail () {
      if (window.screen.width <= 567) {
        return '145'
      } else if (window.screen.width <= 768) {
        return '160'
      } else if (window.screen.width <= 834) {
        return '175'
      } else if (window.screen.width <= 992) {
        return '170'
      } else if (window.screen.width <= 1024) {
        return '158'
      } else if (window.screen.width <= 1180) {
        return '189'
      } else if (window.screen.width <= 1200) {
        return '192'
      } else if (window.screen.width <= 1280) {
        return '149'
      } else {
        if (this.IpadSize) {
          return '156'
        } else if (window.screen.width >= 1600) {
          return '212.5'
        } else {
          return '196.5'
        }
      }
    }
  },
  methods: {
    kFormatter (num) {
      return Math.abs(num) > 999 ? Math.sign(num) * ((Math.abs(num) / 1000).toFixed(1)) + 'k' : Math.sign(num) * Math.abs(num)
    },
    DetailProduct (val) {
      if (val !== 'no') {
        const nameCleaned = val.name.replace(/\s/g, '-')
        this.$router.push({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.id}` } }).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>
.card {
  height: 100%;
  max-height: 346px;
  width: 100% !important;
  max-width: 188px;
  background-color: #FFFFFF;
}
.image-container {
  position: relative;
}

.base-image {
  width: 100%;
  height: 100%;
  z-index: 0;
}

.overlay-image {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
  height: 70%;
  width: 120px;
  max-width: 100%;
  max-height: 100%;
}
.discountText {
  font-weight: 400;
  font-size: 8px;
  line-height: 14px;
}
.priceDecrese {
  font-size: 12px;
  display: flex;
  text-decoration: line-through;
  color: #A1A1A1;
  font-weight: 400;
  margin-right: 0px;
}
.specialPrice {
  font-size: 18px;
  font-weight: 700;
  color: #333333;
}
</style>
