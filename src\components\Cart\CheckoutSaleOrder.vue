<template>
  <div>
    <!-- <v-overlay :value="overlay">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay> -->
    <v-breadcrumbs :items="items">
      <template v-slot:divider>
        <v-icon>mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item :href="item.href" :disabled="item.disabled">
          <span :style="{color: item.disabled === true ? '#27AB9C' : '#636363','font-size': '16px'}">{{ item.text
            }}</span>
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <v-divider class="mt-1"></v-divider>
    <v-row class="mt-1">
      <v-col cols="12" md="8">
        <v-row no-gutters>
          <!-- <v-col cols="12" class="mb-5">
            <v-card class="mt-3">
              <v-row no-gutters>
                <v-col cols="8" md="6" class="pl-5 pt-5 pb-0">
                  <p :style="MobileSize ? 'font-size: 20px;' : 'font-size: 24px;'"><b>ที่อยู่ในการจัดส่งสินค้า</b></p>
                </v-col>
                start แก้ไขที่อยู่
                <v-col cols="4" md="6" class="pt-5 text-right" v-if="Address !== 'คุณยังไม่ได้เพิ่มข้อมูลที่อยู่'">
                  <v-btn text rounded color="#333333" :class="MobileSize || IpadSize || IpadProSize ? 'mr-2' : 'mr-5'" small elevation="0" @click="editAddress(address_data)"><v-icon class="pr-1" small color="#A1A1A1">mdi-pencil</v-icon>แก้ไขที่อยู่</v-btn>
                </v-col>
                end แก้ไขที่อยู่
              </v-row>
              <v-col cols="12" align="left" class="pt-0 pl-5" v-if="Address !== 'คุณยังไม่ได้เพิ่มข้อมูลที่อยู่'">
                <v-radio-group v-model="selectTypeAddress" row>
                  <v-radio color="#27AB9C" label="รับสินค้าหน้าร้าน" value="Shop"></v-radio>
                  <v-radio color="#27AB9C" label="จัดส่งสินค้าปกติ" value="Normal"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col cols="12" align="left" class="pt-0 pl-5" v-if="Address === 'คุณยังไม่ได้เพิ่มข้อมูลที่อยู่'">
                <v-btn outlined icon small color="#A1A1A1">
                  <v-icon small color="#A1A1A1">mdi-plus</v-icon>
                </v-btn>
                <span class="pl-2"><b>เพิ่มที่อยู่ในการจัดส่งสินค้า</b></span>
              </v-col>
              <v-col cols="12" align="left" class="pt-0 pl-5" v-else>
                {{ Fullname }} {{ Address }}
              </v-col>
            </v-card>
          </v-col> -->
          <!-- Start เพิ่มที่อยู่ในการจัดส่งโดย ผู้ใช้งานภายในร้าน -->
          <!-- <v-col cols="12" class="mb-5" v-if="checkOwnShop === 'Y'">
            <v-card class="mt-3">
              <v-row no-gutters>
                <v-col cols="8" md="6" class="pl-5 pt-5 pb-0">
                  <p :style="MobileSize ? 'font-size: 20px;' : 'font-size: 24px;'"><b>ที่อยู่ในการจัดส่งสินค้าสำหรับลูกค้า</b></p>
                </v-col>
              </v-row>
              <v-col cols="12" align="left" class="pt-0 pl-5">
                <v-btn outlined icon small color="#A1A1A1" @click="openModalAddressCustomer()">
                  <v-icon small color="#A1A1A1">mdi-plus</v-icon>
                </v-btn>
                <span class="pl-2"><b>เพิ่มที่อยู่ในการจัดส่งสินค้า</b></span>
              </v-col>
            </v-card>
          </v-col> -->
          <!-- End เพิ่มที่อยู่ในการจัดส่งโดย ผู้ใช้งานภายในร้าน -->
          <!--start เพิ่มที่อยู่ในการจัดส่งใบกำกับภาษี -->
          <!-- <v-col cols="12" class="mb-5">
            <v-card style="padding-bottom: 15px">
              <v-col cols="12" class="pl-5 pt-5 pb-0">
                <p :style="MobileSize ? 'font-size: 24px;' : 'font-size: 24px;'">
                  <b>ที่อยู่ในการจัดส่งใบกำกับภาษี</b>
                </p>
              </v-col>
              <v-row dense no-gutters class="ml-4 pt-0">
                <v-col cols="12">
                  <v-radio-group v-model="taxRoles" row style="margin-top: -10px">
                    <v-radio color="#27AB9C" label="ผู้ใช้ทั่วไป" value="Personal" v-if="role.role !== 'purchaser'">
                    </v-radio>
                    <v-radio color="#27AB9C" label="นิติบุคคล" value="Business"></v-radio>
                    <v-radio color="#27AB9C" label="ไม่รับใบกำกับภาษี" value="No"></v-radio>
                  </v-radio-group>
                </v-col>
              </v-row>
              <v-col v-if="taxAddress === '' && taxRoles !== 'No'" cols="12" align="left" class="pt-2 pl-5">
                <v-btn outlined icon small color="#A1A1A1" @click="openModalTaxAddress()">
                  <v-icon color="#A1A1A1" small>mdi-plus</v-icon>
                </v-btn>
                <span class="pl-2"><b>เพิ่มที่อยู่ในการจัดส่งใบกำกับภาษี </b></span>
              </v-col>
              <v-col v-else-if="taxAddress !== '' && taxRoles !== 'No'" cols="12" align="left" class="pt-0 pl-5">
                {{ companyName }} {{ companyTaxID }} {{ taxAddress }}
              </v-col>
            </v-card>
          </v-col> -->
          <!--end เพิ่มที่อยู่ในการจัดส่งใบกำกับภาษี -->
          <!-- Desktop, ipadPro -->
          <v-col cols="12" md="12" v-if="!MobileSize">
            <v-card class="pa-5" style="border-radius: 8px;">
              <v-col cols="12" class="my-2 pt-0">
                <v-row dense>
                  <p style="font-size: 24px" class="mb-0"><b>รายการสั่งซื้อสินค้า</b></p>
                  <!-- <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 16px; margin-left: 10px;"></v-spacer> -->
                </v-row>
              </v-col>
              <!-- start ใบเสนอราคา และแก้ไขใบเสนอราคา Desktop -->
              <!-- <v-col v-show="checkAdminQU" cols="12" md="12" sm="12">
                <v-avatar min-width="32" width="32" height="32" color="#FFEFEE" style="border-radius: 4px;">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="22.86" height="22.86" contain></v-img>
                </v-avatar>
                <v-btn text style="color: #27AB9C; font-weight: bold;" :ripple="false" class="pl-0 pr-0 ml-2 hide-background-hover" @click="openQuotation()">ขอใบเสนอราคา</v-btn>
              </v-col> -->
              <!-- <v-col v-show="checkAdminQU && oneDataSpecial !== 'yes'" cols="12" md="12" sm="12">
                <v-avatar min-width="32" width="32" height="32" color="#FFEFEE" style="border-radius: 4px;">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="22.86" height="22.86" contain>
                  </v-img>
                </v-avatar>
                <v-btn text style="color: #27AB9C; font-weight: bold;" :ripple="false"
                  class="pl-0 pr-0 ml-2 hide-background-hover" @click="openQuotation()">ตัวอย่างใบเสนอราคา</v-btn>
              </v-col>
              <v-col v-if="role.role === 'purchaser' && oneDataSpecial !== 'yes'" cols="12" md="12" sm="12">
                <v-avatar min-width="32" width="32" height="32" color="#FFEFEE" style="border-radius: 4px;">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="22.86" height="22.86" contain>
                  </v-img>
                </v-avatar>
                <v-btn text style="color: #27AB9C; font-weight: bold;" :ripple="false"
                  class="pl-0 pr-0 ml-2 hide-background-hover"
                  @click="SelectCouponOrPoint ? openEditQU() : openModaleEditQUYesNo()">
                  ขอแก้ไขใบเสนอราคา
                </v-btn>
              </v-col>
              <v-col v-if="role.role === 'purchaser'" cols="12" md="12" sm="12">
                <v-avatar min-width="32" width="32" height="32" color="#FFEFEE" style="border-radius: 4px;">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="22.86" height="22.86" contain>
                  </v-img>
                </v-avatar>
                <v-btn text style="color: #27AB9C; font-weight: bold;" :ripple="false"
                  class="pl-0 pr-0 ml-2 hide-background-hover" @click="openModalPayment()">สร้างใบเสนอราคา</v-btn>
              </v-col> -->
              <!-- end ใบเสนอราคา และแก้ไขใบเสนอราคา Desktop -->
              <v-container grid-list-xs class="pt-0">
                <a-table v-for="(item, index) in itemsCart.choose_list" :key="index" :data-source="item.product_list"
                  :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                  :columns="headers" :pagination="false">
                  <template slot="title">
                    <v-row class="text-left">
                      <v-col justify="center">
                        <v-img class="float-left" src="@/assets/icon_image/store.png" width="24" height="24"></v-img>
                          <b class="ml-3"
                          style="line-height: 35px; cursor: pointer; font-size: 18px; font-weight: 600; color: #F4BC5F;"
                          @click="gotoShopDetail(item.product_list[0].seller_shop_name, item.product_list[0].seller_shop_id)">ร้านค้า: {{ item.product_list[0].seller_shop_name }}</b>
                        <v-btn v-if="role.role === 'sale_order_no_JV' && roleCustomer !== 'vendor'" class="float-end" color="#1B5DD6" text dark style="font-size:12px; font-weight: 600;"
                        @click="GetReviewQuotation()"><v-icon class="pr-2" size="20">mdi-file-document-outline</v-icon> <span
                          style="text-decoration-line: underline;">ตัวอย่างใบเสนอราคา</span></v-btn>
                        <v-btn v-if="role.role === 'sale_order_no_JV'" class="float-end" color="#1B5DD6" text dark style="font-size:12px; font-weight: 600;"
                        @click="EditQuotation()"><v-icon class="pr-2" size="20">mdi-file-document-outline</v-icon> <span
                          style="text-decoration-line: underline;">แก้ไขใบเสนอราคา</span></v-btn>
                      <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 6px;"></v-spacer>
                      </v-col>
                    </v-row>
                  </template>
                  <template slot="sku" slot-scope="text, record">
                    <v-col cols="12" class="pl-0">
                      <span style="font-size: 14px; font-weight: 600;">{{ record.sku }}</span>
                    </v-col>
                  </template>
                  <template slot="productdetails" slot-scope="text, record">
                    <v-row>
                      <v-col cols="12" md="4" sm="4" class="px-0" style="text-align: -webkit-center;">
                        <v-img :src="`${record.product_image}`" contain style="border-radius: 4px; padding-right: 8px;"
                          :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                          @click="goProductDetail(record)" />
                        <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                          style="border-radius: 4px; padding-right: 8px;" v-else @click="goProductDetail(record)" />
                      </v-col>
                      <v-col cols="12" md="8" sm="8" class="">
                        <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                        <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                          style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                            style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                        </p>
                        <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                          style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                            style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                        </p>
                        <!-- <p class="mb-0 captionSku" style="font-size: 10px;">ราคา: {{ Number(record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</p>
                        <p class="mb-0 captionSku" style="font-size: 10px;">ราคา: {{ Number(record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</p> -->
                        <!-- start detail product attribute Desktop -->
                        <!-- <span v-if="record.product_attribute_detail.attribute_priority_1"
                          class="mb-0 captionSku">{{record.key_1_value}}:
                          {{record.product_attribute_detail.attribute_priority_1}}</span>
                        <span v-if="record.product_attribute_detail.attribute_priority_2"
                          class="pl-2 mb-0 captionSku">{{record.key_2_value}}:
                          {{record.product_attribute_detail.attribute_priority_2}}</span> -->
                        <!-- end detail product attribute Desktop -->
                      </v-col>
                    </v-row>
                  </template>
                  <template slot="revenue_default" slot-scope="text, record">
                    <v-col cols="12">
                      <span style="font-size: 14px; font-weight: 600;">{{ Number(record.vat_default === 'yes' ? parseFloat(record.revenue_default) + parseFloat(record.vat_include):record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
                    </v-col>
                  </template>
                  <template slot="quantity" slot-scope="text, record">
                    <span style="font-size: 14px; font-weight: 600;">{{ Number(record.quantity).toLocaleString() }}</span>
                  </template>
                  <template slot="total_revenue_default" slot-scope="text, record">
                    <span v-if="record.vat_default === 'no' && roleCustomer !== 'vendor'" style="font-size: 14px; font-weight: 600;">{{
                      Number(record.show_price).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                    <span v-else style="font-size: 14px; font-weight: 600;">{{
                      Number(record.show_price).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                  </template>
                  <template slot="item_code_pr" slot-scope="text, record">
                    <v-row v-if="roleCustomer === 'vendor'">
                      <v-col>
                        <v-autocomplete  v-model="record.item_code_pr_buyer" :items="itemCodePrList" outlined dense
                          style="height: 50px;" item-text="material_name" item-value="material_code"
                          @change="updateSelectPr()" no-data-text="ไม่พบ Item Code PR"></v-autocomplete>
                      </v-col>
                    </v-row>
                    <!-- <v-row v-else>
                      <v-col>
                        <v-text-field v-model="record.item_code_pr_buyer" outlined dense style="height: 50px;" ></v-text-field>
                      </v-col>
                    </v-row> -->
                  </template>
                </a-table>
                <a-table v-if="itemsCart.product_free.length !== 0" :data-source="itemsCart.product_free"
                  :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                  :columns="headers" :pagination="false">
                  <template slot="title">
                    <v-row class="text-left">
                      <v-col justify="center">
                        <v-img class="float-left" src="@/assets/icons/nullproduct.png" width="30" height="30"></v-img>
                          <b class="ml-2"
                          style="line-height: 35px; font-size: 18px; font-weight: 600; color: #F4BC5F;">แถมฟรี</b>
                      </v-col>
                    </v-row>
                  </template>
                  <template slot="sku" slot-scope="text, record">
                    <v-col cols="12" class="pl-0">
                      <span style="font-size: 14px; font-weight: 600;">{{ record.sku }}</span>
                    </v-col>
                  </template>
                  <template slot="productdetails" slot-scope="text, record">
                    <v-row>
                      <v-col cols="12" md="4" sm="4" class="px-0" style="text-align: -webkit-center;">
                        <v-img :src="`${record.product_image}`" contain style="border-radius: 4px; padding-right: 8px;"
                          :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                          @click="goProductDetail(record)" />
                        <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                          style="border-radius: 4px; padding-right: 8px;" v-else @click="goProductDetail(record)" />
                      </v-col>
                      <v-col cols="12" md="8" sm="8" class="">
                        <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                        <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                          style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                            style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                        </p>
                        <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                          style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                            style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                        </p>
                      </v-col>
                    </v-row>
                  </template>
                  <template slot="revenue_default">
                    <v-col cols="12">
                      <span style="font-size: 14px; font-weight: 600;">0</span>
                    </v-col>
                  </template>
                  <template slot="quantity" slot-scope="text, record">
                    <span style="font-size: 14px; font-weight: 600;">{{ Number(record.quantity).toLocaleString() }}</span>
                  </template>
                  <template slot="total_revenue_default" slot-scope="text, record">
                    <span v-if="record.vat_default === 'no'" style="font-size: 14px; font-weight: 600;">0</span>
                    <span v-else style="font-size: 14px; font-weight: 600;">0</span>
                  </template>
                </a-table>
                <v-card v-if="role.role !== 'sale_order' && roleCustomer !== 'vendor'" class="pl-2 my-3">
                  <v-row class="align-center">
                    <v-col :cols="MobileSize ? '6' : '3'">
                      <span>คูปองส่วนลดร้านค้า</span>
                    </v-col>
                    <v-col :cols="MobileSize ? '6' : '3'">
                      <v-chip v-if="CouponData.filter(coupon => coupon.seller_shop_id !== -1).length === 0" color="#F56E22" @click="clickCoupon('', PointData, XBaht, itemsCart.choose_list[0])" outlined class="pr-5"><span class="px-2">คูปองส่วนลด</span>
                        <v-icon>mdi-ticket-percent-outline</v-icon>
                      </v-chip>
                      <v-chip v-else :color="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount' ? '#FFCDD2' : CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping' ? '#FFEACC' : '#FFECB4'"
                        :style="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount' ? 'color: red;' : CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping' ? 'color: #FF9800;' : 'color: #FFC107;'">
                        <span v-if="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount'" class="px-2 d-inline-block text-truncate" :style="IpadSize ? 'max-width: 150px;' : ''">ส่วนลด {{CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_type === 'baht' ? `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount} บาท` : `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount}%`}}</span>
                        <span v-else-if="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping'" class="px-2 d-inline-block text-truncate" :style="IpadSize ? 'max-width: 150px;' : ''">ส่วนลดค่าขนส่ง {{CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_type === 'baht' ? `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount} บาท` : `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount}%`}}</span>
                        <span v-else class="px-2">แถมฟรี</span>
                        <v-icon small @click="closeCoupon(itemsCart.choose_list[0])" :style="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount' ? 'color: red;' : CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping' ? 'color: #FF9800;' : 'color: #FFC107;'">mdi-close</v-icon>
                      </v-chip>
                    </v-col>
                  <v-col :cols="MobileSize ? '6' : '3'">
                    <span>แต้มส่วนลดร้านค้า</span>
                  </v-col>
                  <v-col :cols="MobileSize ? '6' : '3'" v-if="PointData === 0">
                    <v-chip :disabled="itemsCart.seller_use_point === 'no'" :class="itemsCart.seller_use_point === 'no' ? 'disChipClickCoupon' : ''" class="mr-3" v-if="CouponData.length !== 0" color="#FFC107" @click="clickPoint(CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_id, PointData, XBaht, itemsCart.total_coupon_discount, itemsCart.choose_list[0])" outlined>
                    <span class="px-2">แต้มส่วนลด</span>
                    </v-chip>
                    <v-chip :disabled="itemsCart.seller_use_point === 'no'" :class="itemsCart.seller_use_point === 'no' ? 'disChipClickCoupon' : ''" class="mr-3" v-else color="#FFC107" @click="clickPoint('', PointData, XBaht, itemsCart.total_coupon_discount, itemsCart.choose_list[0])" outlined>
                    <span class="px-2">แต้มส่วนลด</span>
                    </v-chip>
                  </v-col>
                  <v-col :cols="MobileSize ? '6' : '3'" v-if="PointData !== 0">
                    <v-chip color="#FFECB4" style="color: #FFC107;">
                    <span class="px-2">แต้มที่ใช้ {{PointData / XBaht}} แต้ม</span>
                      <v-icon small @click="closePoint()" style="color: #FFC107;">mdi-close</v-icon>
                    </v-chip>
                  </v-col>
                  </v-row>
                </v-card>
                <!-- <v-card v-if="role.role !== 'ext_buyer' && itemsCart.choose_list[0].pay_type !== 'general'" class="mt-6 pa-4" color="#F9FAFD" elevation="0" -->
                <v-card v-if="role.role === 'sale_order_no_JV' && roleCustomer === 'vendor'" class="mt-6 pa-4" color="#F9FAFD" elevation="0"
                style="border-radius: 8px;">
                <v-img class="float-left" src="@/assets/shopping1.png" width="30" height="30"></v-img>
                <b class="ml-3"
                  style="line-height: 35px; font-size: 18px; font-weight: 600; color: #000000;">รายละเอียดรายการสั่งซื้อ</b>
                <v-row class="pt-5 text-left d-block">
                  <v-col>
                    <span style="font-size: 16px; font-weight: 400;">PayType :
                      <v-chip v-if="itemsCart.choose_list[0].pay_type === 'onetime'" small
                        style="color: #1B5DD6; background: rgba(27, 93, 214, 0.10); font-size: 14px; padding: 2px 12px;">{{ itemsCart.choose_list[0].pay_type
                          === 'onetime' ? 'One Time' : itemsCart.choose_list[0].pay_type }}</v-chip>
                      <v-chip v-else small
                        style="color: #FF710B; background: rgba(255, 113, 11, 0.10); padding: 2px 12px; font-size: 14px;">{{ itemsCart.choose_list[0].pay_type
                          === 'recurring' ? 'Recurring' : itemsCart.choose_list[0].pay_type }}</v-chip>
                    </span>
                  </v-col>
                </v-row>
                <v-form class="mt-3" ref="orderListDetails" :lazy-validation="lazy">
                  <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                    <v-row v-if="chooseitem.product_list.some(productitem => (productitem.product_type !== 'general' && itemsCart.is_JV === 'no') || (productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes') || (productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'no' ) || (productitem.product_type === 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'no' && itemsCart.choose_list[0].pay_type !== 'onetime') || (productitem.product_type === 'general' && itemsCart.is_JV === 'no' && itemsCart.is_JV_buyer === 'yes' && itemsCart.choose_list[0].pay_type !== 'onetime') || (productitem.product_type === 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes' && itemsCart.choose_list[0].pay_type !== 'onetime'))" dense style="justify-content: space-between;">
                      <v-col col="6" class="pb-0" >
                        <span style="line-height: 24px; font-size: 16px; color: #333333;">วันที่เริ่มสัญญา <span
                            style="color: red;">*</span></span>
                        <v-dialog ref="dialogContractStartDate" v-model="modalContractStartDate" persistent width="290px">
                          <template v-slot:activator="{ on, attrs }">
                            <v-text-field readonly style="border-radius: 8px;" v-model="contractStartDate" v-bind="attrs"
                              v-on="on" outlined dense placeholder="วว/ดด/ปปปป"><v-icon slot="append"
                                color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
                          </template>
                          <v-date-picker color="#27AB9C" v-model="date" scrollable reactive locale="Th-th">
                            <v-spacer></v-spacer>
                            <v-btn text color="primary" @click="closeModalContractStartDate()">
                              ยกเลิก
                            </v-btn>
                            <v-btn text color="primary" @click="setValueContractStartDate(date, chooseindex)">
                              ตกลง
                            </v-btn>
                          </v-date-picker>
                        </v-dialog>
                      </v-col>
                      <v-col cols="6" class="pb-0">
                        <span style="line-height: 24px; font-size: 16px; color: #333333;">วันที่สิ้นสุดสัญญา <span
                            style="color: red;">*</span></span>
                        <v-dialog ref="dialogContractEndDate" v-model="modalContractEndDate" persistent width="290px">
                          <template v-slot:activator="{ on, attrs }">
                            <v-text-field style="border-radius: 8px;"
                              :disabled="searchContractStartDate !== '' ? false : true" readonly v-model="contractEndDate"
                              v-bind="attrs" v-on="on" outlined dense placeholder="วว/ดด/ปปปป"><v-icon slot="append"
                                color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
                          </template>
                          <v-date-picker color="#27AB9C" v-model="date1" scrollable reactive locale="Th-th"
                            :min="setMinDateContractEndDate">
                            <v-spacer></v-spacer>
                            <v-btn text color="primary" @click="closeModalContractEndDate()">
                              ยกเลิก
                            </v-btn>
                            <v-btn text color="primary" @click="setValueContractEndDate(date1, chooseindex)">
                              ตกลง
                            </v-btn>
                          </v-date-picker>
                        </v-dialog>
                      </v-col>
                    </v-row>
                  </div>
                  <v-col class="pa-0 mb-4">
                    <v-checkbox v-model="contractSet" :readonly="this.itemsCart.total_price_no_vat >= 50000"
                      label="ต้องการระบุสัญญาบริการ" :value="contractSet ? true : false">
                    </v-checkbox>
                  </v-col>
                  <span style="line-height: 24px; font-size: 16px; color: #333333;">หมายเหตุ</span>
                  <v-textarea style="border-radius: 8px;" v-model="reason" outlined
                    placeholder="กรุณาระบุ"></v-textarea>
                </v-form>
              </v-card>
              </v-container>
            </v-card>
            <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
              <v-card class="pa-4" style="border-radius: 8px; margin-top: 24px" v-if="chooseitem.product_list.some(productitem => (productitem.product_type === 'general' && role.role === 'sale_order_no_JV'))">
                <div>
                  <p style="font-size: 24px"><b>ที่อยู่ในการจัดส่งสินค้าของลูกค้า</b></p>
                </div>
                <v-row dense>
                  <v-row>
                    <v-col>
                      <v-radio-group v-model="radios" row class="mt-0">
                        <v-col :cols="IpadSize ? 4 : 3">
                          <span style="font-size: 16px; font-weight: 600; ">รูปแบบการจัดส่ง</span>
                        </v-col>
                        <v-col cols="4" v-if="itemsCart.store_front === 'yes'">
                          <v-radio value="radio-1" @click="ClearRadio()"><template v-slot:label>
                              <span style="font-size: 16px;">รับสินค้าหน้าร้าน</span>
                            </template>
                          </v-radio>
                        </v-col>
                        <v-col cols="4">
                          <v-radio value="radio-2" @click="ClearRadio()"><template v-slot:label>
                              <span tyle="font-size: 16px;">จัดส่งสินค้า</span>
                            </template>
                          </v-radio>
                        </v-col>
                      </v-radio-group>
                      <v-spacer style="border-top: 2px solid #EBEBEB;"></v-spacer>
                      <div v-if="showAddress === false">
                        <div v-for="(item, index) in userdetail" :key="index">
                          <div style="margin-top: 16px;" v-if="radios === 'radio-2' && item.default_address === 'Y'">
                            <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                            <span style="font-size: 16px; font-weight: 600;">{{ roleCustomer !== 'general' ? item.name : item.first_name + ' ' + item.last_name }}</span>
                            <span class="px-1" style="color: #EBEBEB;">|</span>
                            <span style="font-size: 16px; font-weight: 600;">{{ item.phone }}</span>
                            <v-btn class="float-end" color="#27AB9C" text dark
                              style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="changeAddress()"><span
                                style="text-decoration-line: underline;">เปลี่ยนที่อยู่</span></v-btn>
                              <v-col class="mt-2 pl-0 mb-4 ml-8 pr-10">
                                <span style="font-size: 16px;">{{ item.detail_address }} {{ item.sub_district }} {{ item.district
                                }} {{ item.province }} {{ item.postcode }}</span>
                              </v-col>
                          </div>
                        </div>
                      </div>
                      <div v-if="radios === 'radio-1'">
                        <div style="margin-top: 16px;" v-for="(item, index) in itemsCart.choose_list" :key="index">
                          <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                          <span style="font-size: 16px; font-weight: 600;">{{ item.product_list[0].seller_shop_name }}</span>
                          <span class="px-1" style="color: #EBEBEB;">|</span>
                          <span style="font-size: 16px; font-weight: 600;">{{ item.shipping_detail.data_seller_address[0].phone !== undefined ? item.shipping_detail.data_seller_address[0].phone : '-' }}</span>
                            <v-col class="mt-2 pl-0 mb-4 ml-8 pr-10">
                              <span style="font-size: 16px;"> บ้านเลขที่ {{ item.shipping_detail.data_seller_address[0].house_no}} {{ item.shipping_detail.data_seller_address[0].detail }} ตำบล/แขวง {{ item.shipping_detail.data_seller_address[0].sub_district }}
                                อำเภอ/เขต {{ item.shipping_detail.data_seller_address[0].district }} จังหวัด {{ item.shipping_detail.data_seller_address[0].province }} {{ item.shipping_detail.data_seller_address[0].zipcode }}
                                </span>
                            </v-col>
                          <v-row>
                            <v-col cols="6">
                              <span style="line-height: 24px; font-size: 16px; color: #333333;">วันรับสินค้า <span
                                  style="color: red;">*</span></span>
                              <v-menu v-model="menu" :close-on-content-click="false" :nudge-right="40"
                                transition="scale-transition" offset-y min-width="auto">
                                <template v-slot:activator="{ on, attrs }">
                                  <v-text-field v-model="contractDate" style="border-radius: 8px;" outlined dense
                                    placeholder="วว/ดด/ปปปป" readonly v-bind="attrs" v-on="on">
                                    <v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon>
                                  </v-text-field>
                                </template>
                                <v-date-picker v-model="dates" @input="menu = false, setValueDate(dates)" locale="Th-th"
                                  scrollable no-title :min="today" :max="futureDate"></v-date-picker>
                              </v-menu>
                            </v-col>
                            <v-col cols="6" :class="{ 'is-disabled': dates === '' }">
                              <span style="line-height: 24px; font-size: 16px; color: #333333;">เวลารับสินค้า <span style="color: red;">*</span></span>
                              <v-col class="pt-0 pl-0" v-if="dates === today">
                                <template>
                                  <a-space direction="vertical" style="width: 100%;">
                                    <a-time-picker v-model="timeselecttoday" :bordered="false" style="width: 100%;" format="HH:mm น." size="large" placeholder="00.00 น."  :disabledHours="disabledHours" :disabledMinutes="disabledMinutes" />
                                    <a-time-range-picker :bordered="false" style="width: 100%;" />
                                  </a-space>
                                </template>
                              </v-col>
                              <v-col class="pt-0 pl-0" v-else>
                                <template>
                                  <a-space direction="vertical" style="width: 100%;">
                                    <a-time-picker v-model="timeselect" :bordered="false" style="width: 100%;" format="HH:mm น." size="large" placeholder="00.00 น." />
                                    <a-time-range-picker :bordered="false" style="width: 100%;" />
                                  </a-space>
                                </template>
                              </v-col>
                            </v-col>
                          </v-row>
                        </div>
                      </div>
                    </v-col>
                  </v-row>
                </v-row>
              </v-card>
            </div>
            <v-card class="pa-4" style="border-radius: 8px; margin-top: 24px" v-if="role.role === 'sale_order_no_JV'">
                <div>
                  <p class="pb-0 mb-0" style="font-size: 24px"><b>ขอเอกสารเพิ่มเติม</b></p>
                  <p class="pt-0 mt-0" style="font-size: 16px; color: red;">*เอกสารใช้ที่อยู่เดียวกับใบกำกับภาษี</p>
                </div>
                <v-row dense>
                  <v-col cols="4">
                    <v-checkbox disabled class="mt-0" v-model="radioInvoice" label="ใบแจ้งหนี้" true-value="radioinvoice" false-value="">
                    </v-checkbox>
                  </v-col>
                  <v-col cols="4">
                    <v-checkbox :disabled="itemsCart.isEtax === 'no'" class="mt-0" v-model="radioTax" label="ใบกำกับภาษี" true-value="radiotax" false-value="">
                    </v-checkbox>
                  </v-col>
                </v-row>
                <div>
                  <div v-if="showInvoiceAddress === false">
                    <v-spacer class="mt-2" style="border-top: 2px solid #EBEBEB;"></v-spacer>
                    <div style="margin-top: 16px;" v-for="(item, index) in invoicedetail" :key="index">
                      <div v-if="item.default_address === 'Y'">
                        <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="24" height="24"></v-img>
                        <span style="font-size: 16px; font-weight: 600;">{{item.name}}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span style="font-size: 16px; font-weight: 600; color: #27AB9C;">{{item.phone}}</span>
                        <v-btn class="float-end" color="#27AB9C" text dark
                              style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="DialogTaxAddress = !DialogTaxAddress"><span
                                style="text-decoration-line: underline;">เปลี่ยนที่อยู่</span></v-btn>
                        <!-- <v-col class="ml-6 py-0 pt-2">
                          <span style="font-size: 16px;">รหัสสาขา: <b>{{item.branch_id}}</b></span>
                        </v-col> -->
                        <v-col class="ml-6 py-0 pt-3">
                          <span style="font-size: 16px;">เลขประจำตัวผู้เสียภาษี: <b>{{item.tax_id}}</b></span>
                        </v-col>
                        <v-col class="ml-6 pt-1 pr-10">
                          <span style="font-size: 16px;">ที่อยู่: <b>{{item.detail_address}} {{item.sub_district}} {{item.district}} {{item.province}} {{item.postcode}}</b> </span>
                        </v-col>
                        </div>
                      </div>
                  </div>
                  </div>
              </v-card>
          </v-col>
          <!-- Mobile Ipad -->
          <v-col cols="12" md="12" v-if="MobileSize">
            <v-card class="px-1 py-2" style="border-radius: 8px;">
              <v-col cols="12" class="my-2 pt-0">
                <v-row dense>
                  <p style="font-size: 18px" class="mb-0"><b>รายการสั่งซื้อสินค้า</b></p>
                </v-row>
              </v-col>
              <!-- start ใบเสนอราคา และแก้ไขใบเสนอราคา mobile -->
              <!-- <v-col v-show="checkAdminQU" cols="12" md="12" sm="12">
                <v-avatar min-width="32" width="32" height="32" color="#FFEFEE" style="border-radius: 4px;">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="22.86" height="22.86" contain></v-img>
                </v-avatar>
                <v-btn text style="color: #00B500; font-weight: bold;" :ripple="false" class="pl-0 pr-0 ml-2 hide-background-hover" @click="openQuotation()">ขอใบเสนอราคา</v-btn>
              </v-col> -->
              <!-- <v-col v-show="checkAdminQU && oneDataSpecial !== 'yes'" cols="12" md="12" sm="12">
                <v-avatar min-width="32" width="32" height="32" color="#FFEFEE" style="border-radius: 4px;">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="22.86" height="22.86" contain>
                  </v-img>
                </v-avatar>
                <v-btn text style="color: #27AB9C; font-weight: bold;" :ripple="false"
                  class="pl-0 pr-0 ml-2 hide-background-hover" @click="openQuotation()">ตัวอย่างใบเสนอราคา</v-btn>
              </v-col>
              <v-col v-if="role.role === 'purchaser' && oneDataSpecial !== 'yes'" cols="12" md="12" sm="12">
                <v-avatar min-width="32" width="32" height="32" color="#FFEFEE" style="border-radius: 4px;">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="22.86" height="22.86" contain>
                  </v-img>
                </v-avatar>
                <v-btn text style="color: #27AB9C; font-weight: bold;" :ripple="false"
                  class="pl-0 pr-0 ml-2 hide-background-hover"
                  @click="SelectCouponOrPoint ? openEditQU() : openModaleEditQUYesNo()">ขอแก้ไขใบเสนอราคา
                </v-btn>
              </v-col>
              <v-col v-if="role.role === 'purchaser'" cols="12" md="12" sm="12">
                <v-avatar min-width="32" width="32" height="32" color="#FFEFEE" style="border-radius: 4px;">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="22.86" height="22.86" contain>
                  </v-img>
                </v-avatar>
                <v-btn text style="color: #27AB9C; font-weight: bold;" :ripple="false"
                  class="pl-0 pr-0 ml-2 hide-background-hover" @click="openModalPayment()">สร้างใบเสนอราคา</v-btn>
              </v-col> -->
              <!-- end ใบเสนอราคา และแก้ไขใบเสนอราคา mobile-->
              <v-container grid-list-xs class="pt-0">
                <a-table v-for="(item, index) in itemsCart.choose_list" :key="index" :data-source="item.product_list"
                  :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                  :columns="headersMobile" :pagination="false">
                  <template slot="title">
                    <v-row class="text-left">
                      <v-col align="center">
                        <v-img class="float-left" src="@/assets/icon_image/store.png" width="24" height="24"></v-img>
                        <b class="float-left ml-2 d-inline-block text-truncate"
                          style="line-height: 35px; cursor: pointer; font-size: 14px; font-weight: 600; color: #F4BC5F; max-width: 90px"
                          @click="gotoShopDetail(item.product_list[0].seller_shop_name, item.product_list[0].seller_shop_id)">ร้านค้า: {{ item.product_list[0].seller_shop_name }}</b>
                        <v-btn v-if="role.role === 'sale_order_no_JV' && roleCustomer !== 'vendor'" class="float-end" color="#1B5DD6" small text dark style="font-size:12px; font-weight: 600;"
                          @click="GetReviewQuotation()"><v-icon class="pr-1" size="20">mdi-file-document-outline</v-icon> <span
                            style="text-decoration-line: underline;">ตัวอย่างใบเสนอราคา</span></v-btn>
                        <v-btn v-if="role.role === 'sale_order_no_JV'" class="float-end" color="#1B5DD6" small text dark style="font-size:12px; font-weight: 600;"
                          @click="EditQuotation()"><v-icon class="pr-1" size="20">mdi-file-document-outline</v-icon> <span
                            style="text-decoration-line: underline;">แก้ไขใบเสนอราคา</span></v-btn>
                          </v-col>
                        </v-row>
                        <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 6px;"></v-spacer>
                  </template>
                  <template slot="productdetails" slot-scope="text, record">
                    <v-row >
                      <v-col cols="4" md="4" class="pr-2">
                        <v-img :src="`${record.product_image}`" contain
                          :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                          @click="goProductDetail(record)" />
                        <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-else
                          @click="goProductDetail(record)" />
                      </v-col>
                      <v-col cols="8" md="8">
                        <p class="mb-0 captionSku">รหัสสินค้า: <b style="font-size: 14px;">{{ record.sku }}</b></p>
                        <!-- start detail product attribute mobile -->
                        <!-- <span v-if="record.product_attribute_detail.attribute_priority_1"
                          class="mb-0 captionSku">{{record.key_1_value}}:
                          {{record.product_attribute_detail.attribute_priority_1}}</span>
                        <span v-if="record.product_attribute_detail.attribute_priority_2"
                          class="pl-2 mb-0 captionSku">{{record.key_2_value}}:
                          {{record.product_attribute_detail.attribute_priority_2}}</span> -->
                        <!-- end detail product attribute mobile -->
                        <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                        <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                          style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                            style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                        </p>
                        <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                          style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                            style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                        </p>
                        <p class="mb-0 captionSku">ราคาต่อชิ้น: <b style="font-size: 14px;"> {{ Number(record.vat_default === 'yes' ? parseFloat(record.revenue_default) + parseFloat(record.vat_include):record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</b></p>
                        <p class="mb-0 captionSku">จำนวน: <b style="font-size: 14px;">{{ record.quantity }}</b></p>
                        <p v-if="record.vat_default === 'no' && roleCustomer !== 'vendor'" class="mb-0 captionSku">ราคารวม: <b style="font-size: 14px;">{{Number(record.show_price).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</b>
                          <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span>
                        </p>
                        <p v-else class="mb-0 captionSku">ราคารวม: <b style="font-size: 14px;">{{Number(record.show_price).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</b>
                          <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span>
                        </p>
                        <!-- <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                          <div v-if="chooseitem.product_list.some(productitem => role.role !== 'ext_buyer' && itemsCart.is_JV !== 'no' && productitem.product_type !== 'general' && itemsCart.is_JV_buyer === 'yes')" >
                          <div v-if="role.role !== 'ext_buyer'">
                            <p class="mb-0 captionSku">Item Code PR:</p>
                            <v-autocomplete class="mt-1" v-model="record.item_code_pr_buyer" :items="itemCodePrList" outlined
                              dense style="height: 50px; width: 60%;" item-text="material_name" item-value="material_code"
                              @change="updateSelectPr()" no-data-text="ไม่พบ Item Code PR"></v-autocomplete>
                          </div>
                          <div v-else>
                            <p class="mb-0 captionSku">Item Code PR:</p>
                            <v-text-field v-model="record.item_code_pr_buyer" outlined dense style="height: 50px;  width: 60%;" ></v-text-field>
                          </div>
                        </div>
                        </div> -->
                      </v-col>
                    </v-row>
                  </template>
                </a-table>
                <a-table v-if="itemsCart.product_free.length !== 0" :data-source="itemsCart.product_free"
                  :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                  :columns="headersMobile" :pagination="false">
                  <template slot="title">
                    <v-row class="text-left">
                      <v-col align="center">
                        <v-img class="float-left" src="@/assets/icons/nullproduct.png" width="24" height="24"></v-img>
                        <b class="float-left ml-2 d-inline-block text-truncate"
                          style="line-height: 35px; cursor: pointer; font-size: 14px; font-weight: 600; color: #F4BC5F; max-width: 90px">แถมฟรี</b>
                          </v-col>
                        </v-row>
                        <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 6px;"></v-spacer>
                  </template>
                  <template slot="productdetails" slot-scope="text, record">
                    <v-row >
                      <v-col cols="4" md="4" class="pr-2">
                        <v-img :src="`${record.product_image}`" contain
                          :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                          @click="goProductDetail(record)" />
                        <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-else
                          @click="goProductDetail(record)" />
                      </v-col>
                      <v-col cols="8" md="8">
                        <p class="mb-0 captionSku">รหัสสินค้า: <b style="font-size: 14px;">{{ record.sku }}</b></p>
                        <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                        <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                          style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                            style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                        </p>
                        <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                          style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                            style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                        </p>
                        <p class="mb-0 captionSku">ราคาต่อชิ้น: <b style="font-size: 14px;">0</b></p>
                        <p class="mb-0 captionSku">จำนวน: <b style="font-size: 14px;">{{ record.quantity }}</b></p>
                        <p v-if="record.vat_default === 'no'" class="mb-0 captionSku">ราคารวม: <b style="font-size: 14px;">0</b>
                          <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span>
                        </p>
                        <p v-else class="mb-0 captionSku">ราคารวม: <b style="font-size: 14px;">0</b>
                          <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span>
                        </p>
                      </v-col>
                    </v-row>
                  </template>
                </a-table>
                <v-card v-if="role.role !== 'sale_order' && roleCustomer !== 'vendor'" class="pl-2 my-3">
                  <v-row class="align-center">
                    <v-col :cols="MobileSize ? '6' : '3'">
                      <span>คูปองส่วนลดร้านค้า</span>
                    </v-col>
                    <v-col :cols="MobileSize ? '6' : '3'">
                      <v-chip v-if="CouponData.filter(coupon => coupon.seller_shop_id !== -1).length === 0"  color="#F56E22" @click="clickCoupon('', PointData, XBaht, itemsCart.choose_list[0])" outlined class="pr-5"><span class="px-2">คูปองส่วนลด</span>
                        <v-icon>mdi-ticket-percent-outline</v-icon>
                      </v-chip>
                      <v-chip v-else :color="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount' ? '#FFCDD2' : CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping' ? '#FFEACC' : '#FFECB4'"
                        :style="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount' ? 'color: red;' : CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping' ? 'color: #FF9800;' : 'color: #FFC107;'">
                        <span v-if="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount'" class="px-2 d-inline-block text-truncate" style="max-width: 100px;">ส่วนลด {{CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_type === 'baht' ? `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount} บาท` : `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount}%`}}</span>
                        <span v-else-if="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping'" class="px-2 d-inline-block text-truncate" style="max-width: 100px;">ส่วนลดค่าขนส่ง {{CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_type === 'baht' ? `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount} บาท` : `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount}%`}}</span>
                        <span v-else class="px-2">แถมฟรี</span>
                        <v-icon small @click="closeCoupon(itemsCart.choose_list[0])" :style="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount' ? 'color: red;' : CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping' ? 'color: #FF9800;' : 'color: #FFC107;'">mdi-close</v-icon>
                      </v-chip>
                    </v-col>
                  <v-col :cols="MobileSize ? '6' : '3'">
                    <span>แต้มส่วนลดร้านค้า</span>
                  </v-col>
                  <v-col :cols="MobileSize ? '6' : '3'" v-if="PointData === 0">
                    <v-chip :disabled="itemsCart.seller_use_point === 'no'" :class="itemsCart.seller_use_point === 'no' ? 'disChipClickCoupon' : ''" class="mr-3" v-if="CouponData.length !== 0" color="#FFC107" @click="clickPoint(CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_id, PointData, XBaht, itemsCart.total_coupon_discount, itemsCart.choose_list[0])" outlined>
                    <span class="px-2">แต้มส่วนลด</span>
                    </v-chip>
                    <v-chip :disabled="itemsCart.seller_use_point === 'no'" :class="itemsCart.seller_use_point === 'no' ? 'disChipClickCoupon' : ''" class="mr-3" v-else color="#FFC107" @click="clickPoint('', PointData, XBaht, itemsCart.total_coupon_discount, itemsCart.choose_list[0])" outlined>
                    <span class="px-2">แต้มส่วนลด</span>
                    </v-chip>
                  </v-col>
                  <v-col :cols="MobileSize ? '6' : '3'" v-if="PointData !== 0">
                    <v-chip color="#FFECB4" style="color: #FFC107;">
                    <span class="px-2">แต้มที่ใช้ {{PointData / XBaht}} แต้ม</span>
                      <v-icon small @click="closePoint()" style="color: #FFC107;">mdi-close</v-icon>
                    </v-chip>
                  </v-col>
                  </v-row>
                </v-card>
                <v-card v-if="role.role === 'sale_order_no_JV' && roleCustomer === 'vendor'" class="mt-6 pa-4" color="#F9FAFD" elevation="0"
                style="border-radius: 8px;">
                <v-img class="float-left" src="@/assets/shopping1.png" width="24" height="24"></v-img>
                <b class="ml-3"
                  style="line-height: 35px; font-size: 16px; font-weight: 600; color: #000000;">รายละเอียดรายการสั่งซื้อ</b>
                <v-row class="pt-5 text-left d-block">
                  <v-col>
                    <span style="font-size: 14px; font-weight: 400;">PayType :
                      <v-chip v-if="itemsCart.choose_list[0].pay_type === 'onetime'" small
                        style="color: #1B5DD6; background: rgba(27, 93, 214, 0.10); font-size: 14px; padding: 2px 12px;">{{ itemsCart.choose_list[0].pay_type
                          === 'onetime' ? 'One Time' : itemsCart.choose_list[0].pay_type }}</v-chip>
                      <v-chip v-else small
                        style="color: #FF710B; background: rgba(255, 113, 11, 0.10); padding: 2px 12px; font-size: 14px;">{{ itemsCart.choose_list[0].pay_type
                          === 'recurring' ? 'Recurring' : itemsCart.choose_list[0].pay_type }}</v-chip>
                    </span>
                  </v-col>
                </v-row>
                <v-form class="mt-3" ref="orderListDetails" :lazy-validation="lazy">
                  <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                    <v-row v-if="chooseitem.product_list.some(productitem => (productitem.product_type !== 'general' && itemsCart.is_JV === 'no') || (productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes') || (productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'no' ) || (productitem.product_type === 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'no' && itemsCart.choose_list[0].pay_type !== 'onetime') || (productitem.product_type === 'general' && itemsCart.is_JV === 'no' && itemsCart.is_JV_buyer === 'yes' && itemsCart.choose_list[0].pay_type !== 'onetime') || (productitem.product_type === 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes' && itemsCart.choose_list[0].pay_type !== 'onetime'))" dense style="justify-content: space-between;">
                      <v-col col="12" class="pb-0">
                        <span style="line-height: 24px; font-size: 14px; color: #333333;">วันที่เริ่มสัญญา <span
                            style="color: red;">*</span></span>
                        <v-dialog ref="dialogContractStartDate" v-model="modalContractStartDate" persistent width="290px">
                          <template v-slot:activator="{ on, attrs }">
                            <v-text-field readonly style="border-radius: 8px;" v-model="contractStartDate" v-bind="attrs"
                              v-on="on" outlined dense placeholder="วว/ดด/ปปปป"><v-icon slot="append"
                                color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
                          </template>
                          <v-date-picker color="#27AB9C" v-model="date" scrollable reactive locale="Th-th">
                            <v-spacer></v-spacer>
                            <v-btn text color="primary" @click="closeModalContractStartDate()">
                              ยกเลิก
                            </v-btn>
                            <v-btn text color="primary" @click="setValueContractStartDate(date, chooseindex)">
                              ตกลง
                            </v-btn>
                          </v-date-picker>
                        </v-dialog>
                      </v-col>
                      <v-col cols="12" class="pb-0">
                        <span style="line-height: 24px; font-size: 14px; color: #333333;">วันที่สิ้นสุดสัญญา <span
                            style="color: red;">*</span></span>
                        <v-dialog ref="dialogContractEndDate" v-model="modalContractEndDate" persistent width="290px">
                          <template v-slot:activator="{ on, attrs }">
                            <v-text-field style="border-radius: 8px;"
                              :disabled="searchContractStartDate !== '' ? false : true" readonly v-model="contractEndDate"
                              v-bind="attrs" v-on="on" outlined dense placeholder="วว/ดด/ปปปป"><v-icon slot="append"
                                color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
                          </template>
                          <v-date-picker color="#27AB9C" v-model="date1" scrollable reactive locale="Th-th"
                            :min="setMinDateContractEndDate">
                            <v-spacer></v-spacer>
                            <v-btn text color="primary" @click="closeModalContractEndDate()">
                              ยกเลิก
                            </v-btn>
                            <v-btn text color="primary" @click="setValueContractEndDate(date1, chooseindex)">
                              ตกลง
                            </v-btn>
                          </v-date-picker>
                        </v-dialog>
                      </v-col>
                    </v-row>
                  </div>
                  <v-col class="pa-0 mb-4">
                    <v-checkbox v-model="contractSet" :readonly="this.itemsCart.total_price_no_vat >= 50000"
                      label="ต้องการระบุสัญญาบริการ" :value="contractSet ? true : false">
                    </v-checkbox>
                  </v-col>
                  <span style="line-height: 24px; font-size: 14px; color: #333333;">หมายเหตุ</span>
                  <v-textarea style="border-radius: 8px;" v-model="reason" outlined
                    placeholder="กรุณาระบุ"></v-textarea>
                </v-form>
              </v-card>
              </v-container>
            </v-card>
            <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
              <v-card class="pa-4" style="border-radius: 8px; margin-top: 24px" v-if="chooseitem.product_list.some(productitem => (productitem.product_type === 'general' && role.role === 'sale_order_no_JV'))">
                <div>
                  <p style="font-size: 18px"><b>ที่อยู่ในการจัดส่งสินค้าของลูกค้า</b></p>
                </div>
                <v-row dense>
                  <v-row>
                    <v-col>
                      <v-radio-group v-model="radios" row class="mt-n5">
                        <v-col cols="12">
                          <span style="font-size: 14px; font-weight: 600; ">รูปแบบการจัดส่ง</span>
                        </v-col>
                        <v-col cols="6" class="px-0 pt-0" v-if="itemsCart.store_front === 'yes'">
                          <v-radio value="radio-1" @click="ClearRadio()"><template v-slot:label>
                              <span style="font-size: 14px;">รับสินค้าหน้าร้าน</span>
                            </template>
                          </v-radio>
                        </v-col>
                        <v-col cols="6" class="px-0 pt-0">
                          <v-radio value="radio-2" @click="ClearRadio()"><template v-slot:label>
                              <span tyle="font-size: 14px;">จัดส่งสินค้า</span>
                            </template>
                          </v-radio>
                        </v-col>
                      </v-radio-group>
                      <v-spacer style="border-top: 2px solid #EBEBEB;"></v-spacer>
                      <div v-if="showAddress === false">
                        <div v-for="(item, index) in userdetail" :key="index">
                          <div style="margin-top: 16px;" v-if="radios === 'radio-2' && item.default_address === 'Y'">
                            <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="24" height="24"></v-img>
                            <span style="font-size: 14px; font-weight: 600;">{{ roleCustomer !== 'general' ? item.name : item.first_name + ' ' + item.last_name }}</span>
                            <span class="px-1" style="color: #EBEBEB;">|</span>
                            <span style="font-size: 14px; font-weight: 600;">{{ item.phone }}</span>
                            <v-btn class="float-end" color="#27AB9C" text dark
                              style="font-size: 12px; font-weight: 500; margin-top: -5px;" @click="changeAddress()"><span
                                style="text-decoration-line: underline;">เปลี่ยนที่อยู่</span></v-btn>
                              <v-col class="ml-6 pt-1 pr-5" style="max-width: 260px;">
                                <span style="font-size: 12px; font-weight: 600;">{{ item.detail_address }} {{ item.sub_district }} {{ item.district
                                }} {{ item.province }} {{ item.postcode }}</span>
                              </v-col>
                          </div>
                        </div>
                      </div>
                      <div v-if="radios === 'radio-1'">
                        <div style="margin-top: 16px;" v-for="(item, index) in itemsCart.choose_list" :key="index">
                          <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="24" height="24"></v-img>
                          <span style="font-size: 14px; font-weight: 600;">{{ item.product_list[0].seller_shop_name }}</span>
                          <span class="px-1" style="color: #EBEBEB;">|</span>
                          <span style="font-size: 14px; font-weight: 600;">{{ item.shipping_detail.data_seller_address[0].phone !== undefined ? item.shipping_detail.data_seller_address[0].phone : '-' }}</span>
                            <v-col class="mt-2 pl-0 mb-4" style="max-width: 260px;">
                              <span style="font-size: 12px;"> บ้านเลขที่ {{ item.shipping_detail.data_seller_address[0].house_no}} {{ item.shipping_detail.data_seller_address[0].detail }} ตำบล/แขวง {{ item.shipping_detail.data_seller_address[0].sub_district }}
                                อำเภอ/เขต {{ item.shipping_detail.data_seller_address[0].district }} จังหวัด {{ item.shipping_detail.data_seller_address[0].province }} {{ item.shipping_detail.data_seller_address[0].zipcode }}
                                </span>
                            </v-col>
                          <v-row>
                            <v-col cols="6" class="pb-0">
                              <span style="line-height: 24px; font-size: 14px; color: #333333;">วันรับสินค้า <span
                                  style="color: red;">*</span></span>
                              <v-menu v-model="menu" :close-on-content-click="false" :nudge-right="40"
                                transition="scale-transition" offset-y min-width="auto">
                                <template v-slot:activator="{ on, attrs }">
                                  <v-text-field v-model="contractDate" style="border-radius: 8px;" outlined dense
                                    placeholder="วว/ดด/ปปปป" readonly v-bind="attrs" v-on="on">
                                    <v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon>
                                  </v-text-field>
                                </template>
                                <v-date-picker v-model="dates" @input="menu = false, setValueDate(dates)" locale="Th-th"
                                  scrollable no-title :min="today" :max="futureDate"></v-date-picker>
                              </v-menu>
                            </v-col>
                            <v-col cols="6" class="pl-0" :class="{ 'is-disabled': dates === '' }">
                              <span style="line-height: 24px; font-size: 14px; color: #333333;">เวลารับสินค้า <span style="color: red;">*</span></span>
                              <v-col class="pt-0 pl-0" v-if="dates === today">
                                <template>
                                  <a-space direction="vertical" style="width: 100%;">
                                    <a-time-picker v-model="timeselecttoday" :bordered="false" style="width: 100%;" format="HH:mm น." size="large" placeholder="00.00 น."  :disabledHours="disabledHours" :disabledMinutes="disabledMinutes" />
                                    <a-time-range-picker :bordered="false" style="width: 100%;" />
                                  </a-space>
                                </template>
                              </v-col>
                              <v-col class="pt-0 pl-0" v-else>
                                <template>
                                  <a-space direction="vertical" style="width: 100%;">
                                    <a-time-picker v-model="timeselect" :bordered="false" style="width: 100%;" format="HH:mm น." size="large" placeholder="00.00 น." />
                                    <a-time-range-picker :bordered="false" style="width: 100%;" />
                                  </a-space>
                                </template>
                              </v-col>
                            </v-col>
                          </v-row>
                        </div>
                      </div>
                    </v-col>
                  </v-row>
                </v-row>
              </v-card>
            </div>
            <v-card class="pa-4" style="border-radius: 8px; margin-top: 24px" v-if="role.role === 'sale_order_no_JV'">
                <div>
                  <p class="pb-0 mb-0" style="font-size: 18px"><b>ขอเอกสารเพิ่มเติม</b></p>
                  <p class="pt-0 mt-0" style="font-size: 14px; color: red;">*เอกสารใช้ที่อยู่เดียวกับใบกำกับภาษี</p>
                </div>
                <v-row dense>
                  <v-col cols="4">
                    <v-checkbox disabled class="mt-0" v-model="radioInvoice" label="ใบแจ้งหนี้" true-value="radioinvoice" false-value="">
                    </v-checkbox>
                  </v-col>
                  <v-col cols="12">
                    <v-checkbox :disabled="itemsCart.isEtax === 'no'" class="mt-0" v-model="radioTax" label="ใบกำกับภาษี" true-value="radiotax" false-value="">
                      <template v-slot:label>
                        <span style="font-size: 14px;">ใบกำกับภาษี</span>
                      </template>
                    </v-checkbox>
                  </v-col>
                </v-row>
                <div>
                  <div v-if="showInvoiceAddress === false">
                    <v-spacer class="mt-2" style="border-top: 2px solid #EBEBEB;"></v-spacer>
                    <div style="margin-top: 16px;" v-for="(item, index) in invoicedetail" :key="index">
                      <div v-if="item.default_address === 'Y'">
                        <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="24" height="24"></v-img>
                        <span style="font-size: 14px; font-weight: 600;">{{item.name}}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span style="font-size: 12px; font-weight: 600; color: #27AB9C;">{{item.phone}}</span>
                        <v-btn class="float-end" color="#27AB9C" text dark
                              style="font-size: 12px; font-weight: 500; margin-top: -5px;" @click="DialogTaxAddress = !DialogTaxAddress"><span
                                style="text-decoration-line: underline;">เปลี่ยนที่อยู่</span></v-btn>
                        <!-- <v-col class="ml-6 py-0 pt-2">
                          <span style="font-size: 16px;">รหัสสาขา: <b>{{item.branch_id}}</b></span>
                        </v-col> -->
                        <v-col class="ml-6 py-0 pt-3">
                          <span style="font-size: 12px;">เลขประจำตัวผู้เสียภาษี: <b>{{item.tax_id}}</b></span>
                        </v-col>
                        <v-col class="ml-6 pt-1 pr-5">
                          <span style="font-size: 12px;">ที่อยู่: <b>{{item.detail_address}} {{item.sub_district}} {{item.district}} {{item.province}} {{item.postcode}}</b> </span>
                        </v-col>
                        </div>
                      </div>
                  </div>
                  </div>
              </v-card>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" md="4">
        <v-card class="v-Card">
          <v-container grid-list-xs class="pa-5">
            <v-row>
              <v-col cols="12" class="my-2">
                <v-row dense>
                  <!-- <v-icon color="#27AB9C" class="pr-2">mdi-file-document-outline</v-icon> -->
                  <span
                    :style="MobileSize ? 'font-size: 18px;' : IpadSize ? 'font-size: 20px;' : 'font-size: 24px; font-weight: 700;'"><b>สรุปราคาสั่งซื้อสินค้า</b></span>
                </v-row>
              </v-col>
              <v-col cols="6" sm="7" class="Textcard">
                <span>ราคาสินค้า</span>
              </v-col>
              <v-col cols="6" sm="5" align="right" class="TextBaht">
                <span>{{ itemsCart.total_price_no_vat ? formatPrice(itemsCart.total_price_no_vat) : '0.00' }} บาท</span>
              </v-col>
              <!-- <v-col v-if="role.role !== 'sale_order' && roleCustomer !== 'vendor'" cols="6" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                <span>คูปองส่วนลด</span>
              </v-col>
              <v-col v-if="role.role !== 'sale_order' && roleCustomer !== 'vendor'" cols="6" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                <v-chip color="#FF9800" v-if="CouponData.length === 0"  @click="clickCoupon('', PointData, XBaht)" outlined class="pr-5"><span class="px-2">คูปองส่วนลด</span>
                  <v-icon>mdi-ticket-percent-outline</v-icon>
                </v-chip>
                <v-btn v-else small color="#27AB9C" @click="clickCoupon(CouponData[0].coupon_id, PointData, XBaht)" text><span class="text-decoration-underline">เปลี่ยนคูปองส่วนลด</span>
                </v-btn>
              </v-col>
              <v-col class="py-0" v-if="CouponData.length !== 0" cols="6">
              </v-col>
              <v-col class="py-0" v-if="CouponData.length !== 0" cols="6" align="right">
                <v-chip class="mt-3" :color="CouponData[0].coupon_type === 'discount' ? '#FFCDD2' : CouponData[0].coupon_type === 'free_shipping' ? '#FFEACC' : '#FFECB4'"
                :style="CouponData[0].coupon_type === 'discount' ? 'color: red;' : CouponData[0].coupon_type === 'free_shipping' ? 'color: #FF9800;' : 'color: #FFC107;'">
                <span v-if="CouponData[0].coupon_type === 'discount'" class="px-2">ส่วนลด {{CouponData[0].discount_type === 'baht' ? `${CouponData[0].discount_amount} บาท` : `${CouponData[0].discount_amount}%`}}</span>
                <span v-else-if="CouponData[0].coupon_type === 'free_shipping'" class="px-2">ส่วนลดค่าขนส่ง {{CouponData[0].discount_type === 'baht' ? `${CouponData[0].discount_amount} บาท` : `${CouponData[0].discount_amount}%`}}</span>
                <span v-else class="px-2">แถมฟรี</span>
                  <v-icon small @click="closeCoupon()" :style="CouponData[0].coupon_type === 'discount' ? 'color: red;' : CouponData[0].coupon_type === 'free_shipping' ? 'color: #FF9800;' : 'color: #FFC107;'">mdi-close</v-icon>
                </v-chip>
              </v-col>
              <v-col v-if="role.role !== 'sale_order' && itemsCart.seller_use_point === 'yes' && roleCustomer !== 'vendor'" cols="6" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                <span>ใช้งานแต้มส่วนลด</span>
              </v-col>
              <v-col v-if="role.role !== 'sale_order' && itemsCart.seller_use_point === 'yes' && roleCustomer !== 'vendor'" cols="6" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'" style="display: flex; justify-content: end;">
                <v-switch false-value="no" true-value="yes" inset v-model="usePointOrNot" @click="closePoint()"></v-switch>
              </v-col>
              <v-col cols="6" v-if="usePointOrNot === 'yes'" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                <span>แต้มส่วนลด</span>
              </v-col>
              <v-col cols="6" v-if="usePointOrNot === 'yes' && PointData === 0" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                <v-chip class="mr-3" v-if="CouponData.length !== 0" color="#FFC107" @click="clickPoint(CouponData[0].coupon_id, PointData, XBaht, itemsCart.total_coupon_discount)" outlined>
                <span class="px-2">แต้มส่วนลด</span>
                </v-chip>
                <v-chip class="mr-3" v-else color="#FFC107" @click="clickPoint('', PointData, XBaht, itemsCart.total_coupon_discount)" outlined>
                <span class="px-2">แต้มส่วนลด</span>
                </v-chip>
              </v-col>
              <v-col cols="6" v-if="usePointOrNot === 'yes' && PointData !== 0" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                <v-chip class="mr-3" color="#FFECB4" style="color: #FFC107;">
                <span class="px-2">แต้มที่ใช้ {{PointData / XBaht}} แต้ม</span>
                  <v-icon small @click="closePoint()" style="color: #FFC107;">mdi-close</v-icon>
                </v-chip>
              </v-col> -->
              <v-col v-if="role.role !== 'sale_order' && roleCustomer !== 'vendor'" cols="6" class="Textcard">
                <span>ส่วนลดคูปองร้านค้า</span>
              </v-col>
              <v-col v-if="role.role !== 'sale_order' && roleCustomer !== 'vendor'" cols="6" align="right" class="TextBaht">
                <span>{{ itemsCart.total_coupon_discount ? formatPrice(itemsCart.total_coupon_discount) : '0.00' }} บาท</span>
              </v-col>
              <v-col v-if="role.role !== 'sale_order' && roleCustomer !== 'vendor'" cols="6" class="Textcard">
                <span>ส่วนลดแต้ม</span>
                <!-- <span style="font-size: 10px; font-weight: 400; color: #A1A1A1; line-height: 14px;">ซื้อ บาท ได้ แต้ม </span> -->
              </v-col>
              <v-col v-if="role.role !== 'sale_order' && roleCustomer !== 'vendor'" cols="6" align="right" class="TextBaht">
                <span>{{ itemsCart.choose_list[0].total_point ? formatPrice(itemsCart.choose_list[0].total_point) : '0.00' }} บาท</span>
              </v-col>
              <v-col v-if="role.role !== 'sale_order' && roleCustomer !== 'vendor'" cols="6" class="Textcard">
                <span>ราคาหลังหักส่วนลดคูปอง</span>
              </v-col>
              <v-col v-if="role.role !== 'sale_order' && roleCustomer !== 'vendor'" cols="6" align="right" class="TextBaht">
                <span>{{ itemsCart.total_price_after_discount ? formatPrice(itemsCart.total_price_after_discount) : '0.00' }} บาท</span>
              </v-col>
              <v-col v-if="role.role !== 'ext_buyer'" cols="6" class="Textcard">
                <span v-if="role.role === 'purchaser' || role.role === 'sale_order'">ส่วนลดคู่ค้า</span>
                <span v-else-if="role.role === 'sale_order_no_JV'">ส่วนลดลูกค้า</span>
              </v-col>
              <v-col v-if="role.role !== 'ext_buyer'" cols="6" align="right" class="TextBaht">
                <span>{{ itemsCart.total_tier_discount ? formatPrice(itemsCart.total_tier_discount) : '0.00' }} บาท</span>
              </v-col>
              <v-col v-if="role.role !== 'ext_buyer'" cols="6" class="Textcard">
                <span v-if="role.role === 'purchaser' || role.role === 'sale_order'">ราคาหลังหักส่วนลดคู่ค้า</span>
                <span v-else-if="role.role === 'sale_order_no_JV'">ราคาสินค้ารวมส่วนลด</span>
              </v-col>
              <v-col v-if="role.role !== 'ext_buyer'" cols="6" align="right" class="TextBaht">
                <span>{{ itemsCart.total_price_after_all_discount ? formatPrice(itemsCart.total_price_after_all_discount) : '0.00' }} บาท</span>
              </v-col>
              <v-col cols="6" class="Textcard">
                <span>ภาษีมูลค่าเพิ่ม</span>
                <span style="font-size: 10px; font-weight: 400; color: #A1A1A1; line-height: 14px;"> (สินค้าราคารวมภาษี) </span>
              </v-col>
              <v-col cols="6" align="right" class="TextBaht">
                <span>{{ itemsCart.total_include_vat ? formatPrice(itemsCart.total_include_vat) : '0.00' }} บาท</span>
              </v-col>
              <v-col cols="6" class="Textcard">
                <span>ภาษีมูลค่าเพิ่ม</span>
                <span style="font-size: 10px; font-weight: 400; color: #A1A1A1; line-height: 14px;"> (สินค้าราคาไม่รวมภาษี) </span>
              </v-col>
              <v-col cols="6" align="right" class="TextBaht">
                <span>{{ itemsCart.total_exclude_vat ? formatPrice(itemsCart.total_exclude_vat) : '0.00' }} บาท</span>
              </v-col>
              <v-col cols="6" class="Textcard">
                <span>รวมภาษีมูลค่าเพิ่ม</span>
              </v-col>
              <v-col cols="6" align="right" class="TextBaht">
                <span>{{ itemsCart.total_vat ? formatPrice(itemsCart.total_vat) : '0.00' }} บาท</span>
              </v-col>
              <v-col cols="6" class="Textcard">
                <span>ราคารวมภาษีมูลค่าเพิ่ม</span>
              </v-col>
              <v-col cols="6" align="right" class="TextBaht">
                <span>{{ itemsCart.total_price_vat ? formatPrice(itemsCart.total_price_vat) : '0.00' }} บาท</span>
              </v-col>
              <v-col cols="6" class="Textcard">
                <span>ค่าจัดส่ง</span><br>
                <span style="font-size: 10px; font-weight: 400; color: #A1A1A1; line-height: 14px;">ราคานี้เป็นมาตรฐาน - ราคาอาจแตกต่างกันไป </span>
                <span style="font-size: 10px; font-weight: 400; color: #A1A1A1; line-height: 14px;">ขึ้นอยู่กับสินค้า/ปลายทาง เจ้าหน้าที่จัดส่งจะติดต่อคุณ</span>
              </v-col>
              <v-col v-if="parseInt(itemsCart.total_shipping) !== parseInt(itemsCart.shipping_price)" cols="6" align="right" :class="MobileSize ? 'TextBahtMobileSize' : 'TextBaht'">
                <span style="font-weight: 400; color: #BDBDBD;" class="text-decoration-line-through">{{ Number(itemsCart.shipping_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' บาท' }}</span><br>
                <span>{{ Number(itemsCart.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' บาท'}}</span>
              </v-col>
              <v-col v-else cols="6" align="right" :class="MobileSize ? 'TextBahtMobileSize' : 'TextBaht'">
                <span>{{ Number(itemsCart.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' บาท' }}</span><br>
              </v-col>
              <v-row v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
              <v-col cols="12" v-if="chooseitem.product_list.some(productitem => (productitem.product_type === 'general' && role.role === 'sale_order_no_JV') && roleCustomer !== 'vendor')">
                <div v-if="chooseitem.product_list.some(productitem => (productitem.product_type === 'general' && radios !== 'radio-1' && itemsCart.shipping_method.length !== 0))" :style="radioTransport === '' ? 'background-color: #fff1f4;' : ''">
                <v-col cols="12" class="pt-0" >
                  <v-spacer class="mb-3" style="border-top: 2px solid #EBEBEB;"></v-spacer>
                  <span class="" :style="MobileSize ? 'font-size: 14px; color: #FAAD14;' : 'font-size: 16px; color: #FAAD14;'" color="#FAAD14">ขนส่ง</span>
                  <v-btn class="float-end" color="#27AB9C" text :disabled="btnEstimateCost === false" :class="btnEstimateCost === false ? '' : 'theme--dark'"
                    style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="EstimateCost()"><span
                    style="text-decoration-line: underline;">เลือกการจัดส่ง</span>
                  </v-btn>
                </v-col>
                <div v-if="radioTransport !== ''">
                  <v-col cols="12">
                    <v-img class="mt-n2 mr-2 float-left" src="@/assets/Transport.png" width="30" height="30" contain></v-img>
                    <span class="mt-4" :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 18px; font-weight: 600;'">{{nameTransport}}</span>
                    <span class="float-end mr-4" :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 18px; font-weight: 600;'">{{costTransport}} บาท</span>
                    <!-- <span class="mt-4 pr-3" :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 18px; font-weight: 600;'">{{ Number(costTransport).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}  บาท</span> -->
                  </v-col>
                </div>
                <div v-if="radioTransport === ''">
                  <v-col cols="12" class="d-flex justify-end pt-0">
                    <!-- <v-img class="mt-n2 mr-2 float-left" src="@/assets/Transport.png" width="30" height="30" contain></v-img> -->
                    <span class="mt-2" :style="MobileSize ? 'font-size: 12px; font-weight: 400; color: red; padding-right: 12px;' : 'font-size: 14px; font-weight: 400; color: red; padding-right: 16px;'">* คุณยังไม่ได้เลือกขนส่ง กรุณาเลือกขนส่ง</span>
                    <!-- <span class="mt-4 pr-3" :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 18px; font-weight: 600;'">{{ Number(costTransport).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}  บาท</span> -->
                  </v-col>
                </div>
                <v-spacer class="mx-3" style="border-top: 2px solid #EBEBEB;"></v-spacer>
              </div>
              </v-col>
            </v-row>
              <!-- <v-col cols="8" class="py-0">
                <span>ค่าจัดส่ง</span>
              </v-col>
              <v-col cols="4" class="py-0" align="right">
                <span><b>{{ itemsCart.total_shipping ? Number(itemsCart.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }}</b></span>
              </v-col> -->
              <!-- <v-col cols="8" class="py-0"> -->
                <!-- <span style="font-size: 10px" v-if="Number(itemsCart.total_shipping) === 0 &&
                (address_data.province !== 'กรุงเทพมหานคร' && address_data.province !== 'นครปฐม' && address_data.province !== 'นนทบุรี' && address_data.province !== 'ปทุมธานี' && address_data.province !== 'สมุทรปราการ' && address_data.province !== 'สมุทรสาคร')">ค่าจัดส่งขึ้นอยู่เงื่อนที่ทางบริษัทเป็นผู้กำหนด</span>
                <span style="font-size: 10px" v-else-if="Number(itemsCart.total_shipping) === 0 &&
                (address_data.province === 'กรุงเทพมหานคร' || address_data.province === 'นครปฐม' || address_data.province === 'นนทบุรี' || address_data.province === 'ปทุมธานี' || address_data.province === 'สมุทรปราการ' || address_data.province === 'สมุทรสาคร')">ฟรีค่าส่งในกรุงเทพฯ และปริมณฑล</span> -->
                <!-- <span style="font-size: 10px; color: #8C8C8C;" class="mb-0">ราคานี้เป็นมาตรฐาน -
                  ราคาอาจแตกต่างกันไปขึ้นอยู่กับสินค้า / ปลายทาง เจ้าหน้าที่ จัดส่งจะติดต่อคุณ</span>
              </v-col> -->
              <!-- <v-col cols="12" class="">
                <v-row dense class="pl-1 d-flex">
                  <span class="totalPriceFont mr-auto"><b>ราคารวมทั้งหมด</b></span>
                  <span class="totalPriceFont ml-auto pt-0 pr-1"><b>{{ itemsCart.net_price ? Number(itemsCart.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }} <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span></b></span>
                  <span class="" style="font-weight: 600; font-size: 24px; line-height: 29px; color: #F5222D;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  <span class="ml-auto pt-2" style="font-weight: 400; font-size: 10px; line-height: 16px; color: #606060; letter-spacing: -0.2px;" v-if="itemProduct.total_sold !== '-'">ขายแล้ว {{ kFormatter(itemProduct.total_sold) }} ชิ้น</span>
                </v-row>
              </v-col> -->
              <v-col cols="12" class="py-0" style="margin-top: 100px;" v-if="itemsCart.seller_use_point === 'yes' && role.role !== 'sale_order' && roleCustomer !== 'vendor'">
                <v-row dense class="pl-1 d-flex">
                  <span style="color: #faad14;" :style="MobileSize ? 'font-size: 16px; font-weight: 700;' : 'font-size: 18px; font-weight: 700;'"
                    class="mr-auto align-self-center"><b>แต้มสะสมที่ได้ครั้งนี้</b></span>
                  <span :style="MobileSize ? 'font-size: 20px; font-weight: 700; color: #27AB9C;' : 'font-size: 24px; font-weight: 700; color: #27AB9C;'" class="ml-auto pt-0 pr-1">
                    <b>{{Number(parseInt(itemsCart.total_point_receive)).toLocaleString(undefined, {})}}
                    <span style="color: #faad14;"
                    :style="MobileSize ? 'font-size: 14px; font-weight: 700;' : 'font-size: 18px; font-weight: 700;'" class="ml-2">แต้ม</span></b></span>
                  <!-- <span class="" style="font-weight: 600; font-size: 24px; line-height: 29px; color: #F5222D;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  <span class="ml-auto pt-2" style="font-weight: 400; font-size: 10px; line-height: 16px; color: #606060; letter-spacing: -0.2px;" v-if="itemProduct.total_sold !== '-'">ขายแล้ว {{ kFormatter(itemProduct.total_sold) }} ชิ้น</span> -->
                </v-row>
              </v-col>
              <v-col cols="12" class="py-0" :style="role.role === 'sale_order' || itemsCart.seller_use_point !== 'yes' || roleCustomer === 'vendor' ? 'margin-top: 100px;' : ''">
                <v-row dense class="pl-1 d-flex">
                  <span :style="MobileSize ? 'font-size: 16px; font-weight: 700;' : 'font-size: 20px; font-weight: 700;'"
                    class="mr-auto align-self-center"><b>ราคารวมทั้งหมด</b></span>
                  <span :style="MobileSize ? 'font-size: 20px; font-weight: 700; color: #27AB9C;' : 'font-size: 28px; font-weight: 700; color: #27AB9C;'" class="ml-auto pt-0 pr-1"><b>{{
                    itemsCart.net_price  ? formatPrice(itemsCart.net_price) : '0.00' }}<span v-if="choose_list === 'recurring'"
                    :style="MobileSize ? 'font-size: 16px; font-weight: 700; color: #333333' : 'font-size: 20px; font-weight: 700; color: #333333'" class="ml-2">บาท/เดือน</span><span
                        v-else :style="MobileSize ? 'font-size: 16px; font-weight: 700; color: #333333' : 'font-size: 20px; font-weight: 700; color: #333333'"
                        class="ml-2">บาท</span></b></span>
                  <!-- <span class="" style="font-weight: 600; font-size: 24px; line-height: 29px; color: #F5222D;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  <span class="ml-auto pt-2" style="font-weight: 400; font-size: 10px; line-height: 16px; color: #606060; letter-spacing: -0.2px;" v-if="itemProduct.total_sold !== '-'">ขายแล้ว {{ kFormatter(itemProduct.total_sold) }} ชิ้น</span> -->
                </v-row>
              </v-col>
              <v-col cols="12" class="pb-0" v-if="noShipping === true">
                <v-row dense class="pl-1 d-flex">
                  <span :style="MobileSize ? 'font-size: 12px; font-weight: 700;' : 'font-size: 14px; font-weight: 700;'"
                    class="mr-auto align-self-center" style="color: red;">*ส่วนลดค่าจัดส่งจะไม่ถูกใช้งาน เนื่องจากไม่มีค่าจัดส่ง</span>
                </v-row>
              </v-col>
              <v-col v-if="Number(itemsCart.net_price) <= 0" class="py-0 my-0">
                <span style="color: red;">* ราคารวมทั้งหมดมีค่าน้อยกว่า 0 ไม่สามารถทำรายการได้</span>
              </v-col>
              <!-- <v-col cols="7" md="8" :class="MobileSize ? 'pr-0' : ''">
                <span class="totalPriceFont"><b>ราคารวมทั้งหมด</b></span>
              </v-col>
              <v-col cols="5" md="4" align="right" :class="MobileSize ? 'pl-0' : ''">
                <span class="totalPriceFont"><b>{{ itemsCart.net_price ? Number(itemsCart.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }} <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span></b></span>
              </v-col> -->
              <v-col cols="12" align="right" class="mt-2 mb-2">
                <!-- ชำระเงิน -->
                <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                  <v-btn v-if="role.role === 'sale_order_no_JV' && roleCustomer === 'vendor'" style="border-radius: 40px; height: 40px;" class="white--text" block color="#27AB9C" @click="Confirm()"
                    :disabled="Number(itemsCart.net_price) <= 0 || contractStartDate === '' || contractEndDate === ''">
                    <span style="font-size: 18px; font-style: normal; font-weight: 500;">ยืนยันการขอซื้อ</span>
                  </v-btn>
                  <v-btn v-if="role.role === 'sale_order_no_JV' && radios === 'radio-2' && roleCustomer !== 'vendor'" style="border-radius: 40px; height: 40px;" class="white--text" block color="#27AB9C" @click="Confirm()"
                    :disabled="Number(itemsCart.net_price) <= 0">
                    <span style="font-size: 18px; font-style: normal; font-weight: 500;">ยืนยันการขอซื้อ</span>
                  </v-btn>
                  <v-btn v-else-if="role.role === 'sale_order_no_JV' && radios === 'radio-1' && roleCustomer !== 'vendor'" style="border-radius: 40px; height: 40px;" class="white--text" block color="#27AB9C" @click="Confirm()"
                  :disabled="dates === '' || timeselect === '' || timeselecttoday === '' || timeselect === null || timeselecttoday === null || Number(itemsCart.net_price) <= 0">
                    <span style="font-size: 18px; font-style: normal; font-weight: 500;">ยืนยันการขอซื้อ</span>
                  </v-btn>
                  <v-btn v-else-if="role.role === 'sale_order'" style="border-radius: 40px; height: 40px;" class="white--text" block color="#27AB9C" @click="Confirm()"
                    :disabled="Number(itemsCart.net_price) <= 0">
                    <span style="font-size: 18px; font-style: normal; font-weight: 500;">ยืนยันการขอซื้อ</span>
                  </v-btn>
                </div>
                <!-- ชำระเงินสด -->
                <v-btn v-if="selectTypeAddress === 'Shop' && checkOwnShop === 'Y'" class="mt-4" outlined block color="#27AB9C" @click="confirmCreateOrderMobile('cashPayment')"
                  :disabled="(taxRoles === 'Personal' || taxRoles === 'Business') && taxAddress === '' ? true : disableButtonPay ? true : false">
                  <span style="font-size: 16px; font-style: normal; font-weight: bold;">ชำระเงินสด</span>
                </v-btn>
                <!-- ชำระเงินแบบเครดิตเทอม -->
                <!-- <v-btn v-if="role.role === 'purchaser'" class="mt-4" outlined block color="#27AB9C"
                  @click="confirmCreateOrderMobile('creditTerm')"
                  :disabled="(taxRoles === 'Personal' || taxRoles === 'Business') && taxAddress === '' ? true : false">
                  <span style="font-size: 16px; font-style: normal; font-weight: bold;">ชำระเงินแบบเครดิตเทอม</span>
                </v-btn> -->
                <!-- <v-btn rounded block color="#00B500" dark @click="confirmCreateOrderMobile()"><b>ชำระเงิน</b></v-btn> -->
              </v-col>
            </v-row>
          </v-container>
        </v-card>
      </v-col>
    </v-row>
    <!-- <EditModalAddress :EditAddressDetail="EditAddressDetail" :title="titleAddress" :page="page" /> -->
    <ModalTaxInvoice ref="ModalTaxAddress" :taxType="taxRoles" :frompage="CartPage" :ShippingType="selectTypeAddress"/>
    <!-- <ModalQuotation /> -->
    <v-dialog v-model="modalPayment" width="464" persistent>
      <v-card align="center" class="rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">วิธีการชำระเงิน</font>
          </span>
          <v-btn icon dark @click="modalPayment = !modalPayment">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="mt-9 my-3" style="font-size:14px; color: #333333; font-weight: 600;">เลือกวิธีการชำระเงิน
        </v-card-text>
        <v-row no-gutters justify="center" align="center">
          <v-col cols="6" align="center" :class="MobileSize ? '' : 'pl-12'">
            <v-card @click="openCreateQU('ชำระทันที')" align="center" class="mb-9" outlined width="124" height="148">
              <v-col style="transform: translateY(30px);">
                <v-img src="@/assets/icons/money.png" width="40" height="40"></v-img>
                <span>ชำระเงินทันที</span>
              </v-col>
            </v-card>
          </v-col>
          <v-col cols="6" align="center" :class="MobileSize ? '' : 'pr-4'">
            <v-card @click="openCreateQU('เครดิตเทอม')" align="center" class="mb-9" outlined width="124" height="148">
              <v-col style="transform: translateY(30px);">
                <v-img src="@/assets/icons/term-loan.png" width="40" height="40"></v-img>
                <span>ใช้เครดิตเทอม</span>
              </v-col>
            </v-card>
          </v-col>
        </v-row>
      </v-card>
    </v-dialog>
    <CouponCart ref="CouponCart" />
    <PointCart ref="PointCart" />
    <EditModalAddress ref="EditModalAddress"/>
    <EditModalEtaxAddress ref="EditModalEtaxAddress"/>
    <v-dialog v-model="dialog_Cancel_Coupon" width="436px" persistent scrollable>
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense>
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">ขอแก้ไขใบเสนอราคา</font>
          </span>
          <v-btn icon dark @click="dialog_Cancel_Coupon = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text align="center">
          <v-container>
            <v-row align="center" class="pa-2 mt-4">
              <span style="font-weight: 200; font-size: 18px; line-height: 26px; color: #333333;">
                การแก้ไขใบเสนอราคาจะยังไม่สามารถใช้คูปองส่วนลดได้จนกว่าผู้ขายจะทำการอนุมัติ<br />
                <font color="#27AB9C">"ยืนยันการดำเนินการต่อหรือไม่"</font>
              </span>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions align="center">
          <v-container>
            <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-1" @click="dialog_Cancel_Coupon = false">
              ยกเลิก
            </v-btn>
            <v-btn dense color="#27AB9C" class="ml-4 mt-1 pl-8 pr-8 white--text" @click="openEditQU()">
              ยืนยัน
            </v-btn>
          </v-container>
        </v-card-actions>
      </v-card>
    </v-dialog>
        <!-- Dialog เลือกขนส่ง -->
        <v-dialog v-model="DialogTransport" :width="MobileSize ? '100%' : '600'" persistent>
          <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
            <v-card-text class="px-0">
              <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 600px'" class="backgroundHead"
                style="position: absolute; height: 120px;">
                <v-row style="height: 120px;">
                  <v-col style="text-align: center;" class="pt-6">
                    <span
                      :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>เลือกขนส่ง</b></span>
                  </v-col>
                  <v-btn fab small @click="DialogTransport = !DialogTransport" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon
                      color="white">mdi-close</v-icon></v-btn>
                </v-row>
              </div>
              <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
                <v-row :width="MobileSize ? '100%' : '459px'"
                  style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                  <v-col style="text-align: center;">
                  </v-col>
                </v-row>
              </div>
              <div class="backgroundContent" style="position: relative;">
                <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
                  <div :class="MobileSize ? 'py-6 px-4' : 'py-12 px-10'">
                    <v-img class="float-left mt-n2 mr-2" src="@/assets/Transport.png" width="30" height="30" contain></v-img>
                    <span :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'" >ขนส่งในการจัดส่งสินค้า</span>
                    <div v-if="!MobileSize">
                      <v-card class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in estimateCostData"
                        :key="index"
                        style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                        <v-row>
                          <v-col cols="1" class="align-self-center mt-2">
                            <v-radio-group v-model="radioTransport" class="mt-0 pt-0">
                              <v-radio color="#27AB9C" :value="item.tpl_name" @click="selectTransport(item)" style="color: #333333">
                              </v-radio>
                            </v-radio-group>
                          </v-col>
                          <!-- <v-img v-if="item.businessType === 'THAILAND_POST'" class="rounded-circle ma-4" style="border: 1px solid" src="@/assets/THAILAND_POST.jpg" contain max-width="125" max-height="125"></v-img>
                          <v-img v-else class="rounded-circle ma-4" style="border: 1px solid" src="@/assets/Flash-express.jpg" contain max-width="125" max-height="125"></v-img> -->
                          <v-img class="rounded-circle ma-4" style="border: 1px solid" :src="item.media_path" contain max-width="125" max-height="125"></v-img>
                          <v-col  class="align-self-center text-end">
                            <span :style="MobileSize ? 'font-size: 16px; font-weight: 700;' : 'font-size: 18px; font-weight: 700;'">{{item.tpl_name}}</span><br>
                            <span :style="MobileSize ? 'font-size: 16px; font-weight: 700; color: #27AB9C;' : 'font-size: 18px; font-weight: 700; color: #27AB9C;'">ค่าส่งเริ่มต้น {{Math.ceil(item.netEstimatePrice)}} บาท</span>
                          </v-col>
                        </v-row>
                      </v-card>
                    </div>
                    <div v-else>
                      <v-card class="mt-7 pa-2" elevation="0" width="100%" height="100%" v-for="(item, index) in estimateCostData"
                        :key="index"
                        style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                        <v-row>
                          <v-col cols="1" class="align-self-center mt-2">
                            <v-radio-group v-model="radioTransport" class="mt-0 pt-0">
                              <v-radio color="#27AB9C" :value="item.tpl_name" @click="selectTransport(item)" style="color: #333333">
                              </v-radio>
                            </v-radio-group>
                          </v-col>
                          <v-col cols="4" class="pr-0">
                            <!-- <v-img v-if="item.businessType === 'THAILAND_POST'" class="rounded-pill ma-0 ml-2" style="border: 1px solid" src="@/assets/THAILAND_POST.jpg" max-width="60" max-height="60"></v-img>
                            <v-img v-else class="rounded-pill ma-0 ml-2" style="border: 1px solid" src="@/assets/Flash-express.jpg" max-width="60" max-height="60"></v-img> -->
                            <v-img class="rounded-circle ma-4" style="border: 1px solid" :src="item.media_path" contain max-width="60" max-height="60"></v-img>
                          </v-col>
                          <v-specer class="mx-auto"></v-specer>
                          <v-col cols="auto" class="text-end align-self-center pl-0">
                            <span :style="MobileSize ? 'font-size: 12px; font-weight: 700;' : 'font-size: 18px; font-weight: 700;'">{{item.tpl_name}}</span><br>
                            <span :style="MobileSize ? 'font-size: 12px; font-weight: 700; color: #27AB9C;' : 'font-size: 18px; font-weight: 700; color: #27AB9C;'">ค่าส่งเริ่มต้น {{Math.ceil(item.netEstimatePrice)}} บาท</span>
                          </v-col>
                        </v-row>
                      </v-card>
                    </div>
                  </div>
                </v-card>
              </div>
            </v-card-text>
          </v-card>
        </v-dialog>
        <!-- Dialog เปลี่ยนที่อยู่ -->
        <v-dialog v-model="DialogAddress" :width="MobileSize ? '100%' : '918'" persistent>
          <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
            <v-card-text class="px-0">
              <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead"
                style="position: absolute; height: 120px;">
                <v-row style="height: 120px;">
                  <v-col style="text-align: center;" class="pt-6">
                    <span
                      :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>เปลี่ยนที่อยู่ในการจัดส่งสินค้า</b></span>
                  </v-col>
                  <v-btn fab small @click="cancelChangeAddrss()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon
                      color="white">mdi-close</v-icon></v-btn>
                </v-row>
              </div>
              <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
                <v-row :width="MobileSize ? '100%' : '918px'"
                  style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                  <v-col style="text-align: center;">
                  </v-col>
                </v-row>
              </div>
              <div class="backgroundContent" style="position: relative;">
                <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
                  <div :class="MobileSize ? 'py-6 px-4' : 'py-12 px-10'">
                    <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                    <span :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'" >ที่อยู่ในการจัดส่งสินค้า</span>
                    <v-card v-show="!MobileSize" class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in userdetail"
                      :key="index"
                      style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                      <v-row>
                        <v-col cols="1" class="align-self-center mt-2">
                          <v-radio-group v-model="item.default_address" class="mt-0 pt-0">
                            <v-radio color="#27AB9C" value="Y" :disabled="item.default_address === 'Y'" @click="setDefaultAdress(item)" style="color: #333333">
                            </v-radio>
                          </v-radio-group>
                        </v-col>
                        <div class="col-11 row">
                          <v-col class="pl-0 pb-0" cols="7">
                            <span style="font-size: 16px; font-weight: 600;">{{ roleCustomer !== 'general' ? item.name : item.first_name + ' ' + item.last_name }}</span>
                            <span class="px-1" style="color: #EBEBEB;">|</span>
                            <span style="font-size: 16px; font-weight: 600;">{{ item.phone }}</span>
                          </v-col>
                          <v-col class="pl-0 pb-0" cols="5">
                            <v-row no-gutters justify="end">
                              <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                                @click="editAddressCus('edit', item)"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                                  style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                              <v-btn :disabled="userdetail.length === 1 || item.default_address === 'Y'" color="red" text
                                style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                                @click="DeleteAddress(item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                                  style="text-decoration-line: underline;">ลบ</span></v-btn>
                            </v-row>
                          </v-col>
                          <v-col class="pb-2 pl-0 pa-3 pt-0">
                            <span style="font-size: 16px;">{{ item.detail_address }} {{ item.sub_district }} {{ item.district }} {{
                              item.province }} {{ item.postcode }}</span>
                          </v-col>
                        </div>
                      </v-row>
                    </v-card>
                    <v-card v-show="MobileSize" class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in userdetail"
                      :key="index"
                      style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                      <v-row>
                        <v-col  class="align-self-center mt-2">
                          <v-radio-group v-model="item.default_address" class="mt-0 pt-0">
                            <v-radio color="#27AB9C" value="Y" :disabled="item.default_address === 'Y'" @click="setDefaultAdress(item)" style="color: #333333">
                            </v-radio>
                          </v-radio-group>
                        </v-col>
                        <div class="row">
                          <v-col class="pl-0 pb-0 mt-5">
                            <v-row no-gutters justify="end">
                              <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                                @click="editAddressCus('edit', item)"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                                  style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                              <v-btn :disabled="userdetail.length === 1 || item.default_address === 'Y'" color="red" text
                                style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                                @click="DeleteAddress(item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                                  style="text-decoration-line: underline;">ลบ</span></v-btn>
                            </v-row>
                          </v-col>
                        </div>
                        <v-col class="pt-2" cols="12">
                            <span :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 16px; font-weight: 600;'" >{{ roleCustomer !== 'general' ? item.name : item.first_name + ' ' + item.last_name }}</span>
                            <span class="px-1" style="color: #EBEBEB;">|</span>
                            <span :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 16px; font-weight: 600;'">{{ item.phone }}</span>
                          </v-col>
                        <v-col class="pb-2 px-2 pt-0">
                          <span :style="MobileSize ? 'font-size: 12px;' : 'font-size: 16px;'">{{ item.detail_address }} {{ item.sub_district }} {{ item.district }} {{
                            item.province }} {{ item.postcode }}</span>
                        </v-col>
                      </v-row>
                    </v-card>
                    <div v-if="userdetail.length === 0" style="text-align: -webkit-center;">
                      <v-img src="@/assets/emptypo.png" width="500" height="100%"></v-img>
                      <v-col class="pt-10 pb-0">
                        <span  :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'">ไม่มีที่อยู่ในการจัดส่งสินค้า</span>
                      </v-col>
                    </div>
                    <v-card v-if="userdetail.length !== 10" class="mt-7" elevation="0"
                      style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="addAddressCustomer('addAddress','')">
                      <v-card-text class="py-2">
                        <v-row class="my-2 px-3 ">
                          <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                          <span class="pl-2"
                            style="font-weight: 500; font-size: 14px; color: #1B5DD6;">เพิ่มที่อยู่ใหม่</span>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </div>
                </v-card>
              </div>
            </v-card-text>
            <!-- <v-card-actions style="height: 88px; background-color: #F5FCFB;">
              <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="cancelChangeAddrss()">ยกเลิก</v-btn>
              <v-spacer></v-spacer>
              <v-btn color="#27AB9C" rounded width="125" height="40" class="white--text"
                @click="confirmAddress()">ยืนยัน</v-btn>
            </v-card-actions> -->
          </v-card>
        </v-dialog>
            <!-- Dialog เปลี่ยนที่อยู่ Tax -->
    <v-dialog v-model="DialogTaxAddress" :width="MobileSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;" >
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead"
            style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-6">
                <span
                :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>เปลี่ยนที่อยู่ในการออกใบกำกับภาษี</b></span>
              </v-col>
              <v-btn @click="DialogTaxAddress = !DialogTaxAddress" fab small icon  :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <div  :class="MobileSize ? 'py-6 px-4' : 'py-12 px-10'">
                <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                <span :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'">ที่อยู่ใบกำกับภาษี</span>
                <v-card v-show="!MobileSize" class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in invoicedetail" :key="index"
                  style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                  <v-row>
                    <v-col cols="1" class="align-self-center mt-2">
                      <v-radio-group v-model="item.default_address" class="mt-0 pt-0">
                        <v-radio color="#27AB9C" value="Y" :disabled="item.default_address === 'Y'"  @click="setDefaultInvoice(item)" style="color: #333333">
                        </v-radio>
                      </v-radio-group>
                    </v-col>
                    <div class="col-11 row">
                      <v-col class="pl-0 pb-0" cols="6">
                        <span style="font-size: 16px; font-weight: 600;">{{item.name}}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span style="font-size: 16px; font-weight: 600; color: #27AB9C;">{{item.phone}}</span>
                      </v-col>
                      <v-col class="pl-0 pb-0" cols="6">
                        <v-row no-gutters justify="end">
                          <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="editInvoiceAddressCus('edit', item)"
                            ><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                              style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                          <v-btn color="red" text :disabled="invoicedetail.length === 1 || item.default_address === 'Y'"
                            style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="openDeleteInvoice(item)"
                            ><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                              style="text-decoration-line: underline;">ลบ</span></v-btn>
                        </v-row>
                      </v-col>
                      <!-- <v-col class="py-0 pl-0" v-if="item.tax_type !== 'Personal'">
                        <span style="font-size: 16px;">รหัสสาขา: <b>{{item.branch_id}}</b></span>
                      </v-col> -->
                      <v-col cols=12 class="pb-2 pl-0 pa-3 pt-0">
                        <span style="font-size: 16px;">เลขประจำตัวผู้เสียภาษี: <b>{{item.tax_id}}</b></span>
                      </v-col>
                      <v-col cols="12" class="pb-2 pl-0 pa-3 pt-0">
                        <span style="font-size: 16px;">ที่อยู่: {{item.detail_address}} {{item.sub_district}} {{item.district}} {{item.province}} {{item.postcode}}</span>
                      </v-col>
                    </div>
                  </v-row>
                </v-card>
                <v-card v-show="MobileSize" class="mt-4 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in invoicedetail" :key="index"
                  style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                  <v-row>
                    <v-col class="align-self-center mt-2">
                      <v-radio-group v-model="item.default_address" class="mt-0 pt-0">
                        <v-radio color="#27AB9C" value="Y" :disabled="item.default_address === 'Y'" @click="setDefaultInvoice(item)" style="color: #333333">
                        </v-radio>
                      </v-radio-group>
                    </v-col>
                    <div class="row">
                      <v-col class="pl-0 pb-0 mt-5">
                        <v-row no-gutters justify="end">
                          <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="editInvoiceAddressCus('edit', item)"
                            ><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                              style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                          <v-btn color="red" text :disabled="invoicedetail.length === 1 || item.default_address === 'Y'"
                            style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="openDeleteInvoice(item)"
                            ><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                              style="text-decoration-line: underline;">ลบ</span></v-btn>
                        </v-row>
                      </v-col>
                    </div>
                      <v-col class="pt-2" cols="12">
                        <span style="font-size: 14px; font-weight: 600;">{{item.name}}</span>
                        <!-- <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span style="font-size: 14px; font-weight: 600; color: #27AB9C;">{{item.phone}}</span> -->
                      </v-col>
                      <!-- <v-col class="py-0 pl-2" v-if="item.tax_type !== 'Personal'">
                        <span style="font-size: 14px;">รหัสสาขา: <b>{{item.branch_id}}</b></span>
                      </v-col> -->
                      <v-col cols=12 class="pb-2 px-2 pt-0">
                        <span style="font-size: 14px;">เลขประจำตัวผู้เสียภาษี: <b>{{item.tax_id}}</b></span>
                      </v-col>
                      <v-col cols="12" class="pb-2 px-2 pt-0">
                        <span style="font-size: 12px;">ที่อยู่: {{item.detail_address}} {{item.sub_district}} {{item.district}} {{item.province}} {{item.postcode}}</span>
                      </v-col>
                  </v-row>
                </v-card>
                <div v-if="invoicedetail.length === 0" style="text-align: -webkit-center;">
                  <v-img src="@/assets/emptypo.png" width="500" height="100%"></v-img>
                  <v-col class="pt-10 pb-0">
                    <span  :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'">ไม่มีที่อยู่ในการออกใบกำกับภาษี</span>
                  </v-col>
                </div>
                <v-card v-if="invoicedetail.length !== 5" class="mt-7" elevation="0"
                  style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="addAddressInvoiceCustomer('addAddress')">
                  <v-card-text class="py-2">
                    <v-row class="my-2 px-3 ">
                      <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                      <span class="pl-2"
                        style="font-weight: 500; font-size: 14px; color: #1B5DD6;">เพิ่มที่อยู่ใหม่</span>
                    </v-row>
                  </v-card-text>
                </v-card>
              </div>
            </v-card>
          </div>
        </v-card-text>
        <!-- <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="cancelChangeAddrss()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" height="40" class="white--text"
            @click="confirmAddress()">ยืนยัน</v-btn>
        </v-card-actions> -->
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogAwaitConfirm" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="240px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn color="#CCCCCC" icon
              @click="dialogAwaitConfirm = !dialogAwaitConfirm">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p :style="MobileSize ? 'font-weight: 700; font-size: 20px; line-height: 24px; color: #333333;' : 'font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;'" class="my-4"><b>ยืนยันการขอซื้อ</b></p>
            <span :style="MobileSize ? 'font-weight: 400; font-size: 14px; line-height: 24px; color: #9A9A9A;' : 'font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;'">คุณต้องการทำรายการนี้ ใช่
              หรือ ไม่</span>
            <v-spacer class="my-2"></v-spacer>
            <span :style="MobileSize ? 'font-weight: 400; font-size: 14px; line-height: 24px; color: #9A9A9A;' : 'font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;'">* ผู้ซื้อสามารถดูใบสั่งซื้อได้จาก <b class="text-decoration-underline">ใบเสนอราคา</b></span>
          </v-card-text>
          <v-card-text>
            <v-row v-if="!MobileSize" dense justify="center">
              <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4"
                @click="dialogAwaitConfirm = !dialogAwaitConfirm">ยกเลิก</v-btn>
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C"
                @click="CreateOrder('payment')">ตกลง</v-btn>
            </v-row>
            <v-row v-if="MobileSize" dense>
              <v-btn height="38" outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }"
                @click="dialogAwaitConfirm = !dialogAwaitConfirm">ยกเลิก</v-btn>
              <v-btn height="38" class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }"
                @click="CreateOrder('payment')">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogAwaitConfirmDelete" :style="MobileSize ? 'z-index: 16000004' : ''" persistent width="424">
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogAwaitConfirmDelete = !dialogAwaitConfirmDelete"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 20px; line-height: 24px; color: #333333;" class="my-4" ><b>ลบที่อยู่ในการจัดส่งสินค้า</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row v-if="!MobileSize" dense justify="center">
              <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogAwaitConfirmDelete = !dialogAwaitConfirmDelete">ยกเลิก</v-btn>
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="deleteAddress()">ตกลง</v-btn>
            </v-row>
            <v-row v-if="MobileSize" dense justify="center">
              <v-btn height="38" outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }" @click="dialogAwaitConfirmDelete = !dialogAwaitConfirmDelete">ยกเลิก</v-btn>
              <v-btn height="38" class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="deleteAddress()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogDeleteSuccess" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogDeleteSuccess = !dialogDeleteSuccess"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 20px; line-height: 24px; color: #333333;" class="my-4"><b>ลบที่อยู่เสร็จสิ้น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการลบที่อยู่ในการจัดส่งสินค้าเรียบร้อย</span><br/>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center" v-if="!MobileSize">
            <v-btn width="156" height="38" dark rounded color="#27AB9C" @click="dialogDeleteSuccess = !dialogDeleteSuccess">ตกลง</v-btn>
            </v-row>
            <v-row dense justify="center" v-if="MobileSize">
            <v-btn height="38" dark rounded color="#27AB9C" :style="{ flex: '1' }" @click="dialogDeleteSuccess = !dialogDeleteSuccess">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogDeleteTax" :style="MobileSize ? 'z-index: 16000004' : ''" persistent width="424">
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogDeleteTax = !dialogDeleteTax"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 20px; line-height: 24px; color: #333333;" class="my-4" ><b>ลบที่อยู่ในการออกใบกำกับภาษี</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row v-if="!MobileSize" dense justify="center">
              <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogDeleteTax = !dialogDeleteTax">ยกเลิก</v-btn>
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="DeleteInvoice()">ตกลง</v-btn>
            </v-row>
            <v-row v-if="MobileSize" dense justify="center">
              <v-btn height="38" outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }" @click="dialogDeleteTax = !dialogDeleteTax">ยกเลิก</v-btn>
              <v-btn height="38" class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="DeleteInvoice()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogDeleteSuccessTax" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogDeleteSuccessTax = !dialogDeleteSuccessTax"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 20px; line-height: 24px; color: #333333;" class="my-4"><b>ลบที่อยู่เสร็จสิ้น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการลบที่อยู่ในการออกใบกำกับภาษีเรียบร้อย</span><br/>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center" v-if="!MobileSize">
            <v-btn width="156" height="38" dark rounded color="#27AB9C" @click="dialogDeleteSuccessTax = !dialogDeleteSuccessTax">ตกลง</v-btn>
            </v-row>
            <v-row dense justify="center" v-if="MobileSize">
            <v-btn height="38" dark rounded color="#27AB9C" :style="{ flex: '1' }" @click="dialogDeleteSuccessTax = !dialogDeleteSuccessTax">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { Decode, Encode } from '@/services'
import { Table, Space, TimePicker } from 'ant-design-vue'
import Vue from 'vue'
export default {
  components: {
    'a-table': Table,
    'a-space': Space,
    'a-time-picker': TimePicker,
    'a-time-range-picker': TimePicker.RangePicker,
    // EditModalAddress: () => import('@/components/Modal/EditAddressProfile.vue'),
    ModalTaxInvoice: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalAddress/TaxInvoiceAddress'),
    CouponCart: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalCoupon/ListCoupon'),
    PointCart: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalCoupon/ListPoint'),
    EditModalAddress: () => import(/* webpackPrefetch: true */ '@/components/Shop/SalesOrder/ModalSaleOrder/EditAddressCustomerSale'),
    EditModalEtaxAddress: () => import(/* webpackPrefetch: true */ '@/components/Shop/SalesOrder/ModalSaleOrder/EditETAXAddressCustomerSale')
    // ModalQuotation: () => import('@/components/Cart/ModalQuotation/QuotationModal')
  },
  data () {
    return {
      noShipping: false,
      CartInvAddress: [],
      SaleVendor: false,
      usePointOrNot: '',
      XBaht: '',
      PointData: '',
      CouponData: [],
      countproductgen: false,
      RequiredInvoice: '',
      invoiceID: '',
      EtaxType: '',
      DialogTransport: false,
      roleCustomer: '',
      CartAddress: [],
      reShippingData: false,
      ShippingData: [],
      estimateCostData: [],
      btnEstimateCost: false,
      radioTransport: '',
      showInvoiceAddress: false,
      showAddress: false,
      cusID: '',
      deleteTaxAdressData: [],
      dialogDeleteSuccessTax: false,
      dialogDeleteTax: false,
      dialogDeleteSuccess: false,
      dialogAwaitConfirmDelete: false,
      deleteAdressData: [],
      DialogTaxAddress: false,
      invoicedetail: [],
      userdetail: [],
      DialogAddress: false,
      radioTax: '',
      radioInvoice: 'radioinvoice',
      disabledHours () {
        const currentHour = new Date().getHours()
        // Disable current hour and hours before it
        return Array.from({ length: currentHour + 1 }, (v, k) => k)
      },
      disabledMinutes (hour) {
        if (hour === new Date().getHours()) {
          // Disable current minute and minutes before it
          const currentMinute = new Date().getMinutes()
          return Array.from({ length: currentMinute + 1 }, (v, k) => k)
        }
        // Disable all minutes for other hours
        return []
      },
      timeselect: '',
      today: new Date().toISOString().substr(0, 10),
      futureDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().substr(0, 10),
      todayselect: false,
      timeselecttoday: '',
      contractDate: '',
      dates: '',
      menu: false,
      radios: 'radio-2',
      dialogAwaitConfirm: false,
      pathShopSale: '',
      // shopDetail: [],
      modalContractStartDate: false,
      modalContractEndDate: false,
      CartPage: 'CartPage',
      role: '',
      // items: [
      //   {
      //     text: 'หน้าแรก',
      //     disabled: false,
      //     href: this.pathShopSale.path.toString()
      //   },
      //   {
      //     text: 'รถเข็น',
      //     disabled: false,
      //     href: '/shoppingcart'
      //   },
      //   {
      //     text: 'รายการสั่งซื้อสินค้า',
      //     disabled: true,
      //     href: '/checkoutSaleOrder'
      //   }
      // ],
      lazy: false,
      overlay: false,
      cartData: '',
      itemsCart: [],
      Address: '',
      Fullname: '',
      EditAddressDetail: '',
      titleAddress: '',
      address_data: '',
      taxAddress: '',
      companyName: '',
      companyTaxID: '',
      checkAdminQU: true,
      pplURL: '',
      pplToken: '',
      page: '',
      taxinvoiceAddress: [],
      taxinvoiceAddressNew: [],
      googleItem: [],
      taxRoles: 'No',
      selectTypeAddress: 'Normal',
      oneDataSpecial: '',
      modalPayment: false,
      responseSentDataPPL: '',
      SelectCouponOrPoint: true,
      dialog_Cancel_Coupon: false,
      checkOwnShop: 'N',
      discountBaht: '',
      discountCode: '',
      disableButtonPay: false,
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      minDate: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      date1: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      searchContractStartDate: '',
      setMinDateContractEndDate: '',
      contractStartDate: '',
      searchContractEndDate: '',
      contractEndDate: '',
      ActiveDiscount: false,
      selectDiscount: '',
      discount: '',
      contractSet: false,
      reason: '',
      selectBudget: '',
      choose_list: '',
      selectTypeDoc: '',
      tax_id: '',
      itemTypeDoc: [],
      selectedPr: '',
      itemCodePrList: [],
      itemBudget: [
        { text: 'งบดำเนินการ', value: 'operating_budget' },
        { text: 'งบลงทุน', value: 'investment_budget' },
        { text: 'งบรายจ่ายประจำ', value: 'regular_expenditure_budget' }
      ],
      selectCutBudget: '',
      itemCutBudget: [
        { text: 'ต้นทุนขาย (COGS)', value: 'COGS' },
        { text: 'ค่าใช้จ่ายและบริการ (SG&A)', value: 'SG&A' },
        { text: 'ต้นทุนวิจัยและพัฒนา (R&D)', value: 'R&D' }
      ],
      Name_Buyer: '',
      Phone_Buyer: '',
      Position_Buyer: '',
      Email_Buyer: '',
      Name_Audit1: '',
      Phone_Audit1: '',
      Position_Audit1: '',
      Email_Audit1: '',
      Name_Audit2: '',
      Phone_Audit2: '',
      Position_Audit2: '',
      Email_Audit2: '',
      json_personal: [],
      purchasing_cheif: [],
      inspectors_one: [],
      inspectors_two: [],
      name: '',
      position: '',
      phone: '',
      email: '',
      buyer_name: '',
      buyer_phone: '',
      buyer_email: '',
      a: '',
      Rules: {
        Name: [
          v => !!v || 'กรุณากรอกชื่อ-สกุล'
        ],
        Phone: [
          v => !!v || 'กรุณากรอกเบอร์โทร',
          v => /^[0-9]{10}$/.test(v) || 'กรุณากรอกเบอร์โทรให้ถูกต้อง'
        ],
        Position: [
          v => !!v || 'กรุณากรอกตำแหน่ง'
        ],
        EmailBuyer: [
          v => !!v || 'กรุณากรอก Email',
          v => /^\w+([.-]?\w+)@[a-zA-Z]+([.-]?[a-zA-Z]+)(.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอก Email ให้ถูกต้อง'
        ],
        Email: [
          // v => !!v || 'กรุณากรอก Email2',
          // v => /^\w+([.-]?\w+)@[a-zA-Z]+([.-]?[a-zA-Z]+)(.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอก Email ให้ถูกต้อง'
        ]
      }
    }
  },
  computed: {
    formattedTime () {
      if (this.dates !== this.today) {
        const dateObject = new Date(this.timeselect)
        const hours = dateObject.getHours()
        const minutes = dateObject.getMinutes()

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`
      } else {
        const dateObject = new Date(this.timeselecttoday)
        const hours = dateObject.getHours()
        const minutes = dateObject.getMinutes()

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    headers () {
      const headers = [
        {
          title: 'รหัส SKU',
          dataIndex: 'sku',
          key: 'sku',
          scopedSlots: { customRender: 'sku' },
          width: '15%'
        },
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '25%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'revenue_default',
          key: 'revenue_default',
          scopedSlots: { customRender: 'revenue_default' },
          align: 'center',
          width: '20%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          align: 'center',
          width: '10%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'total_revenue_default',
          scopedSlots: { customRender: 'total_revenue_default' },
          key: 'total_revenue_default',
          align: 'center',
          width: '15%'
        }
        // {
        //   title: 'Item Code PR',
        //   dataIndex: 'item_code_pr',
        //   scopedSlots: { customRender: 'item_code_pr' },
        //   key: 'item_code_pr',
        //   align: 'center',
        //   width: '20%'
        // }
      ]
      return headers
    },
    headersMobile () {
      const headersMobile = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '100%'
        }
      ]
      return headersMobile
    },
    checkSelectPr () {
      const checkPr = this.itemsCart.length === 0 ? true : this.itemsCart.choose_list[0].product_list.some(v => v.item_code_pr_buyer === null)
      return checkPr
    }
  },
  async created () {
    this.pathShopSale = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale')))
    this.items = [
      {
        text: 'หน้าแรก',
        disabled: false,
        href: this.pathShopSale.path.toString()
      },
      {
        text: 'รถเข็น',
        disabled: false,
        href: '/shoppingcart'
      },
      {
        text: 'รายการสั่งซื้อสินค้า',
        disabled: true,
        href: '/checkoutSaleOrder'
      }
    ]
    this.$EventBus.$on('SentGetCart', this.getCart)
    this.$EventBus.$on('EditAddressComplete', (data) => { this.getCart(data) })
    // this.$EventBus.$on('GetTaxAddress', this.checkAddressTaxinvoiceData)
    // this.$EventBus.$on('GetInvoiceAddress', this.checkAddressTaxinvoiceDataNew)
    this.$EventBus.$on('selectcouponorpointCheckout', this.selectcouponorpointCheckout)
    this.$EventBus.$emit('GetTaxAddress')
    // this.checkAddressTaxinvoiceDataNew()
    // this.checkAddressTaxinvoiceData()
    // this.$EventBus.$on('closeEditModalAddress', this.checkStatus)
    localStorage.removeItem('SetAddressCustomer')
    // localStorage.removeItem('ShippingData')
    var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    this.role = JSON.parse(localStorage.getItem('roleUser'))
    if (this.role.role !== 'sale_order') {
      this.roleCustomer = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
      if (this.roleCustomer === 'vendor') {
        this.SaleVendor = true
      } else {
        this.SaleVendor = false
      }
    } else {
      this.roleCustomer = ''
    }
    this.pplToken = onedata.user.access_token
    if (Object.prototype.hasOwnProperty.call(onedata, 'cartData')) {
      if (onedata.cartData.coupon.length !== 0) {
        if (onedata.cartData.coupon === undefined || onedata.cartData.coupon.length === 0) {
          if (localStorage.getItem('ClickgoToCheckOut') !== null) {
            this.SelectCouponOrPoint = true
          }
        } else {
          localStorage.setItem('ClickgoToCheckOut', true)
          if (localStorage.getItem('ClickgoToCheckOut') !== null) {
            this.SelectCouponOrPoint = false
            if (localStorage.getItem('CouponOrPoint') === 'Point') {
              this.nameCouponOrPoint = 'ใช้คะแนน ' + onedata.cartData.coupon[0].point + ' คะแนน'
            } else {
              // this.nameCouponOrPoint = onedata.cartData.coupon[0].coupon_name
            }
          }
        }
      }
      this.cartData = onedata.cartData
      this.oneDataSpecial = onedata.cartDataSpecialPrice
      this.CouponData = onedata.cartData.coupon
      this.PointData = onedata.cartData.point
      if (this.PointData.length === 0) {
        this.PointData = 0
      } else {
        this.PointData = onedata.cartData.point[0].total_point
      }
      this.usePointOrNot = this.cartData.usePointOrNot
      if (onedata.cartData.start_date_contract !== undefined || onedata.cartData.end_date_contract !== undefined) {
        this.searchContractStartDate = onedata.cartData.start_date_contract
        this.searchContractEndDate = onedata.cartData.end_date_contract
        this.contractStartDate = this.formatDateToShow(this.searchContractStartDate)
        this.contractEndDate = this.formatDateToShow(this.searchContractEndDate)
      }
      // console.log(onedata)
      if (!Array.isArray(onedata.cartData.shipping_data)) {
        this.radioTransport = onedata.cartData.shipping_data.tpl_name
        this.nameTransport = onedata.cartData.shipping_data.tpl_name
        this.costTransport = onedata.cartData.shipping_data.netEstimatePrice
        this.ShippingData = onedata.cartData.shipping_data
        // console.log(this.radioTransport)
      }
      // this.openTypeDoc()
      await this.getAddress()
      await this.getCart()
      this.checkAddress()
      this.getItemCodePr()
      this.getListUserPointByUser()
      this.getAllinvoice()
      setTimeout(() => { this.btnEstimateCost = true }, 2000)
      localStorage.removeItem('ClickgoToCheckOut')
      // console.log('this.role', this.role)
    } else if (this.role !== 'sale_order') {
      this.cartData = ''
      this.$router.push('/')
    } else {
      this.cartData = ''
      this.$router.push('/')
    }
    this.GetPersonal()
  },
  mounted () {
    window.addEventListener('storage', this.handleStorageChange)
    this.$EventBus.$on('SelectCouponCheckout', this.getCart)
    this.$EventBus.$on('listAddress', this.getAddress)
    this.$EventBus.$on('listInvoiceAddress', this.getAllinvoice)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('listAddress')
      this.$EventBus.$off('listInvoiceAddress')
      this.$EventBus.$off('SelectCouponCheckout')
    })
  },
  beforeDestroy () {
    window.removeEventListener('storage', this.handleStorageChange)
    this.$EventBus.$off('SentGetCart')
    this.$EventBus.$off('GetTaxAddress')
    this.$EventBus.$off('EditAddressComplete')
  },
  watch: {
    usePointOrNot (val) {
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      onedata.cartData.usePointOrNot = val
      localStorage.setItem('oneData', Encode.encode(onedata))
    },
    selectTypeAddress (val) {
      var onedata
      // this.discountCode = ''
      // this.discountBaht = ''
      // onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // onedata.cartData.shipping = 'yes'
      // localStorage.setItem('oneData', Encode.encode(onedata))
      // this.ChangeDiscount(this.itemsCart.total_price_no_vat)
      if (val === 'Normal') {
        this.discountCode = ''
        this.discountBaht = ''
        onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        onedata.cartData.shipping = 'yes'
        localStorage.setItem('oneData', Encode.encode(onedata))
        this.ChangeDiscount(this.itemsCart.total_price_no_vat)
      } else {
        onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        onedata.cartData.shipping = 'no'
        localStorage.setItem('oneData', Encode.encode(onedata))
        this.getCart()
      }
    },
    taxRoles (val) {
      if (val === 'Personal' || val === 'Business') {
        this.taxAddress = ''
      }
    },
    discountCode (val) {
      if (val === '') {
        this.discountBaht = ''
        this.ChangeDiscount(this.itemsCart.total_price_no_vat)
      }
    }
  },
  methods: {
    formatPrice (value) {
      const num = Number(value)
      return num < 0
        ? '0.00'
        : num.toLocaleString(undefined, {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })
    },
    async handleStorageChange (event) {
      if (event.key === 'oneData') {
        var NewValue = JSON.parse(Decode.decode(event.newValue))
        var oldValue = JSON.parse(Decode.decode(event.oldValue))
        if (NewValue.cartData !== oldValue.cartData) {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 4500,
            timerProgressBar: true,
            icon: 'warning',
            text: 'ตระกร้ามีการเปลี่ยนแปลง'
          })
          this.backstep()
        }
      }
    },
    async CalculateQT () {
      this.$store.commit('openLoader')
      var data = {
        product_to_calculate: this.itemsCart.choose_list[0].product_list,
        start_date_contract: this.searchContractStartDate,
        end_date_contract: this.searchContractEndDate
      }
      await this.$store.dispatch('actionsCalculateQT', data)
      var response = await this.$store.state.ModuleCart.stateCalculateQT
      // console.log('object', response)
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        for (let i = 0; i < this.itemsCart.choose_list[0].product_list.length; i++) {
          onedata.cartData.product_to_calculate[i].revenue_vat = response.data.product_list[i].revenue_amount
          onedata.cartData.product_to_calculate[i].vat_revenue = 0
        }
        onedata.cartData.start_date_contract = this.searchContractStartDate
        onedata.cartData.end_date_contract = this.searchContractEndDate
        localStorage.setItem('oneData', Encode.encode(onedata))
        onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        // console.log('object', onedata)
        this.getCart()
        // this.CalculateQTData = response.data
      } else {
        this.$store.commit('closeLoader')
        // this.CalculateQTData = []
      }
    },
    async getListUserPointByUser () {
      this.XBaht = []
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var RoleUser = JSON.parse(localStorage.getItem('roleUser'))
      var PartnerID = JSON.parse(localStorage.getItem('partner_id'))
      var data = {
        role_user: RoleUser.role,
        customer_id: RoleUser.role === 'sale_order_no_JV' ? PartnerID : -1,
        seller_shop_id: onedata.cartData.seller_shop_id,
        company_id: onedata.cartData.company_id,
        com_perm_id: onedata.cartData.com_perm_id
      }
      await this.$store.dispatch('actionsgetDetailUserPointByUser', data)
      var res = await this.$store.state.ModuleManagePoint.stategetDetailUserPointByUser
      if (res.result === 'SUCCESS') {
        this.XBaht = res.data[0].x_baht
        this.XBaht = parseFloat(res.data[0].x_baht) / parseFloat(res.data[0].x_point)
        // console.log('this.PointData', this.PointData)
      } else if (res.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>แต้มขัดข้อง กรุณาลองใหม่ภายหลัง</h3>'
        })
      }
    },
    closePoint () {
      this.PointData = []
      this.getCart(this.CouponData, this.PointData)
      this.$EventBus.$emit('clearPoint')
    },
    closeCoupon (item) {
      this.CouponData = []
      this.PointData = []
      this.usePointOrNot = 'no'
      this.noShipping = false
      this.getCart(this.CouponData, this.PointData)
      this.$EventBus.$emit('clearCoupon', item)
    },
    async clickCoupon (item, point, baht, data) {
      this.page = 'checkoutSaleOrder'
      this.closePoint()
      this.usePointOrNot = 'no'
      this.$refs.CouponCart.open(this.page, item, point, baht, this.radios, data)
      await this.$EventBus.$emit('getListCoupon')
    },
    clickPoint (item, point, baht, discoupon, data) {
      this.page = 'checkout'
      this.$refs.PointCart.open(this.page, item, point, baht, discoupon, data)
      this.$EventBus.$emit('getListPoint')
    },
    async EditQuotation () {
      localStorage.setItem('SetAddressCustomer', Encode.encode(this.CartAddress))
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      onedata.cartData.coupon = this.CouponData
      onedata.cartData.start_date_contract = this.searchContractStartDate
      onedata.cartData.end_date_contract = this.searchContractEndDate
      localStorage.setItem('oneData', Encode.encode(onedata))
      // console.log('onedata', onedata)
      if (this.roleCustomer !== 'vendor') {
        await this.$router.push({ path: '/QuotationSale' }).catch(() => { })
      } else {
        this.invoicedetail.forEach(element => {
          if (element.default_address === 'Y') {
            this.CartInvAddress = element
          }
        })
        localStorage.setItem('SetAddressInvCustomer', Encode.encode(this.CartInvAddress))
        await this.$router.push({ path: '/QuotationVendor' }).catch(() => { })
      }
    },
    selectTransport (item) {
      if (this.radioTransport === item.tpl_name) {
        this.nameTransport = item.tpl_name
        this.costTransport = item.netEstimatePrice
        // var shippingData = {
        //   tpl_name: item.tpl_name,
        //   service_provider: item.service_provider,
        //   estimatePrice: item.estimatePrice,
        //   pricePolicy: item.pricePolicy,
        //   upCountry: item.upCountry,
        //   upCountryAmount: item.upCountryAmount,
        //   insureDeclareValue: item.insureDeclareValue,
        //   valueInsuranceFee: item.valueInsuranceFee,
        //   freightInsureEnabled: item.freightInsureEnabled,
        //   freightInsurance: item.freightInsurance,
        //   expressCategory: item.expressCategory,
        //   insured: item.insured,
        //   netEstimatePrice: item.netEstimatePrice,
        //   businessType: item.businessType,
        //   distance: item.distance,
        //   travelTime: item.travelTime,
        //   vehicleType: item.vehicleType,
        //   formulaName: item.formulaName,
        //   formulaCode: item.formulaCode,
        //   riderActive: item.riderActive,
        //   vehicleType_Id: item.vehicleType_Id,
        //   rawEstimatePrice: item.rawEstimatePrice,
        //   discountAmount: item.discountAmount,
        //   discountAmountPercent: item.discountAmountPercent,
        //   topUpAmount: item.topUpAmount,
        //   topUpAmountPercent: item.topUpAmountPercent,
        //   serviceType: item.serviceType
        // }
        var shippingData = item
        this.ShippingData = shippingData
        localStorage.setItem('ShippingData', Encode.encode(this.ShippingData))
        var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        onedata.cartData.shipping_data = this.ShippingData === undefined ? [] : this.ShippingData
        localStorage.setItem('oneData', Encode.encode(onedata))
        // console.log(onedata)
        this.DialogTransport = false
        this.getCart(this.CouponData, this.PointData)
      }
    },
    async EstimateCost () {
      this.$store.commit('openLoader')
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyId = ''
      // var comPermId = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        // console.log(1)
        var companyDataID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        // console.log(2)
        companyId = companyDataID.company.company_id
      } else {
        companyId = -1
      }
      // console.log(companyId)
      const ShopID = JSON.parse(localStorage.getItem('ShopID'))
      const PartnerID = JSON.parse(localStorage.getItem('partner_id'))
      var conditionMet = false
      if (this.userdetail[0].detail_address === '' || this.userdetail.length === 0) {
        this.DialogTransport = false
        // console.log(6666)
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาเลือกที่อยู่จัดส่งสินค้าก่อน หรือ กรอก/เพิ่มที่อยู่จัดส่งสินค้าให้สมบูรณ์ก่อน</h3>'
        })
        this.DialogAddress = true
        conditionMet = true
      }
      if (conditionMet) {
        this.$store.commit('closeLoader')
        return
      }
      var data = {
        address: this.CartAddress,
        role_user: dataRole.role,
        company_id: companyId,
        customer_id: PartnerID,
        com_perm_id: '-1',
        seller_shop_id: ShopID
      }
      // console.log('data', data)
      await this.$store.dispatch('actionsEstimateCost', data)
      var res = await this.$store.state.ModuleCart.stateEstimateCost
      if (res.result === 'Success') {
        this.DialogTransport = true
        this.estimateCostData = res.data
        this.$store.commit('closeLoader')
        if (this.estimateCostData.length === 0 && this.itemsCart.shipping_method !== 0) {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            html: '<h3>ไม่มีขนส่งรองรับ</h3>'
          })
          this.backstep()
        }
      } else if (res.code === 400) {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          html: `<h4>${res.message}</h4>`
        })
        this.backstep()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          html: '<h3>ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่</h3>'
        })
        this.DialogTransport = false
      }
    },
    ClearRadio () {
      this.dates = ''
      this.contractDate = ''
      this.timeselect = ''
      this.timeselecttoday = ''
      this.radioCreditTerm = 'No'
      this.radioTransport = ''
      this.radioPayment = ''
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      onedata.cartData.shipping_data = []
      localStorage.setItem('oneData', Encode.encode(onedata))
      // console.log(onedata)
      this.getCart()
    },
    async setDefaultInvoice (item) {
      this.showInvoiceAddress = true
      const ShopID = JSON.parse(localStorage.getItem('ShopID'))
      localStorage.setItem('partner_id', this.itemsCart.customer_id)
      var res
      var data
      data = {
        seller_shop_id: ShopID,
        role: this.role.role,
        cus_id: this.itemsCart.customer_id,
        address_id: item.id,
        email: item.email,
        name: item.name,
        first_name: item.first_name,
        last_name: item.last_name,
        phone: item.phone,
        tax_id: item.tax_id,
        house_no: item.house_no,
        room_no: item.room_no,
        floor: item.floor,
        building: item.building,
        moo_ban: item.moo_ban,
        moo_no: item.moo_no,
        soi: item.soi,
        yaek: item.yaek,
        street: item.street,
        sub_district: item.sub_district,
        district: item.district,
        province: item.province,
        postcode: item.postcode,
        invoice_address_id: item.id,
        // default_address: item.default_address
        default_address: 'Y'
      }
      // console.log('sata', data)
      await this.$store.dispatch('actionsUpdateCustomerInvAddress', data)
      res = await this.$store.state.ModuleUser.stateUpdateCustomerInvAddress
      if (res.code === 200) {
        this.getAllinvoice()
        this.$swal.fire({ icon: 'success', title: '<h5>ตั้งค่าเป็นที่อยู่เริ่มต้นแล้ว</h5>', showConfirmButton: false, timer: 1500 })
      } else {
        this.$swal.fire({ icon: 'error', title: '<h5>ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ</5>', showConfirmButton: false, timer: 1500 })
      }
    },
    async setDefaultAdress (item) {
      // this.userdetail = []
      this.showAddress = true
      const ShopID = JSON.parse(localStorage.getItem('ShopID'))
      localStorage.setItem('partner_id', this.itemsCart.customer_id)
      var res
      var data
      data = {
        seller_shop_id: ShopID,
        role: this.role.role,
        cus_id: this.itemsCart.customer_id,
        address_id: item.id,
        name: item.name,
        first_name: item.first_name,
        last_name: item.last_name,
        phone: item.phone,
        house_no: item.house_no,
        room_no: item.room_no,
        floor: item.floor,
        building: item.building,
        moo_ban: item.moo_ban,
        moo_no: item.moo_no,
        soi: item.soi,
        yaek: item.yaek,
        street: item.street,
        sub_district: item.sub_district,
        district: item.district,
        province: item.province,
        postcode: item.postcode,
        // default_address: item.default_address
        default_address: 'Y'
      }
      await this.$store.dispatch('actionsUpdateCustomerAddress', data)
      res = await this.$store.state.ModuleSaleOrder.stateUpdateCustomerAddress
      if (res.code === 200) {
        this.reShippingData = true
        this.getAddress()
        this.$swal.fire({ icon: 'success', title: '<h5>ตั้งค่าเป็นที่อยู่เริ่มต้นแล้ว</h5>', showConfirmButton: false, timer: 1500 })
      } else {
        this.$swal.fire({ icon: 'error', title: '<h5>ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ</5>', showConfirmButton: false, timer: 1500 })
      }
    },
    openDeleteInvoice (item) {
      this.dialogDeleteTax = true
      this.deleteTaxAdressData = item
    },
    async DeleteInvoice () {
      const ShopID = JSON.parse(localStorage.getItem('ShopID'))
      const data = {
        seller_shop_id: ShopID,
        role: this.role.role,
        cus_id: this.deleteTaxAdressData.customer_id,
        invoice_address_id: this.deleteTaxAdressData.id
      }
      // console.log(data)
      // console.log(this.deleteTaxAdressData)
      await this.$store.dispatch('actionsDeleteCustomerInvoiceAddress', data)
      var res = await this.$store.state.ModuleSaleOrder.stateDeleteCustomerInvoiceAddress
      if (res.code === 200) {
        // this.$swal.fire({ icon: 'success', title: 'ลบที่อยู่สำเร็จ', showConfirmButton: false, timer: 1500 })
        this.dialogDeleteSuccessTax = true
        this.getAllinvoice()
        this.$store.commit('openLoader')
        setTimeout(() => { this.$store.commit('closeLoader') }, 2500)
      }
      this.dialogDeleteTax = false
    },
    DeleteAddress (item) {
      this.dialogAwaitConfirmDelete = true
      this.deleteAdressData = item
    },
    async deleteAddress () {
      const ShopID = JSON.parse(localStorage.getItem('ShopID'))
      const data = {
        seller_shop_id: ShopID,
        role: this.role.role,
        cus_id: this.deleteAdressData.customer_id,
        address_id: this.deleteAdressData.id
      }
      // console.log(data)
      // console.log(this.deleteAdressData)
      await this.$store.dispatch('actionsDeleteCustomerAddress', data)
      const res = await this.$store.state.ModuleSaleOrder.stateDeleteCustomerAddress
      if (res.code === 200) {
        // this.$swal.fire({ icon: 'success', title: 'ลบที่อยู่ในการจัดส่งสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.getAddress()
        // this.deleteDialog = false
      } else {
        this.$swal.fire({ icon: 'error', title: 'ลบที่อยู่ในการจัดส่งสินค้าไม่สำเร็จ', showConfirmButton: false, timer: 1500 })
      }
      this.dialogAwaitConfirmDelete = false
      this.dialogDeleteSuccess = true
    },
    editInvoiceAddressCus (actions, item) {
      const ShopID = JSON.parse(localStorage.getItem('ShopID'))
      this.EditAddressDetail = ''
      var val = {
        seller_shop_id: ShopID,
        role: this.role.role,
        cus_id: this.itemsCart.customer_id,
        name: item.name,
        email: item.email,
        first_name: item.first_name,
        last_name: item.last_name,
        phone: item.phone,
        house_no: item.house_no,
        room_no: item.room_no,
        floor: item.floor,
        building: item.building,
        moo_ban: item.moo_ban,
        moo_no: item.moo_no,
        soi: item.soi,
        yaek: item.yaek,
        street: item.street,
        tax_id: item.tax_id,
        sub_district: item.sub_district,
        district: item.district,
        province: item.province,
        postcode: item.postcode,
        address_id: item.id,
        tax_type: item.tax_type,
        default_address: item.default_address,
        invoice_address_id: item.id
      }
      localStorage.setItem('AddAddressCustomer', Encode.encode(val))
      this.customerSaleData = this.customerData
      this.EditAddressDetail = val
      this.titleAddress = 'แก้ไขที่อยู่ในการออกใบกำกับภาษี'
      this.page = 'checkoutSaleOrder'
      this.$EventBus.$emit('actionSale', actions)
      this.$refs.EditModalEtaxAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
    },
    async addAddressInvoiceCustomer (actions) {
      const ShopID = JSON.parse(localStorage.getItem('ShopID'))
      var val = {
        seller_shop_id: ShopID,
        role: this.role.role,
        cus_id: this.itemsCart.customer_id,
        name: '',
        first_name: '',
        last_name: '',
        phone: '',
        house_no: '',
        room_no: '',
        floor: '',
        building: '',
        moo_ban: '',
        moo_no: '',
        soi: '',
        yaek: '',
        street: '',
        sub_district: '',
        district: '',
        province: '',
        tax_type: '',
        postcode: ''
      }
      if (this.cusType === 'general') {
        localStorage.setItem('AddAddressCustomerSale', Encode.encode(val))
      } else {
        localStorage.setItem('AddAddressCustomerBussinessSale', Encode.encode(val))
      }
      this.reShippingData = true
      this.customerSaleData = this.customerData
      this.EditAddressDetail = val
      this.titleAddress = 'เพิ่มที่อยู่ในการออกใบกำกับภาษี'
      this.page = 'checkoutSaleOrder'
      this.$EventBus.$emit('actionSale', actions)
      this.$refs.EditModalEtaxAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
      await this.$EventBus.$emit('getCustomDetail', this.itemsCart.customer_id, ShopID)
    },
    editAddressCus (actions, item) {
      const ShopID = JSON.parse(localStorage.getItem('ShopID'))
      this.EditAddressDetail = ''
      var val = {
        seller_shop_id: ShopID,
        role: this.role.role,
        cus_id: this.itemsCart.customer_id,
        name: item.name,
        first_name: item.first_name,
        last_name: item.last_name,
        phone: item.phone,
        house_no: item.house_no,
        room_no: item.room_no,
        floor: item.floor,
        building: item.building,
        moo_ban: item.moo_ban,
        moo_no: item.moo_no,
        soi: item.soi,
        yaek: item.yaek,
        street: item.street,
        sub_district: item.sub_district,
        district: item.district,
        province: item.province,
        postcode: item.postcode,
        address_id: item.id,
        default_address: item.default_address
      }
      if (val.default_address === 'Y') {
        this.reShippingData = true
      }
      localStorage.setItem('AddAddressCustomer', Encode.encode(val))
      this.customerSaleData = this.customerData
      this.EditAddressDetail = val
      this.titleAddress = 'แก้ไขที่อยู่ในการจัดส่งสินค้า'
      this.page = 'checkoutSaleOrder'
      this.$EventBus.$emit('actionSale', actions)
      this.$refs.EditModalAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
      // this.$EventBus.$emit('EditModalAddress', val, this.titleAddress, this.page, actions)
    },
    addAddressCustomer (actions, item) {
      const ShopID = JSON.parse(localStorage.getItem('ShopID'))
      var val = {
        seller_shop_id: ShopID,
        role: this.role.role,
        cus_id: this.itemsCart.customer_id,
        name: '',
        first_name: '',
        last_name: '',
        phone: '',
        house_no: '',
        room_no: '',
        floor: '',
        building: '',
        moo_ban: '',
        moo_no: '',
        soi: '',
        yaek: '',
        street: '',
        sub_district: '',
        district: '',
        province: '',
        postcode: ''
      }
      localStorage.setItem('AddAddressCustomer', Encode.encode(val))
      this.customerSaleData = this.customerData
      this.EditAddressDetail = val
      this.titleAddress = 'เพิ่มที่อยู่ในการจัดส่งสินค้า'
      this.page = 'checkoutSaleOrder'
      this.$EventBus.$emit('actionSale', actions)
      this.$refs.EditModalAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
    },
    async getAllinvoice () {
      this.$store.commit('openLoader')
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      const ShopID = JSON.parse(localStorage.getItem('ShopID'))
      var data = {
        seller_shop_id: ShopID,
        role: dataRole.role,
        cus_id: this.itemsCart.customer_id,
        page: 1
      }
      await this.$store.dispatch('actionsGetListCustomerInvAddress', data)
      var res = await this.$store.state.ModuleShop.stateGetListCustomerInvAddress
      if (res.code === 200) {
        this.showInvoiceAddress = false
        this.invoicedetail = res.data.all_address
        this.$store.commit('closeLoader')
      }
      this.$store.commit('closeLoader')
      // ดึงข้อมูลจาก page: 2 และถัดไป
    //   for (let page = 2; ; page++) {
    //     data.page = page
    //     await this.$store.dispatch('actionsGetListCustomerInvAddress', data)
    //     var responsePage = await this.$store.state.ModuleShop.stateGetListCustomerInvAddress
    //     if (responsePage.code === 200) {
    //       this.invoicedetail = this.invoicedetail.concat(
    //         responsePage.data.address
    //       )
    //       if (Number(res.data.total_page) < page) {
    //         break
    //       }
    //     } else {
    //       break
    //     }
    //   }
    },
    cancelChangeAddrss () {
      if (this.role.role === 'sale_order_no_JV') {
        this.DialogAddress = false
      }
    },
    async getAddress () {
      // this.userdetail = []
      this.$store.commit('openLoader')
      // console.log(1)
      const customerID = JSON.parse(localStorage.getItem('partner_id'))
      const ShopID = JSON.parse(localStorage.getItem('ShopID'))
      const dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        seller_shop_id: ShopID,
        role: dataRole.role,
        cus_id: customerID,
        page: 1
      }
      // console.log(2)
      await this.$store.dispatch('actionsGetListCustomerAddress', data)
      var res = await this.$store.state.ModuleShop.stateGetListCustomerAddress
      // console.log('res ======>', res)
      if (res.code === 200) {
        // console.log(4)
        this.showAddress = false
        this.userdetail = ''
        this.userdetail = res.data.all_address
        // this.$store.commit('closeLoader')
        // console.log(this.userdetail)
      }
      // ดึงข้อมูลจาก page: 2 และถัดไป
      // for (let page = 2; ; page++) {
      //   data.page = page
      //   await this.$store.dispatch('actionsGetListCustomerAddress', data)
      //   var responsePage = await this.$store.state.ModuleShop.stateGetListCustomerAddress
      //   if (responsePage.code === 200) {
      //     this.userdetail = this.userdetail.concat(
      //       responsePage.data.address
      //     )
      //     if (Number(res.data.total_page) < page) {
      //       break
      //     }
      //   } else {
      //     break
      //   }
      // }
      // console.log('ที่อยู่', this.userdetail)
      this.userdetail.forEach(element => {
        if (element.default_address === 'Y') {
          // console.log(element)
          const roleCustomer = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
          if (roleCustomer === 'general') {
            element.name_th = ''
          } else {
            element.first_name = ''
            element.last_name = ''
          }
          this.CartAddress = [
            {
              name_th: element.name,
              id: element.id,
              first_name: element.first_name,
              last_name: element.last_name,
              detail: element.detail_address,
              house_no: element.house_no,
              room_no: element.room_no,
              floor: element.floor,
              building_name: element.building,
              moo_ban: element.moo_ban,
              moo_no: element.moo_no,
              soi: element.soi,
              yaek: element.yaek,
              street: element.street,
              sub_district: element.sub_district,
              district: element.district,
              province: element.province,
              zip_code: element.postcode,
              phone: element.phone,
              phone_ext: '',
              status: 'Y'
            }
          ]
        }
      })
      await this.getCart()
      this.$store.commit('closeLoader')
    },
    async changeAddress () {
      if (this.role.role === 'sale_order_no_JV') {
        this.DialogAddress = true
      }
    },
    async GetReviewQuotation () {
      this.$store.commit('openLoader')
      // var companyId = ''
      // var comPermId = ''
      const ShopID = JSON.parse(localStorage.getItem('ShopID'))
      const PartnerID = JSON.parse(localStorage.getItem('partner_id'))
      // if (this.role.role !== 'ext_buyer') {
      //   if (localStorage.getItem('SetRowCompany') !== null) {
      //     var companyDataID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      //     companyId = companyDataID.company.company_id
      //     comPermId = companyDataID.position.com_perm_id
      //   } else {
      //     companyId = -1
      //     comPermId = -1
      //   }
      // }
      this.invoicedetail.forEach(element => {
        if (element.default_address === 'Y') {
          this.invoiceID = element.id
        }
      })
      var oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const data = {
        invoice_id: this.invoiceID,
        role_user: this.role.role,
        company_id: '-1',
        customer_id: PartnerID,
        com_perm_id: '-1',
        seller_shop_id: ShopID,
        token: oneData.user.access_token,
        product_free: JSON.stringify(this.itemsCart.product_free)
      }
      // console.log(data)
      await this.$store.dispatch('actionsGetReviewQuotation', data)
      var res = await this.$store.state.ModuleCart.stateGetReviewQuotation
      if (res.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        window.open(res.data.pdf_path, '_blank')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          html: '<h3>ระบบขัดข้องไม่สามารถดูตัวอย่างใบเสนอราคาได้</h3>'
        })
      }
    },
    setValueDate (val) {
      // this.$refs.contractDate.save(val)
      this.contractDate = this.formatDateToShow(val)
      if (this.dates !== this.today) {
        this.timeselect = ''
        this.timeselecttoday = '1'
      } else {
        this.timeselecttoday = ''
        this.timeselect = '1'
      }
    },
    async Confirm () {
      var dataToCheck = ''
      dataToCheck = {
        role_user: this.role.role
      }
      var messageCheckError = ''
      var i
      this.itemsCart.choose_list[0].product_list.forEach(element => {
        if (element.product_type.length >= 1 && element.product_type === 'general') {
          this.countproductgen = true
        }
      })
      if (this.itemsCart.net_price >= 1) {
        if (this.radios === 'radio-2' && this.role.role !== 'sale_order' && this.countproductgen === true && this.radioTransport === '' && this.itemsCart.shipping_method.length !== 0 && this.roleCustomer !== 'vendor') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: '<h3>กรุณาเลือกขนส่งในการจัดส่งสินค้า</h3>'
          })
          await this.EstimateCost()
          this.DialogTransport = true
        } else {
          await this.$store.dispatch('actionsCheckStockBeforeCreateOrder', dataToCheck)
          const response = await this.$store.state.ModuleCart.stateCheckStockBeforeCreateOrder
          if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
            this.dialogAwaitConfirm = true
          } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
            for (i = 0; i < response.data.product_free.length; i++) {
              messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
            }
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'warning',
              text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
            })
            this.backstep()
          } else if (response.message === 'ไม่พบตะกร้า') {
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'warning',
              text: 'ไม่พบตะกร้า'
            })
            this.backstep()
          } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
            for (i = 0; i < response.data.product_list.length; i++) {
              messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
            }
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'warning',
              text: 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
            })
            this.backstep()
          } else {
            for (i = 0; i < response.data.product_list.length; i++) {
              messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
            }
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'warning',
              text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการซื้อ ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
            })
            this.backstep()
          }
        }
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>ราคารวมทั้งหมดไม่ควรมีค่าน้อยกว่า 1 บาท<br>กรุณาเลือก <span style="color: red;">ส่วนลด</span> ใหม่อีกครั้ง</h3>'
        })
      }
    },
    // CheckSpacebarName (e) {
    //   console.log(e)
    //   if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '' || e.target.value === ' ')) {
    //     e.preventDefault()
    //   }
    // },
    // async openTypeDoc () {
    //   this.tax_id = localStorage.getItem('tax_id')
    //   var body = {
    //     tax_id: this.tax_id
    //   }
    //   // console.log('body', body)
    //   await this.$store.dispatch('actionsGetDocumentType', body)
    //   var res = await this.$store.state.ModuleCart.stateGetDocumentType
    //   // console.log('res', res)
    //   console.log('resactionsGetDocumentType', res)
    //   this.itemTypeDoc = res.data.data
    // },
    formatDateToShow (date) {
      if (!date) return null
      // const [year, month, day] = date.split('-')
      // const yearChange = parseInt(year) + 543
      // return `${day}/${month}/${yearChange}`
      return new Date(date).toLocaleDateString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'long', day: 'numeric' })
    },
    setValueContractStartDate (val, index) {
      this.$refs.dialogContractStartDate[index].save(val)
      this.searchContractStartDate = val
      // console.log(this.searchDateNotFormat)
      this.contractStartDate = this.formatDateToShow(val)
      // clear end date กรณีที่ start date เปลี่ยนแปลง
      var setMin = new Date(val)
      this.date1 = new Date(setMin.setDate(setMin.getDate() + 1)).toISOString().substr(0, 10)
      this.setMinDateContractEndDate = this.date1
      // console.log('date1', this.date1)
      this.contractEndDate = ''
      this.searchContractEndDate = ''
      this.CalculateQT()
    },
    setValueContractEndDate (val, chooseindex) {
      this.$refs.dialogContractEndDate[chooseindex].save(val)
      this.searchContractEndDate = val
      // console.log(this.searchDateNotFormat)
      this.contractEndDate = this.formatDateToShow(val)
      this.CalculateQT()
    },
    closeModalContractStartDate () {
      this.modalContractStartDate = false
      this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      this.searchContractStartDate = ''
      this.contractStartDate = ''
      this.contractEndDate = ''
      this.date1 = ''
      this.CalculateQT()
    },
    closeModalContractEndDate () {
      this.modalContractEndDate = false
      // this.date1 = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      // this.searchContractEndDate = ''
      this.date1 = this.setMinDateContractEndDate
      this.contractEndDate = ''
      this.CalculateQT()
    },
    ChangeDiscount (price) {
      var onedata
      this.disableButtonPay = false
      if (this.discountBaht !== '') {
        if (parseFloat(this.discountBaht) < parseFloat(price)) {
          // console.log('value =====>', this.discountCode, this.discountBaht)
          onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
          onedata.cartData.note_of_code = this.discountCode
          onedata.cartData.code_discount = this.discountBaht
          localStorage.setItem('oneData', Encode.encode(onedata))
          this.getCart()
        } else {
          this.disableButtonPay = true
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: '<h3>กรุณากรอกส่วนลดไม่เกินราคา</h3>'
          })
        }
      } else {
        onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        onedata.cartData.note_of_code = this.discountCode
        onedata.cartData.code_discount = this.discountBaht
        localStorage.setItem('oneData', Encode.encode(onedata))
        this.getCart()
      }
      // console.log(onedata)
    },
    // openModalPayment () {
    //   this.modalPayment = true
    // },
    async checkAddress () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('GetUserAddress', data)
      this.propsAddress = this.$store.state.ModuleManageShop.GetUserAddress
      localStorage.setItem('AddressData', Encode.encode(this.propsAddress[0].address_data))
      if (dataRole.role === 'ext_buyer') {
        if (this.propsAddress[0].address_data.length !== 0) {
          if (this.propsAddress[0].address_data.length === 1) {
            localStorage.setItem('AddressUserDetail', Encode.encode(this.propsAddress[0].address_data[0]))
            if (this.propsAddress[0].address_data[0].status === 'Y' && (this.propsAddress[0].address_data[0].default_address === 'N' || this.propsAddress[0].address_data[0].default_address === null)) {
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'warning',
                html: '<h3>คุณยังไม่ได้ตั้งค่าที่อยู่เป็นค่าเริ่มต้น</h3>'
              })
              if (this.MobileSize) {
                this.$router.push({ path: '/addressProfileMobile' })
              } else {
                this.$router.push({ path: '/addressProfile' })
              }
            }
          } else {
            this.propsAddress[0].address_data.forEach(element => {
              if (element.default_address === 'Y') {
                if (element.status === 'N') {
                  localStorage.setItem('AddressUserDetail', Encode.encode(element))
                  this.EditAddressDetail = element
                  this.titleAddress = 'เพิ่มที่อยู่จัดส่งสินค้า'
                  this.page = 'checkout'
                  this.$EventBus.$emit('EditModalAddress')
                }
              }
            })
          }
        }
      }
    },
    // async checkStatus () {
    //   var dataRole = JSON.parse(localStorage.getItem('roleUser'))
    //   var data = {
    //     role_user: dataRole.role
    //   }
    //   await this.$store.dispatch('GetUserAddress', data)
    //   this.propsAddress = this.$store.state.ModuleManageShop.GetUserAddress
    //   if (this.propsAddress[0].address_data.length !== 0) {
    //     this.propsAddress[0].address_data.forEach(element => {
    //       if (element.default_address === 'Y') {
    //         if (element.status === 'N') {
    //           this.backstep()
    //         }
    //       }
    //     })
    //   }
    // },
    async getItemCodePr () {
      this.tax_id = localStorage.getItem('tax_id')
      await this.$store.dispatch('actionsListItemCodePr', this.tax_id)
      var res = await this.$store.state.ModuleCart.stateListItemCodePr
      if (res.message === 'Show section success') {
        this.itemCodePrList = res.data
      }
    },
    async getCart (itemCoupon, itemPoint) {
      this.$store.commit('openLoader')
      // console.log('getCart1', itemCoupon)
      // if (isNaN(itemPoint) && itemPoint !== undefined) {
      //   itemPoint = 0
      // }
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // console.log('1', onedata)
      var PartnerID = JSON.parse(localStorage.getItem('PartnerID'))
      var res
      this.checkOwnShop = 'N'
      this.cartData = onedata.cartData
      if (itemPoint !== undefined) {
        if (onedata.cartData.point.length === 0 || itemPoint.length === 0) {
          this.PointData = 0
          itemPoint = 0
        }
      }
      // if (itemPoint !== null && itemPoint !== undefined) {
      //   this.cartData.point = Number(itemPoint)
      //   this.PointData = Number(itemPoint)
      // } else {
      //   var Point = Number(this.PointData)
      //   this.cartData.point = Number(this.PointData)
      //   this.PointData = Point
      // }
      if (itemPoint !== null && itemPoint !== undefined && itemPoint !== 0) {
        this.cartData.point = Number(itemPoint[0].total_point)
        this.PointData = Number(itemPoint[0].total_point)
      } else {
        var Point = Number(this.PointData)
        this.cartData.point = Number(this.PointData)
        this.PointData = Point
      }
      if (itemCoupon !== null && itemCoupon !== undefined) {
        this.cartData.coupon = itemCoupon
        this.CouponData = itemCoupon
      } else {
        var Coupon = this.CouponData
        this.cartData.coupon = this.CouponData
        this.CouponData = Coupon
      }
      // console.log('tonedata', this.cartData)
      this.cartData.company_id = PartnerID
      if (this.cartData.type_shipping === '') {
        this.cartData.type_shipping = 'online'
      }
      // console.log(this.cartData)
      this.cartData.address = this.CartAddress
      if ((this.reShippingData === false && this.radios === 'radio-2') || !Array.isArray(onedata.cartData.shipping_data)) {
        // console.log(1)
        Vue.set(this.cartData, 'shipping_data', this.ShippingData)
        this.cartData.type_shipping = 'online'
        // console.log(this.cartData)
      } else {
        // console.log(2)
        if (this.CouponData.find(coupon => coupon.seller_shop_id !== -1) !== undefined) {
          if (this.CouponData.find(coupon => coupon.seller_shop_id !== -1).length !== 0) {
            if (this.CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping') {
              this.CouponData = this.CouponData.filter(coupon => coupon.seller_shop_id === -1)
              // console.log('this.CouponData2', this.CouponData)
              this.$EventBus.$emit('clearCoupon')
              this.cartData.coupon = this.CouponData
            } else {
              this.cartData.coupon = this.CouponData
            }
          }
        }
        this.cartData.type_shipping = 'pickup'
        this.ShippingData = []
        Vue.set(this.cartData, 'shipping_data', this.ShippingData)
        this.radioTransport = ''
        // this.radioCreditTerm = 'No'
        // this.radioPayment = ''
        this.reShippingData = false
      }
      // console.log('this.cartData', this.cartData)
      if (Object.prototype.hasOwnProperty.call(onedata, 'cartDataSpecialPrice')) {
        if (onedata.cartDataSpecialPrice === 'yes') {
          await this.$store.dispatch('ActionGetCartSpecialPrice', this.cartData)
          res = await this.$store.state.ModuleCart.stateGetCartSpecialPrice
        } else {
          await this.$store.dispatch('ActionGetCart', this.cartData)
          res = await this.$store.state.ModuleCart.stateGetCart
        }
      }
      if (res.message === 'Get cart success') {
        this.$store.commit('closeLoader')
        this.itemsCart = res.data
        if (this.itemsCart.shipping_method.length === 0 && parseInt(this.itemsCart.shipping_price) === 0) {
          const freeShippingCoupon = this.CouponData.some(coupon => coupon.coupon_type === 'free_shipping')
          this.noShipping = freeShippingCoupon
        } else {
          this.noShipping = false
        }
        if (this.itemsCart.isEtax === 'yes') {
          this.radioTax = 'radiotax'
        }
        // console.log(this.itemsCart.customer_id)
        // this.shopDetail = this.itemsCart.shop_list
        // this.itemsCart.choose_list[0].product_list.forEach(element => {
        //   if (element.item_code_pr_buyer === null) {
        //     element.item_code_pr_buyer = ''
        //   }
        // })
        if (res.data.check_own_shop === 'yes') {
          this.checkOwnShop = 'Y'
        } else {
          this.checkOwnShop = 'N'
        }
        if (this.itemsCart.net_price >= 50000) {
          this.contractSet = true
        }
        // this.googleSentData()
        if (this.itemsCart.address_data.length !== 0) {
          // console.log('getCart2')
          if (dataRole.role === 'sale_order') {
            // console.log('getCart3')
            var addressPurchaser = ''
            this.choose_list = this.itemsCart.choose_list[0].pay_type
            this.address_data = this.itemsCart.address_data[0]
            this.Fullname = this.address_data.first_name + ' ' + this.address_data.last_name
            // addressPurchaser = this.address_data.detail + ' ' + 'แขวง/ตำบล' + ' ' + this.address_data.district + ' ' + 'จังหวัด' + ' ' + this.address_data.province + ' ' + this.address_data.zip_code + ' ' + 'เบอร์โทรศัพท์' + ' ' + this.address_data.phone
            this.Address = addressPurchaser
          } else {
            this.itemsCart.address_data.forEach(element => {
              // console.log(element)
              if (element.default_address === 'Y' || element.default_address !== undefined) {
                this.address_data = element
                this.Fullname = element.first_name + ' ' + element.last_name
                var address = ''
                address = element.detail + ' ' + 'แขวง/ตำบล' + ' ' + element.sub_district + ' ' + 'เขต/อำเภอ' + ' ' + element.district + ' ' + 'จังหวัด' + ' ' + element.province + ' ' + element.zip_code + ' ' + 'เบอร์โทรศัพท์' + ' ' + element.phone
                this.Address = address
              } else {
                this.address_data = element
                this.Fullname = element.first_name + ' ' + element.last_name
                var address1 = ''
                address1 = element.detail + ' ' + 'แขวง/ตำบล' + ' ' + element.sub_district + ' ' + 'เขต/อำเภอ' + ' ' + element.district + ' ' + 'จังหวัด' + ' ' + element.province + ' ' + element.zip_code + ' ' + 'เบอร์โทรศัพท์' + ' ' + element.phone
                this.Address = address1
              }
            })
          }
        } else {
          this.Fullname = ''
          this.Address = 'คุณยังไม่ได้เพิ่มข้อมูลที่อยู่'
        }
        // get user data
        // var data = {
        //   role_user: dataRole.role
        // }
        // await this.$store.dispatch('actionsUserDetailPage', data)
        // var response = await this.$store.state.ModuleUser.stateUserDetailPage
        var response = JSON.parse(Decode.decode(localStorage.getItem('UserDetail')))
        var user = response.data[0]
        this.buyer_name = user.first_name_th + ' ' + user.last_name_th
        this.buyer_phone = user.phone
        this.buyer_email = user.email
        // get admin data
        const sendId = { user_id: user.id }
        await this.$store.dispatch('ActionGetAdminData', sendId)
        var responseAdmin = await this.$store.state.ModuleCart.stateGetAdminData
        var adminStatus = false
        if (responseAdmin.data.length !== 0) {
          adminStatus = true
        } else {
          adminStatus = false
        }
        if (adminStatus === true) {
          this.sentDataPPL()
        }
        this.overlay = false
      } else if (res.message === 'Some parameter missing. [product_to_cal, shop_to_cal, address_id]') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'ใส่ข้อมูลไม่ครบ'
        })
        this.backstep()
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'SERVER ERROR'
        })
        this.backstep()
      } else if (res.message === 'มีความผิดพลาดจากการคำนวนค่าขนส่ง Flash จากที่อยู่ของผู้ใช้งาน') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: 'กรุณาทำรายการยืนยันคำสั่งซื้ออีกครั้ง'
        })
        this.backstep()
      } else if (res.message === 'น้ำหนักของสินค้าในตระกร้ารวมกันแล้วเกิน 50 kg ที่กำหนดขนส่งของ flash') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'สินค้าของคุณมีน้ำหนักรวมของสินค้าเกิน 50 กิโลกรัม หากต้องการสั่งซื้อกรุณาติดต่อเจ้าหน้าที่ ขอบคุณครับ'
        })
        this.backstep()
      } else if (res.message === 'Get cart faild.Some products have weight equal 0.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${'รหัสสินค้า' + ' ' + res.data[0].sku + ' ' + 'มีปัญหาเรื่องน้ำหนัก กรุณาติดต่อเจ้าหน้าที่'}`
        })
        this.backstep()
      } else if (res.message === 'ขออภัยเนื่องจากที่อยู่จัดส่งอยู่นอกเขตพื้นที่บริการ หรือ ขนาดและน้ำหนักของสินค้าเกินมามาตรฐานที่จะจัดส่งได้') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          text: `${res.message}`
        })
        this.backstep()
      } else if (res.message === 'ไม่พบที่อยู่สำหรับจัดส่งสินค้า กรุุณาทำการเพิ่มข้อมูลที่อยู่ใหม่อีกครั้ง') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        if (this.MobileSize) {
          this.$router.push({ path: '/addressProfileMobile' })
        } else {
          this.$router.push({ path: '/addressProfile' })
        }
      } else if (res.message === 'This user is unauthorized.') {
        this.SelectCouponOrPoint = true
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
        // window.location.assign('/')
      } else if (res.message === 'Company User Permission Not Found.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'กรุณาตรวจสอบ เนื่องจากมีการเปลี่ยนแปลงสิทธิ์ของผู้ใช้งาน'
        })
        this.backstep()
      } else if (res.message === 'Not found cart') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        // this.$swal.fire({
        //   showConfirmButton: false,
        //   timer: 2500,
        //   timerProgressBar: true,
        //   icon: 'error',
        //   text: 'กรุณาตรวจสอบ เนื่องจากมีการเปลี่ยนแปลงสิทธิ์ของผู้ใช้งาน'
        // })
        this.backstep()
        this.goHomePage()
      } else {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          text: `${res.message}`
        })
        await this.$EventBus.$emit('CancleBookCouponCheckout')
        this.backstep()
      }
      this.$store.commit('closeLoader')
    },
    async updateSelectPr () {
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = []
      var glAccount = []
      this.itemsCart.choose_list[0].product_list.forEach(element => {
        // console.log(element)
        glAccount = this.itemCodePrList.filter(item => item.material_code === element.item_code_pr_buyer)
        // console.log(element)
        data.push({
          product_id: element.product_id,
          quantity: element.quantity,
          price: element.revenue_vat,
          item_code_pr_buyer: element.item_code_pr_buyer !== undefined || element.item_code_pr_buyer !== null ? element.item_code_pr_buyer : null,
          type_budget_gl_account: glAccount.length !== 0 ? glAccount[0].gl_account : null,
          product_attribute_id: element.product_attribute_detail.product_attribute_id,
          revenue_default: element.revenue_default,
          revenue_default_with_discount: element.revenue_default_with_discount,
          revenue_vat: element.revenue_vat,
          vat_revenue: element.vat_revenue
        })
      })
      onedata.cartData.product_to_calculate = data
      localStorage.setItem('oneData', Encode.encode(onedata))
      await this.getCart()
    },
    // async updateSelectPr () {
    //   var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    //   var data = []
    //   this.itemsCart.choose_list[0].product_list.forEach(element => {
    //     data.push({
    //       product_id: element.product_id,
    //       quantity: element.quantity,
    //       price: element.revenue_vat,
    //       item_code_pr_buyer: element.item_code_pr_buyer !== undefined || element.item_code_pr_buyer !== null ? element.item_code_pr_buyer : null
    //     })
    //   })
    //   onedata.cartData.product_to_calculate = data
    //   localStorage.setItem('oneData', Encode.encode(onedata))
    //   await this.getCart()
    // },
    // googleSentData () {
    //   for (let i = 0; i < this.itemsCart.choose_list[0].product_list.length; i++) {
    //     this.googleItem.push({
    //       item_id: this.itemsCart.choose_list[0].product_list[i].sku,
    //       item_name: this.itemsCart.choose_list[0].product_list[i].product_name,
    //       currency: 'THB',
    //       price: this.itemsCart.choose_list[0].product_list[i].net_price,
    //       quantity: this.itemsCart.choose_list[0].product_list[i].quantity
    //     })
    //   }
    //   // window.dataLayer = window.dataLayer || []
    //   // window.dataLayer.push({
    //   //   event: 'begin_checkout',
    //   //   // currency: 'THB',
    //   //   // value: this.itemsCart.net_price,
    //   //   ecommerce: {
    //   //     items: this.googleItem
    //   //   }
    //   // })
    //   // this.$analytics.fbq.event('AddToCart', {
    //   //   productList: this.itemsCart.choose_list[0].product_list,
    //   //   productTotalPrice: this.itemsCart.net_price
    //   // })
    // },
    backstep () {
      // this.$router.go(-1)
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (onedata.cartDataSpecialPrice === 'yes') {
        this.$router.replace({ path: '/specialPriceBuyerRequest' }).catch(() => { this.$router.go(-1) })
      } else {
        this.$router.replace({ path: '/shoppingcart' }).catch(() => { this.$router.go(-1) })
      }
    },
    confirmCreateOrder () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (dataRole.role === 'sale_order') {
        this.$swal.fire({
          icon: 'warning',
          html: '* ผู้ซื้อสามารถดูใบสั่งซื้อได้จาก <b><u>รายการซื้อของฉัน</u></b>',
          title: '<h5>ยืนยันการขอซื้อหรือไม่</h5>',
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
          // cancelButtonColor: '#d33'
        }).then((result) => {
          if (result.isConfirmed) {
            this.CreateOrder()
            // this.googleSentData()
          } else if (result.isDismissed) {
            this.getCart()
          }
        }).catch(() => {
        })
      } else if (dataRole.role === 'ext_buyer') {
        this.$swal.fire({
          icon: 'warning',
          showCancelButton: true,
          title: '<h5>ยืนยันการขอซื้อหรือไม่</h5>',
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
          // cancelButtonColor: '#aaa'
        }).then((result) => {
          if (result.isConfirmed) {
            this.CreateOrder()
            // this.googleSentData()
          } else if (result.isDismissed) {
            this.getCart()
          }
        }).catch(() => {
        })
      }
    },
    confirmCreateOrderMobile (paymentType) {
      // console.log('confirmCreateOrderMobile', paymentType)
      // if (this.$refs.orderListDetails.validate(true)) {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (dataRole.role === 'sale_order') {
        this.$swal.fire({
          icon: 'warning',
          html: '* ผู้ซื้อสามารถดูใบสั่งซื้อได้จาก <b><u>ใบเสนอราคา</u></b>',
          title: '<h5>ยืนยันการขอซื้อหรือไม่</h5>',
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
        // cancelButtonColor: '#d33'
        }).then((result) => {
          if (result.isConfirmed) {
            this.CreateOrder(paymentType)
          // this.googleSentData()
          } else if (result.isDismissed) {
            this.getCart()
          }
        }).catch(() => {
        })
      } else if (dataRole.role === 'ext_buyer') {
        this.$swal.fire({
          icon: 'warning',
          showCancelButton: true,
          title: '<h5>ยืนยันการขอซื้อหรือไม่</h5>',
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
        // cancelButtonColor: '#aaa'
        }).then((result) => {
          if (result.isConfirmed) {
            this.CreateOrder(paymentType)
          // this.googleSentData()
          } else if (result.isDismissed) {
            this.getCart()
          }
        }).catch(() => {
        })
      }
      // } else {
      //   this.$nextTick(() => {
      //     const el = document.getElementsByClassName('error--text')
      //     if (el) {
      //       document
      //         .getElementsByClassName('error--text')[0]
      //         .scrollIntoView({ behavior: 'smooth', block: 'end' })
      //     }
      //   })
      // }
    },
    // async CreateOrderSyncTaxaddress () {
    //   var data = {
    //     invoice_id: this.taxinvoiceAddress[0].invoice_id,
    //     cart_id: this.itemsCart.id
    //   }
    //   await this.$store.dispatch('ActionCreateOrderSyncTaxaddress', data)
    // },
    async GetPersonal () {
      // const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      var companyId = ''
      var data = ''
      if (localStorage.getItem('PartnerID')) {
        companyId = JSON.parse(localStorage.getItem('PartnerID'))
        data = {
          company_id: companyId.company.company_id
        }
      } else {
        data = {
          company_id: -1
        }
      }
      // console.log('companyId--------->', companyId.company.company_id)
      await this.$store.dispatch('actionsDetailCompany', data)
      var companyData = await this.$store.state.ModuleAdminManage.stateDetailCompany
      if (companyData.result !== 'FAILED') {
        this.Name_Buyer = companyData.data.json_personal[0].purchasing_chief[0].name
        this.Phone_Buyer = companyData.data.json_personal[0].purchasing_chief[0].phone
        this.Position_Buyer = companyData.data.json_personal[0].purchasing_chief[0].position
        this.Email_Buyer = companyData.data.json_personal[0].purchasing_chief[0].email
        this.Name_Audit1 = companyData.data.json_personal[0].inspectors_one[0].name
        this.Phone_Audit1 = companyData.data.json_personal[0].inspectors_one[0].phone
        this.Position_Audit1 = companyData.data.json_personal[0].inspectors_one[0].position
        this.Email_Audit1 = companyData.data.json_personal[0].inspectors_one[0].email
        this.Name_Audit2 = companyData.data.json_personal[0].inspectors_two[0].name
        this.Phone_Audit2 = companyData.data.json_personal[0].inspectors_two[0].phone
        this.Position_Audit2 = companyData.data.json_personal[0].inspectors_two[0].position
        this.Email_Audit2 = companyData.data.json_personal[0].inspectors_two[0].email
      }
    },
    // async EditPersonal () {
    //   await this.$store.dispatch('actionsEditCompany', this.Detail)
    //   var response = await this.$store.state.ModuleAdminManage.stateEditCompany
    //   console.log('response-------->', response)
    // },
    async CreateOrder (paymentTypeData) {
      // console.log('CreateOrder12345', paymentTypeData)
      this.$store.commit('openLoader')
      var msg = ''
      var SaleData = ''
      const paymentType = paymentTypeData
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      const companyId = JSON.parse(localStorage.getItem('PartnerID'))
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var res
      var data
      var couponData = JSON.parse(Decode.decode(localStorage.getItem('couponData')))
      var resCoupon
      var utmMedium = ''
      if (sessionStorage.getItem('utm_medium') !== null) {
        utmMedium = sessionStorage.getItem('utm_medium')
      } else {
        utmMedium = null
      }
      if (this.CouponData.length === 0) {
        resCoupon = {
          message: 'สามารถใช้คูปองได้'
        }
      } else {
        var dataCheckCoupon = {
          coupon_id: this.CouponData.length !== 0 ? [this.CouponData[0].coupon_id] : '',
          shop_id: onedata.cartData.seller_shop_id,
          net_price: couponData.net_price,
          price_inc_vat: couponData.price_inc_vat,
          role_user: dataRole.role,
          company_id: onedata.cartData.company_id,
          customer_id: this.itemsCart.customer_id
        }
        await this.$store.dispatch('actionsRecheckCoupon', dataCheckCoupon)
        resCoupon = await this.$store.state.ModuleCart.stateRecheck
      }
      if (resCoupon.message === 'พบบางคูปองมีปัญหา') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: `<h3>${resCoupon.data[0].error}</h3>`
        })
        this.dialogAwaitConfirm = false
        this.closeCoupon(this.itemsCart.choose_list[0])
        this.$store.commit('closeLoader')
      } else {
        // if (this.SelectCouponOrPoint === false) {
        //   var haveCoupon = [{
        //     type_discount: localStorage.getItem('CouponOrPoint') === 'Point' ? 'point' : 'coupon',
        //     coupon_id: localStorage.getItem('CouponOrPoint') === 'Point' ? '' : onedata.cartData.coupon.length === 0 ? '' : onedata.cartData.coupon[0].coupon_id,
        //     coupon_discount: localStorage.getItem('CouponOrPoint') === 'Point' ? '' : onedata.cartData.coupon.length === 0 ? '' : onedata.cartData.coupon[0].coupon_discount,
        //     net_price: parseInt(this.itemsCart.net_price),
        //     amount: localStorage.getItem('CouponOrPoint') === 'Point' ? onedata.cartData.coupon.length === 0 ? '' : onedata.cartData.coupon[0].point : ''
        //   }]
        // } else {
        //   haveCoupon = []
        // }
        // if (onedata.cartDataSpecialPrice === 'yes') {
        //   var dataSpecialPrice = {
        //     role_user: dataRole.role,
        //     product_special_price_id: onedata.cartData.product_special_price_id,
        //     etax: this.taxRoles,
        //     credit_term: paymentType === 'creditTerm' ? 'yes' : 'no',
        //     company_id: onedata.cartData.company_id,
        //     company_position: onedata.cartData.company_position,
        //     com_perm_id: onedata.cartData.com_perm_id,
        //     coupon: onedata.cartData.coupon.length === 0 ? [] : haveCoupon
        //   }
        //   await this.$store.dispatch('ActionCreateOrderSpecialprice', dataSpecialPrice)
        //   res = await this.$store.state.ModuleCart.stateCreateOrderSpecialprice
        // } else {
        // console.log('CreateOrder12345', paymentTypeData)
        if (paymentType === 'cashPayment') {
          data = {
            address: this.CartAddress,
            total_point_receive: this.itemsCart.total_point_receive,
            utm_source_keyword: utmMedium === 'reach' ? sessionStorage.getItem('utm_source') : null,
            coupon: this.CouponData.length !== 0 && !this.noShipping
              ? this.CouponData
              : this.CouponData.filter(coupon => coupon.coupon_type !== 'free_shipping'),
            point: this.PointData !== 0 ? this.PointData : 0,
            point_used: this.PointData !== 0 ? Number(this.PointData / this.XBaht) : 0,
            product_free: this.itemsCart.product_free.length !== 0 ? this.itemsCart.product_free : [],
            com_perm_id: onedata.cartData.com_perm_id,
            company_id: companyId.company.company_id,
            role_user: dataRole.role,
            // invoice_id: this.taxinvoiceAddress.data.length > 0 ? this.taxinvoiceAddress.data[0].id : '',ของเก่าที่ยังมี invoice_id
            invoice_id: '',
            type_shipping: 'online',
            start_date_contract: this.searchContractStartDate,
            end_date_contract: this.searchContractEndDate,
            use_discount: 'N',
            discount_percent: '0',
            discount_amount: this.itemsCart.total_discount,
            contract_service: this.contractSet === true ? 'Y' : 'N',
            remark: this.reason,
            type_budget: this.selectBudget,
            budget_cut: this.selectCutBudget,
            document_type_name: this.selectTypeDoc,
            json_personal: [{
              purchasing_chief: [
                {
                  name: this.Name_Buyer,
                  phone: this.Phone_Buyer,
                  email: this.Position_Buyer,
                  position: this.Email_Buyer
                }
              ],
              inspectors_one: [
                {
                  name: this.Name_Audit1,
                  phone: this.Phone_Audit1,
                  email: this.Position_Audit1,
                  position: this.Email_Audit1
                }
              ],
              inspectors_two: [
                {
                  name: this.Name_Audit2,
                  phone: this.Phone_Audit2,
                  email: this.Position_Audit2,
                  position: this.Email_Audit2
                }
              ]
            }
            ],
            json_buyer: [
              {
                buyer_name: this.buyer_name,
                buyer_phone: this.buyer_phone,
                buyer_email: this.buyer_email
              }
            ]
            // credit_term: paymentType === 'creditTerm' ? 'yes' : 'no',
            // company_id: onedata.cartData.company_id ? onedata.cartData.company_id : -1,
            // company_position: onedata.cartData.company_position ? onedata.cartData.company_position : -1,
            // com_perm_id: onedata.cartData.com_perm_id ? onedata.cartData.com_perm_id : -1,
            // coupon: onedata.cartData.coupon.length === 0 ? [] : haveCoupon,
            // payment_method: 'Cash'
          }
          await this.$store.dispatch('ActionCreateOrder', data)
          res = await this.$store.state.ModuleCart.stateCreateOrder
        } else if (paymentType === 'payment') {
          onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
          const PartnerID = JSON.parse(localStorage.getItem('PartnerID'))
          const ShopID = JSON.parse(localStorage.getItem('ShopID'))
          if (dataRole.role !== 'sale_order') {
            const roleCustomer = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
            if (roleCustomer === 'general') {
              SaleData = JSON.parse(localStorage.getItem('SaleID'))
              this.EtaxType = 'Personal'
            } else {
              SaleData = JSON.parse(localStorage.getItem('SaleID'))
              this.EtaxType = 'Business'
            }
          }
          this.invoicedetail.forEach(element => {
            if (element.default_address === 'Y') {
              this.invoiceID = element.id
            }
          })
          if (this.radioTax === 'radiotax') {
            this.RequiredInvoice = 'yes'
          } else {
            this.RequiredInvoice = 'no'
          }
          data = {
            address: this.CartAddress,
            start_date_contract: this.searchContractStartDate,
            end_date_contract: this.searchContractEndDate,
            total_point_receive: this.itemsCart.total_point_receive,
            utm_source_keyword: utmMedium === 'reach' ? sessionStorage.getItem('utm_source') : null,
            coupon: this.CouponData.length !== 0 && !this.noShipping
              ? this.CouponData
              : this.CouponData.filter(coupon => coupon.coupon_type !== 'free_shipping'),
            point: this.PointData !== 0 ? this.PointData : 0,
            point_used: this.PointData !== 0 ? Number(this.PointData / this.XBaht) : 0,
            product_free: this.itemsCart.product_free.length !== 0 ? this.itemsCart.product_free : [],
            edit_Quotation: onedata.cartData.edit_Quotation,
            role_user: dataRole.role,
            required_invoice: this.RequiredInvoice,
            etax: this.EtaxType,
            invoice_id: this.invoiceID,
            seller_shop_id: ShopID,
            company_id: PartnerID,
            customer_id: this.itemsCart.customer_id,
            sale_id: SaleData,
            type_shipping: this.radios === 'radio-1' ? 'pickup' : 'online',
            date_pickup: this.dates,
            time_pickup: this.formattedTime,
            shipping_data: this.ShippingData
          }
          // console.log('dataActionCreateOrder', data)
          await this.$store.dispatch('ActionCreateOrder', data)
          res = await this.$store.state.ModuleCart.stateCreateOrder
          // res = {
          //   result: 'SUCCESS',
          //   message: 'This user is purchaser & credit term . create order success.'
          // }
          // console.log('res', res)
        } else {
          data = {
            address: this.CartAddress,
            total_point_receive: this.itemsCart.total_point_receive,
            utm_source_keyword: utmMedium === 'reach' ? sessionStorage.getItem('utm_source') : null,
            coupon: this.CouponData.length !== 0 && !this.noShipping
              ? this.CouponData
              : this.CouponData.filter(coupon => coupon.coupon_type !== 'free_shipping'),
            point: this.PointData !== 0 ? this.PointData : 0,
            point_used: this.PointData !== 0 ? Number(this.PointData / this.XBaht) : 0,
            product_free: this.itemsCart.product_free.length !== 0 ? this.itemsCart.product_free : [],
            com_perm_id: onedata.cartData.com_perm_id,
            company_id: companyId.company.company_id,
            role_user: dataRole.role,
            // invoice_id: this.taxinvoiceAddress.data.length > 0 ? this.taxinvoiceAddress.data[0].id : '',ของเก่าที่ยังมี invoice_id
            invoice_id: '',
            type_shipping: 'online',
            start_date_contract: this.searchContractStartDate,
            end_date_contract: this.searchContractEndDate,
            use_discount: 'N',
            discount_percent: '0',
            discount_amount: this.itemsCart.total_discount,
            contract_service: this.contractSet === true ? 'Y' : 'N',
            remark: this.reason,
            type_budget: this.selectBudget,
            budget_cut: this.selectCutBudget,
            document_type_name: this.selectTypeDoc,
            json_personal: [{
              purchasing_chief: [
                {
                  name: this.Name_Buyer,
                  phone: this.Phone_Buyer,
                  email: this.Position_Buyer,
                  position: this.Email_Buyer
                }
              ],
              inspectors_one: [
                {
                  name: this.Name_Audit1,
                  phone: this.Phone_Audit1,
                  email: this.Position_Audit1,
                  position: this.Email_Audit1
                }
              ],
              inspectors_two: [
                {
                  name: this.Name_Audit2,
                  phone: this.Phone_Audit2,
                  email: this.Position_Audit2,
                  position: this.Email_Audit2
                }
              ]
            }
            ],
            json_buyer: [
              {
                buyer_name: this.buyer_name,
                buyer_phone: this.buyer_phone,
                buyer_email: this.buyer_email
              }
            ]
            // purchasing_cheif: {
            //   name: this.Name_Buyer,
            //   phone: this.Phone_Buyer,
            //   position: this.Position_Buyer,
            //   email: this.Email_Buyer
            // },
            // inspectors_one: {
            //   name: this.Name_Audit1,
            //   phone: this.Phone_Audit1,
            //   position: this.Position_Audit1,
            //   email: this.Email_Audit1
            // },
            // inspectors_two: {
            //   name: this.Name_Audit2,
            //   phone: this.Phone_Audit2,
            //   position: this.Position_Audit2,
            //   email: this.Email_Audit2
            // }
            // role_user: dataRole.role,
            // etax: this.taxRoles,
            // credit_term: paymentType === 'creditTerm' ? 'yes' : 'no',
            // company_id: onedata.cartData.company_id ? onedata.cartData.company_id : -1,
            // company_position: onedata.cartData.company_position ? onedata.cartData.company_position : -1,
            // com_perm_id: onedata.cartData.com_perm_id ? onedata.cartData.com_perm_id : -1,
            // // coupon: onedata.cartData.coupon.length === 0 ? [] : haveCoupon,
            // payment_method: 'ThaiDot'
          }
          await this.$store.dispatch('ActionCreateOrder', data)
          res = await this.$store.state.ModuleCart.stateCreateOrder
        // }
        }
        if (res.result === 'SUCCESS') {
          // console.log('res2', res)
          if (res.message === 'Create Order success.' || res.message === 'This user is purchaser.create order success.') {
            if (res.data.can_pay === 'yes') {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2000,
                timerProgressBar: true,
                icon: 'success',
                text: 'สั่งสินค้าเรียบร้อย'
              })
              // var dataPayment = {
              //   payment_transaction_number: res.data.order_number
              // }
              // if (this.selectTypeAddress === 'Shop') {
              //   if (this.checkOwnShop === 'Y') {
              //     if (paymentType === 'cashPayment') {
              //       await this.$store.dispatch('actionsPayCashInStore', dataPayment)
              //       var resPayCashInStore = await this.$store.state.ModuleCart.statePayCashInStore
              //       console.log(resPayCashInStore)
              //       if (resPayCashInStore.result === 'SUCCESS') {
              //         if (resPayCashInStore.message === 'Cash payment success without e-tax' || resPayCashInStore.message === 'Cash payment success with e-tax') {
              //           this.$EventBus.$emit('getCartPopOver')
              //           if (!this.MobileSize) {
              //             this.$router.push({ path: '/pobuyerProfile' }).catch(() => {})
              //           } else {
              //             this.$router.push({ path: '/pobuyerProfileMobile' }).catch(() => {})
              //           }
              //         }
              //       } else {
              //         this.$swal.fire({
              //           showConfirmButton: false,
              //           timer: 3000,
              //           timerProgressBar: true,
              //           icon: 'error',
              //           text: 'เกิดข้อผิดพลาดของระบบ กรุณาติดต่อเจ้าหน้าที่'
              //         })
              //       }
              //     } else {
              //       await this.$store.dispatch('ActionGetPaymentPage', dataPayment)
              //       var resRedirect2 = await this.$store.state.ModuleCart.stateGetPaymentPage
              //       this.overlay = false
              //       this.$EventBus.$emit('getCartPopOver')
              //       localStorage.setItem('PaymentData', Encode.encode(resRedirect2))
              //       this.$router.push('/RedirectPaymentPage').catch(() => {})
              //     }
              //   } else {
              //     await this.$store.dispatch('ActionGetPaymentPage', dataPayment)
              //     var resRedirect1 = await this.$store.state.ModuleCart.stateGetPaymentPage
              //     this.overlay = false
              //     this.$EventBus.$emit('getCartPopOver')
              //     localStorage.setItem('PaymentData', Encode.encode(resRedirect1))
              //     this.$router.push('/RedirectPaymentPage').catch(() => {})
              //   }
              // } else {
              //   await this.$store.dispatch('ActionGetPaymentPage', dataPayment)
              //   var resRedirect = await this.$store.state.ModuleCart.stateGetPaymentPage
              //   this.overlay = false
              //   this.$EventBus.$emit('getCartPopOver')
              //   localStorage.setItem('PaymentData', Encode.encode(resRedirect))
              //   this.$router.push('/RedirectPaymentPage').catch(() => {})
              // }
            // } else {
            //   if (dataRole.role === 'purchaser') {
            //     const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
            //     var id2 = companyId.company.company_id
            //     const data2 = {
            //       company_id: id2
            //     }
            //     await this.$store.dispatch('actionsDetailCompany', data2)
            //     await this.$store.dispatch('actionsAuthorityUser')
            //     var responseCompany2 = await this.$store.state.ModuleAdminManage.stateDetailCompany
            //     var responsePosition2 = await this.$store.state.ModuleUser.stateAuthorityUser
            //     var listCompany2 = responsePosition2.data.list_company
            //     for (let i = 0; i < listCompany2.length; i++) {
            //       if (responseCompany2.data.id === listCompany2[i].company_id) {
            //         localStorage.removeItem('list_Company_detail')
            //         localStorage.setItem('list_Company_detail', Encode.encode(listCompany2[i]))
            //       }
            //     }
            //     localStorage.setItem('CompanyData', Encode.encode(responseCompany2.data))
            //     this.$EventBus.$emit('getItemNoti')
            //     if (this.MobileSize) {
            //       this.$router.push({ path: `/orderDetailCompanyMobile?orderNumber=${res.data.order_number}` }).catch(() => {})
            //     } else {
            //       this.$router.push({ path: `/orderDetailCompany?orderNumber=${res.data.order_number}` }).catch(() => {})
            //     }
            //   }
            }
          } else if (res.message === 'This user is purchaser & credit term . create order success.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2000,
              timerProgressBar: true,
              icon: 'success',
              text: 'สั่งสินค้าเรียบร้อย'
            })
            // if (localStorage.getItem('SetRowCompany') !== null) {
            //   const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
            //   var id = companyId.company.company_id
            //   const data = {
            //     company_id: id
            //   }
            //   await this.$store.dispatch('actionsDetailCompany', data)
            //   await this.$store.dispatch('actionsAuthorityUser')
            //   var responseCompany = await this.$store.state.ModuleAdminManage.stateDetailCompany
            //   var responsePosition = await this.$store.state.ModuleUser.stateAuthorityUser
            //   var listCompany = responsePosition.data.list_company
            //   for (let i = 0; i < listCompany.length; i++) {
            //     if (responseCompany.data.id === listCompany[i].company_id) {
            //       localStorage.removeItem('list_Company_detail')
            //       localStorage.setItem('list_Company_detail', Encode.encode(listCompany[i]))
            //     }
            //   }
            //   localStorage.setItem('CompanyData', Encode.encode(responseCompany.data))
            //   this.$EventBus.$emit('getItemNoti')
            //   if (this.MobileSize) {
            //     this.$router.push({ path: '/companyListCreditOrderMobile' }).catch(() => {})
            //   } else {
            //     this.$router.push({ path: '/companyListCreditOrder' }).catch(() => {})
            //   }
            // }
          } else if (res.message === 'This user is have approver . create order success.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2000,
              timerProgressBar: true,
              icon: 'success',
              text: 'สั่งสินค้าเรียบร้อย'
            })
            // if (localStorage.getItem('SetRowCompany') !== null) {
            //   const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
            //   var Companyid = companyId.company.company_id
            //   const data = {
            //     company_id: Companyid
            //   }
            //   await this.$store.dispatch('actionsDetailCompany', data)
            //   await this.$store.dispatch('actionsAuthorityUser')
            //   var responseCompanyApprove = await this.$store.state.ModuleAdminManage.stateDetailCompany
            //   var responsePositionApprove = await this.$store.state.ModuleUser.stateAuthorityUser
            //   var listCompanyAprrove = responsePositionApprove.data.list_company
            //   for (let i = 0; i < listCompanyAprrove.length; i++) {
            //     if (responseCompanyApprove.data.id === listCompanyAprrove[i].company_id) {
            //       localStorage.removeItem('list_Company_detail')
            //       localStorage.setItem('list_Company_detail', Encode.encode(listCompanyAprrove[i]))
            //     }
            //   }
            //   localStorage.setItem('CompanyData', Encode.encode(responseCompanyApprove.data))
            //   this.$EventBus.$emit('getItemNoti')
            //   if (this.MobileSize) {
            //     this.$router.push({ path: '/listApproveMobile' }).catch(() => {})
            //   } else {
            //     this.$router.push({ path: '/listApprove' }).catch(() => {})
            //   }
            // }
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2000,
              timerProgressBar: true,
              icon: 'success',
              text: 'สั่งสินค้าเรียบร้อย'
            })
            // window.location.assign('/')
            // this.$router.push({ path: '/' }).catch(() => {})
          }
          // console.log('สั่งสินค้าเรียบร้อย')
          // if (this.CouponData.length !== 0) {
          //   var couponData = {
          //     counpon_id: this.CouponData[0].coupon_id,
          //     order_number: res.data.payment_transaction_number
          //   }
          //   // console.log('couponData', couponData)
          //   await this.$store.dispatch('actionsUseCoupon', couponData)
          //   await this.$store.state.ModuleCart.stateUseCoupon
          // }
          this.$EventBus.$emit('getCartPopOver')
          var shopDetailSale = JSON.parse(Decode.decode(localStorage.getItem('ShopDetailSale')))
          // console.log('shopDetailSale', shopDetailSale)
          // var SellerShopID = ''
          if (shopDetailSale.seller_shop_id === undefined) {
            // console.log(1)
            var SellerShopID = shopDetailSale.id
          } else {
            // console.log(2)
            SellerShopID = shopDetailSale.seller_shop_id
          }
          var dataShop = {
            id: SellerShopID,
            name: shopDetailSale.shop_name_th
          }
          localStorage.setItem('shopDetail', JSON.stringify(dataShop))
          this.$store.commit('openLoader')
          localStorage.setItem('ShopID', SellerShopID)
          localStorage.setItem('shopSellerID', SellerShopID)
          await this.$store.dispatch('actionsAuthorityUser')
          var responseposition = await this.$store.state.ModuleUser.stateAuthorityUser
          var listShop = responseposition.data.list_shop_detail
          for (let i = 0; i < listShop.length; i++) {
            if (SellerShopID === listShop[i].seller_shop_id) {
              localStorage.setItem('list_shop_detail', Encode.encode(listShop[i]))
            }
          }
          var listShopDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
          // console.log('123456', listShopDetail)
          if (dataRole.role !== 'sale_order') {
            if (listShopDetail.can_use_function_in_shop.manage_approve_order === '1') {
              if (!this.MobileSize) {
                this.$store.commit('closeLoader')
                this.$EventBus.$emit('CheckShop')
                this.$EventBus.$emit('checkpath')
                this.$EventBus.$emit('getCountInTable')
                this.$EventBus.$emit('checkJVShop')
                this.$EventBus.$emit('AuthorityUsers')
                // this.$router.push({ path: '/seller?ShopID=' + shopDetailSale.seller_shop_id + '&ShopName=' + shopDetailSale.shop_name_th }).catch(() => {})
                await this.$router.push({ path: '/DetailListApproveSales?QUNumber=' + res.data.payment_transaction_number + '&id=' + SellerShopID }).catch(() => {})
              } else {
                this.$store.commit('closeLoader')
                this.$EventBus.$emit('CheckShop')
                this.$EventBus.$emit('checkpath')
                this.$EventBus.$emit('getCountInTable')
                this.$EventBus.$emit('checkJVShop')
                this.$EventBus.$emit('AuthorityUsers')
                // this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetailSale.seller_shop_id + '&ShopName=' + shopDetailSale.shop_name_th }).catch(() => {})
                await this.$router.push({ path: '/DetailListApproveSalesMobile?QUNumber=' + res.data.payment_transaction_number + '&id=' + SellerShopID }).catch(() => {})
              }
            } else {
              if (!this.MobileSize) {
                this.$store.commit('closeLoader')
                this.$EventBus.$emit('CheckShop')
                this.$EventBus.$emit('checkpath')
                this.$EventBus.$emit('getCountInTable')
                this.$EventBus.$emit('checkJVShop')
                this.$EventBus.$emit('AuthorityUsers')
                // this.$router.push({ path: '/seller?ShopID=' + shopDetailSale.seller_shop_id + '&ShopName=' + shopDetailSale.shop_name_th }).catch(() => {})
                await this.$router.push({ path: '/DetailQuotationSales?QUNumber=' + res.data.payment_transaction_number + '&id=' + SellerShopID }).catch(() => {})
              } else {
                this.$store.commit('closeLoader')
                this.$EventBus.$emit('CheckShop')
                this.$EventBus.$emit('checkpath')
                this.$EventBus.$emit('getCountInTable')
                this.$EventBus.$emit('checkJVShop')
                this.$EventBus.$emit('AuthorityUsers')
                // this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetailSale.seller_shop_id + '&ShopName=' + shopDetailSale.shop_name_th }).catch(() => {})
                await this.$router.push({ path: 'QuotationDetailMobile?QUNumber=' + res.data.payment_transaction_number + '&id=' + SellerShopID }).catch(() => {})
              }
            }
          } else {
            if (!this.MobileSize) {
              this.$store.commit('closeLoader')
              // this.$router.push({ path: '/seller?ShopID=' + shopDetailSale.seller_shop_id + '&ShopName=' + shopDetailSale.shop_name_th }).catch(() => {})
              await this.$router.push({ path: '/QuotationAll' }).catch(() => {})
            } else {
              this.$store.commit('closeLoader')
              // this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetailSale.seller_shop_id + '&ShopName=' + shopDetailSale.shop_name_th }).catch(() => {})
              await this.$router.push({ path: '/QuotationAllMobile' }).catch(() => {})
            }
          }
        } else if (res.result === 'FAILED') {
          if (res.message === 'Credit is not enough for buy.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: 'วงเงินของบริษัทไม่เพียงพอ'
            })
            this.Cancel()
          } else if (res.message === 'ไม่พบตะกร้า') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: 'ไม่พบตะกร้า'
            })
            this.backstep()
          } else if (res.message === 'เกิดข้อผิดพลาด กรุณากลับไปยังหน้าตะกร้า') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: 'เกิดข้อผิดพลาด กรุณากลับไปยังหน้าตะกร้า'
            })
            this.backstep()
          } else if (res.message === 'Not enough product in stock.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: `ไม่สามารถซื้อสินค้า ${res.data[0].product} ได้เนื่องจากจำนวนสินค้ามีไม่เพียงพอ`
            })
            this.Cancel()
          } else if (res.message === 'Type_budget not found.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: 'ไม่พบประเภทการควบคุมวงเงิน'
            })
            this.Cancel()
          } else if (res.message === 'No approver in this company.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: 'ไม่มีผู้อนุมัติในบริษัทนี้'
            })
            this.Cancel()
          } else if (res.message === 'Approver limit exceeded.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: 'วงเงินผู้อนุมัติไม่เพียงพอ'
            })
            this.Cancel()
          } else if (res.message === 'Credit not enough for buy.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: 'วงเงินฝ่าย/แผนกไม่เพียงพอ'
            })
            this.Cancel()
          } else if (res.message === 'This user can not create order becase this user is not in some department') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: 'ผู้ซื้อไม่มีแผนก กรุณาติดต่อเจ้าหน้าที่'
            })
            this.Cancel()
          } else if (res.message === 'This user is unauthorized.') {
            this.$store.commit('closeLoader')
            this.$EventBus.$emit('refreshToken')
            // this.$swal.fire({
            //   showConfirmButton: false,
            //   timer: 3000,
            //   timerProgressBar: true,
            //   icon: 'error',
            //   text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง'
            // })
            // window.location.assign('/')
          } else if (res.message === 'ไม่สามารถใช้คูปองได้ กรุณานำออกจากรถเข็น') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3500,
              timerProgressBar: true,
              icon: 'error',
              text: 'ไม่สามารถใช้คูปองได้'
            })
            this.$EventBus.$emit('clearPoint')
            this.$EventBus.$emit('clearCoupon')
            this.dialogAwaitConfirm = false
          } else {
            // error msg form OrderPurchaserCheck
            this.$store.commit('closeLoader')
            msg = this.getErrorMsg(res.message)
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: msg
            })
            this.Cancel()
          }
        } else {
          if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
            this.$store.commit('closeLoader')
            this.$EventBus.$emit('refreshToken')
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'error',
              text: res.message
            })
          }
          this.Cancel()
        }
      }
      localStorage.removeItem('CouponDetail')
      localStorage.removeItem('PointDetail')
    },
    // error msg form OrderPurchaserCheck
    getErrorMsg (msg) {
      if (msg === 'This user is unauthorized.') {
        return 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง'
      } else if (msg === 'The user was not found in the company.') {
        return 'คุณไม่ได้อยู่ในบริษัทใดเลย'
      } else if (msg === 'Company not found.') {
        return 'ไม่พบข้อมูลบริษัทในระบบ'
      } else if (msg === 'You are not in this Company.') {
        return 'คุณไม่ได้อยู่ในบริษัทนี้'
      } else if (msg === 'Data missing. Please check your [" company_id "] and try again.') {
        return 'ข้อมูลขาดหาย โปรดเช็ค [ รหัสบริษัท ] แล้วลองใหม่อีกครั้ง'
      } else if (msg === 'Order not found.') {
        return 'ไม่พบข้อมูลออเดอร์'
      } else if (msg === 'Data missing. Please check your [" order_number "] and try again.') {
        return 'ข้อมูลขาดหาย โปรดเช็ค [ เลขออเดอร์ ] แล้วลองใหม่อีกครั้ง'
      } else if (msg === 'User Has PDF not found.') {
        return 'ไม่พบข้อมูลใบเสนอราคา'
      } else if (msg === 'Token not found.') {
        return 'ไม่พบข้อมูลโทเค่น'
      } else if (msg === 'You not Have Permission Purchaser.') {
        return 'คุณไม่มีสิทธิ์ผู้ซื้อองค์กร'
      } else if (msg === 'Approve Position Purchaser Not Found.') {
        return 'ไม่พบรูปแบบการอนุมัติ'
      } else if (msg === 'Approve Position Budget Less Than Order Net Price.') {
        return 'วงเงินของรูปแบบการอนุมัติน้อยกว่าเงินสุทธิออเดอร์'
      } else if (msg === 'Your Company is not Partner With This Shop.') {
        return 'บริษัทของคุณไม่ได้เป็นคู่ค้ากับร้านค้า'
      } else if (msg === 'Company Credit With This Seller Shop Less Than Order Net Price.') {
        return 'เงินขององค์กรน้อยกว่าเงินสุทธิของออเดอร์'
      } else if (msg === 'Not Have Approver In Approve Position.') {
        return 'ไม่พบผู้อนุมัติในลำดับอนุมัติ โปรดเพิ่มผู้อนุมัติก่อน'
      } else if (msg === 'Sum Approve Position Budget Less Than Order Net Price.') {
        return 'ผลรวมของวงเงินแต่ละลำดับน้อยกว่าเงินสุทธิของออเดอร์'
      } else if (msg === 'Wrong Approve Position Type.') {
        return 'ประเภทการอนุมัติผิด'
      } else if (msg === 'Some User not Have Permission to Approve Order.') {
        return 'มีผู้ใช้ในลำดับที่ไม่มีสิทธิ์การอนุมัติ กรุณาตรวจสอบรูปแบบการอนุมัติ'
      } else if (msg === '') {
        return ''
      } else {
        // return 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่'
        return msg
      }
    },
    Cancel () {
      this.$router.go(-1)
    },
    // goHomePage () {
    //   this.$router.push({ path: '/' }).catch(() => {})
    // },
    // editAddress (val) {
    //   localStorage.setItem('AddressUserDetail', Encode.encode(val))
    //   this.EditAddressDetail = val
    //   this.titleAddress = 'แก้ไขที่อยู่จัดส่งสินค้า'
    //   this.page = 'checkout'
    //   this.$EventBus.$emit('EditModalAddress')
    // },
    // openModalTaxAddress () {
    //   this.$refs.ModalTaxAddress.open()
    //   // มีการยิง api get ข้อมูลใบกำกับภาษี
    //   // localStorage.setItem('AddressData', Encode.encode(this.propsAddress[0].address_data))
    // },
    // openModalAddressCustomer () {
    //   this.$refs.ModalAddressCustomer.open()
    // },
    // async getTaxAddressPurchaser () {
    //   await this.$store.dispatch('actionUPSGetAddressTaxinvoicePurchase')
    //   var res = await this.$store.state.ModuleUser.stateUPSGetAddressTaxinvoicePurchase
    //   if (res.message === 'Get corporate address success.') {
    //     this.companyName = res.data.company_name_th
    //     this.companyTaxID = 'เลขประจำตัวผู้เสียภาษี' + ' ' + res.data.company_tax_id
    //     this.taxAddress = res.data.address[0].detail + ' ' + 'แขวง/ตำบล' + ' ' + res.data.address[0].sub_district + ' ' + 'เขต/อำเภอ' + ' ' + res.data.address[0].district + ' ' + 'จังหวัด' + ' ' + res.data.address[0].province + ' ' + res.data.address[0].zip_code
    //     this.updateInvoiceAddressPurchaser()
    //   } else {
    //     this.companyName = ''
    //     this.companyTaxID = ''
    //     this.taxAddress = ''
    //   }
    // },
    // async CreateOrderSyncTaxaddressPurchaser (items) {
    //   var data = {
    //     // invoice_id: items,
    //     invoice_id: '',
    //     cart_id: this.itemsCart.id
    //   }
    //   await this.$store.dispatch('ActionCreateOrderSyncTaxaddress', data)
    // },
    // async updateInvoiceAddressPurchaser () {
    //   // this.$store.commit('openLoader')
    //   var dataRole = JSON.parse(localStorage.getItem('roleUser'))
    //   var data = {
    //     role_user: dataRole.role
    //   }
    //   await this.$store.dispatch('ActionCreateInvoiceAddressPurchaser', data)
    //   var res = await this.$store.state.UPSModuleCart.stateCreateInvoiceAddressPurchaser
    //   if (res.message === 'Create invoice address success.') {
    //     this.$store.commit('closeLoader')
    //     // this.CreateOrderSyncTaxaddressPurchaser(res.data.invoice_id)
    //   } else {
    //     this.$store.commit('closeLoader')
    //     this.$swal.fire({
    //       showConfirmButton: false,
    //       timer: 3000,
    //       timerProgressBar: true,
    //       icon: 'error',
    //       text: 'เกิดข้อผิดพลาดเกี่ยวกับที่อยู่ใบกำกับภาษี'
    //     })
    //   }
    // },
    // async checkAddressTaxinvoiceData () {
    //   // console.log('jjjjjjjj')
    //   this.companyName = ''
    //   this.companyTaxID = ''
    //   this.taxAddress = ''
    //   var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    //   // console.log('onedata', onedata)
    //   // console.log('checkAddressTaxinvoiceData')
    //   var dataRole = JSON.parse(localStorage.getItem('roleUser'))
    //   var dataCompany = ''
    //   if (localStorage.getItem('SetRowCompany') !== null) {
    //     var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
    //     dataCompany = companyDataSet.company.company_id
    //   } else {
    //     dataCompany = ''
    //   }
    //   var data = {
    //     user_id: onedata.user.user_id,
    //     role: dataRole.role,
    //     company_id: dataCompany,
    //     tax_type: this.taxRoles
    //   }
    //   await this.$store.dispatch('actionsGetInvoice', data)
    //   this.taxinvoiceAddress = await this.$store.state.ModuleUser.stategetInvoice
    //   // console.log(this.taxinvoiceAddress)
    //   if (this.taxinvoiceAddress.data.length !== 0) {
    //     this.companyName = this.taxinvoiceAddress.data[0].name
    //     this.companyTaxID = 'เลขประจำตัวผู้เสียภาษี :' + ' ' + this.taxinvoiceAddress.data[0].tax_id
    //     this.taxAddress = this.taxinvoiceAddress.data[0].address + ' ' + 'แขวง/ตำบล' + ' ' + this.taxinvoiceAddress.data[0].sub_district + ' ' + 'เขต/อำเภอ' + ' ' + this.taxinvoiceAddress.data[0].district + ' ' + 'จังหวัด' + ' ' + this.taxinvoiceAddress.data[0].province + ' ' + this.taxinvoiceAddress.data[0].postal_code
    //   }
    //   // this.CreateOrderSyncTaxaddress()
    // },
    // async checkAddressTaxinvoiceDataNew () {
    //   this.companyName = ''
    //   this.companyTaxID = ''
    //   this.taxAddress = ''
    //   var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    //   var dataRole = JSON.parse(localStorage.getItem('roleUser'))
    //   var dataCompany = ''
    //   if (localStorage.getItem('SetRowCompany') !== null) {
    //     var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
    //     dataCompany = companyDataSet.company.company_id
    //   } else {
    //     dataCompany = ''
    //   }
    //   var data = {
    //     user_id: onedata.user.user_id,
    //     role: dataRole.role,
    //     company_id: dataCompany,
    //     tax_type: this.taxRoles
    //   }
    //   await this.$store.dispatch('actionsGetInvoiceAddress', data)
    //   this.taxinvoiceAddressNew = await this.$store.state.ModuleUser.stateGetInvoiceAddress
    //   // console.log(this.taxinvoiceAddressNew.data)
    //   if (this.taxinvoiceAddressNew.data.length !== 0) {
    //     this.companyName = this.taxinvoiceAddressNew.data[0].name
    //     this.companyTaxID = 'เลขประจำตัวผู้เสียภาษี :' + ' ' + this.taxinvoiceAddressNew.data[0].tax_id
    //     this.taxAddress = this.taxinvoiceAddressNew.data[0].address + ' ' + 'แขวง/ตำบล' + ' ' + this.taxinvoiceAddressNew.data[0].sub_district + ' ' + 'เขต/อำเภอ' + ' ' + this.taxinvoiceAddressNew.data[0].district + ' ' + 'จังหวัด' + ' ' + this.taxinvoiceAddressNew.data[0].province + ' ' + this.taxinvoiceAddressNew.data[0].postal_code
    //   }
    //   // this.CreateOrderSyncTaxaddress()
    // },
    // async openQuotation () {
    //   var dataList = []
    //   var sellerShopID
    //   var data = ''
    //   this.itemsCart.choose_list.forEach(element => {
    //     sellerShopID = element.seller_shop_id
    //     element.product_list.forEach(product => {
    //       const productList = {
    //         product_id: product.product_id,
    //         attribute_option_1: product.product_attribute_detail.attribute_priority_1 ? product.product_attribute_detail.attribute_priority_1 : '',
    //         attribute_option_2: product.product_attribute_detail.attribute_priority_2 ? product.product_attribute_detail.attribute_priority_2 : '',
    //         quantity: product.quantity,
    //         price: product.price,
    //         status: product.product_status,
    //         attribute_id: product.product_attribute_detail.product_attribute_id
    //       }
    //       dataList.push(productList)
    //     })
    //   })
    //   var dataRole = JSON.parse(localStorage.getItem('roleUser'))
    //   // get user data
    //   // var response = await this.$store.state.ModuleUser.stateUserDetailPage
    //   // var user = response.data[0]
    //   // get admin data
    //   // const sendId = { user_id: user.id }
    //   // await this.$store.dispatch('ActionGetAdminData', sendId)
    //   // var responseAdmin = await this.$store.state.ModuleCart.stateGetAdminData
    //   // var adminStatus = false
    //   // if (responseAdmin.data.length !== 0) {
    //   //   adminStatus = true
    //   // } else {
    //   //   adminStatus = false
    //   // }
    //   // get company Purchaser data
    //   if (dataRole.role === 'purchaser') {
    //     // const sendData = { user_id: user.id }
    //     // await this.$store.dispatch('ActionGetCompanyPurchaser', sendData)
    //     // var responseCompanyPurchaser = await this.$store.state.ModuleCart.stateCompanyPurchaser
    //     // if (responseCompanyPurchaser.message === 'Get company purchaser success.') {
    //     var companyID = ''
    //     if (localStorage.getItem('SetRowCompany') !== null) {
    //       var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
    //       companyID = companyDataSet.company.company_id
    //     } else {
    //       companyID = '-1'
    //     }
    //     data = {
    //       role_user: dataRole.role,
    //       product_list: dataList,
    //       company_id: companyID,
    //       // companyName: responseCompanyPurchaser.data.company_name,
    //       // fullname: this.itemsCart.address_data[0].first_name + ' ' + this.itemsCart.address_data[0].last_name,
    //       // phone: this.itemsCart.address_data[0].phone,
    //       // email: responseCompanyPurchaser.data.user_email,
    //       total_shipping: this.itemsCart.total_shipping,
    //       // discount: this.itemsCart.total_price_discount,
    //       // vat: this.itemsCart.total_vat,
    //       // fullnameAdmin: responseAdmin.data.contact_name,
    //       // phoneAdmin: responseAdmin.data.contact_tel,
    //       // emailAdmin: responseAdmin.data.contact_email,
    //       // isAdmin: adminStatus,
    //       total_discount: this.itemsCart.total_discount ? this.itemsCart.total_discount : '',
    //       seller_shop_id: sellerShopID
    //     }
    //     // this.$EventBus.$emit('openModalQuotation', data)
    //   } else {
    //     data = {
    //       role_user: dataRole.role,
    //       product_list: dataList,
    //       company_id: -1,
    //       // fullname: this.itemsCart.address_data[0].first_name + ' ' + this.itemsCart.address_data[0].last_name,
    //       // phone: this.itemsCart.address_data[0].phone,
    //       // email: '',
    //       total_shipping: this.itemsCart.total_shipping,
    //       // discount: this.itemsCart.total_price_discount,
    //       // vat: this.itemsCart.total_vat,
    //       // fullnameAdmin: responseAdmin.data.contact_name,
    //       // phoneAdmin: responseAdmin.data.contact_tel,
    //       // emailAdmin: responseAdmin.data.contact_email,
    //       // isAdmin: adminStatus,
    //       total_discount: this.itemsCart.total_discount ? this.itemsCart.total_discount : '',
    //       seller_shop_id: sellerShopID
    //     }
    //   }
    //   await this.$store.dispatch('ActionsPreviewQU', data)
    //   var responseData = await this.$store.state.ModuleCart.statePreviewQU
    //   // console.log(responseData.data)
    //   if (responseData.result === 'SUCCESS') {
    //     window.open(responseData.data.link_preview, '_blank')
    //   }
    // },
    async sentDataPPL () {
      var dataList = []
      this.itemsCart.choose_list.forEach(element => {
        element.product_list.forEach(product => {
          const productList = {
            product_id: product.product_id,
            product_name: product.product_name + ' ' + (product.key_1_value ? product.key_1_value : '') + ' ' + (product.product_attribute_detail.attribute_priority_1 ? product.product_attribute_detail.attribute_priority_1 : '') + ' ' + (product.key_2_value ? product.key_2_value : '') + ' ' + (product.product_attribute_detail.attribute_priority_2 ? product.product_attribute_detail.attribute_priority_2 : ''),
            quantity: product.quantity,
            price: product.price,
            status: product.product_status
          }
          dataList.push(productList)
        })
      })
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // get user data
      // var data = {
      //   role_user: dataRole.role
      // }
      // await this.$store.dispatch('actionsUserDetailPage', data)
      // var response = await this.$store.state.ModuleUser.stateUserDetailPage
      var response = JSON.parse(Decode.decode(localStorage.getItem('UserDetail')))
      var user = response.data[0]
      // get admin data
      const sendId = { user_id: user.id }
      await this.$store.dispatch('ActionGetAdminData', sendId)
      var responseAdmin = await this.$store.state.ModuleCart.stateGetAdminData
      var adminStatus = false
      this.checkAdminQU = false
      if (responseAdmin.data.length !== 0) {
        adminStatus = true
        this.checkAdminQU = true
      } else {
        adminStatus = false
        this.checkAdminQU = false
      }
      // get company Purchaser data
      if (dataRole.role === 'purchaser') {
        const sendData = { user_id: user.id }
        await this.$store.dispatch('ActionGetCompanyPurchaser', sendData)
        const responseData = await this.$store.state.UPSModuleCart.stateCompanyPurchaser
        const dataResponse = responseData.data
        if (responseData.message === 'Get company purchaser success.') {
          const data = {
            registered_key: '',
            action: 'input',
            flow_id: '',
            template_id: '619204fea310de0012c4b222',
            json_data: {
              datatable1: {
                isRowImport: true,
                row_data: dataList
              },
              quatation_number: '',
              companyName: dataResponse.company_name,
              fullname: this.itemsCart.address_data[0].first_name + ' ' + this.itemsCart.address_data[0].last_name,
              phone: this.itemsCart.address_data[0].phone,
              email: dataResponse.user_email,
              total_shipping: this.itemsCart.total_shipping,
              discount: this.itemsCart.total_discount,
              vat: this.itemsCart.total_vat,
              fullnameAdmin: responseAdmin.data.contact_name,
              phoneAdmin: responseAdmin.data.contact_tel,
              emailAdmin: responseAdmin.data.contact_email,
              delivery: '3-5 วัน/DATE หลังจากได้รับใบสั่งซื้อ / AFTER RECEIPT OF CONFIRMED PURCHASE ORDER',
              other: 'ลูกค้าใช้งานเปิดบิลขั้นต่ำ 3,000 บาท/ครั้ง ตัวแทนเปิดบิลขั้นต่ำ 5,000 บาท/ครั้ง กรุงเทพ-ปริมณฑลจัดส่งฟรี',
              isAdmin: adminStatus
            },
            is_preinput: true,
            pdf_base: '',
            external_data: {}
          }
          await this.$store.dispatch('ActionSentDataPPL', data)
          const res = await this.$store.state.UPSModuleCart.stateSentDataPPL
          this.responseSentDataPPL = res
        }
      } else {
        const data = {
          registered_key: '',
          action: 'input',
          flow_id: '',
          template_id: '619204fea310de0012c4b222',
          json_data: {
            datatable1: {
              isRowImport: true,
              row_data: dataList
            },
            quatation_number: '',
            companyName: '',
            fullname: this.itemsCart.address_data[0].first_name + ' ' + this.itemsCart.address_data[0].last_name,
            phone: this.itemsCart.address_data[0].phone,
            email: '',
            total_shipping: this.itemsCart.total_shipping,
            discount: this.itemsCart.total_discount,
            vat: this.itemsCart.total_vat,
            fullnameAdmin: responseAdmin.data.contact_name,
            phoneAdmin: responseAdmin.data.contact_tel,
            emailAdmin: responseAdmin.data.contact_email,
            delivery: '3-5 วัน/DATE หลังจากได้รับใบสั่งซื้อ / AFTER RECEIPT OF CONFIRMED PURCHASE ORDER',
            other: 'ลูกค้าใช้งานเปิดบิลขั้นต่ำ 3,000 บาท/ครั้ง ตัวแทนเปิดบิลขั้นต่ำ 5,000 บาท/ครั้ง กรุงเทพ-ปริมณฑลจัดส่งฟรี',
            isAdmin: adminStatus
          },
          is_preinput: true,
          pdf_base: '',
          external_data: {}
        }
        await this.$store.dispatch('ActionSentDataPPL', data)
        const res = await this.$store.state.UPSModuleCart.stateSentDataPPL
        this.pplURL = res.result.redirect_url + this.pplToken
        // window.open(url, '_blank')
      }
    },
    openPPL () {
      window.open(this.pplURL, '_blank')
    },
    async openCreateQU (typeopenModalPayment) {
      this.modalPayment = false
      var dataListEdit = []
      var ShopID = ''
      this.itemsCart.choose_list.forEach(element => {
        ShopID = element.seller_shop_id
        element.product_list.forEach(product => {
          // const productList = {
          //   product_id: product.product_id,
          //   // product_image: product.product_image,
          //   product_name: product.product_name,
          //   product_sku: product.sku,
          //   attribute_1_key: product.key_1_value,
          //   attribute_2_key: product.key_2_value,
          //   attribute_option_1: product.product_attribute_detail.attribute_priority_1 ? product.product_attribute_detail.attribute_priority_1 : '',
          //   attribute_option_2: product.product_attribute_detail.attribute_priority_2 ? product.product_attribute_detail.attribute_priority_2 : '',
          //   quantity: product.quantity,
          //   price: product.price,
          //   status: product.product_status,
          //   attribute_id: product.product_attribute_detail.product_attribute_id
          // }
          const productList = {
            product_id: product.product_id,
            // name: product.product_name,
            // product_sku: product.sku,
            attribute_id: product.product_attribute_detail.product_attribute_id,
            // attribute_1_key: product.key_1_value,
            attribute_option_1: product.product_attribute_detail.attribute_priority_1 ? product.product_attribute_detail.attribute_priority_1 : '',
            // attribute_2_key: product.key_2_value,
            attribute_option_2: product.product_attribute_detail.attribute_priority_2 ? product.product_attribute_detail.attribute_priority_2 : '',
            quantity: product.quantity,
            price: product.price,
            status: product.product_status
          }
          dataListEdit.push(productList)
        })
      })
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyID = ''
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
      } else {
        companyID = '-1'
      }
      var dataSendCreate = {
        role_user: dataRole.role,
        seller_shop_id: ShopID,
        // id: companyID,
        company_id: companyID,
        product_list: dataListEdit,
        total_shipping: this.itemsCart.total_shipping,
        special_price_id: this.oneDataSpecial === 'yes' ? onedata.cartData.product_special_price_id : null,
        payment_type: typeopenModalPayment,
        total_discount: this.itemsCart.total_discount ? this.itemsCart.total_discount : '',
        com_perm_id: this.itemsCart.com_perm_id,
        coupon_id: onedata.cartData.coupon.length === 0 ? '' : onedata.cartData.coupon[0].coupon_id,
        point: onedata.cartData.coupon.length === 0 ? '' : onedata.cartData.coupon[0].point
      }
      await this.$store.dispatch('actionsCreateQuCart', dataSendCreate)
      const { result = '', data = {}, message = '' } = await this.$store.state.ModuleAdminManage.createQuCart
      if (result === 'SUCCESS') {
        // this.$store.state.ModuleAdminManage.QuotationformData = await data
        if (!this.MobileSize) {
          this.$router.push({ path: '/QUCompanyDetail?QU_ID=' + data.qu_id + '&id=' + data.company_id + '&shopID=' + data.seller_shop_id }).catch(() => { })
        } else {
          this.$router.push({ path: '/QUCompanyDetailMobile?QU_ID=' + data.qu_id + '&id=' + data.company_id + '&shopID=' + data.seller_shop_id }).catch(() => { })
        }
      } else {
        this.$swal.fire({
          icon: 'error',
          text: message,
          showConfirmButton: false,
          timer: 1500
        })
        this.$router.push({ path: '/checkout' }).catch(() => { })
      }
    },
    async openModaleEditQUYesNo () {
      this.dialog_Cancel_Coupon = true
    },
    async openEditQU () {
      this.dialog_Cancel_Coupon = false
      this.SelectCouponOrPoint = true
      if (localStorage.getItem('CouponOrPoint') !== null) {
        if (localStorage.getItem('CouponOrPoint') === 'Point') {
          this.nameCouponOrPoint = ''
          await this.$EventBus.$emit('CancleBookPointCheckout')
        } else {
          this.nameCouponOrPoint = ''
          await this.$EventBus.$emit('CancleBookCouponCheckout')
        }
      }
      var dataListEdit = []
      var ShopID = ''
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.itemsCart.choose_list.forEach(element => {
        ShopID = element.seller_shop_id
        element.product_list.forEach(product => {
          // const productList = {
          //   product_id: product.product_id,
          //   // product_image: product.product_image,
          //   product_name: product.product_name,
          //   product_sku: product.sku,
          //   attribute_1_key: product.key_1_value,
          //   attribute_2_key: product.key_2_value,
          //   attribute_option_1: product.product_attribute_detail.attribute_priority_1 ? product.product_attribute_detail.attribute_priority_1 : '',
          //   attribute_option_2: product.product_attribute_detail.attribute_priority_2 ? product.product_attribute_detail.attribute_priority_2 : '',
          //   quantity: product.quantity,
          //   price: product.price,
          //   status: product.product_status,
          //   attribute_id: product.product_attribute_detail.product_attribute_id
          // }
          const productList = {
            product_id: product.product_id,
            name: product.product_name,
            product_sku: product.sku,
            attribute_id: product.product_attribute_detail.product_attribute_id,
            attribute_1_key: product.key_1_value,
            attribute_priority_1: product.product_attribute_detail.attribute_priority_1 ? product.product_attribute_detail.attribute_priority_1 : '',
            attribute_2_key: product.key_2_value,
            attribute_priority_2: product.product_attribute_detail.attribute_priority_2 ? product.product_attribute_detail.attribute_priority_2 : '',
            quantity: product.quantity,
            price: product.price
          }
          dataListEdit.push(productList)
        })
      })
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyID = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
      } else {
        companyID = '-1'
      }
      var dataSendEdit = {
        role_user: dataRole.role,
        seller_shop_id: ShopID,
        id: companyID,
        company_id: companyID,
        // id: 29,
        // company_id: 29,
        qu_id: '-1',
        product_list: dataListEdit,
        total_price_no_vat: this.itemsCart.total_price_no_vat,
        total_vat: this.itemsCart.total_vat,
        total_shipping: this.itemsCart.total_shipping,
        net_price: this.itemsCart.net_price,
        total_discount: this.itemsCart.total_discount ? this.itemsCart.total_discount : '',
        coupon_id: onedata.cartData.coupon.length === 0 ? '' : onedata.cartData.coupon[0].coupon_id,
        point: onedata.cartData.coupon.length === 0 ? '' : onedata.cartData.coupon[0].point
      }
      // var testdat = {
      //   company_id: '29',
      //   qu_id: '1',
      //   role_user: 'purchaser',
      //   seller_shop_id: '288',
      //   id: '29',
      //   total_discount: '0',
      //   coupon_id: '',
      //   point: ''
      // }
      // console.log(testdat)
      await this.$store.dispatch('actionsEditQU', dataSendEdit)
      const { result = '', data = {} } = await this.$store.state.ModuleAdminManage.stateEditQU
      if (result === 'SUCCESS') {
        onedata.cartData.coupon = []
        localStorage.setItem('oneData', Encode.encode(onedata))
        // console.log('test one2', onedata)
        this.$store.state.ModuleAdminManage.QuotationformData = await data
        this.$router.push({ path: '/Quotation?role=purchaser&qu_id=-1' }).catch(() => {})
      } else {
        this.$swal.fire({
          icon: 'error',
          text: 'ไม่มีข้อมูลแก้ไขใบเสนอราคานี้',
          showConfirmButton: false,
          timer: 1500
        })
        this.$router.push({ path: '/checkout' }).catch(() => {})
      }
    },
    async cancelCoupon () {
      // การยกเลิกการเลือกคูปองและคะแนน
      this.SelectCouponOrPoint = true
      //  nameCouponOrPoint คือ ชื่อที่แสดงบนหน้าเว็บตรงส่วนที่ให้เลือก
      this.nameCouponOrPoint = ''
      if (localStorage.getItem('CouponOrPoint') !== null) {
        if (localStorage.getItem('CouponOrPoint') === 'Point') {
          // จะไปเรียกการยกเลิก function CancleBookPointCheckout  ในไฟล์ CouponCart
          this.$EventBus.$emit('CancleBookPointCheckout')
        } else {
          // จะไปเรียกการยกเลิก function CancleBookCouponCheckout  ในไฟล์ CouponCart
          this.$EventBus.$emit('CancleBookCouponCheckout')
        }
      }
    },
    // async selectcouponorpointCheckout () {
    //   // function การเลือกคูปองและคะแนน
    //   if (this.SelectCouponOrPoint === true) {
    //     // การเลือกคูปองและคะแนน
    //     this.SelectCouponOrPoint = false
    //     if (localStorage.getItem('CouponOrPoint') !== null) {
    //       if (localStorage.getItem('CouponOrPoint') === 'Point') {
    //         var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    //         //  nameCouponOrPoint คือ ชื่อที่แสดงบนหน้าเว็บตรงส่วนที่ให้เลือก
    //         this.nameCouponOrPoint = 'ใช้คะแนน ' + onedata.cartData.coupon[0].point + ' คะแนน'
    //         // this.getCart()
    //       } else {
    //         onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    //         //  nameCouponOrPoint คือ ชื่อที่แสดงบนหน้าเว็บตรงส่วนที่ให้เลือก
    //         this.nameCouponOrPoint = onedata.cartData.coupon[0].coupon_name
    //         // this.getCart()
    //       }
    //     }
    //   }
    //   // } else {
    //   //   // การยกเลิกการเลือกคูปองและคะแนน
    //   //   this.SelectCouponOrPoint = true
    //   //   //  nameCouponOrPoint คือ ชื่อที่แสดงบนหน้าเว็บตรงส่วนที่ให้เลือก
    //   //   this.nameCouponOrPoint = ''
    //   //   if (localStorage.getItem('CouponOrPoint') !== null) {
    //   //     if (localStorage.getItem('CouponOrPoint') === 'Point') {
    //   //       // จะไปเรียกการยกเลิก function CancleBookPointCheckout  ในไฟล์ CouponCart
    //   //       await this.$EventBus.$emit('CancleBookPointCheckout')
    //   //     } else {
    //   //       // จะไปเรียกการยกเลิก function CancleBookCouponCheckout  ในไฟล์ CouponCart
    //   //       await this.$EventBus.$emit('CancleBookCouponCheckout')
    //   //     }
    //   //   }
    //   // }
    // },
    // async Click_Coupon () {
    //   var data = ''
    //   var companyID
    //   var list = []
    //   this.$store.commit('openLoader')
    //   if (localStorage.getItem('CouponOrPoint') !== null) {
    //     if (localStorage.getItem('CouponOrPoint') === 'Point') {
    //       this.SelectCouponOrPoint = true
    //       this.nameCouponOrPoint = ''
    //       await this.$EventBus.$emit('CancleBookPointCheckout')
    //     } else {
    //       this.SelectCouponOrPoint = true
    //       this.nameCouponOrPoint = ''
    //       await this.$EventBus.$emit('CancleBookCouponCheckout')
    //     }
    //   }
    //   if (localStorage.getItem('SetRowCompany') !== null) {
    //     var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
    //     companyID = companyDataSet.company.company_id
    //   } else {
    //     companyID = '-1'
    //   }
    //   var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    //   var dataRole = JSON.parse(localStorage.getItem('roleUser'))
    //   var dataCoupon = {
    //     seller_shop_id: this.cartData.seller_shop_id,
    //     role_user: dataRole.role,
    //     company_id: companyID,
    //     special_price_id: this.oneDataSpecial === 'yes' ? onedata.cartData.product_special_price_id : -1,
    //     net_price: parseInt(this.itemsCart.total_price_no_vat)
    //   }
    //   await this.$store.dispatch('actionsGetCouponCart', dataCoupon)
    //   var response = await this.$store.state.ModuleMyCouponsPoints.stateGetCouponCart
    //   if (response.code === 200) {
    //     // จัดเรียงข้อมูล ให้สามารใส่ใน Card ได้
    //     for (let i = 0; i < response.data.coupon.length; i++) {
    //       list.push({
    //         image: response.data.coupon[i].couponImagePath,
    //         name: response.data.coupon[i].couponName,
    //         description: response.data.coupon[i].couponDescription,
    //         couponDate: {
    //           useStartDate: response.data.coupon[i].useStartDate,
    //           useEndDate: response.data.coupon[i].useEndDate
    //         },
    //         couponId: response.data.coupon[i].couponId,
    //         status: response.data.coupon[i].couponType,
    //         discount: response.data.coupon[i].discount,
    //         real_discount: response.data.coupon[i].real_discount,
    //         shop_name: response.data.coupon[i].shop_name
    //       })
    //     }
    //     data = list
    //     this.$refs.CouponCart.open(data, dataCoupon, 'checkout')
    //     this.$store.commit('closeLoader')
    //   } else {
    //     if (response.message === 'NGS ShopID not found.') {
    //       this.$swal.fire({ text: 'ยังไม่มี ShopID ใน NGS', icon: 'error', timerProgressBar: true, showConfirmButton: false, timer: 1500 })
    //     }
    //     this.$store.commit('closeLoader')
    //   }
    // },
    async goProductDetail (item) {
      const nameCleaned = item.product_name.replace(/\s/g, '-')
      this.$router.push({ name: 'DetailProduct', params: { data: `${nameCleaned}-${item.product_id}` } }).catch(() => {})
      // const routeData = this.$router.resolve({ name: 'DetailProduct', params: { data: `${nameCleaned}-${item.product_id}` } })
      // window.location.assign(routeData.href, '_blank')
    },
    gotoShopDetail (name, id) {
      const shopCleaned = encodeURIComponent(name.replace(/\s/g, '-'))
      this.$router.push({ path: `/shoppage/${shopCleaned}-${id}` }).cache(() => {})
    }
  }
}
</script>

<style>
.ant-table-thead>tr>th,
.ant-table-tbody>tr>td {
  padding: 10px 10px;
  overflow-wrap: break-word;
}

.ant-table-thead>tr>th {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  text-align: left;
  background: #D8EFE4 !important;
  border-bottom: 1px solid #e8e8e8;
  transition: background 0.3s ease;
}

.ant-table-column-title {
  color: #27AB9C !important;
  font-weight: 600;
  line-height: 24px;
  font-size: 16px;
}

.ant-time-picker-large .ant-time-picker-input {
  border-radius: 8px;
  border-color: rgba(0, 0, 0, 0.42);
  height: 40px;
  padding: 6px 11px;
  font-size: 16px;
}

.ant-time-picker-large .ant-time-picker-input:hover {
  border-color: rgba(0, 0, 0, 0.87);
}

.ant-time-picker-panel-inner {
  top: 40px;
}

.ant-time-picker-panel {
  width: 418px;
}

.ant-time-picker-panel-select:first-child {
  width: 50%;
}

.ant-time-picker-panel-select:last-child {
  width: 50%;
}

.ant-time-picker-panel-select ul {
  width: auto;
}

li.ant-time-picker-panel-select-option-selected {
  color: #27AB9C;
  font-weight: 600;
  background: rgba(39, 171, 156, 0.10);
}

.ant-time-picker-panel-select li {
  text-align: center;
}

li.ant-time-picker-panel-select-option-selected:hover {
  color: #27AB9C;
  background: rgba(39, 171, 156, 0.10);
}

.ant-time-picker-panel-narrow .ant-time-picker-panel-input-wrap {
  display: none;
}

.ant-time-picker-input:focus {
  border-color: #27AB9C;
  border-width: 2px !important;
  box-shadow: none;
}

.ant-time-picker-panel-input:focus {
  border-color: #27AB9C;
  border-width: 2px !important;
  box-shadow: none;
}

.anticon svg {
  font-size: larger;
  color: #27AB9C;
  display: inline-block;
}
</style>

<style lang="css" scoped>
.m-auto {
  margin: auto;
}

.captionSku {
  font-size: 13px;
  font-style: normal;
  /* font-family: 'Prompt' !important; */
  /* font-weight: 500; */
}

.captionSkuMobile {
  font-size: 12px;
  font-style: normal;
  /* font-family: 'Prompt' !important; */
  /* font-weight: 500; */
}

.v-breadcrumbs {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  flex: 0 1 auto;
  list-style-type: none;
  margin: 0;
  padding: 4px 0px 4px 0px !important;
}

.v-breadcrumbs li .v-icon {
  color: #27AB9C;
}

.imageshow {
  max-width: 75px !important;
  width: 75px;
  height: 75px;
  cursor: pointer;
}

.imageshowIpadPro {
  max-width: 60px !important;
  width: 60px;
  height: 60px;
  cursor: pointer;
}

.totalPriceFont {
  font-size: 20px;
}

::v-deep .ant-table-pagination {
  display: none;
}

::v-deep .ant-table-header-column .ant-table-selection {
  display: none;
}

::v-deep .ant-table-thead>tr>th.ant-table-selection-column {
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}

::v-deep .ant-table-tbody>tr>td.ant-table-selection-column {
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}

::v-deep .ant-table-bordered .ant-table-thead>tr>th {
  border-top: 0px solid #e8e8e8 !important;
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}

::v-deep .ant-table-bordered .ant-table-body>table {
  border: 0px solid #e8e8e8;
}

::v-deep .ant-table-bordered .ant-table-tbody>tr>td {
  border: 0px solid #e8e8e8;
}

::v-deep .ant-table.ant-table-bordered .ant-table-title {
  border: 1px transparent;
  margin-bottom: 6px;
  border-radius: 8px;
}</style>

<style>
.custom-background .v-input__slot {
  background-color: #E6E6E6 !important;
}

.v-input--selection-controls .v-input__slot,
.v-input--selection-controls .v-radio {
  margin-bottom: 0px;
  cursor: pointer;
}
</style>
<style lang="css" scoped>.v-Card {
  border-radius: 8px;
}

.Textcard {
  font-size: 16px;
  font-weight: 400;
  Line-height: 22.4px;
  padding-top: 34.75px !important;
  padding-right: 0px;
}

.TextcardMobileSize {
  font-size: 14px;
  font-weight: 400;
  Line-height: 22.4px;
  padding-top: 34.75px !important;
  padding-right: 0px;
}

.TextBahtMobileSize {
  font-size: 14px;
  font-weight: 700;
  Line-height: 22.4px;
  padding-top: 34.75px !important;
}
.TextBaht {
  font-size: 16px;
  font-weight: 700;
  Line-height: 22.4px;
  padding-top: 34.75px !important;
}
.is-disabled {
  pointer-events: none;
  opacity: 0.5;
}</style>
