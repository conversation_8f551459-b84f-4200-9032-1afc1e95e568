<template>
  <v-container>
    <v-col class="pt-6">
      <v-col cols="12">
        <v-card class="px-0" elevation="0">
          <v-row dense>
            <v-col>
              <v-card-title v-if="!MobileSize" class="f-left ml-3" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 24px;">จัดการประเภทร้านค้า</v-card-title>
              <v-card-title v-else class="f-left ml-1" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 20px;"><v-icon color="primary" @click="backtoPage()">mdi-chevron-left</v-icon> จัดการประเภทร้านค้า</v-card-title>
            </v-col>
          </v-row>
          <v-card-text class="px-0 pt-4">
            <div class="" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 20px 20px 10px 20px;' : 'padding: 0px 48px 10px 48px;'">
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" md="12" sm="12">
                      <span style="font-weight: bold; line-height: 26px; color: #131313;" :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">ชื่อประเภทร้านค้า<span style="color: red;"> *</span></span>
                      <v-text-field style="border-radius: 8px;" class="input_text namedoc_input" placeholder="ระบุชื่อประเภทร้านค้า" outlined dense v-model="FormData.group_name"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="12" sm="12">
                      <span style="font-weight: bold; line-height: 26px; color: #131313;" :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">คำอธิบายสั้นๆ</span>
                      <v-text-field style="border-radius: 8px;" class="input_text namedoc_input" placeholder="ระบุคำอธิบายสั้นๆของประเภทร้านค้า" outlined dense v-model="FormData.group_shop_short_description" oninput="this.value = this.value.replace(/^0/, '')">
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="12" sm="12">
                      <span style="font-weight: bold; line-height: 26px; color: #131313;" :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">คำอธิบาย</span>
                      <v-textarea
                        name="input-7-4"
                        placeholder="ระบุคำอธิบายประเภทร้านค้า"
                        style="border-radius: 8px;"
                        outlined v-model="FormData.group_shop_description"  oninput="this.value = this.value.replace(/^0/, '')"
                      >
                      </v-textarea>
                    </v-col>
                    <!-- รายชื่อร้านค้า -->
                    <v-col cols="12">
                      <v-row dense class="pa-0">
                        <v-card class="px-0" elevation="0" width="100%" height="100%" style="border-radius: 8px;">
                          <v-card-text class="px-1">
                            <v-row dense>
                              <v-col cols="12">
                                <span style="font-weight: bold; line-height: 26px; color: #131313;" :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">รายชื่อร้านค้า</span>
                              </v-col>
                              <v-col cols="12" style="">
                                <v-autocomplete
                                  v-model="selectGroup"
                                  :items="itemGroupName"
                                  :filter="filterData"
                                  color="primary"
                                  placeholder="เลือกร้านค้า"
                                  item-title="shop_name"
                                  item-value="seller_shop_id"
                                  style="border-radius: 8px;"
                                  chips
                                  dense
                                  closable-chips
                                  multiple
                                  outlined
                                  return-object
                                  @change="handleRemoveType"
                                >
                                  <template v-slot:selection="data">
                                    <draggable
                                      :id="data.index"
                                      :key="data.index"
                                      :list="selectGroup"
                                      v-bind="dragOptionsChips"
                                      :move="moveData"
                                      @change="changeData"
                                    >
                                      <v-chip
                                        draggable
                                        v-model="selectGroup[data.index]"
                                        :key="data.index"
                                        @mousedown.stop
                                        @click.stop
                                        v-bind="data.attrs"
                                        :input-value="data.selected"
                                        close
                                        @click="data.select"
                                        @click:close="remove(data.item)"
                                      >
                                        {{ data.item.shop_name }}
                                      </v-chip>
                                    </draggable>
                                  </template>
                                  <template v-slot:item="data">
                                    <template v-if="typeof data.item !== 'object'">
                                      <v-list-item-content>{{ data.item }}</v-list-item-content>
                                    </template>
                                    <template v-else>
                                      <v-list-item-content>
                                        <v-list-item-title >{{ data.item.shop_name }}</v-list-item-title>
                                      </v-list-item-content>
                                    </template>
                                  </template>
                                </v-autocomplete>
                              </v-col>
                            </v-row>
                          </v-card-text>
                        </v-card>
                      </v-row>
                      <div v-if="otopType === true">
                        <!-- <draggable v-model="selectGroup" v-bind="dragOptionsChips">
                          <div v-for="(itemGroupShop, index) in selectGroup" :key="index" style="display: inline-flex;">
                            <v-hover v-slot="{ hover }">
                              <v-card
                               style="display: inline-block;"
                               draggable
                               :elevation="hover ? 4 : 1"
                               :style="hover ? 'border: 1px solid #27AB9C;' : 'border: 1px solid #F3F5F7;'"
                                class="ma-1 rounded-0"
                                height="74"
                                width="173"
                              >
                                <v-card-text>
                                  <v-row dense justify="center">
                                    <v-col cols="12" align="center">
                                      <v-img
                                        v-if="itemGroupShop.shop_profile !== ''"
                                        :src="itemGroupShop.shop_profile"
                                        loading="lazy"
                                        width="130"
                                        height="90"
                                        contain
                                      ></v-img>
                                      v-if="itemGroupShop.shop_profile === ''"
                                      <v-img
                                        src="@/assets/StoreNew.png"
                                        loading="lazy"
                                        width="84"
                                        height="88"
                                        contain
                                      ></v-img>
                                    </v-col>
                                    <v-tooltip bottom>
                                      <template v-slot:activator="{ on, attrs }">
                                        <v-col
                                          cols="12"
                                          align="center"
                                          v-bind="attrs"
                                          v-on="on"
                                        >
                                          <span
                                            class="text-truncate d-inline-block"
                                            style="max-width: 145px"
                                            >{{ itemGroupShop.shop_name }}</span
                                          >
                                        </v-col>
                                      </template>
                                      <span>{{ itemGroupShop.shop_name }}</span>
                                    </v-tooltip>
                                  </v-row>
                                </v-card-text>
                              </v-card>
                            </v-hover>
                          </div>
                        </draggable> -->
                        <v-col :cols="MobileSize || IpadSize ? '12' : '6'" class="mb-2 px-0">
                          <v-text-field
                            v-model="searchShop"
                            dense
                            hide-details
                            outlined
                            placeholder="ค้นหาร้านค้า"
                            style="border-radius: 8px;"
                            append-icon="mdi-magnify"
                          ></v-text-field>
                        </v-col>
                        <v-data-table
                          :headers="headersShop"
                          :items="selectGroup"
                          :search="searchShop"
                          class="elevation-1 mt-3 mb-4"
                          max-height="100%"
                          no-results-text="ไม่พบรายการ"
                          no-data-text="ไม่พบรายการ"
                          style="width: 100%; white-space: nowrap; text-align: center;"
                          :footer-props="{ itemsPerPageText: 'จำนวนแถว' , itemsPerPageOptions: [5, 10, 20] }"
                        >
                        <template v-slot:[`item.shop_name`]="{ item }">
                          <span v-if="item.shop_name" style="white-space: nowrap; text-align: start;">{{ item.shop_name }}</span>
                          <span v-else>-</span>
                        </template>
                        <template v-slot:[`item.type`]="{ item }">
                          <v-select class="my-2" :style="MobileSize ? 'width: 200px;' : ''" v-model="item.type" :items="itemListType" item-text="type" item-value="value" label="ระบุประเภทร้านค้า" @change="handleSelectType(item)" hide-details dense outlined></v-select>
                        </template>
                        </v-data-table>
                      </div>
                    </v-col>
                    <!-- product status -->
                    <v-col cols="12">
                      <v-radio-group v-model="switchType" row>
                        <span style="font-size: 16px; align-items: center; display: flex; font-weight: 600;" class="mr-2">จัดการสินค้า : </span>
                        <v-radio style="font-size: 14px;" label="All" value="all" @click="handleType(switchType)"></v-radio>
                        <v-radio style="font-size: 14px;" label="Custom" value="custom" @click="handleType(switchType)"></v-radio>
                      </v-radio-group>
                    </v-col>
                    <!-- รายชื่อสินค้า -->
                    <v-col cols="12" v-if="productStatus === 'custom'">
                      <v-row dense class="pa-0">
                        <v-card class="px-0" elevation="0" width="100%" height="100%" style="border-radius: 8px;">
                          <v-card-text class="px-1">
                            <v-row dense>
                              <v-col cols="12">
                                <span style="font-weight: bold; line-height: 26px; color: #131313;" :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">รายชื่อสินค้า</span>
                              </v-col>
                              <v-col cols="12" style="">
                                <!-- <v-autocomplete
                                  v-model="selectProduct"
                                  :items="itemProduct"
                                  :filter="filterDataProduct"
                                  color="primary"
                                  placeholder="เลือกสินค้า"
                                  item-title="name"
                                  item-value="id"
                                  @change="itemProductChange(selectProduct)"
                                  style="border-radius: 8px;"
                                  chips
                                  dense
                                  closable-chips
                                  multiple
                                  outlined
                                  return-object
                                >
                                  <template v-slot:selection="data">
                                    <v-chip
                                      :key="data.index"
                                      :input-value="data.selected"
                                      close
                                      @click="data.select"
                                      @click:close="removeProduct(data.item); itemProductChange(selectProduct)"
                                    >
                                      {{ data.item.name }}
                                    </v-chip>
                                  </template>
                                  <template v-slot:item="data">
                                    <template v-if="typeof data.item !== 'object'">
                                      <v-list-item-content>{{ data.item }}</v-list-item-content>
                                    </template>
                                    <template v-else>
                                      <v-list-item-content>
                                        <v-list-item-title >{{ data.item.name }}</v-list-item-title>
                                      </v-list-item-content>
                                    </template>
                                  </template>
                                </v-autocomplete> -->
                              </v-col>
                            </v-row>
                          </v-card-text>
                        </v-card>
                      </v-row>
                      <v-row v-if="!MobileSize && !IpadSize" dense>
                        <v-col cols="6" class="mb-2">
                            <v-text-field
                              v-model="search"
                              dense
                              hide-details
                              outlined
                              placeholder="ค้นหาสินค้า"
                              style="border-radius: 8px;"
                              append-icon="mdi-magnify"
                            ></v-text-field>
                          </v-col>
                          <v-col cols="6'" class="mb-2 d-flex justify-end ">
                            <v-btn color="primary" rounded width="125" height="40" @click="openDialogAddProduct()">เพิ่มสินค้า</v-btn>
                          </v-col>
                      </v-row>
                      <v-row v-else dense>
                        <v-col cols="12" class="mb-2 d-flex">
                          <v-btn color="primary" rounded width="100%" height="40" @click="openDialogAddProduct()">เพิ่มสินค้า</v-btn>
                        </v-col>
                        <v-col cols="12" class="mb-2">
                            <v-text-field
                              v-model="search"
                              dense
                              hide-details
                              outlined
                              placeholder="ค้นหาสินค้า"
                              style="border-radius: 8px;"
                              append-icon="mdi-magnify"
                            ></v-text-field>
                          </v-col>
                      </v-row>
                      <v-data-table
                        :headers="headers"
                        :items="dataProduct"
                        :search="search"
                        class="elevation-1 mt-3 mb-4"
                        max-height="100%"
                        no-results-text="ไม่พบรายการ"
                        no-data-text="ไม่พบรายการ"
                        style="width: 100%; white-space: nowrap; text-align: center;"
                        :footer-props="{ itemsPerPageText: 'จำนวนแถว' , itemsPerPageOptions: [5, 10, 20] }"
                      >
                      <template v-slot:[`item.sku`]="{ item }">
                        <span v-if="item.sku" style="white-space: nowrap; text-align: start;">{{ item.sku }}</span>
                        <span v-else>-</span>
                      </template>
                      <!-- <template v-slot:[`item.media_path`]="{ item }">
                        <v-avatar v-if="item.media_path" class="my-1" size="56">
                          <img :src="item.media_path">
                        </v-avatar>
                        <v-avatar v-else class="my-1" size="56">
                          <img src="@/assets/NoImage.png" alt="No Image">
                        </v-avatar>
                      </template> -->
                      <template v-slot:[`item.name`]="{ item }">
                        <span v-if="item.name" style="white-space: nowrap; text-align: start;">{{ item.name }}</span>
                        <span v-else>-</span>
                      </template>
                      <template v-slot:[`item.delete`]="{ item }">
                        <v-btn v-if="item" style="" icon small @click="removeProduct(item)"><v-icon color="error">mdi-delete</v-icon></v-btn>
                      </template>
                      </v-data-table>
                    </v-col>
                    <v-col cols="12" md="12">
                      <span style="font-weight: bold; line-height: 26px; color: #131313;" :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพโลโก้ประเภทร้านค้า</span>
                      <v-card
                        v-if="Detail2.product_image.length === 0"
                        class="mt-3"
                        elevation="0"
                        style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;"
                        height="100%"
                        @click="onPickFile2()"
                      >
                        <v-card-text>
                          <v-row dense align="center" justify="center" style="cursor: pointer;">
                            <v-file-input v-model="DataImage2" :items="DataImage2"
                              accept="image/jpeg, image/jpg, image/png" @change="UploadImage2()" id="file_input2"
                              multiple :clearable="false" style="display:none">
                            </v-file-input>
                            <v-col cols="12" md="12">
                              <v-row justify="center" align="center">
                                <v-col cols="12" md="12" align="center">
                                  <v-img
                                    src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                                    width="280.34"
                                    height="154.87"
                                    contain
                                  ></v-img>
                                </v-col>
                                <v-col cols="12" md="12" style="text-align: center;">
                                  <span style="line-height: 24px; font-weight: 400;"
                                    :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                                  <span style="line-height: 24px; font-weight: 400;"
                                    :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                                  <span style="line-height: 16px; font-weight: 400;"
                                    :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">{{'(ขนาดรูปภาพ 600 X 600 px  ไฟล์นามสกุล .JPEG, .PNG, .JPG)'}}</span><br />
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                      <v-card
                        v-if="Detail2.product_image.length !== 0 "
                        elevation="0" width="100%" height="300px"
                        style="background: #FFFFFF; border-radius: 8px;"
                      >
                        <v-card-text>
                          <div class="mt-4">
                            <draggable v-model="Detail2.product_image" :move="onMove" @start="drag = true" @end="drag = false" class="fill-height align-center sortable-list">
                              <v-row align="center" justify="center">
                                <v-col v-for="(item, index) in Detail2.product_image" :key="index" cols="12" md="12" sm="12">
                                  <v-card outlined class="pa-1" width="100%" height="260">
                                    <v-img :src="item.url[0]" :lazy-src="item.url[0]"  width="100%" height="250" contain>
                                      <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                        <v-icon x-small color="white" dark @click="RemoveImage2(index, item)">mdi-close</v-icon>
                                      </v-btn>
                                    </v-img>
                                  </v-card>
                                </v-col>
                              </v-row>
                            </draggable>
                            <!-- <draggable v-model="Detail2.product_image" :move="onMove" @start="drag = true" @end="drag = false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                              <v-row align="center" justify="center">
                                <v-col v-for="(item, index) in Detail2.product_image" :key="index" cols="12" md="6" sm="6">
                                  <v-card outlined class="pa-1" width="350" height="260">
                                    <v-img :src="item.url[0]" :lazy-src="item.url[0]" width="350" height="250" contain>
                                      <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                        <v-icon x-small color="white" dark @click="RemoveImage2(index, item)">mdi-close</v-icon>
                                      </v-btn>
                                    </v-img>
                                  </v-card>
                                </v-col>
                              </v-row>
                            </draggable> -->
                          </div>
                        </v-card-text>
                      </v-card>
                    </v-col>
                    <!-- เพิ่มรูปภาพแบนเนอร์ประเภทร้านค้า -->
                    <v-col cols="12" md="12" class="mt-16">
                      <span style="font-weight: bold; line-height: 26px; color: #131313;" :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" class="subTitle">เพิ่มรูปภาพแบนเนอร์ประเภทร้านค้า</span>
                      <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                        <v-card-text class="px-0">
                          <v-card v-if="Detail.product_image.length === 0 " elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;" @click="onPickFile()">
                            <v-card-text class="px-0">
                              <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                                <v-file-input v-model="DataImage" :items="DataImage" accept="image/jpeg, image/jpg, image/png"
                                  @change="UploadImage()" id="file_input" multiple :clearable="false" style="display:none">
                                </v-file-input>
                                <v-col cols="12" md="12" class="mb-6">
                                  <v-row justify="center" class="pt-10">
                                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="280.34"
                                      height="154.87" contain></v-img>
                                  </v-row>
                                </v-col>
                                <v-col cols="12" md="12" class="mt-6">
                                  <v-row justify="center" align="center">
                                    <v-col cols="12" md="5" style="text-align: center;">
                                      <span
                                        style="font-size: 16px; line-height: 24px; font-weight: 400;">เพิ่มรูปภาพของคุณที่นี่</span><br />
                                      <span
                                        style="font-size: 16px; line-height: 24px; font-weight: 400;">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                                      <!-- <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .JPEG,
                                        .PNG
                                        เพิ่มได้สูงสุด 6 รูปภาพ)</span><br /> -->
                                      <span style="font-size: 12px; line-height: 16px; font-weight: 400;"><span
                                          style="color: red;">***</span> หมายเหตุ
                                        ไฟล์รูปควรมีขนาดไม่เกิน 2 MB</span>
                                    </v-col>
                                  </v-row>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                          <v-card
                            v-if="Detail.product_image.length !== 0 "
                            elevation="0" width="100%" height="300px"
                            style="background: #FFFFFF; border-radius: 8px;"
                          >
                            <v-card-text>
                              <div class="mt-4">
                                <draggable v-model="Detail.product_image" :move="onMove" @start="drag = true" @end="drag = false" class="  fill-height align-center sortable-list">
                                  <v-row align="center" justify="center">
                                    <v-col v-for="(item, index) in Detail.product_image" :key="index" cols="12" md="12" sm="12">
                                      <v-card outlined class="pa-1" width="100%" height="260">
                                        <v-img :src="item.path" :lazy-src="item.path" width="100%" height="250" contain>
                                          <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                            <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                                          </v-btn>
                                        </v-img>
                                      </v-card>
                                    </v-col>
                                  </v-row>
                                </draggable>
                              </div>
                            </v-card-text>
                          </v-card>
                          <!-- <div v-if="Detail.product_image.length !== 0" class="mt-4">
                            <draggable v-model="Detail.product_image" :move="onMove" @start="drag=true" @end="drag=false"
                              class="pl-5 pr-5 row  fill-height align-center sortable-list">
                              <v-col v-for="(item, index) in Detail.product_image" :key="index" cols="6" sm="4" md="2">
                                <v-card v-if="item.type === 'image'" outlined class="pa-1" width="146" height="146">
                                  <v-img :src="item.path" :lazy-src="item.url" width="130" height="130" contain>
                                    <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                      <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                                    </v-btn>
                                  </v-img>
                                </v-card>
                              </v-col>
                            </draggable>
                          </div> -->
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
          </v-card-text>
          <v-card-actions>
            <v-row dense justify="end" style="height: 88px; background: #ffffff;" :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
              <v-btn rounded width="125" height="40" class="my-auto" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
              <v-btn rounded width="125" height="40" class="white--text my-auto ml-3" color="#27AB9C" @click="saveData()">บันทึก</v-btn>
            </v-row>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-col>
    <!-- Dialog เพิ่มสินค้า  -->
  <v-dialog v-model="dialogAddProduct" persistent width="400">
    <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
      <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
        <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
          เพิ่มสินค้า
        </span>
          <v-btn icon dark small @click="closeDialogAddProduct()">
          <v-icon small color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-row justify="center" dense class="pt-6">
          <v-col cols="12" align="center">
            <v-autocomplete
              v-model="selectProduct"
              :items="itemProduct"
              :filter="filterDataProduct"
              color="primary"
              label="เลือกสินค้า"
              item-title="name"
              item-value="id"
              style="border-radius: 8px; max-width: 350px;"
              chips
              dense
              closable-chips
              multiple
              outlined
              return-object
            >
              <template v-slot:selection="data">
                <v-chip
                  :key="data.index"
                  :input-value="data.selected"
                  class="mt-1"
                >
                <span style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{ data.item.name }}</span>
                </v-chip>
              </template>
              <template v-slot:item="data">
                <template v-if="typeof data.item !== 'object'">
                  <v-list-item-content>{{ data.item }}</v-list-item-content>
                </template>
                <template v-else>
                  <v-list-item-content style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                    <v-list-item-title >{{ data.item.name }}</v-list-item-title>
                  </v-list-item-content>
                </template>
              </template>
            </v-autocomplete>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-actions>
        <v-row dense justify="center" class="pb-4">
          <v-btn width="110" height="40" outlined rounded color="#27AB9C" class="mr-2"  @click="closeDialogAddProduct()">ยกเลิก</v-btn>
          <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="itemProductChange(selectProduct); closeDialogAddProduct()" :disabled="selectProduct.length === 0">ตกลง</v-btn>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
  </v-container>
</template>
<script>
import draggable from 'vuedraggable'
export default {
  components: {
    draggable
  },
  data () {
    return {
      lazy: false,
      dragged: {
        from: -1,
        to: -1,
        newIndex: -1
      },
      setStaus: '',
      dragging: -1,
      selectIndex: null,
      openModalCreateGroupShop: false,
      upload_file_images: [],
      itemGroupName: [],
      DataImage: [],
      DataImage2: [],
      Detail2: {
        product_image: [],
        shop_name_th: '',
        shop_name_en: '',
        shop_description: '',
        path_logo: ''
      },
      Detail: {
        product_image: [],
        shop_name_th: '',
        shop_name_en: '',
        shop_description: '',
        path_logo: ''
      },
      FormData: {
        group_id: '',
        group_name: '',
        group_shop_description: '',
        group_shop_short_description: '',
        group_seller_shop: [],
        group_seller_shop_id: [],
        group_seller_shop_otop_type: [],
        group_shop_logo: '',
        group_status: ''
      },
      selectGroup: [],
      statusRoute: null,
      selectGroupImage: [],
      Rules: {
        empty: [v => !!v || 'กรุณากรอกข้อมูล'],
        emptyCheckbox: [v => !!v || '']
      },
      search: '',
      searchShop: '',
      itemShopID: [],
      itemProduct: [],
      itemAddProductType: [],
      itemDeleteProductType: [],
      selectProduct: [],
      headers: [
        { text: 'รหัส sku', value: 'sku', sortable: false, align: 'start', width: 160, class: 'backgroundTable fontTable--text' },
        // { text: 'ภาพสินค้า', value: 'media_path', sortable: false, align: 'center', width: 120, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อสินค้า', value: 'name', sortable: false, align: 'start', class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'delete', sortable: false, align: 'start', width: 60, class: 'backgroundTable fontTable--text' }
      ],
      headersShop: [
        { text: 'ชื่อร้านค้า', value: 'shop_name', sortable: false, align: 'start', width: '60%', class: 'backgroundTable fontTable--text' },
        // { text: 'ภาพสินค้า', value: 'media_path', sortable: false, align: 'center', width: 120, class: 'backgroundTable fontTable--text' },
        { text: 'ประเภท', value: 'type', sortable: false, align: 'start', width: '40%', class: 'backgroundTable fontTable--text' }
      ],
      showCountRequest: 0,
      itemsPerPage: 10,
      page: 1,
      totalItems: 0,
      options: {
        page: 1,
        itemsPerPage: 10
      },
      dialogAddProduct: false,
      dataProduct: [],
      productStatus: 'all',
      switchType: 'all',
      otopType: false,
      otopShopType: [],
      itemListType: [],
      itemRemoveShopID: []
    }
  },
  async created () {
    // this.$EventBus.$on('modalCreateGroupShop', this.manageGroupStoreModal)
    // console.log('this.selectGroup1', this.selectGroup)
    this.statusRoute = await this.$router.currentRoute.params.data
    // console.log('this.pathRoute', this.statusRoute)
    if (this.statusRoute === 'edit') {
      await this.getNameGroupStore()
      const itemData = await JSON.parse(localStorage.getItem('itemGroupStore') || '[]')
      // console.log('itemData', itemData)
      this.otopType = itemData.data.otop
      // console.log('otopType', this.otopType)
      this.otopShopType = itemData.data.data_group_seller_shop_otop_type || []
      // console.log('otopShopType1', this.otopShopType)
      await this.manageGroupStoreModal(itemData.data)
      if (this.otopType === true) {
        await this.getListType()
        // console.log('this.selectGroup2', this.selectGroup)
        await this.itemGroupNameChange(this.selectGroup)
      }
      await this.getNameProduct()
    } else {
      this.getNameGroupStore()
    }
    this.getProductStatus()
    // window.setInterval(async () => {
    //   await console.log('ทุกๆๆ 5 นาที')
    // }, 50000)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    dragOptionsChips () {
      return {
        animation: 200,
        group: 'group',
        disabled: false,
        ghostClass: 'ghost',
        sort: true
      }
    }
  },
  watch: {
    // async MobileSize (val) {
    //   if (val === true) {
    //     this.$router.push({ path: `/manageGroupStoreMobile/${this.$router.currentRoute.params.data}` }).catch(() => {})
    //   } else {
    //     // await localStorage.setItem('pathAdmin', `/manageGroupStore/${this.$router.currentRoute.params.data}`)
    //     // this.$router.push({ path: '/AdminPanit' }).catch(() => {})
    //     // this.$router.push({ path: `/manageGroupStore/${this.$router.currentRoute.params.data}` }).catch(() => {})
    //   }
    // },
    MobileSize (val) {
      var statusManagegroup = localStorage.getItem('status_managegroup')
      if (val === true) {
        this.$router.push({ path: `/manageGroupStoreMobile/${statusManagegroup}` }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'manageGroupStore')
        // await this.$router.push({ path: '/AdminPanit' }).catch(() => {})
        this.$router.push({ path: `/manageGroupStore/${statusManagegroup}` }).catch(() => {})
      }
    },
    selectionItem () {
      return this.selectGroup
    }
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/groupStoreMobile' }).catch(() => {})
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    updateItemsPerPage (newItemsPerPage) {
      this.itemsPerPage = newItemsPerPage
      // console.log('Items per page updated to:', newItemsPerPage)
    },
    filterData (item, queryText) {
      const shopName = item.shop_name ? item.shop_name.toLowerCase() : ''
      const taxId = item.tax_id ? item.tax_id.toLowerCase() : ''
      return shopName.includes(queryText.toLowerCase()) || taxId.includes(queryText.toLowerCase())
    },
    // filterData (item, queryText) {
    //   if (item.shop_name === null) {
    //     item.shop_name = ''
    //   }
    //   console.log('queryText', queryText)
    //   return (
    //     item.shop_name.toLowerCase().includes(queryText.toLowerCase()) || item.tax_id.toLowerCase().includes(queryText.toLowerCase())
    //   )
    // },
    onMove () {
    },
    moveData (value) {
      this.dragged = {
        from: parseInt(value.from.id),
        to: parseInt(value.to.id),
        newIndex: value.draggedContext.futureIndex
      }
    },
    changeData (value) {
      if (value.removed) {
        // insert
        this.selectGroup.splice(this.dragged.to + this.dragged.newIndex, 0, this.selectGroup[this.dragged.from])
        // delete
        if (this.dragged.from < this.dragged.to) {
          this.selectGroup.splice(this.dragged.from, 1)
        } else {
          this.selectGroup.splice(this.dragged.from + 1, 1)
        }
      }
    },
    removeItemAt (index) {
      this.selectGroup.splice(index, 1)
    },
    dragStart (which, ev) {
      ev.dataTransfer.setData('Text', this.id)
      ev.dataTransfer.dropEffect = 'move'
      this.dragging = which
    },
    dragEnter (ev) {
      /*
      if (ev.clientY > ev.target.height / 2) {
        ev.target.style.marginBottom = '10px'
      } else {
        ev.target.style.marginTop = '10px'
      }
      */
    },
    dragLeave (ev) {
      /*
      ev.target.style.marginTop = '2px'
      ev.target.style.marginBottom = '2px'
      */
    },
    dragEnd (ev) {
      this.dragging = -1
    },
    dragFinish (to, ev) {
      // console.log('fgfg', this.dragging, to, ev)
      this.moveItem(this.dragging, to)
      ev.target.style.marginTop = '2px'
      ev.target.style.marginBottom = '2px'
    },
    moveItem (from, to) {
      if (to === -1) {
        this.removeItemAt(from)
      } else {
        var temp = this.selectGroup
        temp.splice(to, 0, this.selectGroup.splice(from, 1)[0])
        this.selectGroup = []
        this.selectGroup = temp
        this.FormData.group_seller_shop = []
        this.FormData.group_seller_shop = this.selectGroup
        // for (const c in this.Detail.product_image) {
        //   if (this.Detail.product_image[c].showStartDate !== null && this.Detail.product_image[c].showEndDate !== null) {
        //     this.selectTimes[c] = '2'
        //   } else {
        //     this.selectTimes[c] = '1'
        //   }
        // }
        // console.log('sdl2', this.selectGroup)
      }
    },
    checkLastest (index) {
      var a = this.length - 1
      if (index === a) {
        return false
      } else {
        return true
      }
    },
    async setIndex (index) {
      // console.log('cccindex', index)
      this.selectIndex = await index
      // console.log('cccindex', this.selectIndex, index)
    },
    setModel (item) {
      this.selectGroup[this.selectIndex] = item

      this.itemGroupName.forEach(e => { if (!this.selectGroup.map(x => { return x.seller_shop_id }).includes(e.seller_shop_id)) { e.disabled = false } else { e.disabled = true } })
      // console.log('cccc', item, this.selectIndex, this.selectGroup)
      // if (this.selectGroup.includes(item.seller_shop_id)) {

      // } else if (!this.selectGroup.includes(item.seller_shop_id)) {
      //   if () {
      //     this.selectGroup.push(item.seller_shop_id)
      //     item.disabled = true
      //   }
      // }
    },
    async getProductStatus () {
      this.$store.commit('openLoader')
      var data = {
        group_id: this.FormData.group_id
      }
      await this.$store.dispatch('actionsProductStatus', data)
      var response = await this.$store.state.ModuleHompage.stateProductStatus
      if (response.result === 'SUCCESS') {
        this.productStatus = response.data[0].product_status
        this.switchType = this.productStatus
      }
      this.$store.commit('closeLoader')
    },
    async getNameGroupStore () {
      // await this.$store.dispatch('actionsGraczSellerShopPage', data)
      // var res = await this.$store.state.ModuleGraczShop.stateGraczSellerShopPage
      // const data = {
      //   id: '1'
      // }
      const data = {
        unset_shop_profile: 'YES'
      }
      await this.$store.dispatch('actionsGroupStoreName', data)
      var response = await this.$store.state.ModuleHompage.stateGroupStoreName
      // console.log('fffhhiiii', response)
      if (response.result === 'SUCCESS') {
        this.itemGroupName = await response.data.seller_shop_data.map(el => {
          return {
            ...el,
            disabled: false,
            type: null
          }
        })
        // console.log(this.itemGroupName, 'this itemGroupName')
        // this.addform()
      } else {
      }
    },
    // addform () {
    //   const data = { image: '', name: '', group_id: null }
    //   this.FormData.group_seller_shop.push(data)
    // },
    manageGroupStoreModal (item) {
      // console.log('item---', item)
      var temp = JSON.parse(item.group_seller_shop_id)
      this.setStaus = this.statusRoute
      this.FormData.group_id = item.id
      this.FormData.group_name = item.group_name
      this.FormData.group_shop_short_description = item.group_shop_short_description
      this.FormData.group_status = item.group_status
      const shopMediaPath = []
      shopMediaPath.push(item.group_shop_media_path)
      // this.FormData.group_shop_logo = item.group_shop_media_path
      const detail2 = {
        url: shopMediaPath
      }
      this.Detail2.product_image.push(detail2)
      this.DataImage = []
      // console.log(typeof this.DataImage)
      if (item.group_shop_banner_path !== null && item.group_shop_banner_path !== '') {
        const detail = {
          type: 'image',
          path: item.group_shop_banner_path,
          url: item.group_shop_banner_path
        }
        this.Detail.product_image.push(detail)
      } else {
        this.Detail.product_image = []
      }
      this.FormData.group_shop_description = item.group_shop_description
      // console.log('this.groupNameOtop', this.groupNameOtop)
      // for (const x in temp) {
      //   this.selectGroup = this.itemGroupName.map(e => { return e.seller_shop_id }).includes(temp[x])
      // }
      // this.selectGroup = temp.reduce((acc, curr) => {
      //   const index = this.itemGroupName.findIndex(item => item.seller_shop_id === curr)
      //   console.log('index', index)
      //   if (index > -1) {
      //     curr.shop_profile = this.itemGroupName[index].shop_profile
      //     curr.shop_name = this.itemGroupName[index].shop_name
      //     curr.seller_shop_id = this.itemGroupName[index].seller_shop_id
      //     curr.name_th = this.itemGroupName[index].name_th
      //     curr.disabled = this.itemGroupName[index].disabled
      //   }
      //   acc.push(curr)
      //   return acc
      // }, [])
      temp.map((e, i) => {
        const temps = this.itemGroupName.find(element => element.seller_shop_id === e)
        this.selectGroup.push(temps)
      })
      // this.selectGroup = this.itemGroupName.filter(x => { return temp.includes(x.seller_shop_id) })
      // console.log('sdsd', this.selectGroup)
      this.openModalCreateGroupShop = true
    },
    deleteGroupName (index) {
      this.FormData.group_seller_shop.splice(index, 1)
    },
    async cancel () {
      if (this.MobileSize) {
        await this.$router.push({ path: '/groupStoreMobile' }).catch(() => {})
      } else {
        await this.$router.push({ path: '/groupStore' }).catch(() => {})
      }
    },
    async saveData () {
      this.$store.commit('openLoader')
      // console.log('selectGroup', this.Detail, this.Detail2)
      var list = []
      var list2 = []
      if (this.setStaus === 'edit') {
        if (this.Detail.product_image.length !== 0) {
          for (let i = 0; i < this.Detail.product_image.length; i++) {
            if (this.Detail.product_image[i].name === 'default') {
              list.push({
                id: this.Detail.product_image[i].id
              })
            } else {
              list.push({
                id: '-1',
                image_data: this.Detail.product_image[i].image_data === undefined ? null : this.Detail.product_image[i].image_data
              })
            }
          }
        }
        if (this.Detail2.product_image.length !== 0) {
          for (let i = 0; i < this.Detail2.product_image.length; i++) {
            if (this.Detail2.product_image[i].name === 'default') {
              list2.push({
                id: this.Detail2.product_image[i].id
              })
            } else {
              list2.push({
                id: '-1',
                image_data: this.Detail2.product_image[i].image_data === undefined ? null : this.Detail2.product_image[i].image_data
              })
            }
          }
        }
        // console.log('list', list)
        if (this.checkVal(this.FormData.group_name, 'name')) {
          var editVal = await {
            group_id: this.FormData.group_id,
            group_name: this.FormData.group_name,
            group_status: this.FormData.group_status,
            group_shop_description: this.FormData.group_shop_description,
            group_shop_short_description: this.FormData.group_shop_short_description,
            group_seller_shop_otop_type: this.selectGroup.map(x => { return { seller_shop_id: x.seller_shop_id, type: x.type } }),
            group_seller_shop_id: this.selectGroup.map(x => { return x.seller_shop_id }),
            group_shop_logo: list2.length === 0 ? null : list2[0].image_data,
            group_shop_banner: list.length === 0 ? -1 : list[0].image_data,
            list_product_add: this.itemAddProductType,
            list_product_delete: this.itemDeleteProductType,
            product_status: this.productStatus
          }
          // console.log('delVal', editVal)
          await this.$store.dispatch('actionsEditGroupStoreName', editVal)
          var response = await this.$store.state.ModuleHompage.stateEditGroupStoreName
          if (response.result === 'SUCCESS') {
            this.$store.commit('closeLoader')
            await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'ดำเนินการสำเร็จ' })
            await this.$router.push({ path: '/groupStore' }).catch(() => {})
            // await this.$EventBus.$emit('callModalCreateGroupStore')
            // this.openModalCreateGroupShop = await false
          } else {
            if (response.result === 'FAILED' && response.message === 'Some key or value are empty.[group_name,group_shop_logo]') {
              this.$store.commit('closeLoader')
              await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'กรุณาเพิ่ม ชื่อและรูปของประเภทร้านค้า' })
            } else if (response.result === 'FAILED' && response.message === 'Some key or value are empty.[group_name]') {
              this.$store.commit('closeLoader')
              await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'กรุณาเพิ่ม ชื่อของประเภทร้านค้า' })
            } else if (response.result === 'FAILED' && response.message === 'Some key or value are empty.[group_shop_logo]') {
              this.$store.commit('closeLoader')
              await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'กรุณาเพิ่ม รูปของประเภทร้านค้า' })
            } else {
              this.$store.commit('closeLoader')
              await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'บันทึกไม่สำเร็จ' })
            }
          }
        }
      } else {
        list = []
        list2 = []
        if (this.Detail.product_image.length !== 0) {
          for (let i = 0; i < this.Detail.product_image.length; i++) {
            if (this.Detail.product_image[i].name === 'default') {
              list.push({
                id: this.Detail.product_image[i].id
              })
            } else {
              list.push({
                id: '-1',
                image_data: this.Detail.product_image[i].image_data === undefined || this.Detail.product_image[i].image_data === null ? null : this.Detail.product_image[i].image_data
              })
            }
          }
        }
        if (this.Detail2.product_image.length !== 0) {
          for (let i = 0; i < this.Detail2.product_image.length; i++) {
            if (this.Detail2.product_image[i].name === 'default') {
              list2.push({
                id: this.Detail2.product_image[i].id
              })
            } else {
              list2.push({
                id: '-1',
                image_data: this.Detail2.product_image[i].image_data === undefined || this.Detail2.product_image[i].image_data === null ? null : this.Detail2.product_image[i].image_data
              })
            }
          }
        }
        var delVal = await {
          group_name: this.FormData.group_name,
          group_shop_description: this.FormData.group_shop_description,
          group_status: this.FormData.group_status,
          group_shop_short_description: this.FormData.group_shop_short_description,
          group_seller_shop_otop_type: this.selectGroup.map(x => { return { seller_shop_id: x.seller_shop_id, type: x.type } }),
          group_seller_shop_id: this.selectGroup.map(x => { return x.seller_shop_id }),
          group_shop_logo: list2.length === 0 ? null : list2[0].image_data,
          group_shop_banner: list.length === 0 ? null : list[0].image_data,
          product_status: this.productStatus,
          list_product_add: this.itemAddProductType,
          list_product_delete: this.itemDeleteProductType
        }
        // console.log('delVal', delVal)
        this.selectProduct = []
        await this.$store.dispatch('actionsCreateGroupStoreName', delVal)
        var responses = await this.$store.state.ModuleHompage.stateCreateGroupStoreName
        if (responses.result === 'SUCCESS') {
          await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'ดำเนินการสำเร็จ' })
          await this.$router.push({ path: '/groupStore' }).catch(() => {})
          // await this.$EventBus.$emit('callModalCreateGroupStore')
          // this.openModalCreateGroupShop = await false
          this.$store.commit('closeLoader')
        } else {
          if (responses.result === 'FAILED' && responses.message === 'Some key or value are empty.[group_name,group_shop_logo]') {
            await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'กรุณาเพิ่ม ชื่อและรูปของประเภทร้านค้า' })
            this.$store.commit('closeLoader')
          } else if (responses.result === 'FAILED' && responses.message === 'Some key or value are empty.[group_name]') {
            await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'กรุณาเพิ่ม ชื่อของประเภทร้านค้า' })
            this.$store.commit('closeLoader')
          } else if (responses.result === 'FAILED' && responses.message === 'Some key or value are empty.[group_shop_logo]') {
            await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'กรุณาเพิ่ม รูปของประเภทร้านค้า' })
            this.$store.commit('closeLoader')
          } else if (responses.result === 'FAILED' && responses.message === 'This Group name has already used.') {
            await this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'error', title: 'ชื่อประเภทร้านค้านี้มีอยู่ในระบบแล้ว' })
            this.$store.commit('closeLoader')
          } else {
            this.$store.commit('closeLoader')
          }
        }
      }
      // console.log('FormData', response)
    },
    checkVal (val, name) {
      if (name === 'name' && (val === '' || val === undefined || val === null)) {
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'ชื่อประเภทร้านค้าไม่สามารถว่างได้' })
        return false
      } else {
        return true
      }
      // if (name === 'logo' && (val === '' || val === undefined || val === null)) {
      //   this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'โลโก้กลุ่มร้านค้าไม่สามารถว่างได้' })
      //   return false
      // }
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    onPickFile2 () {
      document.getElementById('file_input2').click()
    },
    async UploadImage2 () {
      for (let i = 0; i < this.DataImage2.length; i++) {
        const element = this.DataImage2[i]
        if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
          var url = URL.createObjectURL(element)
          const image = new Image()
          const imageDimensions = await new Promise((resolve) => {
            image.onload = () => {
              const dimensions = {
                height: image.height,
                width: image.width
              }
              resolve(dimensions)
            }
            image.src = url
          })
          if (this.Detail2.product_image.length < 2) {
            if (i < 1) {
              if (imageDimensions.height <= 600 && imageDimensions.width <= 600) {
                const reader = new FileReader()
                reader.readAsDataURL(element)
                reader.onload = () => {
                  var resultReader = reader.result
                  var url = URL.createObjectURL(element)
                  if (this.$route.query.Status !== 'Edit') {
                    this.upload_file_images = element // เอาค่าไฟล์มาเก็บเอาไว้เพื่อเอาไปใช้ในการส่ง api ตอนกด confirmCreate
                    this.Detail2.product_image.push({
                      image_data: resultReader.split(',')[1],
                      url: [url],
                      link: '',
                      name: this.DataImage2[i].name
                    })
                  } else {
                    this.upload_file_images = element // เอาค่าไฟล์มาเก็บเอาไว้เพื่อเอาไปใช้ในการส่ง api ตอนกด confirmCreate
                    this.Detail2.product_image.push({
                      image_data: resultReader.split(',')[1],
                      media_path: url,
                      name: this.DataImage2[i].name
                    })
                  }
                }
              } else {
                this.upload_file_images = []
                this.$swal.fire({ icon: 'warning', text: 'โปรดใช้รูปตามขนาดที่กำหนด', showConfirmButton: false, timer: 1500 })
              }
            } else {
              this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่แบนเนอร์หลักไม่เกิน 1 ภาพ', showConfirmButton: false, timer: 1500 })
            }
          } else {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่แบนเนอร์หลักไม่เกิน 1 ภาพ', showConfirmButton: false, timer: 1500 })
          }
        } else {
          this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ', showConfirmButton: false, timer: 2500 })
        }
      }
    },
    RemoveImage2 (index, val) {
      if (this.$route.query.Status === 'Edit') {
        if (val.id !== undefined) {
          this.Detail2.product_image_delete.push(val.id)
        }
        this.Detail2.product_image.splice(index, 1)
      } else {
        this.Detail2.product_image.splice(index, 1)
      }
    },
    RemoveImage (index, val) {
      if (this.$route.query.Status === 'Edit') {
        if (val.id !== undefined) {
          this.Detail.remove_img.push({
            id: val.id
          })
        }
        this.Detail.product_image.splice(index, 1)
      } else {
        this.Detail.product_image.splice(index, 1)
      }
      this.DataImage = []
    },
    UploadImage () {
      // console.log(this.DataImage, 'this.DataImage')
      // var mediaType = ''
      // var showImage = []
      // this.shop_media = []
      // this.Detail.product_image = []
      // console.log(this.Detail.product_image.length, 'aaaaa')
      if (this.Detail.product_image.length < 2) {
        for (let i = 0; i < this.DataImage.length; i++) {
          const element = this.DataImage[i]
          const imageSize = element.size / 1024 / 1024
          if (i < 1) {
            if (imageSize < 2) {
              const reader = new FileReader()
              reader.readAsDataURL(element)
              reader.onload = () => {
                var resultReader = reader.result
                var url = URL.createObjectURL(element)
                if (this.Detail.product_image.length < 2) {
                  this.Detail.product_image.push({
                    image_data: resultReader.split(',')[1],
                    path: url,
                    name: this.DataImage[i].name,
                    type: (this.DataImage[i].type.split('/', 1)).toString()
                  })
                  // console.log(this.Detail.product_image.length, 'aaaaa2')
                } else {
                  this.$swal.fire({
                    icon: 'warning',
                    text: 'กรุณาใส่รูปไม่เกิน 1 รูป',
                    showConfirmButton: false,
                    timer: 1500
                  })
                }
                // console.log(this.Detail.product_image, 'this.Detail.product_image')
                // mediaType = this.DataImage[i].type
                // var checkType = mediaType.split('/', 1)
                // if (checkType.toString() === 'video') {
                //   checkType = 'vdo'
                // } else {
                //   checkType = 'image'
                // }
                // this.shop_media.push({
                //   media: element,
                //   media_type: checkType
                // })
                // console.log(this.shop_media, 'this.shop_media')
              }
            } else {
              this.$swal.fire({
                icon: 'warning',
                text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 2 MB',
                showConfirmButton: false,
                timer: 1500
              })
            }
          } else {
            this.$swal.fire({
              icon: 'warning',
              text: 'กรุณาใส่รูปไม่เกิน 1 รูป',
              showConfirmButton: false,
              timer: 1500
            })
          }
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาใส่รูปไม่เกิน 1 รูป',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    async getProduct () {
      this.itemShopID = []
      this.selectGroup.forEach((item) => {
        this.itemShopID.push(item.seller_shop_id)
      })
      // console.log('this.selectGroup', this.selectGroup)
      // console.log('this.itemShopID', this.itemShopID)
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.itemShopID,
        page: 1,
        offset: -1,
        search: this.search
      }
      // if (this.groupNameOTOP === true) {
      //   data = {
      //     otop: true,
      //     seller_shop_id: this.itemShopID,
      //     page: 1,
      //     offset: -1,
      //     search: ''
      //   }
      // }
      // if (this.groupNameEtax === true) {
      //   data = {
      //     etax: true,
      //     seller_shop_id: this.itemShopID,
      //     page: 1,
      //     offset: -1,
      //     search: ''
      //   }
      // }
      await this.$store.dispatch('actionsGetProduct', data)
      var response = await this.$store.state.ModuleHompage.stateGetProduct
      // console.log('response', response)
      if (response.result === 'SUCCESS') {
        // this.itemProduct = response.data.list_product
        this.itemProduct = response.data.list_product.filter(product =>
          !this.dataProduct.some(data => data.id === product.id)
        )
        // console.log('selectProduct 1', this.selectProduct)
        // this.itemProductChange(this.selectProduct)
        // console.log('selectProduct 2', this.selectProduct)
        // console.log('this.itemProduct', this.itemProduct)
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          html: '<h3>ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
      this.$store.commit('closeLoader')
    },
    filterDataProduct (item, queryText) {
      const Name = item.name ? item.name.toLowerCase() : ''
      return Name.includes(queryText.toLowerCase())
    },
    // filterDataProduct (item, queryText) {
    //   return (
    //     item.name.toLowerCase().includes(queryText.toLowerCase())
    //   )
    // },
    async itemGroupNameChange (value) {
      // console.log('itemRemoveShopID', this.itemRemoveShopID)
      if (this.otopType === true) {
        this.otopShopType.map((item) => {
          const sellerID = parseInt(item.seller_shop_id)
          // const existingValue = ''
          const existingValue = value.find(v => v.seller_shop_id === sellerID)
          if (existingValue) {
            if (existingValue.type === null || existingValue.type === undefined || existingValue.type === '') {
              existingValue.type = item.type
            }
          } else {
            const matchedGroup = this.itemGroupName.find(group => group.seller_shop_id === sellerID)
            if (matchedGroup) {
              value.push({
                ...matchedGroup,
                type: item.type
              })
            }
          }
          this.handleSelectType(item.seller_shop_id, item.type)
        })
        this.selectGroup = value
        // console.log('this.selectGroup', this.selectGroup)
      }
    },
    handleRemoveType (val) {
      // console.log('this.itemRemoveShopID', this.itemRemoveShopID)
      // console.log('val', val)
      val.forEach(item => {
        const sellerID = parseInt(item.seller_shop_id)
        if (this.itemRemoveShopID.includes(sellerID)) {
          const index = this.selectGroup.findIndex(i => i.seller_shop_id === sellerID)
          if (index !== -1) {
            this.selectGroup[index].type = null
          }
          this.itemRemoveShopID = this.itemRemoveShopID.filter(id => id !== sellerID)
        }
      })
      // console.log('val', val)
      // console.log('this.selectGroup', this.selectGroup)
      // console.log('this.itemRemoveShopID', this.itemRemoveShopID)
    },
    handleSelectType (item) {
      const index = this.selectGroup.findIndex(i => i.seller_shop_id === item.seller_shop_id)
      if (index !== -1) {
        this.selectGroup[index].type = item.type
      }
    },
    async getListType () {
      this.$store.commit('openLoader')
      var data = {}
      await this.$store.dispatch('actionsGetListType', data)
      var response = await this.$store.state.ModuleAdminManage.stateGetListType
      if (response.code === 200) {
        this.itemListType = response.data.list_otop_type
        // console.log('this.itemListType', this.itemListType)
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          html: '<h3>ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
      this.$store.commit('closeLoader')
    },
    remove (item) {
      // const x = this.selectGroup.filter(e => e.seller_shop_id !== item.seller_shop_id)
      // this.selectGroup = x
      // console.log('this.dataProduct', this.dataProduct)
      // var deleteProduct = this.dataProduct.filter(e => e.seller_shop_id === item.seller_shop_id)
      // this.itemDeleteProductType.push(...deleteProduct.map(e => e.id))
      // this.dataProduct = this.dataProduct.filter(e => e.seller_shop_id !== item.seller_shop_id)
      // console.log('this.itemDeleteProductType', this.itemDeleteProductType)
      this.itemRemoveShopID.push(item.seller_shop_id)
      // console.log('itemRemoveShopID', this.itemRemoveShopID)
      this.selectGroup = this.selectGroup.filter(e => e.seller_shop_id !== item.seller_shop_id)
      const deleteProduct = this.dataProduct.filter(e => e.seller_shop_id === item.seller_shop_id)
      deleteProduct.forEach(e => {
        if (!this.itemDeleteProductType.includes(e.id)) {
          this.itemDeleteProductType.push(e.id)
        }
      })
      this.dataProduct = this.dataProduct.filter(e => e.seller_shop_id !== item.seller_shop_id)
      this.itemAddProductType = this.itemAddProductType.filter(id =>
        !this.itemDeleteProductType.includes(id)
      )
    },
    itemProductChange (value) {
      // console.log('itemProductChange', value)
      // value.forEach(item => {
      //   if (!this.itemAddProductType.includes(item.id)) {
      //     this.itemAddProductType.push(item.id)
      //   }
      //   if (!this.dataProduct.some(product => product.id === item.id)) {
      //     this.dataProduct.push(item)
      //   }
      //   this.itemDeleteProductType = this.itemDeleteProductType.filter(id => id !== item.id)
      // })
      // this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'เพิ่มสินค้าในตารางสำเร็จ' })
      // this.itemAddProductType.forEach(item => {
      // })
      value.forEach(item => {
        if (!this.itemAddProductType.includes(item.id)) {
          this.itemAddProductType.push(item.id)
        }
        if (!this.dataProduct.some(product => product.id === item.id)) {
          this.dataProduct.push(item)
        }
        this.itemDeleteProductType = this.itemDeleteProductType.filter(id => id !== item.id)
      })
      this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'เพิ่มสินค้าในตารางสำเร็จ' })
    },
    removeProduct (item) {
      // this.dataProduct = this.dataProduct.filter(e => e.id !== item.id)
      // if (!this.itemDeleteProductType.includes(item.id)) {
      //   this.itemDeleteProductType.push(item.id)
      // }
      // this.itemAddProductType = this.itemAddProductType.filter(id => id !== item.id)
      // this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'ลบสินค้าในตารางสำเร็จ' })
      this.dataProduct = this.dataProduct.filter(e => e.id !== item.id)
      if (!this.itemDeleteProductType.includes(item.id)) {
        this.itemDeleteProductType.push(item.id)
      }
      this.itemAddProductType = this.itemAddProductType.filter(id => id !== item.id)
      this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'ลบสินค้าในตารางสำเร็จ' })
    },
    async getNameProduct () {
      this.$store.commit('openLoader')
      var data = {
        group_id: this.FormData.group_id,
        page: 1,
        offset: -1,
        search: this.search
      }
      // if (this.groupNameOTOP === true) {
      //   data = {
      //     otop: true,
      //     page: 1,
      //     offset: -1,
      //     search: this.search
      //   }
      // }
      // if (this.groupNameEtax === true) {
      //   data = {
      //     etax: true,
      //     page: 1,
      //     offset: -1,
      //     search: this.search
      //   }
      // }
      // await this.$store.dispatch('actionsProductType', data)
      // var response = await this.$store.state.ModuleHompage.stateProductType
      await this.$store.dispatch('actionsProductTypeV2', data)
      var response = await this.$store.state.ModuleHompage.stateProductTypeV2
      if (response.result === 'SUCCESS') {
        // console.log('response', response)
        // this.selectProduct = this.selectProduct.filter(product =>
        //   this.itemProduct.find(item => item.seller_shop_id === product.seller_shop_id)
        // )
        // console.log('เข้า')
        this.dataProduct = response.data.list_product
        // this.totalItems = response.data.total_all
        // console.log('this.selectProduct', this.selectProduct)
        // if (this.groupNameEtax === true) {
        //   this.selectProduct = this.selectProduct.filter(product => product.etax === 'Y')
        // } else if (this.groupNameOTOP === true) {
        //   this.selectProduct = this.selectProduct.filter(product => product.otop === 'Y')
        // }
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          html: '<h3>ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
      this.$store.commit('closeLoader')
    },
    async openDialogAddProduct () {
      this.dialogAddProduct = true
      await this.getProduct()
      this.selectProduct = []
    },
    closeDialogAddProduct () {
      this.dialogAddProduct = false
      this.selectProduct = []
    },
    handleType (value) {
      this.productStatus = value
    }
  }
}
</script>
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(3) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          // background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(3) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
