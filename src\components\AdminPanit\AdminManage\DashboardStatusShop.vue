<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">ร้านค้าเปิด-ปิดในระบบ</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>ร้านค้าเปิด-ปิดในระบบ</v-card-title>
      <!-- box dashcoard -->
      <v-row v-if="MobileSize">
        <!-- ร้านค้าทั้งหมด -->
        <v-col cols="12">
            <v-card class="cardBox" width="100%" outlined style="background-color: #2bc08f; border-radius: 1vw">
            <v-col cols="12">
                <v-row no-gutters class="format_box">
                  <p style="color: #fff; font-size: 22px; font-weight: 600;">ร้านค้าทั้งหมด</p>
                  <!-- <p class="boxData_mobile">18ล้าน</p> -->
                  <p class="boxData_mobile">{{ showStatusShop.shop_all }}</p>
                </v-row>
            </v-col>
            </v-card>
        </v-col>
        <!-- ร้านค้าที่เปิดในระบบ -->
        <v-col cols="12">
            <v-card class="cardBox" width="100%" outlined style="background-color: #fdcf71; border-radius: 1vw">
            <v-col cols="12">
                <p class="format_box" style="color: #fff; font-size: 22px; font-weight: 600;">ร้านค้าที่เปิดในระบบ</p>
                <v-row no-gutters>
                  <v-col cols="6" >
                    <p class="topicShop_mobile"><span style="font-size: 16px">ร้านค้า JV</span><span style="font-size: 24px">{{ showStatusShop.shop_jv_active }}</span></p>
                  </v-col>
                  <v-col cols="6">
                    <p class="topicShop_mobile"><span style="font-size: 16px">ร้านค้าทั่วไป</span><span style="font-size: 24px">{{ showStatusShop.shop_general_active }}</span></p>
                  </v-col>
                </v-row>
            </v-col>
            </v-card>
        </v-col>
        <!-- ร้านค้าที่ปิดในระบบ -->
        <v-col cols="12">
            <v-card class="cardBox" width="100%" outlined style="background-color: #ef947b; border-radius: 1vw">
            <v-col cols="12">
                <p class="format_box" style="color: #fff; font-size: 22px; font-weight: 600;">ร้านค้าที่ปิดในระบบ</p>
                <v-row no-gutters>
                  <v-col >
                    <p class="topicShop_mobile"><span style="font-size: 16px">ร้านค้า JV</span><span style="font-size: 24px">{{showStatusShop.shop_jv_inactive}}</span></p>
                  </v-col>
                  <v-col >
                    <p class="topicShop_mobile"><span style="font-size: 16px">ร้านค้าทั่วไป</span><span style="font-size: 24px">{{ showStatusShop.shop_general_inactive }}</span></p>
                  </v-col>
                </v-row>
            </v-col>
            </v-card>
        </v-col>
      </v-row>
      <v-row v-else-if="IpadProSize">
        <!-- ร้านค้าทั้งหมด -->
        <v-col cols="4">
            <v-card class="cardBox" width="100%" outlined style="background-color: #2bc08f; border-radius: 1vw">
            <v-col cols="12">
                <v-row no-gutters class="format_box">
                  <p style="color: #fff; font-size: 24px; font-weight: 600;">ร้านค้าทั้งหมด</p>
                  <p class="boxData">{{ showStatusShop.shop_all }}</p>
                </v-row>
            </v-col>
            </v-card>
        </v-col>
        <!-- ร้านค้าที่เปิดในระบบ -->
        <v-col cols="4">
            <v-card class="cardBox" width="100%" outlined style="background-color: #fdcf71; border-radius: 1vw">
            <v-col cols="12">
                <p class="format_box" style="color: #fff; font-size: 24px; font-weight: 600;">ร้านค้าที่เปิดในระบบ</p>
                <v-row no-gutters>
                  <v-col >
                    <p class="topicShop"><span style="font-size: 14px">ร้านค้า JV</span><span style="font-size: 24px">{{ showStatusShop.shop_jv_active }}</span></p>
                  </v-col>
                  <v-col >
                    <p class="topicShop"><span style="font-size: 14px">ร้านค้าทั่วไป</span><span style="font-size: 24px">{{ showStatusShop.shop_general_active }}</span></p>
                  </v-col>
                </v-row>
            </v-col>
            </v-card>
        </v-col>
        <!-- ร้านค้าที่ปิดในระบบ -->
        <v-col cols="4">
            <v-card class="cardBox" width="100%" outlined style="background-color: #ef947b; border-radius: 1vw">
            <v-col cols="12">
                <p class="format_box" style="color: #fff; font-size: 24px; font-weight: 600;">ร้านค้าที่ปิดในระบบ</p>
                <v-row no-gutters>
                  <v-col >
                    <p class="topicShop"><span style="font-size: 14px">ร้านค้า JV</span><span style="font-size: 24px">{{ showStatusShop.shop_jv_inactive }}</span></p>
                  </v-col>
                  <v-col >
                    <p class="topicShop"><span style="font-size: 14px">ร้านค้าทั่วไป</span><span style="font-size: 24px">{{ showStatusShop.shop_general_inactive }}</span></p>
                  </v-col>
                </v-row>
            </v-col>
            </v-card>
        </v-col>
      </v-row>
      <v-row v-else-if="IpadSize">
        <!-- ร้านค้าทั้งหมด -->
        <v-col cols="4">
            <v-card class="cardBox" width="100%" outlined style="background-color: #2bc08f; border-radius: 1vw">
            <v-col cols="12">
                <v-row no-gutters class="format_box">
                  <p style="color: #fff; font-size: 14px; font-weight: 600;">ร้านค้าทั้งหมด</p>
                  <p class="dataIP">{{ showStatusShop.shop_all }}</p>
                </v-row>
            </v-col>
            </v-card>
        </v-col>
        <!-- ร้านค้าที่เปิดในระบบ -->
        <v-col cols="4">
            <v-card class="cardBox" width="100%" outlined style="background-color: #fdcf71; border-radius: 1vw">
            <v-col cols="12">
                <p class="format_box" style="color: #fff; font-size: 14px; font-weight: 600;">ร้านค้าที่เปิดในระบบ</p>
                <v-row no-gutters>
                  <v-col >
                    <p class="topicShop_IP"><span style="font-size: 8px">ร้านค้า JV</span><span style="font-size: 24px">{{ showStatusShop.shop_jv_active }}</span></p>
                  </v-col>
                  <v-col >
                    <p class="topicShop_IP"><span style="font-size: 8px">ร้านค้าทั่วไป</span><span style="font-size: 24px">{{ showStatusShop.shop_general_active }}</span></p>
                  </v-col>
                </v-row>
            </v-col>
            </v-card>
        </v-col>
        <!-- ร้านค้าที่ปิดในระบบ -->
        <v-col cols="4">
            <v-card class="cardBox" width="100%" outlined style="background-color: #ef947b; border-radius: 1vw">
            <v-col cols="12">
                <p class="format_box" style="color: #fff; font-size: 14px; font-weight: 600;">ร้านค้าที่ปิดในระบบ</p>
                <v-row no-gutters>
                  <v-col >
                    <p class="topicShop_IP"><span style="font-size: 8px">ร้านค้า JV</span><span style="font-size: 24px">{{ showStatusShop.shop_jv_inactive }}</span></p>
                  </v-col>
                  <v-col >
                    <p class="topicShop_IP"><span style="font-size: 8px">ร้านค้าทั่วไป</span><span style="font-size: 24px">{{ showStatusShop.shop_general_inactive }}</span></p>
                  </v-col>
                </v-row>
            </v-col>
            </v-card>
        </v-col>
      </v-row>
      <v-row v-else>
        <!-- ร้านค้าทั้งหมด -->
        <v-col cols="4">
            <v-card class="cardBox" width="100%" outlined style="background-color: #2bc08f; border-radius: 1vw">
            <v-col cols="12">
                <v-row no-gutters class="format_box">
                  <p style="color: #fff; font-size: 24px; font-weight: 600;">ร้านค้าทั้งหมด</p>
                  <p class="boxData">{{ showStatusShop.shop_all }}</p>
                </v-row>
            </v-col>
            </v-card>
        </v-col>
        <!-- ร้านค้าที่เปิดในระบบ -->
        <v-col cols="4">
            <v-card class="cardBox" width="100%" outlined style="background-color: #fdcf71; border-radius: 1vw">
            <v-col cols="12">
                <p class="format_box" style="color: #fff; font-size: 24px; font-weight: 600;">ร้านค้าที่เปิดในระบบ</p>
                <v-row no-gutters style="display: flex; justify-content: center; gap: 1vw;">
                    <p class="topicShop"><span style="font-size: 18px">ร้านค้า JV</span><span style="font-size: 36px">{{ showStatusShop.shop_jv_active }}</span></p>
                    <p class="topicShop"><span style="font-size: 18px">ร้านค้าทั่วไป</span><span style="font-size: 36px">{{ showStatusShop.shop_general_active }}</span></p>
                </v-row>
            </v-col>
            </v-card>
        </v-col>
        <!-- ร้านค้าที่ปิดในระบบ -->
        <v-col cols="4">
            <v-card class="cardBox" width="100%" outlined style="background-color: #ef947b; border-radius: 1vw">
            <v-col cols="12">
                <p class="format_box" style="color: #fff; font-size: 24px; font-weight: 600;">ร้านค้าที่ปิดในระบบ</p>
                <v-row no-gutters style="display: flex; justify-content: center; gap: 1vw;">
                    <p class="topicShop"><span style="font-size: 18px">ร้านค้า JV</span><span style="font-size: 36px">{{ showStatusShop.shop_jv_inactive }}</span></p>
                    <p class="topicShop"><span style="font-size: 18px">ร้านค้าทั่วไป</span><span style="font-size: 36px">{{ showStatusShop.shop_general_inactive }}</span></p>
                </v-row>
            </v-col>
            </v-card>
        </v-col>
      </v-row>
      <!-- tab change table -->
      <div style="padding-top: 20px; padding-bottom: 20px;">
          <v-row align="center">
            <v-col cols="auto">
              <v-tabs v-model="activeTab" :show-arrows="MobileSize">
                <v-tab :key="0" @click="showLoader()">
                  <span>ร้านค้าทั้งหมด</span>
                </v-tab>
                <v-tab :key="1" @click="showLoader()">
                  <span>ร้านค้าที่เปิดในระบบ</span>
                </v-tab>
                <v-tab :key="2" @click="showLoader()">
                  <span>ร้านค้าที่ปิดในระบบ</span>
                </v-tab>
              </v-tabs>
            </v-col>
          </v-row>
        </div>
      <!-- table shop all -->
      <v-row>
        <v-col cols="12" v-if="activeTab === 0">
          <v-card-title style="font-weight: 700; color: #38b2a4;">ร้านค้าทั้งหมดในระบบ</v-card-title>
          <v-col cols="12" md="6" sm="12">
            <v-text-field v-model="search" placeholder="ค้นหาร้านค้าในระบบ" outlined rounded dense hide-details>
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <!-- btn export excel -->
          <v-col cols="12" style="display: flex;" class="pa-0 flex-wrap">
            <v-col :cols="MobileSize || IpadSize ? 6 : 3" style="display: grid;">
              <v-btn style="border-radius: 20px; background: #27AB9C; padding: 1vw; font-size: small;" @click="getExcel('seller all')">
                <v-icon style="color: #fff">mdi-microsoft-excel</v-icon><br>
                <span style="color: #fff">{{ MobileSize || IpadSize || IpadProSize ? 'ร้านค้าทั้งหมด' : 'Export ร้านค้าทั้งหมด' }}</span>
              </v-btn>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 6 : 3" style="display: grid;">
              <v-btn style="border-radius: 20px; background: #27AB9C; padding: 1vw; font-size: small;" @click="getExcel('seller user')">
                <v-icon style="color: #fff">mdi-microsoft-excel</v-icon><br>
                <span style="color: #fff">{{ MobileSize || IpadSize || IpadProSize ? 'ผู้ใช้ร้านค้า' : 'Export ผู้ใช้ร้านค้า' }}</span>
              </v-btn>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 6 : 3" style="display: grid;">
              <v-btn style="border-radius: 20px; background: #27AB9C; padding: 1vw; font-size: small;" @click="getExcel('seller active')">
                <v-icon style="color: #fff">mdi-microsoft-excel</v-icon><br>
                <span style="color: #fff">{{ MobileSize || IpadSize || IpadProSize ? 'ร้านค้าที่เปิด': 'Export ร้านค้าที่เปิด' }}</span>
              </v-btn>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 6 : 3" style="display: grid;">
              <v-btn style="border-radius: 20px; background: #27AB9C; padding: 1vw; font-size: small;" @click="getExcel('seller inactive')">
                <v-icon style="color: #fff">mdi-microsoft-excel</v-icon><br>
                <span style="color: #fff">{{ MobileSize || IpadSize || IpadProSize ? 'ร้านค้าที่ปิด' : 'Export ร้านค้าที่ปิด' }}</span>
              </v-btn>
            </v-col>
          </v-col>
          <v-col cols="12">
            <v-data-table
            :headers="headers"
            :search="search"
            :items="showStatusShop.shop_list"
            style="width:100%; text-align: center; white-space: nowrap;"
            height="100%"
            @pagination="countRequest"
            no-results-text="ไม่พบรายการผู้ใช้งานระบบ"
            no-data-text="ไม่พบรายการผู้ใช้งานระบบ"
            :items-per-page="10"
            class="elevation-1 mt-4"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            >
              <template v-slot:[`item.shop_name`]="{ item }">
                <span v-if="item.shop_name" color="#27AB9C" > {{ item.shop_name}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.name_en`]="{ item }">
                  <div v-if="MobileSize">
                    <div v-if="item.name_en" color="#27AB9C">
                      <v-tooltip top>
                        <template v-slot:activator="{ on, attrs }">
                          <span v-bind="attrs" v-on="on"> {{ substring(item.name_en) }}</span>
                        </template>
                        <span>{{ item.name_en }}</span>
                      </v-tooltip>
                    </div>
                    <span v-else>-</span>
                  </div>
                  <div v-else>
                    <span v-if="item.name_en" color="#27AB9C" > {{ item.name_en }}</span>
                    <span v-else>-</span>
                  </div>
              </template>
              <template v-slot:[`item.bank_code`]="{ item }">
                <span v-if="item.bank_code" color="#27AB9C" > {{ item.bank_code}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.account_no`]="{ item }">
                <span v-if="item.account_no" color="#27AB9C" > {{ item.account_no}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.account_bank`]="{ item }">
                <span v-if="item.account_bank" color="#27AB9C" > {{ item.account_bank}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.merchant_key`]="{ item }">
                  <div v-if="MobileSize">
                    <div v-if="item.merchant_key" color="#27AB9C">
                      <v-tooltip top>
                        <template v-slot:activator="{ on, attrs }">
                          <span v-bind="attrs" v-on="on"> {{ substring(item.merchant_key) }}</span>
                        </template>
                        <span>{{ item.merchant_key }}</span>
                      </v-tooltip>
                    </div>
                    <span v-else>-</span>
                  </div>
                  <div v-else>
                    <span v-if="item.merchant_key" color="#27AB9C" > {{ item.merchant_key }}</span>
                    <span v-else>-</span>
                  </div>
              </template>
              <template v-slot:[`item.first_name`]="{ item }">
                <span v-if="item.first_name" color="#27AB9C" > {{ item.first_name}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.last_name`]="{ item }">
                <span v-if="item.last_name" color="#27AB9C" > {{ item.last_name}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.shop_description`]="{ item }">
                <v-btn v-if="item.shop_description" color="#27AB9C" outlined @click="OpenDialogShowData(item.shop_description)">ดูคำอธิบายร้านค้า</v-btn>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.use_ewht`]="{ item }">
                <span v-if="item.use_ewht" color="#27AB9C" > {{ item.use_ewht}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.use_attorney`]="{ item }">
                <span v-if="item.use_attorney" color="#27AB9C" > {{ item.use_attorney}}</span>
                <span v-else>-</span>
              </template>
            </v-data-table>
          </v-col>
        </v-col>
      </v-row>
      <!-- table shop active -->
      <v-row>
        <v-col cols="12" v-if="activeTab === 1">
          <v-card-title style="font-weight: 700; color: #38b2a4;">ร้านค้าที่เปิดในระบบ</v-card-title>
          <v-col cols="12" md="6" sm="12">
            <v-text-field v-model="search" placeholder="ค้นหาร้านค้าในระบบ" outlined rounded dense hide-details>
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <!-- btn export excel -->
          <v-col cols="12" style="display: flex;" class="pa-0 flex-wrap">
            <v-col :cols="MobileSize || IpadSize ? 6 : 3" style="display: grid;">
              <v-btn style="border-radius: 20px; background: #27AB9C; padding: 1vw; font-size: small;" @click="getExcel('seller all')">
                <v-icon style="color: #fff">mdi-microsoft-excel</v-icon><br>
                <span style="color: #fff">{{ MobileSize || IpadSize || IpadProSize ? 'ร้านค้าทั้งหมด' : 'Export ร้านค้าทั้งหมด' }}</span>
              </v-btn>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 6 : 3" style="display: grid;">
              <v-btn style="border-radius: 20px; background: #27AB9C; padding: 1vw; font-size: small;" @click="getExcel('seller user')">
                <v-icon style="color: #fff">mdi-microsoft-excel</v-icon><br>
                <span style="color: #fff">{{ MobileSize || IpadSize || IpadProSize ? 'ผู้ใช้ร้านค้า' : 'Export ผู้ใช้ร้านค้า' }}</span>
              </v-btn>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 6 : 3" style="display: grid;">
              <v-btn style="border-radius: 20px; background: #27AB9C; padding: 1vw; font-size: small;" @click="getExcel('seller active')">
                <v-icon style="color: #fff">mdi-microsoft-excel</v-icon><br>
                <span style="color: #fff">{{ MobileSize || IpadSize || IpadProSize ? 'ร้านค้าที่เปิด': 'Export ร้านค้าที่เปิด' }}</span>
              </v-btn>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 6 : 3" style="display: grid;">
              <v-btn style="border-radius: 20px; background: #27AB9C; padding: 1vw; font-size: small;" @click="getExcel('seller inactive')">
                <v-icon style="color: #fff">mdi-microsoft-excel</v-icon><br>
                <span style="color: #fff">{{ MobileSize || IpadSize || IpadProSize ? 'ร้านค้าที่ปิด' : 'Export ร้านค้าที่ปิด' }}</span>
              </v-btn>
            </v-col>
          </v-col>
          <v-col cols="12">
            <v-data-table
            :headers="headers"
            :search="search"
            :items="activeShops"
            style="width:100%; text-align: center; white-space: nowrap;"
            height="100%"
            @pagination="countRequest"
            no-results-text="ไม่พบรายการผู้ใช้งานระบบ"
            no-data-text="ไม่พบรายการผู้ใช้งานระบบ"
            :items-per-page="10"
            class="elevation-1 mt-4"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            >
              <template v-slot:[`item.shop_name`]="{ item }">
                <span v-if="item.shop_name" color="#27AB9C" > {{ item.shop_name}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.name_en`]="{ item }">
                  <div v-if="MobileSize">
                    <div v-if="item.name_en" color="#27AB9C">
                      <v-tooltip top>
                        <template v-slot:activator="{ on, attrs }">
                          <span v-bind="attrs" v-on="on"> {{ substring(item.name_en) }}</span>
                        </template>
                        <span>{{ item.name_en }}</span>
                      </v-tooltip>
                    </div>
                    <span v-else>-</span>
                  </div>
                  <div v-else>
                    <span v-if="item.name_en" color="#27AB9C" > {{ item.name_en }}</span>
                    <span v-else>-</span>
                  </div>
              </template>
              <template v-slot:[`item.bank_code`]="{ item }">
                <span v-if="item.bank_code" color="#27AB9C" > {{ item.bank_code}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.account_no`]="{ item }">
                <span v-if="item.account_no" color="#27AB9C" > {{ item.account_no}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.account_bank`]="{ item }">
                <span v-if="item.account_bank" color="#27AB9C" > {{ item.account_bank}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.merchant_key`]="{ item }">
                  <div v-if="MobileSize">
                    <div v-if="item.merchant_key" color="#27AB9C">
                      <v-tooltip top>
                        <template v-slot:activator="{ on, attrs }">
                          <span v-bind="attrs" v-on="on"> {{ substring(item.merchant_key) }}</span>
                        </template>
                        <span>{{ item.merchant_key }}</span>
                      </v-tooltip>
                    </div>
                    <span v-else>-</span>
                  </div>
                  <div v-else>
                    <span v-if="item.merchant_key" color="#27AB9C" > {{ item.merchant_key }}</span>
                    <span v-else>-</span>
                  </div>
              </template>
              <template v-slot:[`item.first_name`]="{ item }">
                <span v-if="item.first_name" color="#27AB9C" > {{ item.first_name}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.last_name`]="{ item }">
                <span v-if="item.last_name" color="#27AB9C" > {{ item.last_name}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.shop_description`]="{ item }">
                <v-btn v-if="item.shop_description" color="#27AB9C" outlined @click="OpenDialogShowData(item.shop_description)">ดูคำอธิบายร้านค้า</v-btn>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.use_ewht`]="{ item }">
                <span v-if="item.use_ewht" color="#27AB9C" > {{ item.use_ewht}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.use_attorney`]="{ item }">
                <span v-if="item.use_attorney" color="#27AB9C" > {{ item.use_attorney}}</span>
                <span v-else>-</span>
              </template>
            </v-data-table>
          </v-col>
        </v-col>
      </v-row>
      <!-- table shop inactive -->
      <v-row>
        <v-col cols="12" v-if="activeTab === 2">
          <v-card-title style="font-weight: 700; color: #38b2a4;">ร้านค้าที่ปิดในระบบ</v-card-title>
          <v-col cols="12" md="6" sm="12">
            <v-text-field v-model="search" placeholder="ค้นหาร้านค้าในระบบ" outlined rounded dense hide-details>
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <!-- btn export excel -->
          <v-col cols="12" style="display: flex;" class="pa-0 flex-wrap">
            <v-col :cols="MobileSize || IpadSize ? 6 : 3" style="display: grid;">
              <v-btn style="border-radius: 20px; background: #27AB9C; padding: 1vw; font-size: small;" @click="getExcel('seller all')">
                <v-icon style="color: #fff">mdi-microsoft-excel</v-icon><br>
                <span style="color: #fff">{{ MobileSize || IpadSize || IpadProSize ? 'ร้านค้าทั้งหมด' : 'Export ร้านค้าทั้งหมด' }}</span>
              </v-btn>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 6 : 3" style="display: grid;">
              <v-btn style="border-radius: 20px; background: #27AB9C; padding: 1vw; font-size: small;" @click="getExcel('seller user')">
                <v-icon style="color: #fff">mdi-microsoft-excel</v-icon><br>
                <span style="color: #fff">{{ MobileSize || IpadSize || IpadProSize ? 'ผู้ใช้ร้านค้า' : 'Export ผู้ใช้ร้านค้า' }}</span>
              </v-btn>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 6 : 3" style="display: grid;">
              <v-btn style="border-radius: 20px; background: #27AB9C; padding: 1vw; font-size: small;" @click="getExcel('seller active')">
                <v-icon style="color: #fff">mdi-microsoft-excel</v-icon><br>
                <span style="color: #fff">{{ MobileSize || IpadSize || IpadProSize ? 'ร้านค้าที่เปิด': 'Export ร้านค้าที่เปิด' }}</span>
              </v-btn>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 6 : 3" style="display: grid;">
              <v-btn style="border-radius: 20px; background: #27AB9C; padding: 1vw; font-size: small;" @click="getExcel('seller inactive')">
                <v-icon style="color: #fff">mdi-microsoft-excel</v-icon><br>
                <span style="color: #fff">{{ MobileSize || IpadSize || IpadProSize ? 'ร้านค้าที่ปิด' : 'Export ร้านค้าที่ปิด' }}</span>
              </v-btn>
            </v-col>
          </v-col>
          <v-col cols="12">
            <v-data-table
            :headers="headers"
            :search="search"
            :items="inactiveShops"
            style="width:100%; text-align: center; white-space: nowrap;"
            height="100%"
            @pagination="countRequest"
            no-results-text="ไม่พบรายการผู้ใช้งานระบบ"
            no-data-text="ไม่พบรายการผู้ใช้งานระบบ"
            :items-per-page="10"
            class="elevation-1 mt-4"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            >
              <template v-slot:[`item.shop_name`]="{ item }">
                <span v-if="item.shop_name" color="#27AB9C" > {{ item.shop_name}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.name_en`]="{ item }">
                  <div v-if="MobileSize">
                    <div v-if="item.name_en" color="#27AB9C">
                      <v-tooltip top>
                        <template v-slot:activator="{ on, attrs }">
                          <span v-bind="attrs" v-on="on"> {{ substring(item.name_en) }}</span>
                        </template>
                        <span>{{ item.name_en }}</span>
                      </v-tooltip>
                    </div>
                    <span v-else>-</span>
                  </div>
                  <div v-else>
                    <span v-if="item.name_en" color="#27AB9C" > {{ item.name_en }}</span>
                    <span v-else>-</span>
                  </div>
              </template>
              <template v-slot:[`item.bank_code`]="{ item }">
                <span v-if="item.bank_code" color="#27AB9C" > {{ item.bank_code}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.account_no`]="{ item }">
                <span v-if="item.account_no" color="#27AB9C" > {{ item.account_no}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.account_bank`]="{ item }">
                <span v-if="item.account_bank" color="#27AB9C" > {{ item.account_bank}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.merchant_key`]="{ item }">
                  <div v-if="MobileSize">
                    <div v-if="item.merchant_key" color="#27AB9C">
                      <v-tooltip top>
                        <template v-slot:activator="{ on, attrs }">
                          <span v-bind="attrs" v-on="on"> {{ substring(item.merchant_key) }}</span>
                        </template>
                        <span>{{ item.merchant_key }}</span>
                      </v-tooltip>
                    </div>
                    <span v-else>-</span>
                  </div>
                  <div v-else>
                    <span v-if="item.merchant_key" color="#27AB9C" > {{ item.merchant_key }}</span>
                    <span v-else>-</span>
                  </div>
              </template>
              <template v-slot:[`item.first_name`]="{ item }">
                <span v-if="item.first_name" color="#27AB9C" > {{ item.first_name}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.last_name`]="{ item }">
                <span v-if="item.last_name" color="#27AB9C" > {{ item.last_name}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.shop_description`]="{ item }">
                <v-btn v-if="item.shop_description" color="#27AB9C" outlined @click="OpenDialogShowData(item.shop_description)">ดูคำอธิบายร้านค้า</v-btn>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.use_ewht`]="{ item }">
                <span v-if="item.use_ewht" color="#27AB9C" > {{ item.use_ewht}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.use_attorney`]="{ item }">
                <span v-if="item.use_attorney" color="#27AB9C" > {{ item.use_attorney}}</span>
                <span v-else>-</span>
              </template>
            </v-data-table>
          </v-col>
        </v-col>
      </v-row>
      <!-- dialog show description -->
      <v-dialog v-model="modalShowData" :width="MobileSize ? '100%' : '50%'" persistent>
        <v-card>
            <v-card-title class="text-h5 grey lighten-2">
              <span>คำอธิบายของร้านค้า</span>
            </v-card-title>

            <v-card-text class="pt-4">
              <pre style="white-space: wrap" v-html="dataToShow"></pre>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                color="primary"
                text
                @click="modalShowData = false"
              >
                ปิด
              </v-btn>
            </v-card-actions>
        </v-card>
      </v-dialog>
  </v-card>
</v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      showStatusShop: {},
      search: '',
      modalShowData: false,
      dataHeader: '',
      dataToShow: '',
      activeTab: null,
      showCountRequest: false,
      headers: [
        { text: 'shop_name', value: 'shop_name', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'shop_status', value: 'shop_status', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'public_show', value: 'public_show', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'partner_show', value: 'partner_show', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'store_front', value: 'store_front', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'name_th', value: 'name_th', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'name_en', value: 'name_en', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'bank_code', value: 'bank_code', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'account_no', value: 'account_no', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'account_bank', value: 'account_bank', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'merchant_key', value: 'merchant_key', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'first_name', value: 'first_name', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'last_name', value: 'last_name', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'shop_description', value: 'shop_description', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'index', value: 'index', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'use_ewht', value: 'use_ewht', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'use_attorney', value: 'use_attorney', sortable: false, class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    // condition item active in data table
    activeShops () {
      return this.showStatusShop.shop_list.filter(item => item.shop_status === 'active')
    },
    // condition inactive in item data table
    inactiveShops () {
      return this.showStatusShop.shop_list.filter(item => item.shop_status === 'inactive')
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardStatusShopMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'dashboardStatusShop')
        this.$router.push({ path: '/dashboardStatusShop' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    if (localStorage.getItem('oneData') !== null) {
      this.getStatusShop()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    // func show data status shop
    async getStatusShop () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('ActionsGetStatusShop')
      var response = this.$store.state.ModuleManageShop.GetStatusShop
      if (response.data.code === 200) {
        this.$store.commit('closeLoader')
        this.showStatusShop = response.data.data
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          html: '<h3>ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่</h3>'
        })
        this.showStatusShop = {}
      }
    },
    showLoader () {
      this.$store.commit('openLoader')
      setTimeout(() => {
        this.$store.commit('closeLoader')
      }, 1000)
    },
    OpenDialogShowData (data) {
      this.dataToShow = data
      this.modalShowData = true
    },
    // cut string
    substring (data) {
      return data.length > 20 ? data.substring(0, 20) + '...' : data
    },
    // export excel
    async getExcel (val) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // excel seller all
      if (val === 'seller all') {
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}seller/export/seller-all`,
          data: this.exportExcelBody,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'GET',
          responseType: 'blob'
        }).then((response) => {
          const fileURL = window.URL.createObjectURL(new Blob([response.data]))
          const fileLink = document.createElement('a')
          fileLink.href = fileURL
          fileLink.setAttribute('download', 'seller all.xlsx')
          // fileLink.setAttribute('download', 'report.xlsx')
          document.body.appendChild(fileLink)
          fileLink.click()
        })
      }
      // excel seller user
      if (val === 'seller user') {
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}seller/export/seller-user`,
          data: this.exportExcelBody,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'GET',
          responseType: 'blob'
        }).then((response) => {
          const fileURL = window.URL.createObjectURL(new Blob([response.data]))
          const fileLink = document.createElement('a')
          fileLink.href = fileURL
          fileLink.setAttribute('download', 'seller user.xlsx')
          // fileLink.setAttribute('download', 'report.xlsx')
          document.body.appendChild(fileLink)
          fileLink.click()
        })
      }
      // excel seller active
      if (val === 'seller active') {
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}seller/export/seller-active`,
          data: this.exportExcelBody,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'GET',
          responseType: 'blob'
        }).then((response) => {
          const fileURL = window.URL.createObjectURL(new Blob([response.data]))
          const fileLink = document.createElement('a')
          fileLink.href = fileURL
          fileLink.setAttribute('download', 'seller active.xlsx')
          // fileLink.setAttribute('download', 'report.xlsx')
          document.body.appendChild(fileLink)
          fileLink.click()
        })
      }
      // excel seller inactive
      if (val === 'seller inactive') {
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}exports/shop_inactive`,
          data: this.exportExcelBody,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'POST',
          responseType: 'blob'
        }).then((response) => {
          const fileURL = window.URL.createObjectURL(new Blob([response.data]))
          const fileLink = document.createElement('a')
          fileLink.href = fileURL
          fileLink.setAttribute('download', 'seller inactive.xlsx')
          // fileLink.setAttribute('download', 'report.xlsx')
          document.body.appendChild(fileLink)
          fileLink.click()
        })
      }
    }
  }
}
</script>

<style scoped>
  .format_box {
    display: flex;
    flex-direction: column;
    text-align: center;
  }
  .colBox .card{
    border-radius: 10px !important;
    background-color: #f5f5f5;
  }
  .cardBox {
    max-width: none !important;
    display: flex;
    align-items: center;
    /* height: 24vw; */
    border-radius: 1vw;
  }
  .theme--light.v-data-table > .v-data-table__wrapper > table > thead > tr:last-child > th {
    white-space: nowrap !important;
    text-align: center !important;
  }
  .v-data-table > .v-data-table__wrapper > table > tbody > tr > td {
    text-align: center !important;
  }
  .boxData {
    color: #535353;
    font-size: 65px;
    font-weight: 600;
    background-color: #fff;
    border-radius: .5vw;
    padding: 1vw;
    width: 19vw;
    margin: auto;
    height: 9vw;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  .dataIP {
    color: #535353;
    font-size: 38px;
    font-weight: 600;
    background-color: #fff;
    border-radius: .5vw;
    padding: 1vw;
    width: 15vw;
    margin: auto;
    height: 9vw;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  .boxData_mobile {
    color: #535353;
    font-size: 45px;
    font-weight: 600;
    background-color: #fff;
    border-radius: 1.5vw;
    padding: 3vw;
    width: 45vw;
    margin: auto;
    /* height: 9vw; */
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  .topicShop {
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    font-weight: 700;
    background-color: #fff;
    color: #535353;
    padding: 1vw;
    border-radius: .5vw;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    width: 9vw;
    height: 9vw;
  }
  .topicShop_IP {
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    font-weight: 700;
    background-color: #fff;
    color: #535353;
    padding: 1vw;
    border-radius: .5vw;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    width: 7vw;
    height: 9vw;
  }
  .topicShop_mobile {
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    font-weight: 700;
    background-color: #fff;
    color: #535353;
    padding: 1vw;
    border-radius: 1.5vw;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    /* width: 9vw; */
    height: 21vw;
    margin-left: 1.5vw;
  }
</style>
