<template>
  <div>
    <!-- Mobile ipad-->
    <v-container v-if="MobileSize || IpadSize" :class="IpadSize ? 'px-0 py-0' : MobileSize ? '' : ''" grid-list-xl>
      <v-card outlined v-for="(order, index) in items.data_list" :key="index">
        <!-- <v-card-title><v-icon color="#27AB9C" class="mr-2" @click="backtoPOBuyer()">mdi-chevron-left</v-icon> รายการสั่งซื้อของฉัน</v-card-title> -->
        <v-row justify="start" class="my-4">
          <v-col cols="12" md="6" sm="12" class="d-flex justify-space-between align-center">
            <span style="font-weight: 700; font-size: 16px;">
              <v-icon color="#27AB9C" class="mr-2" @click="backtoPOBuyer()">mdi-chevron-left</v-icon>{{$t('DetailOrderBuyer.OrderDetails')}}
            </span>
            <v-btn v-if="(trackingStatus === 'Not Paid' || trackingStatus === 'Pick Up') && items.tracking[0].status_payment === 'Not Paid' && statusCancel === '' && items.is_lotus === 'N'" @click="modalInputReason = true" small rounded outlined color="red" class="mr-4">{{$t('DetailOrderBuyer.CancelOrder')}}</v-btn>
            <v-btn v-if="(trackingStatus === 'Not Sent' || (trackingStatus === 'Pick Up'&& items.transportation_status !== 'การจัดส่งสำเร็จ')) && items.tracking[0].status_payment === 'Success' && (statusCancel === '' || statusCancel === 'reject') && items.order_mobilyst_no === '-' && items.is_lotus === 'N' && items.transaction_status !== 'Waiting_Cancel'" @click="getAccountBank()" rounded outlined color="red" class="mr-4">{{$t('DetailOrderBuyer.CancelOrder')}}</v-btn>
          </v-col>
          <!-- <v-col class="d-flex justify-center" style="gap: 1vw;" v-if="items.transportation_status === 'อยู่ระหว่างการตรวจสอบและยอมรับสินค้า'">
            <div>
              <v-btn color="#27AB9C" style="color: #fff;">ได้รับสินค้าแล้ว</v-btn>
            </div>
            <div>
              <v-btn color="#cdcdcd" @click="gotoRefund()">คืนเงิน/คืนสินค้า</v-btn>
            </div>
          </v-col> -->
          <v-col cols="6" md="12" class="pa-2 pr-6" align="end" v-if="((trackingStatus === 'Not Sent' && items.transportation_status !== 'อยู่ระหว่างดำเนินการ') || trackingStatus === 'Cancel by buyer' || (trackingStatus === 'Pick Up' && items.transportation_status !== 'การจัดส่งสำเร็จ')) && (items.tracking[0].status_payment === 'Success' || items.tracking[0].status_payment === 'Not Paid' || items.tracking[0].status_payment === 'Cancel') && (statusCancel === '' || statusCancel !== '') && items.order_mobilyst_no === '-' && items.is_lotus === 'N' && items.transaction_status !== 'Waiting_Cancel'">
            <!-- <v-btn v-if="(trackingStatus === 'Not Paid' || trackingStatus === 'Pick Up') && items.tracking[0].status_payment === 'Not Paid' && statusCancel === '' && items.is_lotus === 'N'" @click="modalInputReason = true" small rounded outlined color="red" class="mr-4">ยกเลิกคำสั่งซื้อ</v-btn> -->
            <!-- <v-btn v-if="(trackingStatus === 'Not Sent' || trackingStatus === 'Pick Up') && items.tracking[0].status_payment === 'Success' && (statusCancel === '' || statusCancel === 'reject') && items.order_mobilyst_no === '-' && items.is_lotus === 'N'" @click="getAccountBank()" small rounded outlined color="red" class="mr-4">ยกเลิกคำสั่งซื้อ</v-btn> -->
            <v-btn v-if="(trackingStatus === 'Cancel by buyer' || trackingStatus === 'Not Sent' || (trackingStatus === 'Pick Up' && statusCancel === 'reject')) && (items.tracking[0].status_payment === 'Success' || items.tracking[0].status_payment === 'Cancel') && statusCancel !== '' && items.order_mobilyst_no === '-' && items.is_lotus === 'N'" @click="openmodalInputReasonDetail()" rounded small color="#27AB9C" outlined class="mr-4">{{$t('DetailOrderBuyer.TrackCancellationRequest')}}</v-btn>
          </v-col>
          <!-- <v-col cols="12" md="12" class="pa-2 pr-6" align="end" v-if="trackingStatus === 'Cancel by buyer' && items.shipping_type === 'online' && items.tracking[0].status_payment === 'Success' && detailCancel.status !== ''">
            <v-btn @click="modalInputReasonDetail = true" small rounded outlined color="#27AB9C" class="mr-4">ยกเลิกคำสั่งซื้อ</v-btn>
          </v-col> -->
          <!-- <v-col class="pa-0" cols="6" md="6" align="end" v-if="trackingStatus === 'Not Received' && items.shipping_type === 'online'">
            <v-btn v-if="trackingStatus === 'Not Received' && items.shipping_type === 'online'" @click="acceptProduct(order)" rounded outlined color="#27AB9C"><b class="buttonFontSize">ได้รับสินค้าแล้ว</b></v-btn>
            <v-btn v-if="dataRole.role === 'ext_buyer' && items.transaction_status === 'Success'" class="white--text px-5 ml-2" @click="addtoCart()" color="primary" rounded>
              <v-icon>mdi-shopping-outline</v-icon>สั่งซื้ออีกครั้ง
            </v-btn>
          </v-col> -->
          <v-col cols="12" md="12" class="pa-2 pr-6" align="end" v-if="trackingStatus === 'Not Received' && items.shipping_type === 'online' && statusTransactionText(items.transaction_status, items.transportation_status) !== 'กำลังจัดส่งสินค้า'">
            <v-btn v-if="trackingStatus === 'Not Received' && items.shipping_type === 'online'" @click="acceptProduct(order)" small rounded outlined color="#27AB9C"><b>{{$t('DetailOrderBuyer.ItemReceived')}}</b></v-btn>
            <v-btn v-if="dataRole.role === 'ext_buyer' && items.transaction_status === 'Success'" small class="ml-2" @click="addtoCart()" color="primary" rounded>
              <v-icon>mdi-shopping-outline</v-icon>{{$t('DetailOrderBuyer.OrderAgain')}}
            </v-btn>
          </v-col>
          <v-col :class="MobizeSize ? 'px-5' : 'pa-5'" cols="12" md="12" align="end" v-else-if="trackingStatus === 'Received' && items.shipping_type === 'online' && statusTransactionText(items.transaction_status, items.transportation_status) === 'จัดส่งสินค้าสำเร็จ'">
            <v-btn :block="MobileSize ? true : false" @click.stop="reviewProduct(items.payment_transaction)" rounded outlined color="#3EC6B6" :class="MobileSize ? '' : 'mr-4'"><v-icon small class="pr-1">mdi-star</v-icon>{{$t('DetailOrderBuyer.ProductReview')}}</v-btn>
            <v-btn :block="MobileSize ? true : false" v-if="dataRole.role === 'ext_buyer' && items.transaction_status === 'Success'" :class="MobileSize ? 'white--text px-5 mt-3' : 'white--text px-5 ml-2'" @click="addtoCart()" color="primary" rounded>
              <v-icon>mdi-shopping-outline</v-icon>{{$t('DetailOrderBuyer.OrderAgain')}}
            </v-btn>
          </v-col>
          <!-- อัปโหลด slip ของร้านค้า lotus -->
          <v-col cols="12" md="12" class="pa-2 pr-6" align="end" v-if="items.is_lotus === 'Y' && items.transaction_status === 'Pending'">
            <v-btn @click="OpenUploadSlip()" rounded outlined color="#27AB9C"><b class="buttonFontSize">{{$t('DetailOrderBuyer.UploadSlip')}}</b></v-btn>
          </v-col>
        </v-row>
        <v-row no-gutters class="pt-5 pb-0 pl-5">
          <v-col cols="6">
            <v-row no-gutters>
              <div class="d-flex">
                <v-row no-gutters class="pr-2">
                  <v-img class="sizeIMG" src="@/assets/ICON/headerDetailOrderBuyer.png"></v-img>
                </v-row>
                <span class="fontHeadDetail">{{$t('DetailOrderBuyer.OrderStatus')}}</span>
              </div>
            </v-row>
          </v-col>
          <v-col cols="12" md="12" :sm="IpadSize ? 12 : 6" :class="MobileSize || IpadSize ? 'd-flex align-center justify-start pr-3' : 'd-flex align-center justify-end pr-3'" v-if="items.transaction_status">
            <span :class="MobileSize || IpadSize ? 'pt-3' : ''">{{$t('DetailOrderBuyer.OrderNumber')}} : {{order.order_number}} |
              <v-chip :color="statusTransactionChipColor(items.transaction_status, items.transportation_status)">
                <span :style="`color: ${statusTransactionTextColor(items.transaction_status, items.transportation_status)}`">{{statusTransactionText(items.transaction_status, items.transportation_status)}}</span>
              </v-chip>
            </span>
          </v-col>
          <!-- v-if="trackingStatus === 'Not Received' && typeShipping !== 'front' && checkAcceptProduct[0].status === 'waiting_accept'" -->
        </v-row>
        <v-row no-gutters class="mx-2">
          <v-col cols="12" v-if="productNormal.length === 0 && productService.length !== 0 && trackingStatus !== 'Pick Up'">
            <v-row no-gutters v-if="trackingStatus === 'Not Paid'" class="mb-2">
              <v-col cols="12" align="center" class="pb-3">
                <v-img src="@/assets/stepperNew/service/step1.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <!-- <span class="captionSku">คำสั่งซื้อใหม่</span> -->
              </v-col>
              <v-col cols="3" align="center" class="pr-2">
                <span class="fontActive captionSku"><b>{{$t('DetailOrderBuyer.NewOrder')}}</b></span>
              </v-col>
              <v-col cols="3" align="center" class="pl-4">
                <span class="fontInactive captionSku">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <!-- <span class="fontInactive captionSku">ที่ต้องได้รับ</span> -->
              </v-col>
            </v-row>
            <v-row no-gutters v-else class="mb-2">
              <v-col cols="12" align="center" class="pb-3">
                <v-img src="@/assets/stepperNew/service/step2.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <!-- <span class="captionSku">คำสั่งซื้อใหม่</span> -->
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontPass captionSku"><b>{{$t('DetailOrderBuyer.EnterInformation')}}</b></span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <!-- <span class="fontInactive captionSku">ที่ต้องได้รับ</span> -->
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" v-else>
            <v-row no-gutters v-if="trackingStatus === 'Not Paid'" class="mb-5">
              <v-col cols="12" align="center" class="pb-2 px-3">
                <v-img src="@/assets/stepperNew/transport/newui-step1.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="5"></v-img>
              </v-col>
              <!-- <v-col cols="3" align="center">
                <span class="fontActive captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku"><b>ชำระเงินแล้ว</b></span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku">จัดส่งแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku">ที่ต้องได้รับ</span>
              </v-col> -->
              <v-col cols="12" class="five-step-transport">
                <span style="color: #269afd;">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                <span>{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                <span>{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                <span>{{$t('DetailOrderBuyer.OutForDelivery')}}</span>
                <span>{{$t('DetailOrderBuyer.DeliverySuccessful')}}</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Not Sent' && items.transportation_status === 'อยู่ระหว่างดำเนินการ' && items.transaction_status === 'Success'" class="mb-5">
              <v-col cols="12" align="center" class="pb-2 px-3">
                <v-img src="@/assets/stepperNew/transport/newui-step2.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="5"></v-img>
              </v-col>
              <!-- <v-col cols="3" align="center">
                <span class="fontPass captionSku">กรอกข้อมูลทั่วไป</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku">ชำระเงินแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku">จัดส่งแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku">ที่ต้องได้รับ</span>
              </v-col> -->
              <v-col cols="12" class="five-step-transport">
                <span>{{$t('DetailOrderBuyer.NewOrder')}}</span>
                <span style="color: #269afd;">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                <span>{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                <span>{{$t('DetailOrderBuyer.OutForDelivery')}}</span>
                <span>{{$t('DetailOrderBuyer.DeliverySuccessful')}}</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Not Sent' && (items.transportation_status === 'รอขนส่งเข้ารับพัสดุ' || items.transportation_status === 'ผู้ส่งกำลังเตรียมพัสดุ' || items.transportation_status === 'พัสดุอยู่ระหว่างการขนส่ง')" class="mb-5">
              <v-col cols="12" align="center" class="pb-2 px-3">
                <v-img src="@/assets/stepperNew/transport/newui-step3.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="5"></v-img>
              </v-col>
              <!-- <v-col cols="3" align="center">
                <span class="fontPass captionSku">กรอกข้อมูลทั่วไป</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku">ชำระเงินแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku">จัดส่งแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku">ที่ต้องได้รับ</span>
              </v-col> -->
              <v-col cols="12" class="five-step-transport">
                <span>{{$t('DetailOrderBuyer.NewOrder')}}</span>
                <span>{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                <span style="color: #269afd;">{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                <span>{{$t('DetailOrderBuyer.OutForDelivery')}}</span>
                <span>{{$t('DetailOrderBuyer.DeliverySuccessful')}}</span>
              </v-col>
            </v-row>
            <!-- <v-row no-gutters v-else-if="trackingStatus === 'Not Sent' && items.transportation_status === 'พัสดุอยู่ระหว่างการขนส่ง'" class="mb-5">
              <v-col cols="12" align="center" class="pb-2 px-3">
                <v-img src="@/assets/stepperNew/transport/newui-step4.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="5"></v-img>
              </v-col>
              <v-col cols="12" class="five-step-transport">
                <span>คำสั่งซื้อใหม่</span>
                <span>ชำระเงินแล้ว</span>
                <span>เตรียมจัดส่ง</span>
                <span style="color: #269afd;">กำลังจัดส่ง</span>
                <span>จัดส่งสำเร็จ</span>
              </v-col>
            </v-row> -->
            <v-row no-gutters v-else-if="trackingStatus === 'Sent'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepperNew/transport/newui-step4.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="5"></v-img>
              </v-col>
              <!-- <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อที่ชำระเงินแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku" >คำสั่งซื้อทำการจัดส่งแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku">ที่ต้องได้รับ</span>
              </v-col> -->
              <v-col cols="12" class="five-step-transport">
                <span>{{$t('DetailOrderBuyer.NewOrder')}}</span>
                <span>{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                <span style="color: #269afd;">{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                <span>{{$t('DetailOrderBuyer.OutForDelivery')}}</span>
                <span>{{$t('DetailOrderBuyer.DeliverySuccessful')}}</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Not Received' && items.transportation_status === 'พัสดุอยู่ระหว่างการขนส่ง'" class="mb-5">
              <v-col cols="12" align="center" class="pb-2 px-3">
                <v-img src="@/assets/stepperNew/transport/newui-step4.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="5"></v-img>
              </v-col>
              <v-col cols="12" class="five-step-transport">
                <span style="font-weight: bold;">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                <span style="font-weight: bold;">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                <span style="font-weight: bold;">{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                <span style="color: #269afd; font-weight: bold;">{{$t('DetailOrderBuyer.OutForDelivery')}}</span>
                <span style="color: #cccccc; font-weight: bold;">{{$t('DetailOrderBuyer.DeliverySuccessful')}}</span>
              </v-col>
              <!-- <v-col cols="3" align="center">
                <span class="fontPass captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontPass captionSku">ชำระเงินแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontPass captionSku">เตรียมจัดส่ง</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku">กำลังจัดส่ง</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku">จัดส่งสำเร็จ</span>
              </v-col> -->
              <!-- <v-col cols="3" align="center">
                <span class="fontInactive captionSku">ที่ต้องได้รับ</span>
              </v-col> -->
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Not Received' && items.transportation_status === 'รอการตรวจสอบและยอมรับสินค้า'" class="mb-5">
              <v-col cols="12" align="center" class="pb-2 px-3">
                <v-img src="@/assets/stepperNew/transport/newui-step5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="5"></v-img>
              </v-col>
              <v-col cols="12" class="five-step-transport">
                <span style="font-weight: bold;">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                <span style="font-weight: bold;">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                <span style="font-weight: bold;">{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                <span style="font-weight: bold;">{{$t('DetailOrderBuyer.OutForDelivery')}}</span>
                <span style="color: #269afd; font-weight: bold;">{{$t('DetailOrderBuyer.DeliverySuccessful')}}</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Received'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepperNew/transport/newui-step5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="12" class="five-step-transport">
                <span style="font-weight: bold;">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                <span style="font-weight: bold;">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                <span style="font-weight: bold;">{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                <span style="font-weight: bold;">{{$t('DetailOrderBuyer.OutForDelivery')}}</span>
                <span style="color: #269afd; font-weight: bold;">{{$t('DetailOrderBuyer.DeliverySuccessful')}}</span>
              </v-col>
              <!-- <v-col cols="3" align="center">
                <span class="fontPass captionSku">กรอกข้อมูลทั่วไป</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontPass captionSku">ชำระเงินแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontPass captionSku">จัดส่งแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku">ที่ต้องได้รับ</span>
              </v-col> -->
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'waiting_refund' || trackingStatus === 'refund'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">{{$t('DetailOrderBuyer.NewOrder')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">{{$t('DetailOrderBuyer.OrderHasBeenShipped')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">{{$t('DetailOrderBuyer.BuyerReviewedAndRequestedReturn')}}</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="(trackingStatus === 'Not Sent' || trackingStatus === 'Pick Up') && (items.transaction_status === 'Waiting_Cancel' || items.transaction_status === 'Cancel') && (items.transportation_status === 'อยู่ระหว่างดำเนินการ' || items.transportation_status === 'ยกเลิกคำสั่งซื้อ') && (statusCancel === 'waiting' || statusCancel === '')" class="mb-5">
              <v-col cols="12" align="center" class="px-10">
                <v-img src="@/assets/stepperNew/transport/refundModal-step2.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="6"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #000000">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #269AFD">{{$t('DetailOrderBuyer.CancelOrder')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #cccccc">{{$t('DetailOrderBuyer.WaitingForRefundApproval')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #cccccc">{{$t('DetailOrderBuyer.RefundSuccessful')}}</span>
                  </v-col>
            </v-row>
            <v-row no-gutters v-else-if="(trackingStatus === 'Cancel by buyer' || trackingStatus === 'Pick Up') && items.transaction_status === 'Cancel' && items.transportation_status === 'ยกเลิกคำสั่งซื้อ' && statusCancel === 'approve' && items.tracking[0].time_cancel_4 === '-'" class="mb-5">
              <v-col cols="12" align="center" class="px-10">
                <v-img src="@/assets/stepperNew/transport/refundModal-step3.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="6"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #000000">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #000000">{{$t('DetailOrderBuyer.CancelOrder')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #269AFD">{{$t('DetailOrderBuyer.WaitingForRefundApproval')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #cccccc">{{$t('DetailOrderBuyer.RefundSuccessful')}}</span>
                  </v-col>
            </v-row>
            <!-- <v-row no-gutters v-else-if="trackingStatus === 'Success'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อที่ชำระเงินแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku">ที่ต้องจัดส่ง</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku">ที่ต้องได้รับ</span>
              </v-col>
            </v-row> -->
            <v-row no-gutters v-else-if="trackingStatus === 'Cancel by approver'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper7.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="6" align="left" :style="{'padding-left': IpadSize ? '20px' : '10px'}">
                <span class="captionSku">{{$t('DetailOrderBuyer.NewOrder')}}</span>
              </v-col>
              <v-col cols="6" align="right" :style="{'padding-right': IpadSize ? '20px' : '10px'}">
                <span class="captionSku">{{$t('DetailOrderBuyer.CancelOrder')}}</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Cancel'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper8.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="6" align="left" :style="{'padding-left': IpadSize ? '20px' : '10px'}">
                <span class="captionSku">{{$t('DetailOrderBuyer.NewOrder')}}</span>
              </v-col>
              <v-col cols="6" align="right" :style="{'padding-right': IpadSize ? '20px' : '10px'}">
                <span class="captionSku">{{$t('DetailOrderBuyer.CancelOrder')}}</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Pick Up' && trackingPayment === 'Cash'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper-onshop.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">{{$t('DetailOrderBuyer.NewOrder')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku">{{$t('DetailOrderBuyer.WaitingForPickup')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku">{{$t('DetailOrderBuyer.ReceivedSuccessfully')}}</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Pick Up' && trackingPayment === 'Success' && trackingStatusFront !== 'Received'" class="mb-5">
              <v-col cols="12" align="center" class="px-3">
                <v-img src="@/assets/stepperNew/receive/step3.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">{{$t('DetailOrderBuyer.EnterInformation')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku pl-3">{{$t('DetailOrderBuyer.WaitingForPickup')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku pl-8">{{$t('DetailOrderBuyer.ReceivedSuccessfully')}}</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Pick Up' && trackingPayment === 'Success' && trackingStatusFront === 'Received'" class="mb-5">
              <v-col cols="12" align="center" class="px-3">
                <v-img src="@/assets/stepperNew/receive/step4.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">{{$t('DetailOrderBuyer.EnterInformation')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku pl-3">{{$t('DetailOrderBuyer.WaitingForPickup')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku " :class="IpadSize ? 'pl-8' : 'pl-auto'">{{$t('DetailOrderBuyer.ReceivedSuccessfully')}}</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Pick Up' && trackingPayment === 'Not Paid'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepperNew/receive/step1.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku">{{$t('DetailOrderBuyer.NewOrder')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku">{{$t('DetailOrderBuyer.WaitingForPickup')}}</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku">{{$t('DetailOrderBuyer.ToReceived')}}</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Cancel by buyer' && statusCancel !== 'approve'" class="mb-5">
              <v-col cols="12" align="center" class="px-5">
                <v-img src="@/assets/stepperNew/transport/newui-step1.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="6"></v-img>
              </v-col>
              <v-col cols="12" class="five-step-transport">
                <span style="color: #269afd;">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                <span>{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                <span>{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                <span>{{$t('DetailOrderBuyer.OutForDelivery')}}</span>
                <span>{{$t('DetailOrderBuyer.DeliverySuccessful')}}</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-if="trackingStatus === ''" class="mb-5">
              <h1>{{$t('DetailOrderBuyer.DataNotFound')}}</h1>
            </v-row>
          </v-col>
          <v-col cols="12" class="mb-3">
            <v-row>
              <v-col cols="6" class="pl-6">
                <span style="font-weight: bold; font-size: 16px;">{{$t('DetailOrderBuyer.ShippingMethod')}} : </span><span>{{items.transportation_type}}</span>
              </v-col>
              <v-col cols="6">
                <v-row dense>
                  <b>Tracking Number : </b>
                  <div v-if="items.list_tracking.length !== 0" class="d-flex flex-wrap">
                    <div v-for="(item, index) in items.list_tracking" :key="index">
                      <span v-if="item.url_tracking !== '-' && item.url_tracking !== ''" :style="item.url_tracking !== '-' && item.url_tracking !== '' ? 'text-decoration: underline; font-size: 16px; font-weight: 400; color: #27AB9C !important; cursor: pointer;' : 'font-size: 16px; font-weight: 400;'" class="pl-2" @click="linkToURLTracking(item.url_tracking)">{{ item.tracking_no + `${index === items.list_tracking.length - 1 ? '' : ','}` }}</span>
                      <a id="urlTracking" :href="urlTracking" target="_blank" style="display: none;"></a>
                      <div @click="copyClipboard()" v-if="item.url_tracking === ''" style="cursor: pointer;" class="pl-2">
                        <v-icon color="#1B5DD6" size="16" class="pr-1">mdi-content-copy</v-icon><span style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #27AB9C;">{{ item.tracking_no + `${index === items.list_tracking.length - 1 ? '' : ','}` }}</span>
                        <input type="text" :value="item.tracking_no" id="trackingNumber" style="display: none;">
                      </div>
                    </div>
                  </div>
                  <div v-else>
                    <span style="font-size: 16px; font-weight: 400;" class="pl-2"> - </span>
                  </div>
                </v-row>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12">
          <v-divider></v-divider>
            <v-card outlined class="elevation-0" style="border: 0px;">
              <!-- address and tracking mobile ipad-->
              <v-container grid-list-lg>
                <v-row no-gutters>
                  <v-col cols="12">
                    <v-row no-gutters>
                      <!-- <v-col class="pa-4" cols="12" md="12" sm="12" style="background-color: #F9FAFD; border-radius: 8px;">
                        <v-row no-gutters>
                          <v-col cols="12" md="12">
                            <v-row no-gutters>
                              <v-col cols="7" md="7" class="fontSizeDetailMobile">
                                <v-row no-gutters class="fontHeight">
                                  <span>รหัสการสั่งซื้อ : </span>
                                </v-row>
                                <v-row no-gutters class="fontHeight">
                                  <span>ผู้ซื้อ :  </span>
                                </v-row>
                                <v-row no-gutters class="fontHeight">
                                  <span>วันที่ทำรายการ :  </span>
                                </v-row>
                                <v-row no-gutters class="fontHeight">
                                  <span>วันที่ชำระเงิน :  </span>
                                </v-row>
                                <v-row no-gutters >
                                  <span>ใบกำกับภาษี :  </span>
                                </v-row>
                              </v-col>
                              <v-col cols="5" md="5" class="fontDetailMobile">
                                <v-row no-gutters class="fontHeightMobile">
                                  <span> {{order.order_number}}</span>
                                </v-row>
                                <v-row no-gutters class="fontHeightMobile">
                                  <span>{{items.buyer_name}}</span>
                                </v-row>
                                <v-row no-gutters class="fontHeightMobile">
                                  <span>{{new Date(items.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })}}</span>
                                </v-row>
                                <v-row no-gutters class="fontHeightMobile">
                                  <span v-if="items.tracking[0].time_step_2 !== '-'">{{dateCreateOrderStep2}}</span>
                                  <span v-else>-</span>
                                </v-row>
                                <v-row no-gutters class="fontDetailMobile">
                                  <div v-if="items.transaction_code !== '-' && items.required_invoice !== '-'">
                                    <span style="color: #1B5DD6; text-decoration: underline; cursor: pointer;" @click="GetETax(items)">{{items.payment_transaction}}</span>
                                    <a :href="downloadLink" download="filename.pdf" id="downloadLink" style="display: none;"></a>
                                  </div>
                                  <div v-else-if="items.transaction_code === '-' && items.required_invoice !== '-'">
                                    <span>{{ items.required_invoice }}</span>
                                  </div>
                                  <div v-else>
                                    <span>{{ '-' }}</span>
                                  </div>
                                </v-row>
                              </v-col>
                            </v-row>
                          </v-col>
                          <v-col cols="12" md="12">
                            <v-row class="pt-3 mt-3" no-gutters style="background-color: #FFFFFF; border-radius: 8px;">
                              <v-col cols="5" md="5" class="fontSizeDetailMobile">
                                <v-row no-gutters class="fontHeight">
                                  <span>สถานะคำสั่งซื้อ : </span>
                                </v-row>
                                <v-row v-if="trackingStatus !== 'Pick Up' && productNormal.length !== 0" no-gutters class="fontHeightMobile">
                                  <span>วัน-เวลาส่งสินค้า :</span>
                                </v-row>
                                <v-row no-gutters v-if="productNormal.length !== 0">
                                  <span>วัน-เวลารับสินค้า : </span>
                                </v-row>
                              </v-col>
                              <v-col cols="7" md="7" class="fontSizeDetailMobile">
                                <v-row no-gutters class="fontHeightMobile">
                                  <div v-if="items.detail_status === '-'">
                                    <v-icon :class="trackingStatus === 'Not Paid' || trackingPayment === 'Not Paid' ? 'notPay' : trackingStatus === 'Not Received' || trackingPayment === 'Not Received' ? 'notReceived' : trackingStatus === 'Received'|| trackingPayment === 'Received' ? 'received': trackingStatus === 'Not Sent'|| trackingPayment === 'Success' ? 'Pay' : ''">mdi-circle-medium</v-icon>
                                    <span class="" :class="trackingStatus === 'Not Paid' || trackingPayment === 'Not Paid' ? 'notPay' : trackingStatus === 'Not Received' || trackingPayment === 'Not Received' ? 'notReceived' : trackingStatus === 'Received'|| trackingPayment === 'Received' ? 'received': trackingStatus === 'Not Sent'|| trackingPayment === 'Success' ? 'Pay' : ''">{{trackingText}} </span>
                                  </div>
                                  <v-row no-gutters style="margin-top:-5px;" v-if="items.detail_status !== '-'">
                                    <v-icon color="#636363">mdi-circle-medium</v-icon>
                                    <span>{{items.detail_status}}</span>
                                    <v-tooltip bottom>
                                      <template v-slot:activator="{ on, attrs }">
                                        <v-icon
                                          color="#636363"
                                          v-bind="attrs"
                                          v-on="on"
                                        >
                                          mdi-information-outline
                                        </v-icon>
                                      </template>
                                      <span>{{items.action_reason}}</span>
                                    </v-tooltip>
                                  </v-row>
                                </v-row>
                                <v-row no-gutters class="fontHeightMobile pl-2" style="margin-top:-5px;" v-if="trackingStatus !== 'Pick Up' && productNormal.length !== 0">
                                  <span v-if="items.tracking[0].time_step_3 !== '-'">{{dateCreateOrderStep3}}</span>
                                  <span v-else>-</span>
                                </v-row>
                                <v-row no-gutters class="fontHeightMobile pl-2" style="margin-top:-5px;" v-if="productNormal.length !== 0">
                                  <span v-if="items.received_date !== '-'">{{dateCreateOrderStep4}}</span>
                                  <span v-else>-</span>
                                </v-row>
                              </v-col>
                            </v-row>
                            <v-row no-gutters class="pt-2">
                              <div class="d-flex fontSizeDetailMobile">
                                <v-img height="20px" width="20px" src="@/assets/infoWarn.png"></v-img>
                                <span style=" color:#989898;">ถ้าต้องการยกเลิกคำสั่งซื้อกรุณาติดต่อร้านค้า</span>
                              </div>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-col> -->
                      <v-col cols="12" class="pt-2">
                        <v-row no-gutters>
                          <div class="d-flex">
                            <v-row no-gutters class="pa-1">
                              <v-img class="sizeIMGMobile" src="@/assets/ICON/detail_doc_icon.png"></v-img>
                            </v-row>
                            <p class="fontSizeTitleMobile" style="font-weight: 700;">{{$t('DetailOrderBuyer.DocumentDetails')}}</p>
                          </div>
                        </v-row>
                      </v-col>
                      <v-col cols="12" class="mb-4 ml-1" >
                        <v-row dense>
                          <v-col cols="12" md="6" sm="12">
                            <v-row class="pt-6">
                              <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                                <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">{{$t('DetailOrderBuyer.QuotationNumber')}} : </span>
                                <a v-if="items.payment_transaction !== '' && QTorder !== '-' && items.transaction_status === 'Success'" :href="QTorder" target="_blank" style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #1B5DD6 !important;" :class="MobileSize ? 'ml-auto' : ''"> {{items.payment_transaction}}</a>
                                <span v-else-if="items.payment_transaction !== '' && QTorder !== '-' && items.transaction_status !== 'Success'" style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''"> {{items.payment_transaction}}</span>
                                <span v-else style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">-</span>
                              </v-col>
                              <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                                <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">{{$t('DetailOrderBuyer.PurchaseOrderNumber')}} : </span>
                                <a :class="MobileSize ? 'ml-auto' : ''" v-if="((documents.PoNumber !== '' && documents.PoNumber !== null) || items.po_document_id !== '-') && (poPDF !== '-' || items.PO_External !== '-')" :href="poPDF !== '-' ? poPDF : items.PO_External" target="_blank" style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #1B5DD6 !important;"> {{(documents.PoNumber !== '' && documents.PoNumber !== null)? documents.PoNumber : items.po_document_id}}</a>
                                <span :class="MobileSize ? 'ml-auto' : ''" v-else-if="((documents.PoNumber !== '' && documents.PoNumber !== null) || items.po_document_id !== '-') && (poPDF === '-' || items.PO_External === '-')"> {{ (documents.PoNumber !== '' && documents.PoNumber !== null) ? documents.PoNumber : items.po_document_id }}</span>
                                <span :class="MobileSize ? 'ml-auto' : ''" v-else style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                              </v-col>
                              <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                                <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">{{$t('DetailOrderBuyer.ReceiptNumber')}} : </span>
                                <span :class="MobileSize ? 'ml-auto' : ''" style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.receipt_number }}</span>
                              </v-col>
                            </v-row>
                          </v-col>
                          <v-col cols="12" md="6" sm="12">
                            <v-row class="pt-6">
                              <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                                <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">{{$t('DetailOrderBuyer.PRNumber')}} : </span>
                                <a v-if="((documents.PrNumber !== '' && documents.PrNumber !== null) || items.pr_document_id !== '-') && (prPDF !== '-' || items.PR_External !== '-')" :href="prPDF !== '-' ? prPDF : items.PR_External" target="_blank" style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #1B5DD6 !important;"> {{ (documents.PrNumber !== '' && documents.PrNumber !== null) ? documents.PrNumber : items.pr_document_id }}</a>
                                <span v-else-if="((documents.PrNumber !== '' && documents.PrNumber !== null) || items.pr_document_id !== '-') && (prPDF === '-' || items.PR_External === '-')"> {{ (documents.PrNumber !== '' && documents.PrNumber !== null) ? documents.PrNumber : items.pr_document_id }}</span>
                                <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                              </v-col>
                              <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                                <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">{{$t('DetailOrderBuyer.SONumber')}} : </span>
                                <a v-if="((documents.SoNumber !== '' && documents.SoNumber !== null) || items.ref_callback_so_id !== '-' || items.so_document_id !== '-') && soPDF !== '-'" :href="soPDF" target="_blank" style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #1B5DD6 !important;"> {{ (documents.SoNumber !== '' && documents.SoNumber !== null) ? documents.SoNumber : items.ref_callback_so_id !== '-' ? items.ref_callback_so_id : items.so_document_id !== '-' ? items.so_document_id : documents.SoNumber }}</a>
                                <span v-else-if="((documents.SoNumber !== '' && documents.SoNumber !== null) || items.ref_callback_so_id !== '-' || items.so_document_id !== '-') && soPDF === '-'"> {{ (documents.SoNumber !== '' && documents.SoNumber !== null) ? documents.SoNumber : items.ref_callback_so_id !== '-' ? items.ref_callback_so_id : items.so_document_id !== '-' ? items.so_document_id : documents.SoNumber }}</span>
                                <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-col>
                      <v-col cols="12">
                      <v-divider></v-divider>
                        <v-row no-gutters>
                          <!-- cols="8" -->
                          <v-col cols="12" class="pt-3">
                            <v-row no-gutters class="pa-1">
                              <v-img class="sizeIMGMobile" src="@/assets/address.png" max-height="18px" max-width="18px"></v-img>
                              <p class="pl-2" :style="MobileSize ? 'font-size: 18px' : 'font-size: 18px'" style="font-weight: 700;">{{ items.shipping_type === 'front' ? $t('DetailOrderBuyer.PickupAddress') : $t('DetailOrderBuyer.ShippingAddress') }} <v-chip v-if="items.shipping_type === 'front'" color="#ADDFFF" text-color="#0059FF">{{$t('DetailOrderBuyer.PickUpAtStore')}}</v-chip></p>
                            </v-row>
                          </v-col>
                          <!-- <v-col cols="4" align="end" v-if="items.transaction_status === 'Success' && items.shipping_type === 'online' && trackingStatus === 'Not Sent' && items.transportation_type === '-'">
                            <v-btn small text color="primary" @click="openChangeAddress(items)"><v-icon left>mdi-pencil-outline</v-icon> เปลี่ยนที่อยู่</v-btn>
                          </v-col> -->
                        </v-row>
                      </v-col>
                      <v-col cols="12" class="mb-4 ml-1" >
                        <span class="d-flex" style="font-weight: bold;">{{items.buyer_name}} <span style="color: #cccccc">|</span> {{items.buyer_phone}} </span>
                        <span v-if="productNormal.length !== 0 && trackingStatus !== 'Pick Up'" class="fontSizeDetailMobile">
                          {{ items.address_data }}<br/>
                          <span v-if="items.note_address !== '' && items.note_address !== undefined && items.note_address !== null"><b>{{$t('DetailOrderBuyer.Remarks')}} :</b> {{ items.note_address }}</span>
                        </span>
                        <span v-else-if="trackingStatus === 'Pick Up'" class="fontSizeDetailMobile">{{ items.shipping_detail }}</span>
                        <span v-else>-</span>
                      </v-col>
                      <v-col :cols="12">
                        <v-divider></v-divider>
                        <v-row no-gutters>
                          <div class="d-flex">
                            <v-row no-gutters class="pa-1">
                              <v-img class="sizeIMGMobile" src="@/assets/ICON/tax_invoice_icon.png"></v-img>
                            </v-row>
                            <p class="fontSizeTitleMobile" style="font-weight: 700;">{{$t('DetailOrderBuyer.TaxInvoiceDeliveryAddress')}}</p>
                          </div>
                        </v-row>
                      </v-col>
                      <v-col cols="12" class="mb-2 ml-1" v-if="items.invoice_address !== ''" >
                        <span class="fontSizeDetailMobile">{{ items.required_invoice !== '-' ? items.invoice_address : '-' }}</span>
                      </v-col>
                      <v-col cols="12" class="mb-2 ml-1" v-else>
                        <span class="fontSizeDetailMobile">-</span>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-container>
              <!-- accept = success , review review mobile ipad-->
              <!-- product table mobile ipad-->
              <v-container grid-list-xs class="px-0">
                <v-col cols="12" class="pt-0">
                  <v-divider></v-divider>
                  <v-row no-gutters>
                    <div class="d-flex">
                      <v-row no-gutters class="pa-1">
                        <v-img class="sizeIMGMobile" src="@/assets/ICON/order_list_icon.png"></v-img>
                      </v-row>
                      <span class="fontSizeTitleMobile"><b>{{$t('DetailOrderBuyer.OrderList')}}</b></span>
                    </div>
                  </v-row>
                </v-col>
                <!-- <v-col cols="12" class="ml-2"> -->
                  <!-- <div v-if="items.url_tracking !== '-'">
                    <b>Tracking Number : </b>
                    <a :href="items.url_tracking" target="_blank" >{{items.order_mobilyst_no}}</a>
                  </div> -->
                  <!-- mdi-content-copy -->
                  <!-- <v-row dense>
                    <span><b>รหัสการสั่งซื้อ :</b> {{order.order_number}}</span>
                  </v-row>
                  <v-row dense>
                    <b>Tracking Number : </b>
                    <div v-if="items.list_tracking.length !== 0" class="d-flex flex-wrap">
                      <div v-for="(item, index) in items.list_tracking" :key="index">
                        <span v-if="item.url_tracking !== '-' && item.url_tracking !== ''" :style="item.url_tracking !== '-' && item.url_tracking !== '' ? 'text-decoration: underline; font-size: 16px; font-weight: 400; color: #27AB9C !important; cursor: pointer;' : 'font-size: 16px; font-weight: 400;'" class="pl-2" @click="linkToURLTracking(item.url_tracking)">{{ item.tracking_no + `${index === items.list_tracking.length - 1 ? '' : ','}` }}</span>
                        <a id="urlTracking" :href="urlTracking" target="_blank" style="display: none;"></a>
                        <div @click="copyClipboard()" v-if="item.url_tracking === ''" style="cursor: pointer;" class="pl-2">
                          <v-icon color="#1B5DD6" size="16" class="pr-1">mdi-content-copy</v-icon><span style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #27AB9C;">{{ item.tracking_no + `${index === items.list_tracking.length - 1 ? '' : ','}` }}</span>
                          <input type="text" :value="item.tracking_no" id="trackingNumber" style="display: none;">
                        </div>
                      </div>
                    </div>
                    <div v-else>
                      <span style="font-size: 16px; font-weight: 400;" class="pl-2"> - </span>
                    </div>
                  </v-row> -->
                <!-- </v-col> -->
                <v-col cols="12" v-if="productNormal.length !== 0" class="pl-4">
                  <p style="font-size: 14px; font-weight: 700;">{{$t('DetailOrderBuyer.GeneralProducts')}}</p>
                  <a-table :data-source="productNormal" :rowKey="record => record.sku" :columns="headersMobile" :pagination="{ pageSize: 100 }">
                    <template slot="productdetails" slot-scope="text, record">
                      <v-row>
                        <v-col cols="3" md="4" class="pr-0 mt-2 py-1">
                          <v-img :src="`${record.product_image}`" class="imageshowMobile" v-if="record.product_image !== ''"/>
                          <v-img src="@/assets/NoImage.png" class="imageshowMobile" v-else/>
                        </v-col>
                        <v-col cols="9" md="8">
                          <b class="mb-0 DetailsProductFrontMobile">{{ record.product_name }}</b> <br/>
                          <div class="mb-0" v-if="record.have_attribute === 'yes'">
                            <span v-if="record.product_attribute_detail.attribute_priority_1" class="mb-0 DetailsProductFrontMobile">{{record.product_attribute_detail.key_1_value}}: <b>{{record.product_attribute_detail.attribute_priority_1}}</b></span> <br/>
                            <span v-if="record.product_attribute_detail.attribute_priority_2" class="mb-0 DetailsProductFrontMobile">{{record.product_attribute_detail.key_2_value}}: <b>{{record.product_attribute_detail.attribute_priority_2}}</b></span>
                            <v-chip color="#3EC6B6" x-small>
                              <span style="color: #ffffff">{{$t('DetailOrderBuyer.GeneralProducts')}}</span>
                            </v-chip>
                          </div>
                          <!-- <span class="mb-0 DetailsProductFrontMobile">จำนวน: <span style="font-weight: 700;">{{ record.quantity }}</span></span>
                          <span class="mb-0 ml-3 DetailsProductFrontMobile">ราคา: <span style="font-weight: 700;">{{ Number(record.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span></span><br>
                          <span class="pt-1 mb-0 DetailsProductFrontMobile">ราคารวม: <span style="font-weight: 700;">{{ Number(record.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span></span> -->
                        </v-col>
                      </v-row>
                    </template>
                  </a-table>
                </v-col>
                <v-col cols="12" v-if="productService.length !== 0" class="pl-4">
                  <p style="font-size: 14px; font-weight: 700;">{{$t('DetailOrderBuyer.ServiceProducts')}}</p>
                  <a-table :data-source="productService" :rowKey="record => record.sku" :columns="headersMobile" :pagination="{ pageSize: 100 }">
                    <template slot="productdetails" slot-scope="text, record">
                      <v-row>
                        <v-col cols="3" md="4" class="pr-0 mt-2 py-1">
                          <v-img :src="`${record.product_image}`" class="imageshowMobile" v-if="record.product_image !== ''"/>
                          <v-img src="@/assets/NoImage.png" class="imageshowMobile" v-else/>
                        </v-col>
                        <v-col cols="9" md="8">
                          <b class="mb-0 DetailsProductFrontMobile">{{ record.product_name }}</b> <br/>
                          <div class="mb-0" v-if="record.have_attribute === 'yes'">
                            <span v-if="record.product_attribute_detail.attribute_priority_1" class="mb-0 DetailsProductFrontMobile">{{record.product_attribute_detail.key_1_value}}: <b>{{record.product_attribute_detail.attribute_priority_1}}</b></span> <br/>
                            <span v-if="record.product_attribute_detail.attribute_priority_2" class="mb-0 DetailsProductFrontMobile">{{record.product_attribute_detail.key_2_value}}: <b>{{record.product_attribute_detail.attribute_priority_2}}</b></span>
                          </div>
                          <v-chip color="#fda808" x-small>
                            <span style="color: #ffffff">{{$t('DetailOrderBuyer.ServiceProducts')}}</span>
                          </v-chip>
                          <!-- <span class="mb-0 DetailsProductFrontMobile">จำนวน: <span style="font-weight: 700;">{{ record.quantity }}</span></span>
                          <span class="mb-0 ml-3 DetailsProductFrontMobile">ราคา: <span style="font-weight: 700;">{{ Number(record.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span></span><br>
                          <span class="pt-1 mb-0 DetailsProductFrontMobile">ราคารวม: <span style="font-weight: 700;">{{ Number(record.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span></span> -->
                        </v-col>
                      </v-row>
                    </template>
                  </a-table>
                </v-col>
                <v-col cols="12" class="pt-3" v-if="items.product_free !== null && items.product_free.length !== 0">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">{{$t('DetailOrderBuyer.FreeGift')}}</span>
                  <a-table :data-source="productFree" :rowKey="record => record.sku" :columns="headersMobile" :pagination="{ pageSize: 100 }">
                    <template slot="productdetails" slot-scope="text, record">
                      <v-row>
                        <v-col cols="3" md="4" class="pr-0 mt-2 py-1">
                          <v-img :src="`${record.product_image}`" class="imageshowMobile" v-if="record.product_image !== ''"/>
                          <v-img src="@/assets/NoImage.png" class="imageshowMobile" v-else/>
                        </v-col>
                        <v-col cols="9" md="8">
                          <b class="mb-0 DetailsProductFrontMobile">{{ record.product_name }}</b> <br/>
                          <div class="mb-0" v-if="record.have_attribute === 'yes'">
                            <span v-if="record.product_attribute_detail.attribute_priority_1" class="mb-0 DetailsProductFrontMobile">{{record.product_attribute_detail.key_1_value}}: <b>{{record.product_attribute_detail.attribute_priority_1}}</b></span> <br/>
                            <span v-if="record.product_attribute_detail.attribute_priority_2" class="mb-0 DetailsProductFrontMobile">{{record.product_attribute_detail.key_2_value}}: <b>{{record.product_attribute_detail.attribute_priority_2}}</b></span>
                            <v-chip color="#ff3d1e" x-small>
                              <span style="color: #ffffff">{{$t('DetailOrderBuyer.FreeGift')}}</span>
                            </v-chip>
                          </div>
                        </v-col>
                      </v-row>
                    </template>
                  </a-table>
                  <!-- <v-data-table
                  :headers="MobileSize ? header : header"
                  :items="items.product_free"
                  style="width: 100%"
                  :class="MobileSize ? '' : 'row-height-64'"
                  :items-per-page="100"
                  :hide-default-header="MobileSize ? true : false"
                  hide-default-footer
                  >
                    <template v-slot:[`item.productdetails`]="{ item }">
                      <v-row dense>
                        <v-col cols="12" md="12" class="pr-0 py-2 d-flex">
                          <v-img :src="item.product_image" class="imageshow" v-if="item.product_image !== '' && item.product_image !== null" max-height="48" max-width="48"/>
                          <v-img src="@/assets/NoImage.png" class="imageshow" max-height="48" max-width="48" v-else/>
                          <p class="mb-0 ml-3" style="font-size: 14px; font-weight: 400; color: #333333;">{{ item.product_name }}</p>
                        </v-col>
                        <v-col cols="12" md="12" class="pb-2">
                          <v-chip color="#ff3d1e">
                            <span style="color: #ffffff">สินค้าแถม</span>
                          </v-chip>
                        </v-col>
                      </v-row>
                    </template>
                    <template v-slot:[`item.revenue_default`]="{ }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.total_revenue_default`]="{ }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.revenue_amount`]="{ }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.productdetailsMobile`]="{ item }">
                      <v-row class="d-flex px-0">
                        <v-col class="ml-auto" style="max-width: 100px; margin: auto;">
                          <v-img :src="item.product_image" class="imageshow" v-if="item.product_image !== '' && item.product_image !== null" width="100" height="100" max-width="80" max-height="80"/>
                          <v-img src="@/assets/NoImage.png" class="imageshow" width="80" height="80" v-else/>
                        </v-col>
                        <v-col class="mlauto" align="start">
                          <span style="font-size: 12px; color: #989898; font-weight: 400;">รหัส SKU :</span><span class="pl-1" style="font-size: 12px; color: #333333; font-weight: 400;">{{ item.sku }}</span><br/>
                          <span style="font-size: 14px; color: #333333; font-weight: 500;">{{ item.product_name }}</span><br/>
                          <span style="font-size: 12px; color: #989898; font-weight: 400;">ราคาต่อชิ้น :</span><span class="pl-1" style="font-size: 12px; color: #333333; font-weight: 400;">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br/>
                          <span style="font-size: 12px; color: #989898; font-weight: 400;">จำนวน :</span><span class="pl-1" style="font-size: 12px; color: #333333; font-weight: 400;">{{ item.quantity }}</span><br/>
                          <span style="font-size: 14px; color: #333333; font-weight: 500;">ราคารวม :</span><span style="font-size: 14px; color: #333333; font-weight: 500;">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br/>
                        </v-col>
                      </v-row>
                    </template>
                  </v-data-table> -->
                </v-col>
                <v-col cols="12" class="pa-0" v-if="items.point !== null || (items.coupon.length !== 0 && items.coupon !== null)">
                  <v-row no-gutters >
                    <div class="d-flex">
                      <v-row no-gutters class="pr-2">
                        <v-img class="sizeIMG" src="@/assets/ICON/payment_transaction_icon.png"></v-img>
                      </v-row>
                      <p class="fontHeadDetail"><b>{{$t('DetailOrderBuyer.Payment')}}</b></p>
                    </div>
                  </v-row>
                  <!-- <span style="font-size: 16px; font-weight: 600; color: #333333;" class="pl-3">โปรโมชันและคูปองส่วนลด</span> -->
                  <v-card class="pt-0" style="background: #FFFFFF; border-radius: 4px;" min-height="100%" elevation="0">
                    <!-- <v-toolbar align="start" color="#E6F5F3" dark dense elevation="0" v-if="items.point !== null">
                      <span class="" style="font-size:16px; font-weight:500; color: #333333;">
                        <font>ส่วนลดจากแต้ม {{items.point === null ? 0 : items.point}} บาท</font>
                      </span>
                    </v-toolbar> -->
                    <!-- <v-container class="px-0" v-if="items.coupon !== null && items.coupon.length !== 0">
                      <v-card-text class="pa-0">
                        <v-row :class=" MobileSize? 'pa-1 pt-1':''" dense>
                          <v-col cols="12" md="4" sm="6" v-for="(item, index) in items.coupon" :key="index" class="pa-2">
                            <v-col :class="!MobileSize && !IpadSize ? 'couponIMGDesk': MobileSize? 'couponIMGMobile pa-0': 'couponIMG'" :style="MobileSize ? 'height: 8.5vh;' : 'height: 6vh;'">
                              <v-col cols="12" md="12" class="pa-0" :class="MobileSize? 'pa-1': IpadSize? 'pl-0': IpadProSize? 'pl-0': 'pl-0'">
                                <v-row no-gutters>
                                  <v-col cols="12" md="12" sm="12" :style="MobileSize ? 'padding-left: 6% !important;': 'padding-left: 10% !important;'">
                                    <v-row no-gutters>
                                      <v-col :cols="MobileSize? '5':'6'" md="6" sm="5" align="start">
                                        <span style="color: #27AB9C; font-size: 14px; font-weight: 600;">{{item.coupon_name | truncate(15, '...')}}</span><br>
                                        <span style="font-size: 12px;"> ขั้นต่ำ {{item.spend_minimum}} บาท</span><br>
                                      </v-col>
                                      <v-col :cols="MobileSize? '6':'6'" md="6" sm="6" align="end">
                                        <span style="color: #FB9372; font-size: 12px;"> {{item.coupon_type === 'free_shipping' ? 'โค้ดส่งฟรี' : 'ส่วนลด'}}</span><br>
                                        <span style="color: #F56E22; font-size: 22px; font-weight: 600;"> {{item.discount_amount}} {{item.discount_type === 'percent'? '%':'บาท'}}</span><br>
                                      </v-col>
                                    </v-row>
                                  </v-col>
                                </v-row>
                              </v-col>
                            </v-col>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-container> -->
                  </v-card>
                </v-col>
                <!-- <v-col cols="12" v-for="(order, index) in items.data_list" :key="index">
                  <v-card outlined>
                    <v-row no-gutters class="pa-3" type="flex" justify="start">
                      <v-col cols="12">
                        <span>รหัสการสั่งซื้อ
                          <b style="font-size: 12px;">{{order.order_number}}</b><br>
                        </span>
                      </v-col>
                      <v-col cols="12" align="start" class="mt-2">
                        <span v-if="order.service_type === 'chilled'">
                          <v-chip small color="#E5EFFF" text-color="#1B5DD6">ส่งแบบควบคุมอุณหภูมิ {{ order.business_type }} Express</v-chip>
                        </span>
                        <span v-if="order.service_type === 'normal'">
                          <v-chip small color="#E6F5F3" text-color="#27AB9C">ส่งแบบปกติ {{ order.business_type }} Express</v-chip>
                        </span>
                        <span v-if="order.service_type === 'frozen'">
                          <v-chip small color="#E6F5F3" text-color="#26C6DA">ส่งแบบแช่แข็ง {{ order.business_type }} Express</v-chip>
                        </span>
                        <span v-if="order.service_type === 'bulk'">
                          <v-chip small color="#FCF0DA" text-color="#E9A016">ส่งของขนาดใหญ่ {{ order.business_type }} Express</v-chip>
                        </span>
                      </v-col>
                      <v-col cols="12" class="mt-2" v-if="order.order_mobilyst_no">
                        <span class="fontSizeDetailMobile">Tracking Number <b>{{order.order_mobilyst_no}}</b></span>
                      </v-col>
                      <v-col cols="12" class="mt-1">
                        <v-btn class="pr-1" :class="IpadSize ? 'pl-3' : 'pl-0'" v-if="order.order_mobilyst_no"  text color="#27AB9C" @click="GoToMobily(order.url_tracking)" style="color: #27AB9C; text-decoration: underline; font-size: 12px;">
                          <v-img src="@/assets/icons/Vector.png" contain></v-img>
                          ติดตามสถานะขนส่ง
                        </v-btn>
                      </v-col>
                      <v-col cols="12" class="mt-3">
                        <span>
                          {{ Object.keys(order.product_list).length }} รายการสินค้า
                        </span>
                      </v-col>
                    </v-row>
                    <a-table :data-source="order.product_list" :rowKey="record => record.sku" :columns="headersMobile" :pagination="{ pageSize: 100 }">
                      <template slot="productdetails" slot-scope="text, record">
                        <v-row>
                          <v-col cols="3" md="4" class="pr-0 mt-2 py-1">
                            <v-img :src="`${record.product_image}`" class="imageshowMobile" v-if="record.product_image !== ''"/>
                            <v-img src="@/assets/NoImage.png" class="imageshowMobile" v-else/>
                          </v-col>
                          <v-col cols="9" md="8">
                            <span class="mb-0 DetailsProductFrontMobile">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</span><br>
                            <div class="mb-0" v-if="record.have_attribute === 'yes'">
                              <span class="mb-0 DetailsProductFrontMobile" v-if="record.product_attribute_detail.attribute_priority_1  !== null" >{{record.key_1_value}}: <span style="font-weight: 700;"> {{record.product_attribute_detail.attribute_priority_1}} </span></span>
                              <span class="ml-3 mb-0 DetailsProductFrontMobile"  v-if="record.product_attribute_detail.attribute_priority_2 !== null ">{{record.key_2_value}}: <span style="font-weight: 700;"> {{record.product_attribute_detail.attribute_priority_2}} </span> </span>
                            </div>
                            <span class="mb-0 DetailsProductFrontMobile">จำนวน: <span style="font-weight: 700;">{{ record.quantity }}</span></span>
                            <span class="mb-0 ml-3 DetailsProductFrontMobile">ราคา: <span style="font-weight: 700;">{{ Number(record.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span></span><br>
                            <span class="pt-1 mb-0 DetailsProductFrontMobile">ราคารวม: <span style="font-weight: 700;">{{ Number(record.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span></span>
                          </v-col>
                        </v-row>
                      </template>
                    </a-table>
                    btn comfirm accept, refund, contact seller mobile ipad
                    <v-row no-gutters v-if="checkAcceptProduct[index].status !== 'Pickup' && checkAcceptProduct[index].status !== 'expired' && items.transaction_status !== 'Not Paid'" :class="checkAcceptProduct[index].status === 'waiting_review' || checkAcceptProduct[index].status === 'success' ? 'pa-0' : 'pa-3'">
                      <v-col cols="12" align="end">
                        <span v-if="checkAcceptProduct[index].status === 'reject'" style="color: #BDBDBD">คำขอคืนสินค้าไม่ได้รับการอนุมัติจากร้านค้า</span>
                        <span v-if="checkAcceptProduct[index].status === 'refund'" style="color: #BDBDBD">คำขอคืนสินค้าได้รับการอนุมัติแล้ว</span>
                        <span v-if="checkAcceptProduct[index].status === 'waiting_refund'" style="color: #BDBDBD">ส่งคำขอคืนสินค้าแล้ว รอร้านค้าอนุมัติ</span>
                      </v-col>
                      <v-col cols="12">
                        <v-btn small block v-if="checkAcceptProduct[index].status !== 'waiting_review' && checkAcceptProduct[index].status !== 'reject' && checkAcceptProduct[index].status !== 'waiting_refund' && checkAcceptProduct[index].status !== 'refund' && checkAcceptProduct[index].status !== 'success'" :disabled="checkAcceptProduct[index].refund === 'no'" outlined class="mb-3 mr-2 px-5" color="#D1392B" @click="refundProductBuyer(order)"><b class="buttonFontSize">คืนสินค้า</b></v-btn>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-btn max-width="10px" small block v-if="checkAcceptProduct[index].status !== 'waiting_review' && checkAcceptProduct[index].status !== 'reject' && checkAcceptProduct[index].status !== 'waiting_refund' && checkAcceptProduct[index].status !== 'refund' && checkAcceptProduct[index].status !== 'success'" :disabled="checkAcceptProduct[index].status !== 'waiting_accept'" class="white--text px-5" color="#27AB9C" @click="acceptProduct(order)"><b class="buttonFontSize ">ฉันตรวจสอบและได้รับสินค้าแล้ว</b></v-btn>
                      </v-col>
                    </v-row>
                    <v-container grid-list-xs v-if="checkAcceptProduct[index].status !== 'Pickup' && checkAcceptProduct[index].status === 'waiting_review'">
                      <v-row>
                        <v-col cols="12" align="right" class="px-3">
                          <v-chip rounded class="white--text px-5" color="#52C41A" @click="openModalReviewProduct(order, index)"><b class="buttonFontSize">ประเมินความพึงพอใจสินค้า</b></v-chip>
                        </v-col>
                      </v-row>
                    </v-container>
                    <v-container grid-list-xs v-if="checkAcceptProduct[index].status !== 'Pickup' && checkAcceptProduct[index].status === 'success'">
                      <v-row>
                        <v-col cols="12" :align="MobileSize ? 'center' : 'right'" :class="MobileSize ? 'pr-0 pl-1' : 'px-3'">
                          <v-btn rounded :class="MobileSize ? 'white--text px-3' : 'white--text px-5'" color="#27AB9C" @click="openModalEditReviewProduct(order, index)"><b :class="MobileSize ? 'buttonFontSizeMobile' : 'buttonFontSize'">รายละเอียดการประเมินความพึงพอใจสินค้า</b></v-btn>
                        </v-col>
                      </v-row>
                    </v-container>
                  </v-card>
                </v-col> -->
              </v-container>
              <!-- price, total_price_vat, shipping mobile ipad-->
              <v-container grid-list-xs>
                <!-- สรุปรายการสั่งซื้อ ipad -->
                <v-row v-if="IpadSize">
                  <v-col cols="12" md="10">
                    <v-row dense>
                      <v-col cols="9">
                        <span class="fontSizeTotalPriceMobile">{{$t('DetailOrderBuyer.PriceExcludingVAT')}} :</span>
                      </v-col>
                      <v-col cols="3" class="d-flex justify-end">
                        <span class="fontSizeTotalPriceMobile">{{ Number(items.total_price_no_vat_web).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="9">
                        <span class="fontSizeTotalPriceMobile">{{$t('DetailOrderBuyer.VAT')}} :</span>
                      </v-col>
                      <v-col cols="3" class="d-flex justify-end">
                        <span class="fontSizeTotalPriceMobile">{{ Number(items.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="9">
                        <span class="fontSizeTotalPriceMobile">{{$t('DetailOrderBuyer.PriceIncludingVAT')}} :</span>
                      </v-col>
                      <v-col cols="3" class="d-flex justify-end">
                        <span class="fontSizeTotalPriceMobile">{{ Number(items.total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="9">
                        <span class="fontSizeTotalPriceMobile fontSizeDiscount ml-3">{{$t('DetailOrderBuyer.StoreDiscountSummary')}} :</span>
                      </v-col>
                      <v-col cols="3" class="d-flex justify-end">
                        <span class="fontSizeDiscount">- {{ Number(items.total_coupon_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="9">
                        <span class="fontSizeTotalPriceMobile fontSizeDiscount ml-3">{{$t('DetailOrderBuyer.SystemDiscountSummary')}} :</span>
                      </v-col>
                      <v-col cols="3" class="d-flex justify-end">
                        <span class="fontSizeDiscount">- {{ Number(items.total_coupon_platform_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="9">
                        <span class="fontSizeTotalPriceMobile fontSizeDiscount ml-3">{{$t('DetailOrderBuyer.PointsDiscountSummary')}} :</span>
                      </v-col>
                      <v-col cols="3" class="d-flex justify-end">
                        <span class="fontSizeDiscount">- {{ Number(items.total_point).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="9">
                        <span class="fontSizeTotalPriceMobile">{{$t('DetailOrderBuyer.SubTotal')}} :</span>
                      </v-col>
                      <v-col cols="3" class="d-flex justify-end">
                        <span class="fontSizeTotalPriceMobile">{{ Number(items.total_price_after_all_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="9">
                        <span class="fontSizeTotalPriceMobile">{{$t('DetailOrderBuyer.ShippingFee')}} :</span>
                      </v-col>
                      <v-col cols="3" class="d-flex justify-end">
                        <span class="fontSizeTotalPriceMobile">{{ Number(items.shipping_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="9">
                        <span class="fontSizeTotalPriceMobile fontSizeDiscount ml-3">{{$t('DetailOrderBuyer.ShippingDiscountSeller')}} :</span>
                      </v-col>
                      <v-col cols="3" class="d-flex justify-end">
                        <span class="fontSizeDiscount">- {{ Number(items.total_coupon_shipping_discount_v2).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="9">
                        <span class="fontSizeTotalPriceMobile fontSizeDiscount ml-3">{{$t('DetailOrderBuyer.ShippingDiscountNexgen')}} :</span>
                      </v-col>
                      <v-col cols="3" class="d-flex justify-end">
                        <span class="fontSizeDiscount">- {{ Number(items.total_coupon_platform_shipping_discount_v2).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="9">
                        <span class="subheader fontSizeTotalPriceMobile">{{$t('DetailOrderBuyer.TotalPriceAll')}} :</span>
                      </v-col>
                      <v-col cols="3" class="d-flex justify-end">
                        <span class="fontSizeTotalPriceMobile">{{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
                <!-- สรุปรายการสั่งซื้อ mobile -->
                <v-row v-else>
                  <v-col cols="12">
                    <OrderSummary :items="items"></OrderSummary>
                  </v-col>
                </v-row>
              </v-container>
            </v-card>
          </v-col>
          <!-- status payment mobile ipad-->
          <v-col cols="12" class="mt-2">
            <v-container grid-list-xs>
              <v-row no-gutters>
                <v-col v-if="items.tracking[0].status_tracking === 'Not Paid'" cols="12" class="mt-4">
                  <v-row no-gutters class="py-2">
                    <div class="d-flex fontSizeTitleMobile pt-2" style="font-weight: 700;">
                      <v-row no-gutters class="pr-1">
                        <v-img class="sizeIMGMobile" src="@/assets/paymentHead.png"></v-img>
                      </v-row>
                      {{$t('DetailOrderBuyer.Payment')}}
                    </div>
                    <div class="pt-2 notPay" >
                      <v-icon class="notPay">mdi-circle-medium</v-icon>
                      {{$t('DetailOrderBuyer.PendingPayment')}}
                    </div>
                    <v-row no-gutters justify="end" class="pt-2" v-if="items.is_lotus === 'N'">
                      <v-btn small class="white--text" color="#27AB9C" rounded @click="CheckStockBeforeOpenModalPayment(items.payment_transaction)">
                        <v-img class="sizeIMGMobile" src="@/assets/payment.png"></v-img>
                        <span class="pt-1">{{$t('DetailOrderBuyer.Payment')}}</span>
                      </v-btn>
                    </v-row>
                  </v-row>
                </v-col>
                <v-col v-else>
                  <v-row class="pa-4" no-gutters style="background-color: #F9FAFD; border-radius: 8px;">
                    <v-col cols="6" md="6">
                      <span style="font-size: 16px; font-weight: 400;" ><v-avatar size="20" class="mr-2"><v-img src="@/assets/stepperNew/transport/successPayment.png"></v-img></v-avatar>{{$t('DetailOrderBuyer.PaymentSuccessfully')}} !</span><br>
                      <!-- <span style="font-size: 18px; font-weight: 400;" >ธนาคารที่ชำระเงิน</span> -->
                    </v-col>
                    <v-col cols="12" md="6" sm="6" :align="MobileSize ? 'start' : 'end'">
                      <span  style="font-size: 16px; font-weight: 700;" class="mt-5" v-if="items.installment_month === '0'">{{$t('DetailOrderBuyer.PaymentMethod')}} : {{ items.receipt[0].payType }}</span>
                      <span  style="font-size: 16px; font-weight: 700;" class="mt-5" v-else>{{$t('DetailOrderBuyer.PaymentMethod')}} : {{ items.receipt[0].payType }} ({{items.installment_month}}x @0%)</span><br>
                      <!-- <span class="fontHeadDetail">{{ bankName }}</span> -->
                    </v-col>
                  </v-row>
                </v-col>
                <!-- <v-col v-if="items.transaction_status === 'Success'" cols="12" class="mt-4">
                  <p class="fontSizeTitleMobile" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#E6F5F3" small text-color="#27AB9C">ชำระเงินสำเร็จ</v-chip></p>
                </v-col>
                <v-col v-else-if="items.transaction_status === 'Cash'" cols="12" class="mt-4">
                  <p class="fontSizeTitleMobile" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#E6F5F3" small text-color="#27AB9C">ชำระเงินสด</v-chip></p>
                </v-col>
                <v-col v-else-if="items.transaction_status === 'Cancel'" cols="12" class="mt-4">
                  <p class="fontSizeTitleMobile" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#F7D9D9" small text-color="#D1392B">ยกเลิกสินค้า</v-chip></p>
                </v-col>
                <v-col v-else-if="items.transaction_status === 'Fail'" cols="12" class="mt-4">
                  <p class="fontSizeTitleMobile" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#FFECB3" small text-color="#FFB300">ชำระเงินไม่สำเร็จ</v-chip></p>
                </v-col>
                <v-col v-else cols="12" class="mt-4">
                  <p class="fontSizeTitleMobile" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#E5EFFF" small text-color="#1B5DD6">ยังไม่ชำระเงิน</v-chip></p>
                </v-col> -->
                <!-- <v-row v-if="items.transaction_status === 'Success'" no-gutters>
                  <v-col cols="12" class="mt-4">
                    <span class="mr-2">คุณได้ชำระเงินเสร็จเรียบร้อยแล้ว ขอบคุณสำหรับการใช้บริการ</span>
                  </v-col>
                  <v-col cols="12" class="mt-5">
                    <span style="color: #27AB9C;"><b>&#8226; หลักฐานการชำระเงิน</b></span>
                  </v-col>
                  <v-col cols="11" class="mt-5">
                    <v-row no-gutters>
                      <v-col cols="6" md="3">
                        <span>รหัสการชำระเงิน : </span>
                      </v-col>
                      <v-col cols="6" md="9">
                        <span>{{ items.receipt[0].orderId }}</span>
                      </v-col>
                      <v-col cols="6" md="3" class="mt-2">
                        <span>จำนวนเงิน : </span>
                      </v-col>
                      <v-col cols="6" md="9" class="mt-2">
                        <span>{{ Number(items.receipt[0].TxnAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                      </v-col>
                      <v-col cols="6" md="3" class="mt-2">
                        <span>วันและเวลาที่ทำรายการ : </span>
                      </v-col>
                      <v-col cols="6" md="9" class="mt-2">
                        <span>{{new Date(items.receipt[0].created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })}}</span>
                      </v-col>
                      <v-col cols="6" md="3" class="mt-2">
                        <span>Ref : </span>
                      </v-col>
                      <v-col cols="6" md="9" class="mt-2">
                        <span>{{ items.receipt[0].orderIDRef }}</span>
                      </v-col>
                      <v-col cols="6" md="3" class="mt-2">
                        <span>ธนาคาร : </span>
                      </v-col>
                      <v-col cols="6" md="9" class="mt-2">
                        <span>{{ bankName }}</span>
                      </v-col>
                      <v-col cols="6" md="3" class="mt-2">
                        <span>รูปแบบการชำระเงิน :</span>
                      </v-col>
                      <v-col cols="6" md="9" class="mt-2">
                        <span class="" v-if="items.installment_month === '0'">{{ items.receipt[0].payType }}</span>
                        <span class="" v-else>{{ items.receipt[0].payType }} ({{items.installment_month}}x @0%)</span><br>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row> -->
                <v-row v-if="items.transaction_status === 'Cancel' && dataRole.role === 'purchaser'" no-gutters>
                  <v-col cols="12">
                    <span>{{ $t('DetailOrderBuyer.YourOrderHasBeenCancelled') }}</span>
                  </v-col>
                  <v-col cols="12" class="mt-5">
                    <span style="color: #27AB9C;"><b>&#8226; {{$t('DetailOrderBuyer.CancelledOrderDetails')}}</b></span>
                  </v-col>
                  <v-col cols="12" align="left">
                    <v-timeline dense>
                      <v-timeline-item v-for="(item,index) in items.approver_list" :key="index" fill-dot class="white--text mb-12" color="#27AB9C" small>
                        <template v-slot:icon>
                          <span>{{ index+1 }}</span>
                        </template>
                        <v-row no-gutters>
                          <v-col cols="12">
                            <span style="color: #27AB9C;"><b>{{$t('DetailOrderBuyer.Approver')}}</b></span>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: black;">สถานะ :</span>
                            <v-chip v-if="item.status === 'cancel'" class="ma-2" color="#F7D9D9" small text-color="#D1392B">{{$t('DetailOrderBuyer.CancelOrder')}}</v-chip>
                            <v-chip v-else class="ma-2" color="#E6F5F3" small text-color="#27AB9C">{{$t('DetailOrderBuyer.Approve')}}</v-chip>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: black;">{{$t('DetailOrderBuyer.Approver')}} : {{ item.approver_name }}({{ item.email }})</span>
                          </v-col>
                          <v-col v-if="item.time_approve === '-'" cols="12" class="mt-3">
                            <span style="color: black;">{{$t('DetailOrderBuyer.ApprovalDate')}} : {{ item.time_approve }}</span>
                          </v-col>
                          <v-col v-else cols="12" class="mt-3">
                            <span style="color: black;">{{$t('DetailOrderBuyer.ApprovalDate')}} : {{new Date(item.time_approve).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
                          </v-col>
                        </v-row>
                      </v-timeline-item>
                    </v-timeline>
                  </v-col>
                </v-row>
                <!-- <v-row v-else-if="items.transaction_status === 'Cancel' && dataRole.role === 'ext_buyer'" no-gutters>
                  <v-col cols="12" class="mt-4">
                    <span class="mr-2">คุณได้ทำการยกเลิกคำสั่งซื้อ หากต้องการซื้อสินค้าอีกครั้ง สามารถเข้าไปเลือกซื้อสินค้ากับเราได้เลย</span>
                  </v-col>
                </v-row> -->
                <v-row v-else-if="items.transaction_status === 'Fail'" no-gutters>
                  <v-col cols="12" class="mt-4" v-if="items.is_lotus === 'N'">
                    <span class="mr-2">{{ $t('DetailOrderBuyer.YourPaymentWasUnsuccessfulPleaseCheckYourPaymentAnd') }}</span>
                    <v-btn class="white--text" color="#27AB9C" small @click="GoToPayment()">{{$t('DetailOrderBuyer.Payment2')}}</v-btn>
                  </v-col>
                </v-row>
                <!-- <v-row v-else-if="items.transaction_status === 'Not Paid' && !items.transaction_status === 'Cash'" no-gutters> -->
                <v-row v-else-if="items.transaction_status === 'Not Paid'" no-gutters>
                  <v-col cols="12" class="mt-4" v-if="items.tracking[0].status_tracking === 'Pick Up' || items.tracking[0].status_tracking === 'Not Paid'">
                    <span class="mr-2">{{$t('DetailOrderBuyer.YouHaveNotCompletedPayment')}}</span>
                    <!-- <v-btn class="white--text" color="#27AB9C" small @click="GoToPayment()">ชำระเงิน</v-btn> -->
                  </v-col>
                </v-row>
              </v-row>
            </v-container>
          </v-col>
          <v-col v-if="items.inet_relation_ship.length !== 0" cols="12" class="mt-2">
            <v-card outlined>
              <v-container grid-list-xs>
                <v-col>
                  <v-row class="pl-4 pr-3 pb-2 align-baseline">
                    <v-col cols="12" class="pl-0 pb-1">
                      <span style="font-size: 16px; font-weight: 700;">{{$t('DetailOrderBuyer.DiscountCoupon')}}</span>
                    </v-col>
                    <v-col cols="12" class="pl-0 pt-0">
                      <span style="font-size: 14px; font-weight: 700;">{{$t('DetailOrderBuyer.ListOf')}} {{items.inet_relation_ship.length}} {{$t('DetailOrderBuyer.Employees')}}</span>
                    </v-col>
                  </v-row>
                  <v-card class="pa-2 py-4 custom-scroll" elevation="0" style="background: #F9FAFD; border-radius: 20px;  max-height: 435px; overflow-y: auto; overflow-x: hidden;">
                    <v-row class="pb-1 pt-2"  v-for="(item, index) in items.inet_relation_ship" :key="index">
                      <v-col cols="12" class="px-4 py-0">
                        <v-col cols="12" class="pa-0">
                          <span style="font-size: 14px; font-weight: 400;">{{index + 1}}. {{$t('DetailOrderBuyer.Department')}}: <b>{{item.team}}</b></span>
                        </v-col>
                        <v-col cols="12" class="pa-0 pl-4">
                          <span style="font-size: 14px; font-weight: 400;">{{$t('DetailOrderBuyer.Company')}}: <b>{{item.company}}</b></span>
                        </v-col>
                        <v-col cols="12" class="pa-0 pl-4">
                          <span style="font-size: 14px; font-weight: 400;"><b>{{item.employee_one_id + ' ' + item.first_name_th + ' ' + item.last_name_th}} ({{ item.code }})</b></span>
                        </v-col>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>
              </v-container>
            </v-card>
          </v-col>
        </v-row>
      </v-card>
    </v-container>
    <!-- Desktop ipadPro -->
    <v-container v-else grid-list-xl v-for="(order, index) in items.data_list" :key="index">
      <v-row justify="start" class="my-4">
        <v-row no-gutters class="pa-4">
          <v-col class="pa-0" cols="6" md="6">
            <span style="font-weight: 700; font-size: 24px;">
              <v-icon color="#27AB9C" @click="backtoPOBuyer()">mdi-chevron-left</v-icon>{{$t('DetailOrderBuyer.OrderDetails')}}
            </span>
          </v-col>
          <!-- <v-col class="d-flex justify-end" style="gap: 1vw;" v-if="items.transportation_status === 'อยู่ระหว่างการตรวจสอบและยอมรับสินค้า'">
            <div>
              <v-btn color="#27AB9C" style="color: #fff;">ได้รับสินค้าแล้ว</v-btn>
            </div>
            <div>
              <v-btn color="#cdcdcd" @click="gotoRefund()">คืนเงิน/คืนสินค้า</v-btn>
            </div>
          </v-col> -->
          <!-- ----------- -->
          <!-- ----------- -->
          <!-- ----------- -->
          <!-- ต้องให้ดักตอน status ว่าง -->
          <v-col class="pa-0" cols="6" md="6" align="end" v-if="(trackingStatus === 'Not Sent' || trackingStatus === 'Not Paid' || trackingStatus === 'Cancel by buyer' || trackingStatus === 'Pick Up') && (items.tracking[0].status_payment === 'Success' || items.tracking[0].status_payment === 'Not Paid' || items.tracking[0].status_payment === 'Cancel') && (statusCancel === '' || statusCancel !== '') && items.order_mobilyst_no === '-' && items.is_lotus === 'N'">
            <v-btn v-if="(trackingStatus === 'Not Paid' || trackingStatus === 'Pick Up') && items.tracking[0].status_payment === 'Not Paid' && statusCancel === '' && items.is_lotus === 'N'" @click="modalInputReason = true" rounded height="40" width="135" color="red" outlined class="mr-4">{{$t('DetailOrderBuyer.CancelOrder')}}</v-btn>
            <v-btn v-if="(trackingStatus === 'Not Sent' || (trackingStatus === 'Pick Up' && items.transportation_status !== 'การจัดส่งสำเร็จ')) && items.tracking[0].status_payment === 'Success' && (statusCancel === '' || statusCancel === 'reject') && items.order_mobilyst_no === '-' && items.is_lotus === 'N'" @click="getAccountBank()" rounded outlined color="red" class="mr-4">{{$t('DetailOrderBuyer.CancelOrder')}}</v-btn>
            <v-btn
              v-if="(trackingStatus === 'Cancel by buyer' || trackingStatus === 'Not Sent' || trackingStatus === 'Pick Up' || trackingStatus === 'Not Received') && (items.tracking[0].status_payment === 'Success' || items.tracking[0].status_payment === 'Cancel') && statusCancel === 'reject' && items.order_mobilyst_no === '-' && items.is_lotus === 'N'"
              @click="openmodalInputReasonDetail()"
              rounded
              height="40"
              width="135"
              color="#27AB9C"
              outlined
              class="mr-4">{{$t('DetailOrderBuyer.TrackCancellationRequest')}}
            </v-btn>
            <!-- <v-btn
              v-if="(trackingStatus === 'Not Sent' || trackingStatus === 'Pick Up') && items.tracking[0].status_payment === 'Success' && (statusCancel === '' || statusCancel === 'reject') && items.order_mobilyst_no === '-' && items.is_lotus === 'N'"
              @click="getAccountBank()"
              rounded height="40"
              width="135"
              color="#27AB9C"
              outlined
              class="mr-4">ยกเลิกคำสั่งซื้อ</v-btn>
            <v-btn
              v-if="(trackingStatus === 'Cancel by buyer' || trackingStatus === 'Not Sent' || trackingStatus === 'Pick Up') && (items.tracking[0].status_payment === 'Success' || items.tracking[0].status_payment === 'Cancel') && statusCancel !== '' && items.order_mobilyst_no === '-' && items.is_lotus === 'N'"
              @click="openmodalInputReasonDetail()"
              rounded
              height="40"
              width="135"
              color="#27AB9C"
              outlined
              class="mr-4">ติดตามคำขอยกเลิก</v-btn> -->
          </v-col>
          <!-- <v-col class="pa-0" cols="6" md="6" align="end" v-if="(trackingStatus === 'Cancel by buyer' || trackingStatus === 'Not Paid') && items.shipping_type === 'online' && items.tracking[0].status_payment === 'Success' && detailCancel.status !== ''">
            <v-btn @click="modalInputReasonDetail = true" rounded height="40" width="135" color="#27AB9C" outlined class="mr-4">ยกเลิกคำสั่งซื้อ</v-btn>
          </v-col> -->
          <!-- {{statusTransactionText(items.transaction_status, items.transportation_status)}} -->
          <v-col class="pa-0" cols="6" md="6" align="end" v-if="trackingStatus === 'Not Received' && items.shipping_type === 'online' && statusTransactionText(items.transaction_status, items.transportation_status) !== 'กำลังจัดส่งสินค้า'">
            <v-btn v-if="trackingStatus === 'Not Received' && items.shipping_type === 'online'" @click="acceptProduct(order)" rounded outlined color="#27AB9C"><b class="buttonFontSize">{{$t('DetailOrderBuyer.ItemReceived')}}</b></v-btn>
            <v-btn v-if="dataRole.role === 'ext_buyer' && items.transaction_status === 'Success' && trackingStatus === 'Not Received'" class="white--text px-5 ml-2" @click="addtoCart()" color="primary" rounded>
              <v-icon>mdi-shopping-outline</v-icon>{{$t('DetailOrderBuyer.BuyAgain')}}
            </v-btn>
            <v-btn
              v-if="(trackingStatus === 'Cancel by buyer' || trackingStatus === 'Not Sent' || trackingStatus === 'Pick Up') && (items.tracking[0].status_payment === 'Success' || items.tracking[0].status_payment === 'Cancel') && statusCancel === 'reject' && items.order_mobilyst_no === '-' && items.is_lotus === 'N'"
              @click="openmodalInputReasonDetail()"
              rounded
              height="40"
              width="135"
              color="#27AB9C"
              outlined
              class="mr-4">{{$t('DetailOrderBuyer.TrackCancellationRequest')}}
            </v-btn>
          </v-col>
          <v-col class="pa-0" cols="6" md="6" align="end" v-else-if="trackingStatus === 'Received' && items.shipping_type === 'online' && statusTransactionText(items.transaction_status, items.transportation_status) === 'จัดส่งสินค้าสำเร็จ'">
            <v-btn :block="MobileSize ? true : false" @click.stop="reviewProduct(items.payment_transaction)" rounded outlined color="#3EC6B6" class="mr-4"><v-icon small class="pr-1">mdi-star</v-icon>{{$t('DetailOrderBuyer.ProductReview')}}</v-btn>
            <v-btn v-if="dataRole.role === 'ext_buyer' && items.transaction_status === 'Success'" class="white--text px-5 ml-2" @click="addtoCart()" color="primary" rounded>
              <v-icon>mdi-shopping-outline</v-icon>{{$t('DetailOrderBuyer.BuyAgain')}}
            </v-btn>
          </v-col>
          <!-- สถานะขนส่ง -->
          <v-col cols="12">
            <v-row no-gutters v-if="productNormal.length === 0 && productService.length !== 0 && trackingStatus !== 'Pick Up'">
              <v-col>
                <v-row no-gutters v-if="trackingStatus === 'Not Paid'" class="mb-5">
                  <v-col cols="12" align="center" :class="IpadProSize ? 'px-0 pb-5' : 'px-10 pb-5'">
                    <v-img src="@/assets/stepperNew/transport/newui-step1.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="5"></v-img>
                  </v-col>
                  <!-- <v-col cols="3" align="center">
                    <span class="fontInactive">ที่ต้องได้รับ</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-1">
                    <span class="fontActive">คำสั่งซื้อใหม่</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-1">
                    <span class="fontInactive"><b>ชำระเงินแล้ว</b></span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-8">
                    <span class="fontInactive">ที่ต้องได้รับ</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col> -->
                  <!-- <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(เหลือเวลาชำระเงินถึง {{new Date(items.last_payment_date_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })}})</span>
                  </v-col> -->
                </v-row>
                <v-row no-gutters v-else class="mb-5">
                  <v-col cols="12" align="center" :class="IpadProSize ? 'px-0 pb-5' : 'px-10 pb-5'">
                    <v-img src="@/assets/stepperNew/service/step2.svg" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <!-- <span class="fontInactive">ที่ต้องได้รับ</span> -->
                  </v-col>
                  <v-col cols="3" align="center" class="pl-1">
                    <span class="fontPass">{{$t('DetailOrderBuyer.FillInData')}}</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-1">
                    <span class="fontActive"><b>{{$t('DetailOrderBuyer.PaymentSuccessful')}}</b></span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-8">
                    <!-- <span class="fontInactive">ที่ต้องได้รับ</span> -->
                  </v-col>
                  <v-col cols="3" align="center">
                    <!-- <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span> -->
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PaymentDate')}} {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <v-row no-gutters v-else>
              <!-- สถานะการสั่งซื้อ -->
              <v-col cols="12" class="px-0">
                <v-row no-gutters class="pt-5 pb-0 pl-5">
                  <v-col cols="4">
                    <v-row no-gutters>
                      <div class="d-flex">
                        <v-row no-gutters class="pr-2">
                          <v-img class="sizeIMG" src="@/assets/ICON/headerDetailOrderBuyer.png"></v-img>
                        </v-row>
                        <span class="fontHeadDetail">{{$t('DetailOrderBuyer.OrderStatus')}}</span>
                      </div>
                    </v-row>
                  </v-col>
                  <v-col cols="8" class="d-flex align-center justify-end" v-if="items.transaction_status">
                    <span>{{$t('DetailOrderBuyer.OrderNumber')}} : {{order.order_number}} |
                      <v-chip :color="statusTransactionChipColor(items.transaction_status, items.transportation_status)">
                        <span :style="`color: ${statusTransactionTextColor(items.transaction_status, items.transportation_status)}`">{{statusTransactionText(items.transaction_status, items.transportation_status)}}</span>
                      </v-chip>
                    </span>
                  </v-col>
                  <!-- v-if="trackingStatus === 'Not Received' && typeShipping !== 'front' && checkAcceptProduct[0].status === 'waiting_accept'" -->
                </v-row>
              </v-col>
              <v-col>
                <v-row no-gutters v-if="trackingStatus === 'Not Paid'" class="mb-5">
                  <v-col cols="12" align="center" :class="IpadProSize ? 'px-0 pb-5' : 'px-10 pb-5'">
                    <v-img src="@/assets/stepperNew/transport/newui-step1.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="5"></v-img>
                  </v-col>
                  <v-col cols="12" class="d-flex justify-space-around">
                    <div style="width: 120px;">
                      <span style="color: #269afd; font-weight: bold; display: flex; justify-content: center;">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                      <span class="fontSizeStepOrder">{{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }}</span>
                    </div>
                    <div style="width: 120px;">
                      <span style="color: #cccccc; font-weight: bold; display: flex; justify-content: center;">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                    </div>
                    <div style="width: 120px;">
                      <span style="color: #cccccc; font-weight: bold; display: flex; justify-content: center;">{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                    </div>
                    <div style="width: 120px;">
                      <span style="color: #cccccc; font-weight: bold; display: flex; justify-content: center;">{{$t('DetailOrderBuyer.OutForDelivery')}}</span>
                    </div>
                    <div style="width: 120px;">
                      <span style="color: #cccccc; font-weight: bold; display: flex; justify-content: center;">{{$t('DetailOrderBuyer.DeliverySuccessful')}}</span>
                    </div>
                  </v-col>
                  <!-- <v-col cols="3" :align="IpadProSize ? 'start' : 'center'">
                    <span :class="IpadProSize ? 'fontActive pl-4' : 'fontActive'">คำสั่งซื้อใหม่</span>
                  </v-col>
                  <v-col cols="3" align="center" :class="IpadProSize ? 'pl-0' : 'pl-1'">
                    <span class="fontInactive"><b>ชำระเงินแล้ว</b></span>
                  </v-col>
                  <v-col cols="3" align="center" :class="IpadProSize ? 'pl-4' : 'pl-1'">
                    <span class="fontInactive">จัดส่งแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" :class="IpadProSize ? 'pl-14' : 'pl-8'">
                    <span class="fontInactive">ที่ต้องได้รับ</span>
                  </v-col>
                  <v-col cols="3" :align="IpadProSize ? 'start' : 'center'">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col> -->
                  <!-- <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(เหลือเวลาชำระเงินถึง {{new Date(items.last_payment_date_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })}})</span>
                  </v-col> -->
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Not Sent' && items.transportation_status === 'อยู่ระหว่างดำเนินการ' && items.transaction_status === 'Success'" class="mb-5">
                  <v-col cols="12" align="center" :class="IpadProSize ? 'px-6 pb-5' : 'px-10 pb-5'">
                    <v-img src="@/assets/stepperNew/transport/newui-step2.png" :max-height="IpadProSize ? '100vh' : '1000px'" height="100%" width="100%" contain aspect-ratio="5"></v-img>
                  </v-col>
                  <v-col cols="12" class="d-flex justify-space-around">
                    <div style="width: 120px;">
                      <span style="color: #000000; font-weight: bold; display: flex; justify-content: center;">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                      <span class="fontSizeStepOrder">{{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }}</span>
                    </div>
                    <div>
                      <span style="color: #269afd; font-weight: bold; display: flex; justify-content: center;">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                      <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PaymentDate')}} {{ dateCreateOrderStep2 }})</span>
                    </div>
                    <div style="width: 120px;">
                      <span style="color: #cccccc; font-weight: bold; display: flex; justify-content: center;">{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                    </div>
                    <div style="width: 120px;">
                      <span style="color: #cccccc; font-weight: bold; display: flex; justify-content: center;">{{$t('DetailOrderBuyer.OutForDelivery')}}</span>
                    </div>
                    <div style="width: 120px;">
                      <span style="color: #cccccc; font-weight: bold; display: flex; justify-content: center;">{{$t('DetailOrderBuyer.DeliverySuccessful')}}</span>
                    </div>
                  </v-col>
                  <!-- <v-col cols="3" :align="IpadProSize ? 'start' : 'center'">
                    <span :class="IpadProSize ? 'fontPass pl-2' : 'fontPass'">กรอกข้อมูลทั่วไป</span>
                  </v-col>
                  <v-col cols="3" align="center"> -->
                    <!-- <span>คำสั่งซื้อที่ชำระเงินแล้ว ({{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2})}} บาท)</span> -->
                    <!-- <span class="fontActive">ชำระเงินแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" :class="IpadProSize ? 'pl-6' : 'pl-4'">
                    <span class="fontInactive">จัดส่งแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" :class="IpadProSize ? 'pl-15' : 'pl-8'">
                    <span class="fontInactive">ที่ต้องได้รับ</span>
                  </v-col>
                  <v-col cols="3" :align="IpadProSize ? 'start' : 'center'">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
                  </v-col> -->
                  <!-- <v-col cols="12" class="five-step-transport">
                    <div align="center">
                      <span>คำสั่งซื้อใหม่</span>
                      <br/>
                      <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                    </div>
                    <div align="center" class="">
                      <span>ชำระเงินแล้ว</span>
                      <br/>
                      <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
                    </div>
                    <span class="mx-5">เตรียมจัดส่ง</span>
                    <span class="mx-5 ml-8">กำลังจัดส่ง</span>
                    <span class="mr-10 ml-10">จัดส่งสำเร็จ</span>
                  </v-col> -->
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Not Sent' && items.transportation_status === 'พัสดุอยู่ระหว่างการขนส่ง'" class="mb-5">
                  <v-col cols="12" align="center" :class="IpadProSize ? 'px-6 pb-5' : 'px-10 pb-5'">
                    <v-img src="@/assets/stepperNew/transport/newui-step3.png" :max-height="IpadProSize ? '100vh' : '1000px'" height="100%" width="100%" contain aspect-ratio="5"></v-img>
                  </v-col>
                  <v-col cols="12" class="five-step-transport">
                    <div align="center">
                      <span style="color: #000000; font-weight: bold;">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                    </div>
                    <div align="center"  class="">
                      <span style="color: #000000; font-weight: bold;">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PaymentDate')}} {{ dateCreateOrderStep2 }})</span>
                    </div>
                    <div>
                      <span class="mx-5" style="color: #269afd; font-weight: bold;">{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PreparingShipmentDate')}} {{ dateCreateOrderStepNew }})</span>
                    </div>
                    <!-- <div>
                      <span class="mx-5" style="color: #269afd; font-weight: bold;">กำลังจัดส่ง</span>
                      <br/>
                      <span class="fontSizeStepOrder">(วันที่กำลังจัดส่ง {{ dateCreateOrderStep3 }})</span>
                    </div> -->
                    <div>
                      <span class="mx-5" style="color: #cccccc; font-weight: bold;">{{$t('DetailOrderBuyer.OutForDelivery')}}</span>
                    </div>
                    <span class="mr-10 ml-10" style="color: #cccccc; font-weight: bold;">{{$t('DetailOrderBuyer.DeliverySuccessful')}}</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Not Sent' && items.transportation_status === 'รอขนส่งเข้ารับพัสดุ' || items.transportation_status === 'ผู้ส่งกำลังเตรียมพัสดุ' || items.transportation_status === 'ผู้ส่งกำลังเตรียมพัสดุ' " class="mb-5">
                  <v-col cols="12" align="center" :class="IpadProSize ? 'px-6 pb-5' : 'px-10 pb-5'">
                    <v-img src="@/assets/stepperNew/transport/newui-step3.png" :max-height="IpadProSize ? '100vh' : '1000px'" height="100%" width="100%" contain aspect-ratio="5"></v-img>
                  </v-col>
                  <!-- <v-col cols="3" :align="IpadProSize ? 'start' : 'center'">
                    <span :class="IpadProSize ? 'fontPass pl-2' : 'fontPass'">กรอกข้อมูลทั่วไป</span>
                  </v-col>
                  <v-col cols="3" align="center"> -->
                    <!-- <span>คำสั่งซื้อที่ชำระเงินแล้ว ({{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2})}} บาท)</span> -->
                    <!-- <span class="fontActive">ชำระเงินแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" :class="IpadProSize ? 'pl-6' : 'pl-4'">
                    <span class="fontInactive">จัดส่งแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" :class="IpadProSize ? 'pl-15' : 'pl-8'">
                    <span class="fontInactive">ที่ต้องได้รับ</span>
                  </v-col>
                  <v-col cols="3" :align="IpadProSize ? 'start' : 'center'">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
                  </v-col> -->
                  <v-col cols="12" class="five-step-transport">
                    <div align="center">
                      <span style="font-weight: bold;">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                    </div>
                    <div align="center" class="">
                      <span style="font-weight: bold;">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PaymentDate')}} {{ dateCreateOrderStep2 }})</span>
                    </div>
                    <div>
                      <span class="mx-5" style="color: #269afd; font-weight: bold;">{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                      <br>
                      <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PreparingShipmentDate')}} {{ dateCreateOrderStepNew }})</span>
                    </div>
                    <span class="mx-5 ml-8" style="color: #cccccc; font-weight: bold;">{{$t('DetailOrderBuyer.OutForDelivery')}}</span>
                    <span class="mr-10 ml-10" style="color: #cccccc; font-weight: bold;">{{$t('DetailOrderBuyer.DeliverySuccessful')}}</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Sent'" class="mb-5">
                  <v-col cols="12" align="center" :class="IpadProSize ? 'px-0 pb-5' : 'px-10 pb-5'">
                    <v-img src="@/assets/stepper3.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontPass">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontPass">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontInactive">{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontInactive">{{$t('DetailOrderBuyer.OutForDelivery')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PaymentDate')}} {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PreparingShipmentDate')}} {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Not Received' && items.transportation_status === 'พัสดุอยู่ระหว่างการขนส่ง'" class="mb-5">
                  <v-col cols="12" align="center" :class="IpadProSize ? 'px-0 pb-5' : 'px-10 pb-5'">
                    <v-img src="@/assets/stepperNew/transport/newui-step4.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="6"></v-img>
                  </v-col>
                  <v-col cols="12" class="five-step-transport">
                    <div align="center">
                      <span style="font-weight: bold;">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder" style="font-weight: bold;">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                    </div>
                    <div align="center" class="mr-4">
                      <span style="font-weight: bold;">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder" style="font-weight: bold;">({{$t('DetailOrderBuyer.PaymentDate')}} {{ dateCreateOrderStep2 }})</span>
                    </div>
                    <div align="center" class="mr-4">
                      <span style="font-weight: bold; color: #000000;">{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder" style="font-weight: bold;">({{$t('DetailOrderBuyer.Date')}} {{ dateCreateOrderStepNew }})</span>
                    </div>
                    <div align="center" class="mr-4">
                      <span style="font-weight: bold; color: #269afd;">กำลังจัดส่ง</span>
                      <br/>
                      <span class="fontSizeStepOrder" style="font-weight: bold;">({{$t('DetailOrderBuyer.Date')}} {{ dateCreateOrderStep3 }})</span>
                    </div>
                    <!-- <span class="mx-5 mr-10" style="font-weight: bold; ">เตรียมจัดส่ง</span> -->
                    <!-- <span class="mx-5 " style="font-weight: bold; color: #cccccc;">กำลังจัดส่ง</span> -->
                    <span class="mr-5 ml-10" style="font-weight: bold; color: #cccccc;">{{$t('DetailOrderBuyer.DeliverySuccessful')}}</span>
                  </v-col>
                  <!-- <v-col cols="3" align="center">
                    <span class="fontPass">กรอกข้อมูลทั่วไป</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pr-2">
                    <span class="fontPass">ชำระเงินแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-1">
                    <span class="fontActive">จัดส่งแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-8">
                    <span class="fontInactive">ที่ต้องได้รับ</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ทำการจัดส่ง {{ dateCreateOrderStep3 }})</span>
                  </v-col> -->
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Not Received' && items.transportation_status === 'รอการตรวจสอบและยอมรับสินค้า'" class="mb-5">
                  <v-col cols="12" align="center" :class="IpadProSize ? 'px-0 pb-5' : 'px-10 pb-5'">
                    <v-img src="@/assets/stepperNew/transport/newui-step5.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="5"></v-img>
                  </v-col>
                  <v-col cols="12" class="five-step-transport">
                    <div align="center">
                      <span style="font-weight: bold;">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder" style="font-weight: bold;">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                    </div>
                    <div align="center" class="">
                      <span style="font-weight: bold;">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder" style="font-weight: bold;">({{$t('DetailOrderBuyer.PaymentDate')}} {{ dateCreateOrderStep2 }})</span>
                    </div>
                    <div align="center" class="">
                      <span style="font-weight: bold;">{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder" style="font-weight: bold;">({{$t('DetailOrderBuyer.Date')}} {{ dateCreateOrderStepNew }})</span>
                    </div>
                    <div align="center" class="">
                      <span style="font-weight: bold;">{{$t('DetailOrderBuyer.OutForDelivery')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder" style="font-weight: bold;">({{$t('DetailOrderBuyer.Date')}} {{ dateCreateOrderStep3 }})</span>
                    </div>
                    <div align="center" class="px-3 pr-5">
                      <span style="font-weight: bold; color: #269afd;">{{$t('DetailOrderBuyer.DeliverySuccessful')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder" style="font-weight: bold; ">({{ items.tracking[0].time_cancel_4 === '-' ? $t('DetailOrderBuyer.WaitingConfirmation') : `(${$t('DetailOrderBuyer.Date')}` + dateCreateOrderStep4 }})</span>
                    </div>
                    <!-- <span class="mx-5 mr-10" style="font-weight: bold; ">เตรียมจัดส่ง</span>
                    <span class="mx-5 ml-8" style="font-weight: bold;">กำลังจัดส่ง</span>
                    <span class="mr-5 ml-10" style="font-weight: bold; color: #269afd;">จัดส่งสำเร็จ</span> -->
                  </v-col>
                  <!-- <v-col cols="3" align="center">
                    <span class="fontPass">กรอกข้อมูลทั่วไป</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pr-2">
                    <span class="fontPass">ชำระเงินแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-1">
                    <span class="fontActive">จัดส่งแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-8">
                    <span class="fontInactive">ที่ต้องได้รับ</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ทำการจัดส่ง {{ dateCreateOrderStep3 }})</span>
                  </v-col> -->
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Received'" class="mb-5">
                  <v-col cols="12" align="center" :class="IpadProSize ? 'px-0 pb-5' : 'px-10 pb-5'">
                    <v-img src="@/assets/stepperNew/transport/newui-step5.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="5"></v-img>
                  </v-col>
                  <v-col cols="12" class="five-step-transport">
                    <div align="center">
                      <span style="font-weight: bold;">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder" style="font-weight: bold;">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                    </div>
                    <div align="center" class="">
                      <span style="font-weight: bold;">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder" style="font-weight: bold;">({{$t('DetailOrderBuyer.PaymentDate')}} {{ dateCreateOrderStep2 }})</span>
                    </div>
                    <div align="center" class="">
                      <span style="font-weight: bold;">{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder" style="font-weight: bold;">({{$t('DetailOrderBuyer.Date')}} {{ dateCreateOrderStepNew }})</span>
                    </div>
                    <div align="center" class="">
                      <span style="font-weight: bold;">{{$t('DetailOrderBuyer.OutForDelivery')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder" style="font-weight: bold;">({{$t('DetailOrderBuyer.Date')}} {{ dateCreateOrderStep3 }})</span>
                    </div>
                    <div align="center" class="">
                      <span style="font-weight: bold;">{{$t('DetailOrderBuyer.DeliverySuccessful')}}</span>
                      <br/>
                      <span class="fontSizeStepOrder" style="font-weight: bold;">({{$t('DetailOrderBuyer.Date')}} {{ dateCreateOrderStep4 }})</span>
                    </div>
                    <!-- <span class="mx-5 mr-10" style="font-weight: bold; ">เตรียมจัดส่ง</span>
                    <span class="mx-5 ml-8" style="font-weight: bold;">กำลังจัดส่ง</span>
                    <span class="mr-5 ml-10" style="font-weight: bold; color: #269afd;">จัดส่งสำเร็จ</span> -->
                  </v-col>
                  <!-- <v-col cols="3" :align="IpadProSize ? 'start' : 'center'" class="pr-3">
                    <span :class="IpadProSize ? 'fontPass pl-2' : 'fontPass'">กรอกข้อมูลทั่วไป</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pr-4">
                    <span class="fontPass">ชำระเงินแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-1">
                    <span class="fontPass">จัดส่งแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" :class="IpadProSize ? 'pl-14' : 'pl-4'">
                    <span class="fontActive">ที่ต้องได้รับ</span>
                  </v-col>
                  <v-col cols="3" :align="IpadProSize ? 'start' : 'center'">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ทำการจัดส่ง {{ dateCreateOrderStep3 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่สินค้าถึง {{ dateCreateOrderStep4 }})</span>
                  </v-col> -->
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'waiting_refund' || trackingStatus === 'refund'" class="mb-5">
                  <v-col cols="12" align="center">
                    <v-img src="@/assets/stepper5.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>{{$t('DetailOrderBuyer.NewOrder')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>{{$t('DetailOrderBuyer.OrdersWithCompletedPayment')}} ({{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2})}} บาท)</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>{{$t('DetailOrderBuyer.OrderHasBeenShipped')}} ({{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2})}} บาท)</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>{{$t('DetailOrderBuyer.BuyerReviewedAndRequestedReturn')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PaymentDate')}} {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.ShippingDate')}} {{ dateCreateOrderStep3 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.ReturnDate')}} {{ dateCreateOrderStep4 }})</span>
                  </v-col>
                </v-row>
                <!-- <v-row no-gutters v-else-if="trackingStatus === 'Success'" class="mb-5">
                  <v-col cols="12" align="center">
                    <v-img src="@/assets/stepper5.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>คำสั่งซื้อใหม่</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>คำสั่งซื้อที่ชำระเงินแล้ว ({{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2})}} บาท)</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontActive">ที่ต้องจัดส่ง</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontInactive">ที่ต้องได้รับ</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                </v-row> -->
                <!-- <v-row no-gutters v-else-if="trackingStatus === 'Not Sent' && items.transaction_status === 'Waiting_Cancel' && items.transportation_status === 'อยู่ระหว่างดำเนินการ' && statusCancel === 'waiting'" class="mb-5"> -->
                <v-row no-gutters v-else-if="(trackingStatus === 'Not Sent' || trackingStatus === 'Pick Up') && (items.transaction_status === 'Waiting_Cancel' || items.transaction_status === 'Cancel') && (items.transportation_status === 'อยู่ระหว่างดำเนินการ' || items.transportation_status === 'ยกเลิกคำสั่งซื้อ') && (statusCancel === 'waiting' || statusCancel === '')" class="mb-5">
                  <v-col cols="12" align="center" class="px-12">
                    <v-img src="@/assets/stepperNew/transport/refundModal-step2.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="6"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #000000">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #269AFD">{{$t('DetailOrderBuyer.CancelOrder')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #cccccc">{{$t('DetailOrderBuyer.WaitingForRefundApproval')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #cccccc">{{$t('DetailOrderBuyer.RefundSuccessful')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PaymentDate')}} {{ items.tracking[0].time_cancel_2 === '-' ? dateCreateOrderStep2 : dateCreateOrderCancel2 }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="(trackingStatus === 'Cancel by buyer' || trackingStatus === 'Pick Up') && items.transaction_status === 'Cancel' && items.transportation_status === 'ยกเลิกคำสั่งซื้อ' && statusCancel === 'approve' && items.tracking[0].time_cancel_4 === '-'" class="mb-5">
                  <v-col cols="12" align="center" class="px-12">
                    <v-img src="@/assets/stepperNew/transport/refundModal-step3.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="6"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #000000">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #000000">{{$t('DetailOrderBuyer.CancelOrder')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #269AFD">{{$t('DetailOrderBuyer.WaitingForRefundApproval')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #cccccc">{{$t('DetailOrderBuyer.RefundSuccessful')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.CancelDate')}} {{  dateCreateOrderCancel2 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.Date')}} {{ dateCreateOrderCancel }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="(trackingStatus === 'Cancel by buyer' || trackingStatus === 'Pick Up') && items.transaction_status === 'Cancel' && items.transportation_status === 'ยกเลิกคำสั่งซื้อ' && statusCancel === 'approve' && items.tracking[0].time_cancel_4 !== '-'" class="mb-5">
                  <v-col cols="12" align="center" class="px-12">
                    <v-img src="@/assets/stepperNew/transport/refundModal-step4.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="6"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #000000">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #000000">{{$t('DetailOrderBuyer.CancelOrder')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #000000">{{$t('DetailOrderBuyer.WaitingForRefundApproval')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span style="font-weight: bold; color: #269AFD">{{$t('DetailOrderBuyer.RefundSuccessful')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.CancelDate')}} {{  dateCreateOrderCancel2 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.Date')}} {{ dateCreateOrderCancel }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.RefundDate')}} {{ dateCreateOrderCancel4 }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Cancel by approver'" class="mb-5">
                  <v-col cols="12" align="center">
                    <v-img src="@/assets/stepper7.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="6" align="left" :style="{'padding-left': IpadProSize ? '50px' : '80px'}">
                    <span>{{$t('DetailOrderBuyer.NewOrder')}}</span>
                  </v-col>
                  <v-col cols="6" :style="{'padding-right': IpadProSize ? '40px' : '75px'}">
                    <span>{{$t('DetailOrderBuyer.CancelOrder')}}</span>
                  </v-col>
                  <v-col cols="6" align="left" :style="{'padding-left': IpadProSize ? '10px' : '50px'}">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="6" align="right" :style="{'padding-right': IpadProSize ? '10px' : '50px'}">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.CancelDate')}} {{ dateCreateOrderCancel }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-if="trackingStatus === 'Cancel'" class="mb-5">
                  <v-col cols="12" align="center">
                    <v-img src="@/assets/stepper8.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="6" align="left" :style="{'padding-left': IpadProSize ? '50px' : '80px'}">
                    <span>{{$t('DetailOrderBuyer.NewOrder')}}</span>
                  </v-col>
                  <v-col cols="6" align="right" :style="{'padding-right': IpadProSize ? '40px' : '75px'}">
                    <span>{{$t('DetailOrderBuyer.CancelOrder')}}</span>
                  </v-col>
                  <v-col cols="6" align="left" :style="{'padding-left': IpadProSize ? '10px' : '50px'}">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="6" align="right" :style="{'padding-right': IpadProSize ? '10px' : '50px'}">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.CancelDate')}} {{ dateCreateOrderCancel }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Pick Up' && trackingPayment === 'Cash'" class="mb-5">
                  <v-col cols="12" align="center">
                    <v-img src="@/assets/stepper-onshop.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>{{$t('DetailOrderBuyer.EnterInformation')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>{{$t('DetailOrderBuyer.CashPayment')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>{{$t('DetailOrderBuyer.WaitingForPickup')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>{{$t('DetailOrderBuyer.ReceivedSuccessfully')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Pick Up' && trackingPayment === 'Success' && trackingStatusFront !== 'Received'" class="mb-5">
                  <v-col cols="12" align="center" :class="IpadProSize ? 'px-0 pb-5' : 'px-10 pb-5'">
                    <v-img src="@/assets/stepperNew/receive/step3.svg" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" :align="IpadProSize ? 'start' : 'center'" >
                    <span :class="IpadProSize ? 'fontPass pl-4' : 'fontPass'">{{$t('DetailOrderBuyer.EnterInformation')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontPass">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-5">
                    <span class="fontPass">{{$t('DetailOrderBuyer.WaitingForPickup')}}</span>
                  </v-col>
                  <v-col cols="3" align="center" :class="IpadProSize ? 'pl-14' : 'pl-8'">
                    <span class="fontInactive">{{$t('DetailOrderBuyer.ReceivedSuccessfully')}}</span>
                  </v-col>
                  <v-col cols="3" :align="IpadProSize ? 'start' : 'center'">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PaymentDate')}} {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PickupDate')}} {{ dataReceived }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Pick Up' && trackingPayment === 'Success' && trackingStatusFront === 'Received'" class="mb-5">
                  <v-col cols="12" align="center" :class="IpadProSize ? 'px-0 pb-5' : 'px-10 pb-5'">
                    <v-img src="@/assets/stepperNew/receive/step4.svg" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" :align="IpadProSize ? 'start' : 'center'" >
                    <span :class="IpadProSize ? 'fontPass pl-4' : 'fontPass'">{{$t('DetailOrderBuyer.EnterInformation')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontPass">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-5">
                    <span class="fontPass">{{$t('DetailOrderBuyer.WaitingForPickup')}}</span>
                  </v-col>
                  <v-col cols="3" align="center" :class="IpadProSize ? 'pl-14' : 'pl-8'">
                    <span class="fontActive">{{$t('DetailOrderBuyer.ReceivedSuccessfully')}}</span>
                  </v-col>
                  <v-col cols="3" :align="IpadProSize ? 'start' : 'center'">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PaymentDate')}} {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PickupDate')}} {{ dataReceived }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.ReceivingDate')}} {{ dateCreateOrderStep4 }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Pick Up' && trackingPayment === 'Paid'" class="mb-5">
                  <v-col cols="12" align="center">
                    <v-img src="@/assets/stepper-onshop.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center" >
                    <span class="fontPass">{{$t('DetailOrderBuyer.EnterInformation')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontActive">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontInactive">{{$t('DetailOrderBuyer.WaitingForPickup')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontInactive">{{$t('DetailOrderBuyer.ReceivedSuccessfully')}}</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PaymentDate')}} {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Pick Up' && trackingPayment === 'Not Paid'" class="mb-5">
                  <v-col cols="12" align="center" :class="IpadProSize ? 'px-0 pb-5' : 'px-10 pb-5'">
                    <v-img src="@/assets/stepperNew/receive/step1.svg" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" :align="IpadProSize ? 'start' : 'center'">
                    <span :class="IpadProSize ? 'fontActive pl-6' : 'fontActive'">{{$t('DetailOrderBuyer.EnterInformation')}}</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-1">
                    <span class="fontInactive">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-6">
                    <span class="fontInactive">{{$t('DetailOrderBuyer.WaitingForPickup')}}</span>
                  </v-col>
                  <v-col cols="3" align="center" :class="IpadProSize ? 'pl-14' : 'pl-9'">
                    <span class="fontInactive">{{$t('DetailOrderBuyer.ToReceived')}}</span>
                  </v-col>
                  <v-col cols="3" :align="IpadProSize ? 'start' : 'center'">
                    <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Cancel by buyer' && statusCancel !== 'approve'" class="mb-5">
                  <v-col cols="12" align="center" :class="IpadProSize ? 'px-0 pb-5' : 'px-10 pb-5'">
                    <v-img src="@/assets/stepperNew/transport/newui-step1.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="5"></v-img>
                  </v-col>
                  <v-col cols="12" class="d-flex justify-space-around">
                    <div style="width: 120px;">
                      <span style="color: #269afd; font-weight: bold; display: flex; justify-content: center;">{{$t('DetailOrderBuyer.NewOrder')}}</span>
                      <span class="fontSizeStepOrder">({{$t('DetailOrderBuyer.PurchaseDate')}} {{ dateCreateOrderStep1 }}</span>
                    </div>
                    <div style="width: 120px;">
                      <span style="color: #cccccc; font-weight: bold; display: flex; justify-content: center;">{{$t('DetailOrderBuyer.PaymentSuccessful')}}</span>
                    </div>
                    <div style="width: 120px;">
                      <span style="color: #cccccc; font-weight: bold; display: flex; justify-content: center;">{{$t('DetailOrderBuyer.PreparingForShipment')}}</span>
                    </div>
                    <div style="width: 120px;">
                      <span style="color: #cccccc; font-weight: bold; display: flex; justify-content: center;">{{$t('DetailOrderBuyer.OutForDelivery')}}</span>
                    </div>
                    <div style="width: 120px;">
                      <span style="color: #cccccc; font-weight: bold; display: flex; justify-content: center;">{{$t('DetailOrderBuyer.DeliverySuccessful')}}</span>
                    </div>
                  </v-col>
                </v-row>
                <v-row no-gutters v-if="trackingStatus === ''" class="mb-5">
                  <h1>{{$t('DetailOrderBuyer.DataNotFound')}}</h1>
                </v-row>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" class="mb-3">
            <v-row>
              <v-col cols="6">
                <span style="font-weight: bold; font-size: 16px;">{{$t('DetailOrderBuyer.ShippingMethod')}} : </span><span>{{items.transportation_type}}</span>
              </v-col>
              <v-col cols="6">
                <!-- <span style="font-weight: bold; font-size: 16px;">Tracking Number : </span><span>{{items.list_tracking[0].tracking_no}}</span> -->
                <v-row dense>
                  <b>Tracking Number : </b>
                  <div v-if="items.list_tracking.length !== 0" class="d-flex flex-wrap">
                    <div v-for="(item, index) in items.list_tracking" :key="index">
                      <span v-if="item.url_tracking !== '-' && item.url_tracking !== ''" :style="item.url_tracking !== '-' && item.url_tracking !== '' ? 'text-decoration: underline; font-size: 16px; font-weight: 400; color: #27AB9C !important; cursor: pointer;' : 'font-size: 16px; font-weight: 400;'" class="pl-2" @click="linkToURLTracking(item.url_tracking)">{{ item.tracking_no + `${index === items.list_tracking.length - 1 ? '' : ','}` }}</span>
                      <a id="urlTracking" :href="urlTracking" target="_blank" style="display: none;"></a>
                      <div @click="copyClipboard()" v-if="item.url_tracking === ''" style="cursor: pointer;" class="pl-2">
                        <v-icon color="#1B5DD6" size="16" class="pr-1">mdi-content-copy</v-icon><span style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #27AB9C;">{{ item.tracking_no + `${index === items.list_tracking.length - 1 ? '' : ','}` }}</span>
                        <input type="text" :value="item.tracking_no" id="trackingNumber" style="display: none;">
                      </div>
                    </div>
                  </div>
                  <div v-else>
                    <span style="font-size: 16px; font-weight: 400;" class="pl-2"> - </span>
                  </div>
                </v-row>
              </v-col>
            </v-row>
          </v-col>
          <!-- อัปโหลด slip ของร้านค้า lotus -->
          <v-col class="pa-0" cols="6" md="6" align="end" v-if="items.is_lotus === 'Y' && items.transaction_status === 'Pending'">
            <v-btn @click="OpenUploadSlip()" rounded outlined color="#27AB9C"><b class="buttonFontSize">{{$t('DetailOrderBuyer.UploadSlip')}}</b></v-btn>
          </v-col>
        </v-row>
      </v-row>
      <v-row no-gutters class="mx-2">
        <!-- <v-col class="pa-4" cols="12" md="12" sm="12" style="background-color: #F9FAFD; border-radius: 8px;">
          <v-row no-gutters>
            <v-col cols="7" md="8">
              <v-row no-gutters>
                <v-col cols="4" md="4" sm="6" class="fontSizeDetailUp">
                  <v-row no-gutters class="fontHeight">
                    <span>รหัสการสั่งซื้อ : </span>
                  </v-row>
                  <v-row no-gutters class="fontHeight">
                    <span>ผู้ซื้อ :  </span>
                  </v-row>
                  <v-row no-gutters class="fontHeight">
                    <span>วันที่ทำรายการ :  </span>
                  </v-row>
                  <v-row no-gutters class="fontHeight">
                    <span>วันที่ชำระเงิน :  </span>
                  </v-row>
                  <v-row no-gutters >
                    <span>ใบกำกับภาษี :  </span>
                  </v-row>
                </v-col>
                <v-col cols="8" md="8" sm="6" class="fontSizeTotalPrice">
                  <v-row no-gutters class="fontHeight">
                    <span> {{order.order_number}}</span>
                  </v-row>
                  <v-row no-gutters class="fontHeight">
                    <span>{{items.buyer_name}}</span>
                  </v-row>
                  <v-row no-gutters class="fontHeight">
                    <span>{{new Date(items.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })}}</span>
                  </v-row>
                  <v-row no-gutters class="fontHeight">
                    <span v-if="items.tracking[0].time_step_2 !== '-'">{{dateCreateOrderStep2}}</span>
                    <span v-else>-</span>
                  </v-row>
                  <v-row no-gutters >
                    <div v-if="items.transaction_code !== '-' && items.required_invoice !== '-'">
                      <span style="color: #1B5DD6; text-decoration: underline; cursor: pointer;" @click="GetETax(items)">{{items.payment_transaction}}</span>
                      <a :href="downloadLink" download="filename.pdf" id="downloadLink" style="display: none;"></a>
                    </div>
                    <div v-else-if="items.transaction_code === '-' && items.required_invoice !== '-'">
                      <span>{{ items.required_invoice }}</span>
                    </div>
                    <div v-else>
                      <span>{{ '-' }}</span>
                    </div>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="5" md="4" class="mt-4">
              <v-row no-gutters class="pa-3" style="background-color: #FFFFFF; border-radius: 8px;">
                <v-col cols="5" md="5" class="fontSizeDetailUp">
                  <v-row no-gutters class="fontHeight">
                    <span>สถานะคำสั่งซื้อ : </span>
                  </v-row>
                  <v-row v-if="trackingStatus !== 'Pick Up' && productNormal.length !== 0" no-gutters class="fontHeight">
                    <span>วัน-เวลาส่งสินค้า :</span>
                  </v-row>
                  <v-row no-gutters v-if="productNormal.length !== 0">
                    <span>วัน-เวลารับสินค้า : </span>
                  </v-row>
                </v-col>
                <v-col cols="7" md="7" class="fontSizeTotalPrice">
                  <v-row no-gutters class="fontHeight">
                    <div v-if="items.detail_status === '-'">
                      <v-icon :class="trackingStatus === 'Not Paid' || trackingPayment === 'Not Paid'? 'notPay':trackingStatus === 'Not Received' || trackingPayment === 'Not Received'? 'notReceived':trackingStatus === 'Received'|| trackingPayment === 'Received'? 'received':trackingStatus === 'Not Sent'|| trackingPayment === 'Success'?'Pay':''">mdi-circle-medium</v-icon>
                      <span class="" :class="trackingStatus === 'Not Paid' || trackingPayment === 'Not Paid'? 'notPay':trackingStatus === 'Not Received' || trackingPayment === 'Not Received'? 'notReceived':trackingStatus === 'Received'|| trackingPayment === 'Received'? 'received':trackingStatus === 'Not Sent'|| trackingPayment === 'Success'?'Pay':''">{{trackingText}} </span>
                    </div>
                    <v-row no-gutters v-if="items.detail_status !== '-'">
                      <v-icon color="#636363">mdi-circle-medium</v-icon>
                      <span>{{items.detail_status}}</span>
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon
                            color="#636363"
                            v-bind="attrs"
                            v-on="on"
                          >
                            mdi-information-outline
                          </v-icon>
                        </template>
                        <span>{{items.action_reason}}</span>
                      </v-tooltip>
                    </v-row>
                  </v-row>
                  <v-row no-gutters class="fontHeight pl-2" v-if="trackingStatus !== 'Pick Up' && productNormal.length !== 0">
                    <span v-if="items.tracking[0].time_step_3 !== '-'">{{dateCreateOrderStep3}}</span>
                    <span v-else>-</span>
                  </v-row>
                  <v-row no-gutters class="fontHeight pl-2" v-if="productNormal.length !== 0">
                    <span v-if="items.received_date !== '-'">{{dateCreateOrderStep4}}</span>
                    <span v-else>-</span>
                  </v-row>
                </v-col>
              </v-row>
              <v-row no-gutters class="pt-2">
                <div class="d-flex">
                  <v-img height="20px" width="20px" src="@/assets/infoWarn.png"></v-img>
                  <span style="font-size: 15px; font-weight: 400; color:#989898;">ถ้าต้องการยกเลิกคำสั่งซื้อกรุณาติดต่อร้านค้า</span>
                </div>
              </v-row>
            </v-col>
          </v-row>
        </v-col> -->
        <v-col cols="12" md="12" sm="12" class="pt-0 pb-5 borderSty">
        <v-divider></v-divider>
          <v-row no-gutters>
            <div class="d-flex">
              <v-row no-gutters class="pr-2">
                <v-img class="sizeIMG" src="@/assets/ICON/detail_doc_icon.png"></v-img>
              </v-row>
              <span class="fontHeadDetail">{{$t('DetailOrderBuyer.DocumentDetails')}}</span>
            </div>
          </v-row>
          <v-row dense>
            <v-col cols="12" md="6" sm="12">
              <v-row class="pt-6">
                <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">{{$t('DetailOrderBuyer.QuotationNumber')}} : </span>
                  <a v-if="items.payment_transaction !== '' && QTorder !== '-' && items.transaction_status === 'Success'" :href="QTorder" target="_blank" style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #1B5DD6 !important;" :class="MobileSize ? 'ml-auto' : ''"> {{items.payment_transaction}}</a>
                  <span v-else-if="items.payment_transaction !== '' && QTorder !== '-' && items.transaction_status !== 'Success'" style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''"> {{items.payment_transaction}}</span>
                  <span v-else style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">-</span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">{{$t('DetailOrderBuyer.PurchaseOrderNumber')}} : </span>
                  <a :class="MobileSize ? 'ml-auto' : ''" v-if="((documents.PoNumber !== '' && documents.PoNumber !== null) || items.po_document_id !== '-') && (poPDF !== '-' || items.PO_External !== '-')" :href="poPDF !== '-' ? poPDF : items.PO_External" target="_blank" style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #1B5DD6 !important;"> {{(documents.PoNumber !== '' && documents.PoNumber !== null)? documents.PoNumber : items.po_document_id}}</a>
                  <span :class="MobileSize ? 'ml-auto' : ''" v-else-if="((documents.PoNumber !== '' && documents.PoNumber !== null) || items.po_document_id !== '-') && (poPDF === '-' || items.PO_External === '-')"> {{ (documents.PoNumber !== '' && documents.PoNumber !== null) ? documents.PoNumber : items.po_document_id }}</span>
                  <span :class="MobileSize ? 'ml-auto' : ''" v-else style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">{{$t('DetailOrderBuyer.ReceiptNumber')}} : </span>
                  <span :class="MobileSize ? 'ml-auto' : ''" style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.receipt_number }}</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="6" sm="12">
              <v-row class="pt-6">
                <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">{{$t('DetailOrderBuyer.PRNumber2')}} : </span>
                  <a v-if="((documents.PrNumber !== '' && documents.PrNumber !== null) || items.pr_document_id !== '-') && (prPDF !== '-' || items.PR_External !== '-')" :href="prPDF !== '-' ? prPDF : items.PR_External" target="_blank" style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #1B5DD6 !important;"> {{ (documents.PrNumber !== '' && documents.PrNumber !== null) ? documents.PrNumber : items.pr_document_id }}</a>
                  <span v-else-if="((documents.PrNumber !== '' && documents.PrNumber !== null) || items.pr_document_id !== '-') && (prPDF === '-' || items.PR_External === '-')"> {{ (documents.PrNumber !== '' && documents.PrNumber !== null) ? documents.PrNumber : items.pr_document_id }}</span>
                  <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">{{$t('DetailOrderBuyer.SONumber')}} : </span>
                  <a v-if="((documents.SoNumber !== '' && documents.SoNumber !== null) || items.ref_callback_so_id !== '-' || items.so_document_id !== '-') && soPDF !== '-'" :href="soPDF" target="_blank" style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #1B5DD6 !important;"> {{ (documents.SoNumber !== '' && documents.SoNumber !== null) ? documents.SoNumber : items.ref_callback_so_id !== '-' ? items.ref_callback_so_id : items.so_document_id !== '-' ? items.so_document_id : documents.SoNumber }}</a>
                  <span v-else-if="((documents.SoNumber !== '' && documents.SoNumber !== null) || items.ref_callback_so_id !== '-' || items.so_document_id !== '-') && soPDF === '-'"> {{ (documents.SoNumber !== '' && documents.SoNumber !== null) ? documents.SoNumber : items.ref_callback_so_id !== '-' ? items.ref_callback_so_id : items.so_document_id !== '-' ? items.so_document_id : documents.SoNumber }}</span>
                  <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- <v-row no-gutters class="pl-1 pt-3 fontSizeDetailUp">
            -
          </v-row> -->
        </v-col>
        <!-- <v-col v-if="dateCreateOrderStep4 !== 'Invalid Date'" cols="12" align="left" class="mb-4">
          <span>วันที่สินค้าทั้งหมดถึงผู้รับ : </span>
          <span>{{ dateCreateOrderStep4 }}</span>
        </v-col> -->
        <v-col cols="12">
          <!-- <v-card outlined> -->
          <!-- address, tracking Desktop ipadPro-->
          <v-container grid-list-lg class="px-0">
            <v-row no-gutters>
              <v-col :cols="IpadSize || IpadProSize || MobileSize ? 12 : 12">
                <v-row no-gutters>
                  <v-col cols="12">
                    <v-row no-gutters>
                      <!-- cols="6" -->
                      <v-col cols="12">
                        <v-row no-gutters>
                          <v-img class="sizeIMG" src="@/assets/address.png" contain max-height="24px" max-width="24px"></v-img>
                          <p class="fontHeadDetail pl-2">{{ items.shipping_type === 'front' ? $t('DetailOrderBuyer.PickupAddress') : $t('DetailOrderBuyer.ShippingAddress') }} <v-chip v-if="items.shipping_type === 'front'" color="#ADDFFF" text-color="#0059FF">{{$t('DetailOrderBuyer.PickUpAtStore')}}</v-chip></p>
                        </v-row>
                      </v-col>
                      <!-- <v-col cols="6" align="end" v-if="items.transaction_status === 'Success' && items.shipping_type === 'online' && trackingStatus === 'Not Sent' && items.transportation_type === '-'">
                        <v-btn small text color="primary" @click="openChangeAddress(items)"><v-icon left>mdi-pencil-outline</v-icon> เปลี่ยนที่อยู่</v-btn>
                      </v-col> -->
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="12" class="mb-4 ml-1 pb-4 borderSty">
                    <span class="d-flex" style="font-weight: bold;">{{items.buyer_name}} <span style="color: #cccccc">|</span> {{items.buyer_phone}} </span>
                    <span v-if="productNormal.length !== 0 && trackingStatus !== 'Pick Up'" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">
                      {{ items.address_data }}<br/>
                      <span v-if="items.note_address !== '' && items.note_address !== undefined && items.note_address !== null"><b>{{$t('DetailOrderBuyer.Remarks')}} :</b> {{ items.note_address }}</span>
                    </span>
                    <span v-else-if="trackingStatus === 'Pick Up'" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.shipping_detail }}</span>
                    <span v-else>-</span>
                  </v-col>
                  <v-col :cols="12" >
                   <v-row no-gutters>
                      <div class="d-flex">
                        <v-row no-gutters class="pr-2">
                          <v-img class="sizeIMG" src="@/assets/ICON/tax_invoice_icon.png"></v-img>
                        </v-row>
                        <p class="fontHeadDetail">{{$t('DetailOrderBuyer.TaxInvoiceDeliveryAddress')}}</p>
                      </div>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="12" class="mb-2 ml-1" v-if="items.invoice_address !== ''" >
                    <span class="d-flex" style="font-weight: bold;">{{items.buyer_name}} <span style="color: #cccccc">|</span> {{items.buyer_phone}} </span>
                    <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.required_invoice !== '-' ? items.invoice_address : '-' }}</span>
                  </v-col>
                  <v-col cols="12" md="12" class="mb-2 ml-1 pb-4 borderSty" v-else >
                    <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ '-' }}</span>
                  </v-col>
                  <v-col :cols="12" >
                   <v-row no-gutters>
                      <div class="d-flex">
                        <v-row no-gutters class="pr-2">
                          <v-img class="sizeIMG" src="@/assets/ICON/USER_INTERFACE_1.png"></v-img>
                        </v-row>
                        <p class="fontHeadDetail">{{$t('DetailOrderBuyer.RemarksToSeller')}}</p>
                      </div>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="12" class="mb-2 ml-1">
                    <span class="d-flex" style="font-weight: bold; color: #333333;">{{items.remark_to_shop !== null ? items.remark_to_shop : '-'}}</span>
                  </v-col>
                </v-row>
              </v-col>
              <v-col :cols="IpadSize || IpadProSize || MobileSize ? 12 : 6"></v-col>
            </v-row>
          </v-container>
          <!-- <v-col v-if="productService.length === 0" cols="12" md="12" sm="12">
            <v-row no-gutters v-if=" trackingStatus === 'Not Received' || trackingStatus === 'Received'">
              <v-col cols="6" md="6" sm="12" class="fontSizeDetailUp">
                <span>รูปแบบการจัดส่ง : </span>
                <span>-</span>
              </v-col>
              <v-col cols="6" md="6" sm="12" class="fontSizeDetailUp">
                <span>Tracking Number: </span>
                <span>-</span>
              </v-col>
            </v-row>
          </v-col> -->
            <!-- accept status = success, review Desktop ipadPro-->
            <!-- product table Desktop ipadPro-->
          <v-container grid-list-xs class="px-0">
            <v-col cols="12" class="pt-0 px-0" >
              <v-row no-gutters >
                <div class="d-flex">
                  <v-row no-gutters class="pr-2">
                    <v-img class="sizeIMG" src="@/assets/ICON/order_list_icon.png"></v-img>
                  </v-row>
                  <p class="fontHeadDetail"><b>{{$t('DetailOrderBuyer.OrderList')}}</b></p>
                </div>
              </v-row>
              <v-row no-gutters>
              <!-- url_tracking -->
                <!-- <v-col cols="6" ><b>รหัสการสั่งซื้อ :</b> {{order.order_number}}</v-col> -->
                <v-col cols="6" >
                  <!-- <div v-if="items.url_tracking !== '-'">
                    <b>Tracking Number : </b>
                    <a :href="items.url_tracking" target="_blank" >{{items.order_mobilyst_no}}</a>
                  </div> -->
                  <!-- mdi-content-copy -->
                  <!-- <v-row dense>
                    <b>Tracking Number : </b>
                    <div v-if="items.list_tracking.length !== 0" class="d-flex flex-wrap">
                      <div v-for="(item, index) in items.list_tracking" :key="index">
                        <span v-if="item.url_tracking !== '-' && item.url_tracking !== ''" :style="item.url_tracking !== '-' && item.url_tracking !== '' ? 'text-decoration: underline; font-size: 16px; font-weight: 400; color: #27AB9C !important; cursor: pointer;' : 'font-size: 16px; font-weight: 400;'" class="pl-2" @click="linkToURLTracking(item.url_tracking)">{{ item.tracking_no + `${index === items.list_tracking.length - 1 ? '' : ','}` }}</span>
                        <a id="urlTracking" :href="urlTracking" target="_blank" style="display: none;"></a>
                        <div @click="copyClipboard()" v-if="item.url_tracking === ''" style="cursor: pointer;" class="pl-2">
                          <v-icon color="#1B5DD6" size="16" class="pr-1">mdi-content-copy</v-icon><span style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #27AB9C;">{{ item.tracking_no + `${index === items.list_tracking.length - 1 ? '' : ','}` }}</span>
                          <input type="text" :value="item.tracking_no" id="trackingNumber" style="display: none;">
                        </div>
                      </div>
                    </div>
                    <div v-else>
                      <span style="font-size: 16px; font-weight: 400;" class="pl-2"> - </span>
                    </div>
                  </v-row> -->
                  <!-- <div v-if="items.url_tracking === ''">
                    <b>Tracking Number : </b>
                    <input type="text" :value="items.order_mobilyst_no" id="trackingNumber" style="display: none;">
                    <span v-if="items.order_mobilyst_no !== '-'">{{items.order_mobilyst_no}} <a @click="copyClipboard()"><v-icon color="#27AB9C" size="18" class="pr-1">mdi-content-copy</v-icon>คัดลอก</a></span>
                    <span v-if="items.order_mobilyst_no === '-'">-</span>
                  </div> -->
                </v-col>
              </v-row>
              <!-- <span class="pl-3">รหัสการสั่งซื้อ
                <b class="left">{{order.order_number}}</b>
              </span> -->
            </v-col>
            <!-- <v-col cols="12" v-for="(order, index) in items.data_list" :key="index" class="pa-0 ">
              <v-card outlined>
              <v-row no-gutters class="pa-3 mr-4 mb-2" type="flex" justify="start">
                <v-col :cols="IpadProSize ? 7 : 9">
                  <span class="pl-3">รหัสการสั่งซื้อ
                    <b class="left">{{order.order_number}}</b>
                  </span>
                </v-col>
                <v-col :cols="IpadProSize ? 5 : 3" align="end">
                  <span class="pl-3" v-if="order.service_type === 'chilled'">
                    <v-chip small color="#E5EFFF" text-color="#1B5DD6">ส่งแบบควบคุมอุณหภูมิ {{ order.business_type }} Express</v-chip>
                  </span>
                  <span class="pl-3" v-if="order.service_type === 'normal'">
                    <v-chip small color="#E6F5F3" text-color="#27AB9C">ส่งแบบปกติ {{ order.business_type }} Express</v-chip>
                  </span>
                  <span class="pl-3" v-if="order.service_type === 'frozen'">
                    <v-chip small color="#E6F5F3" text-color="#26C6DA">ส่งแบบแช่แข็ง {{ order.business_type }} Express</v-chip>
                  </span>
                  <span class="pl-3" v-if="order.service_type === 'bulk'">
                    <v-chip small color="#FCF0DA" text-color="#E9A016">ส่งของขนาดใหญ่ {{ order.business_type }} Express</v-chip>
                  </span>
                </v-col>
                <v-col cols="6" class="pl-3 mt-2" v-if="order.order_mobilyst_no">
                  <span>Tracking Number <b>{{order.order_mobilyst_no}}</b></span>
                </v-col>
                <v-col align="end" cols="6" class="mt-2">
                  <v-btn v-if="order.order_mobilyst_no" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile' : 'fontSizeDetail mr-0'" text color="#27AB9C" @click="GoToMobily(order.url_tracking)" style="color: #27AB9C; text-decoration: underline;">
                    <v-img src="@/assets/icons/Vector.png" contain></v-img>
                    ติดตามสถานะขนส่ง
                  </v-btn>
                </v-col>
                <v-col cols="12" class="mt-3">
                  <span class="pl-3">
                    {{ Object.keys(order.product_list).length }} รายการสินค้า
                  </span>
                </v-col>
              </v-row>
              <a-table :data-source="order.product_list" :rowKey="record => record.sku" :columns="headers" :pagination="{ pageSize: 100 }">
                <template slot="productdetails" slot-scope="text, record">
                  <v-row>
                    <v-col cols="12" md="4" class="pr-0 py-1">
                      <v-img :src="record.product_image" class="imageshow" v-if="record.product_image !== ''" contain/>
                      <v-img src="@/assets/NoImage.png" class="imageshow" contain v-else/>
                    </v-col>
                    <v-col cols="12" md="8">
                      <p class="mb-0 DetailsProductFront">รหัสสินค้า: {{ record.sku }}<br/>{{ record.product_name }}</p>
                      <span v-if="record.product_attribute_detail.attribute_priority_1" class="mb-0 DetailsProductFront">{{record.key_1_value}}: <b>{{record.product_attribute_detail.attribute_priority_1}}</b></span>
                      <span v-if="record.product_attribute_detail.attribute_priority_2" class="pl-2 mb-0 DetailsProductFront">{{record.key_2_value}}: <b>{{record.product_attribute_detail.attribute_priority_2}}</b></span>
                    </v-col>
                  </v-row>
                </template>
                <template slot="quantity" slot-scope="text, record">
                  <v-col cols="12">
                    <span>{{ record.quantity }}</span>
                  </v-col>
                </template>
                <template slot="price" slot-scope="text, record">
                  <span style="font-weight: 700; font-size: 16px; line-height: 26px; color: #000000;">{{ Number(record.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                </template>
                <template slot="net_price" slot-scope="text, record">
                  <span style="font-weight: 700; font-size: 16px; line-height: 26px; color: #000000;">{{ Number(record.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                </template>
              </a-table>
              btn comfirm accept, refund, contact seller Desktop ipadPro
              <v-row no-gutters v-if="checkAcceptProduct[index].status !== 'Pickup' && checkAcceptProduct[index].status !== 'expired' && items.transaction_status !== 'Not Paid'" :class="checkAcceptProduct[index].status === 'waiting_review' || checkAcceptProduct[index].status === 'success' ? 'pa-0' : 'pa-3 px-0'" >
                <v-col cols="12" md="12" align="right">
                  <span v-if="checkAcceptProduct[index].status === 'reject'" style="color: #BDBDBD">คำขอคืนสินค้าไม่ได้รับการอนุมัติจากร้านค้า</span>
                  <span v-if="checkAcceptProduct[index].status === 'refund'" style="color: #BDBDBD">คำขอคืนสินค้าได้รับการอนุมัติแล้ว</span>
                  <span v-if="checkAcceptProduct[index].status === 'waiting_refund'" style="color: #BDBDBD">ส่งคำขอคืนสินค้าแล้ว รอร้านค้าอนุมัติ</span>
                  <v-btn v-if="checkAcceptProduct[index].status !== 'waiting_review' && checkAcceptProduct[index].status !== 'reject' && checkAcceptProduct[index].status !== 'waiting_refund' && checkAcceptProduct[index].status !== 'refund' && checkAcceptProduct[index].status !== 'success'" :disabled="checkAcceptProduct[index].refund === 'no'" outlined class="px-5 mr-2" color="#D1392B" @click="refundProductBuyer(order)"><b class="buttonFontSize">คืนสินค้า</b></v-btn>
                  <v-btn v-if="checkAcceptProduct[index].status !== 'waiting_review' && checkAcceptProduct[index].status !== 'reject' && checkAcceptProduct[index].status !== 'waiting_refund' && checkAcceptProduct[index].status !== 'refund' && checkAcceptProduct[index].status !== 'success'" :disabled="checkAcceptProduct[index].status !== 'waiting_accept'" class="white--text px-5" color="#27AB9C" @click="acceptProduct(order)"><b class="buttonFontSize">ฉันตรวจสอบและได้รับสินค้าแล้ว</b></v-btn>
                </v-col>
              </v-row>
              <v-container grid-list-xs v-if="checkAcceptProduct[index].status !== 'Pickup' && checkAcceptProduct[index].status === 'waiting_review'">
                <v-row class="px-3">
                  <v-col cols="12" md="9">
                    <span class="pr-5">ขอบคุณที่ซื้อสินค้ากับเราหากท่านชื่นชอบกรุณาประเมินความพึงพอใจสินค้า<br/>และให้คำแนะนำเพื่อที่เราจะได้นำความคิดเห็นของท่านไปปรับปรุงและพัฒนาการบริการให้ดียิ่งขึ้น</span>
                  </v-col>
                  <v-col cols="12" md="3" align="right" class="pt-4">
                    <v-chip rounded class="white--text px-5" color="#52C41A" @click="openModalReviewProduct(order, index)"><b class="buttonFontSize">ประเมินความพึงพอใจสินค้า</b></v-chip>
                  </v-col>
                </v-row>
              </v-container>
              <v-container grid-list-xs v-if="checkAcceptProduct[index].status !== 'Pickup' && checkAcceptProduct[index].status === 'success'">
                <v-row class="px-3">
                  <v-col cols="12" align="right" class="pt-4">
                    <v-btn rounded class="white--text px-5" color="#27AB9C" @click="openModalEditReviewProduct(order, index)"><b class="buttonFontSize">รายละเอียดการประเมินความพึงพอใจสินค้า</b></v-btn>
                  </v-col>
                </v-row>
              </v-container>
              </v-card>
            </v-col> -->
            <v-col cols="12" md="12" v-if="productNormal.length !== 0" class="px-0">
              <v-row no-gutters>
                <span style="font-size: 16px; font-weight: 700;">{{$t('DetailOrderBuyer.GeneralProducts')}}</span> <br>
                <v-col cols="12" md="12">
                  <a-table :data-source="productNormal" :rowKey="record => record.sku" :columns="headers" :pagination="{ pageSize: 100 }">
                    <template slot="productdetails" slot-scope="text, record">
                      <v-row>
                        <v-col cols="12" md="4" class="pr-0 py-1">
                          <v-img :src="record.product_image" class="imageshow" v-if="record.product_image !== ''" contain/>
                          <v-img src="@/assets/NoImage.png" class="imageshow" contain v-else/>
                        </v-col>
                        <v-col cols="12" md="8">
                          <b class="mb-0 DetailsProductFront">{{ record.product_name }}</b> <br/>
                          <span v-if="record.product_attribute_detail.attribute_priority_1" class="mb-0 DetailsProductFront">{{record.product_attribute_detail.key_1_value}}: <b>{{record.product_attribute_detail.attribute_priority_1}}</b></span> <br/>
                          <span v-if="record.product_attribute_detail.attribute_priority_2" class="mb-0 DetailsProductFront">{{record.product_attribute_detail.key_2_value}}: <b>{{record.product_attribute_detail.attribute_priority_2}}</b></span>
                          <v-chip color="#3EC6B6" x-small>
                            <span style="color: #ffffff">{{$t('DetailOrderBuyer.GeneralProducts')}}</span>
                          </v-chip>
                        </v-col>
                      </v-row>
                    </template>
                    <template slot="quantity" slot-scope="text, record">
                      <v-col cols="12">
                        <span>{{ record.quantity }}</span>
                      </v-col>
                    </template>
                    <template slot="price" slot-scope="text, record">
                      <span class="fontStatus">{{ Number(record.vat_default === 'yes' ? parseFloat(record.real_price) + parseFloat(record.vat_include) : parseFloat(record.real_price)).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template slot="net_price" slot-scope="text, record">
                      <span class="fontStatus">{{ Number(record.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                  </a-table>
                  <!-- <v-data-table
                    :items="productNormal"
                    :headers="headersDetailProduct"
                  >
                    <template v-slot:[`item.productdetails`]="{ item }">
                      <v-row>
                        <v-col cols="12" md="4" class="pr-0 py-1">
                          <v-img :src="item.product_image" class="imageshow" v-if="item.product_image !== ''" contain/>
                          <v-img src="@/assets/NoImage.png" class="imageshow" contain v-else/>
                        </v-col>
                        <v-col cols="12" md="8" class="d-flex align-center">
                          <b class="mb-0 DetailsProductFront">{{ item.product_name }}</b> <br/>
                          <span v-if="item.product_attribute_detail.attribute_priority_1" class="mb-0 DetailsProductFront">{{item.product_attribute_detail.key_1_value}}: <b>{{item.product_attribute_detail.attribute_priority_1}}</b></span> <br/>
                          <span v-if="item.product_attribute_detail.attribute_priority_2" class="mb-0 DetailsProductFront">{{item.product_attribute_detail.key_2_value}}: <b>{{item.product_attribute_detail.attribute_priority_2}}</b></span>
                        </v-col>
                      </v-row>
                    </template>
                    <template v-slot:[`item.price`]="{ item }">
                      <span class="fontStatus">{{ Number(item.vat_default === 'yes' ? parseFloat(item.real_price) + parseFloat(item.vat_include) : parseFloat(item.real_price)).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.net_price`]="{ item }">
                      <span class="fontStatus">{{ Number(item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                  </v-data-table> -->
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" v-if="productService.length !== 0" class="px-0">
              <v-row no-gutters>
                <span style="font-size: 16px; font-weight: 700;">{{$t('DetailOrderBuyer.ServiceProducts')}}</span><br>
                <v-col cols="12" md="12">
                  <a-table :data-source="productService" :rowKey="record => record.sku" :columns="headers" :pagination="{ pageSize: 100 }">
                    <template slot="productdetails" slot-scope="text, record">
                      <v-row>
                        <v-col cols="12" md="4" class="pr-0 py-1">
                          <v-img :src="record.product_image" class="imageshow" v-if="record.product_image !== ''" contain/>
                          <v-img src="@/assets/NoImage.png" class="imageshow" contain v-else/>
                        </v-col>
                        <v-col cols="12" md="8">
                          <b class="mb-0 DetailsProductFront">{{ record.product_name }}</b> <br/>
                          <span v-if="record.product_attribute_detail.attribute_priority_1" class="mb-0 DetailsProductFront">{{record.product_attribute_detail.key_1_value}}: <b>{{record.product_attribute_detail.attribute_priority_1}}</b></span> <br/>
                          <span v-if="record.product_attribute_detail.attribute_priority_2" class="mb-0 DetailsProductFront">{{record.product_attribute_detail.key_2_value}}: <b>{{record.product_attribute_detail.attribute_priority_2}}</b></span>
                          <v-chip color="#fda808" x-small>
                            <span style="color: #ffffff">{{$t('DetailOrderBuyer.ServiceProducts')}}</span>
                          </v-chip>
                        </v-col>
                      </v-row>
                    </template>
                    <template slot="quantity" slot-scope="text, record">
                      <v-col cols="12">
                        <span>{{ record.quantity }}</span>
                      </v-col>
                    </template>
                    <template slot="price" slot-scope="text, record">
                      <span class="fontStatus">{{ Number(record.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template slot="net_price" slot-scope="text, record">
                      <span class="fontStatus">{{ Number(record.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                  </a-table>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-4 px-0" v-if="productFree !== null && productFree.length !== 0">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">{{$t('DetailOrderBuyer.FreeGift')}}</span>
                  <a-table :data-source="productFree" :rowKey="record => record.sku" :columns="headers" :pagination="{ pageSize: 100 }">
                    <template slot="productdetails" slot-scope="text, record">
                      <v-row>
                        <v-col cols="12" md="4" class="pr-0 py-1">
                          <v-img :src="record.product_image" class="imageshow" v-if="record.product_image !== '' && record.product_image !== null" contain/>
                          <v-img src="@/assets/NoImage.png" class="imageshow" contain v-else/>
                        </v-col>
                        <v-col cols="12" md="8">
                          <b class="mb-0 DetailsProductFront">{{ record.product_name }}</b> <br/>
                          <span v-if="record.product_attribute_detail.attribute_priority_1" class="mb-0 DetailsProductFront">{{record.product_attribute_detail.key_1_value}}: <b>{{record.product_attribute_detail.attribute_priority_1}}</b></span> <br/>
                          <span v-if="record.product_attribute_detail.attribute_priority_2" class="mb-0 DetailsProductFront">{{record.product_attribute_detail.key_2_value}}: <b>{{record.product_attribute_detail.attribute_priority_2}}</b></span>
                          <v-chip color="#ff3d1e" x-small>
                            <span style="color: #ffffff">{{$t('DetailOrderBuyer.FreeGift')}}</span>
                          </v-chip>
                        </v-col>
                      </v-row>
                    </template>
                    <template slot="quantity" slot-scope="text, record">
                      <v-col cols="12">
                        <span>{{ record.quantity }}</span>
                      </v-col>
                    </template>
                    <template slot="price">
                    <!-- {{record.vat_include}} -->
                      <!-- <span class="fontStatus">{{ Number(record.vat_default === 'yes' ? parseFloat(record.real_price) + parseFloat(record.vat_include) : parseFloat(record.real_price)).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span> -->
                      <span class="fontStatus">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template slot="net_price">
                      <!-- <span class="fontStatus">{{ Number(record.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span> -->
                      <span class="fontStatus">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                  </a-table>
                </v-col>
                <v-col cols="12" class="pa-0" v-if="items.point !== null || (items.coupon.length !== 0 && items.coupon !== null)">
                  <v-row no-gutters >
                    <div class="d-flex">
                      <v-row no-gutters class="pr-2">
                        <v-img class="sizeIMG" src="@/assets/ICON/payment_transaction_icon.png"></v-img>
                      </v-row>
                      <p class="fontHeadDetail"><b>{{$t('DetailOrderBuyer.Payment')}}</b></p>
                    </div>
                  </v-row>
                  <!-- <span style="font-size: 16px; font-weight: 600; color: #333333;" class="pl-3">โปรโมชันและคูปองส่วนลด</span> -->
                  <v-card style="background: #FFFFFF; border-radius: 4px;" min-height="100%" elevation="0">
                    <!-- <v-toolbar align="start" color="#E6F5F3" dark dense elevation="0" v-if="items.point !== null">
                      <span class="" style="font-size:16px; font-weight:500; color: #333333;">
                        <font>ส่วนลดจากแต้ม {{items.point === null ? 0 : items.point}} บาท</font>
                      </span>
                    </v-toolbar> -->
                    <!-- <v-container class="pa-0 pt-4" v-if="items.coupon !== null && items.coupon.length !== 0">
                      <v-card-text class="pa-0">
                        <v-row :class=" MobileSize? 'pa-1 pt-1':''">
                          <v-col cols="12" md="4" sm="6" v-for="(item, index) in items.coupon" :key="index" class="pa-1">
                            <v-col :class="!MobileSize && !IpadSize ? 'couponIMGDesk': MobileSize? 'couponIMGMobile': 'couponIMG'">
                              <v-col cols="12" md="12" class="" :class="MobileSize? 'pa-1': IpadSize? 'pl-0': IpadProSize? 'pl-0': 'pl-0'">
                                <v-row no-gutters>
                                  <v-col cols="12" md="12" sm="12" :style="MobileSize ? 'padding-left: 6% !important;': 'padding-left: 10% !important;'">
                                    <v-row no-gutters>
                                      <v-col :cols="MobileSize? '5':'6'" md="6" sm="5" align="start">
                                        <span style="color: #27AB9C; font-size: 14px; font-weight: 600;">{{item.coupon_name | truncate(15, '...')}}</span><br>
                                        <span> ขั้นต่ำ {{item.spend_minimum}} บาท</span><br>
                                      </v-col>
                                      <v-col :cols="MobileSize? '6':'6'" md="6" sm="6" align="end">
                                        <span style="color: #FB9372; font-size: 12px;"> {{item.coupon_type === 'free_shipping' ? 'โค้ดส่งฟรี' : 'ส่วนลด'}}</span><br>
                                        <span v-if="item.coupon_type === 'free_product'" style="color: #F56E22; font-size: 22px; font-weight: 600;"> แถมฟรี</span><br>
                                        <span v-if="item.coupon_type !== 'free_product'" style="color: #F56E22; font-size: 22px; font-weight: 600;"> {{item.discount_amount}} {{item.discount_type === 'percent'? '%':'บาท'}}</span><br>
                                      </v-col>
                                    </v-row>
                                  </v-col>
                                </v-row>
                              </v-col>
                            </v-col>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-container> -->
                  </v-card>
                </v-col>
            <!-- </v-col> -->
          </v-container>
             <!-- price, total_price_vat, shipping Desktop ipadPro-->
          <v-container grid-list-xs>
            <v-row no-gutters>
              <v-col cols="12" sm="9" md="10" align="start" >
                <v-row dense>
                  <v-col cols="12">
                    <span class="fontSizeTotalPrice">{{$t('DetailOrderBuyer.PriceExcludingVAT')}} :</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="fontSizeTotalPrice">{{$t('DetailOrderBuyer.VAT')}} :</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="fontSizeTotalPrice">{{$t('DetailOrderBuyer.PriceIncludingVAT')}} :</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="fontSizeDiscount ml-3">{{$t('DetailOrderBuyer.StoreDiscountSummary')}} :</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="fontSizeDiscount ml-3">{{$t('DetailOrderBuyer.SystemDiscountSummary')}} :</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="fontSizeDiscount ml-3">{{$t('DetailOrderBuyer.PointsDiscountSummary')}} :</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="fontSizeTotalPrice">{{$t('DetailOrderBuyer.SubTotal')}} :</span>
                  </v-col>
                  <!-- <v-col cols="12">
                    <span class="fontSizeTotalPrice">ค่าจัดส่ง :</span><br>
                    <span style="font-size: 10px; color: #A1A1A1;">
                      <span>ราคานี้เป็นมาตรฐาน - ราคาอาจแตกต่างกันไป</span><br>
                      <span>ขึ้นอยู่กับสินค้า / ปลายทาง เจ้าหน้าที่จัดส่งจะติดต่อคุณ</span>
                    </span>
                  </v-col> -->
                  <v-col cols="12">
                    <span class="fontSizeTotalPrice">{{$t('DetailOrderBuyer.ShippingFee')}} :</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="fontSizeDiscount ml-3">{{$t('DetailOrderBuyer.ShippingDiscountSeller')}} :</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="fontSizeDiscount ml-3">{{$t('DetailOrderBuyer.ShippingDiscountNexgen')}} :</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="subheader fontHeadDetail">{{$t('DetailOrderBuyer.TotalPriceAll')}} :</span>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" sm="3" md="2" align="end">
                <v-row dense>
                  <v-col cols="12">
                    <span class="fontSizeTotalPriceNumber">{{ Number(items.total_price_no_vat_web) > 0.00 ? Number(items.total_price_no_vat_web).toLocaleString(undefined, { minimumFractionDigits: 2 }) : '0.00' }}  {{$t('DetailOrderBuyer.Baht')}}</span>
                  </v-col>
                  <!-- ส่วนลดคูปอง -->
                  <v-col cols="12">
                    <span class="fontSizeTotalPriceNumber">{{ Number(items.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}  {{$t('DetailOrderBuyer.Baht')}}</span>
                  </v-col>
                  <!-- ส่วนลดคูปองระบบ -->
                  <v-col cols="12">
                    <span class="fontSizeTotalPriceNumber">{{ Number(items.total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}  {{$t('DetailOrderBuyer.Baht')}}</span>
                  </v-col>
                  <!-- ราคารวมส่วนลด -->
                  <v-col cols="12">
                    <span class="fontSizeDiscountBold">- {{ Number(items.total_coupon_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}  {{$t('DetailOrderBuyer.Baht')}}</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="fontSizeDiscountBold">- {{ Number(items.total_coupon_platform_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}  {{$t('DetailOrderBuyer.Baht')}}</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="fontSizeDiscountBold">- {{ Number(items.total_point).toLocaleString(undefined, {minimumFractionDigits: 2}) }}  {{$t('DetailOrderBuyer.Baht')}}</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="fontSizeTotalPriceNumber">{{ Number(items.total_price_after_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}  {{$t('DetailOrderBuyer.Baht')}}</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="fontSizeTotalPriceNumber">{{ Number(items.shipping_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}  {{$t('DetailOrderBuyer.Baht')}}</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="fontSizeDiscountBold">- {{ Number(items.total_coupon_shipping_discount_v2).toLocaleString(undefined, {minimumFractionDigits: 2}) }}  {{$t('DetailOrderBuyer.Baht')}}</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="fontSizeDiscountBold">- {{ Number(items.total_coupon_platform_shipping_discount_v2).toLocaleString(undefined, {minimumFractionDigits: 2}) }}  {{$t('DetailOrderBuyer.Baht')}}</span>
                  </v-col>
                  <v-col cols="12" class="fontHeadDetail">
                    <span style="color: #27AB9C;">{{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span> <span style="color: #27AB9C;">  {{$t('DetailOrderBuyer.Baht')}}</span>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-container>
          <!-- </v-card> -->
        </v-col>
        <!-- status payment Desktop ipadPro-->
        <v-col cols="12" class="mt-2">
          <!-- <v-card outlined> -->
          <v-container grid-list-xs class="px-2">
            <v-col v-if="items.tracking[0].status_tracking === 'Not Paid' || items.transaction_status === 'Cancel'" cols="12" md="12" sm="12" class="px-0">
              <v-row no-gutters class="py-4" v-if="items.transaction_status !== 'Cancel'">
                <div class="d-flex fontHeadDetail pt-2">
                  <v-row no-gutters class="pr-2">
                    <v-img class="sizeIMG" src="@/assets/paymentHead.png"></v-img>
                  </v-row>
                  {{$t('DetailOrderBuyer.Payment')}}
                </div>
                <div class="pt-2 notPay" >
                  <v-icon class="notPay">mdi-circle-medium</v-icon>
                  {{$t('DetailOrderBuyer.PendingPayment')}}
                </div>
                <v-row no-gutters justify="end" v-if="items.is_lotus === 'N'">
                  <v-btn class="white--text" color="#27AB9C" rounded @click="CheckStockBeforeOpenModalPayment(items.payment_transaction)">
                    <v-img class="sizeIMG" src="@/assets/payment.png"></v-img>
                    <span class="pl-1 pt-1">{{$t('DetailOrderBuyer.Payment2')}}</span>
                  </v-btn>
                </v-row>
              </v-row>
              <v-row no-gutters v-if="items.transaction_status !== 'Cancel'">
                <span class="fontSizeTotalPrice">{{$t('DetailOrderBuyer.YouHaveNotCompletedPayment')}}</span>
              </v-row>
            </v-col>
            <v-col v-else cols="12" md="12" sm="12" class="pa-0">
              <v-row class="pa-4" no-gutters style="background-color: #F9FAFD; border-radius: 8px;">
                <v-col cols="6" md="6">
                  <span style="font-size: 18px; font-weight: 400;" ><v-avatar size="20" class="mr-2"><v-img src="@/assets/stepperNew/transport/successPayment.png"></v-img></v-avatar>{{$t('DetailOrderBuyer.PaymentSuccessfully')}} !</span><br>
                  <!-- <span style="font-size: 18px; font-weight: 400;" >ธนาคารที่ชำระเงิน</span> -->
                </v-col>
                <v-col cols="6" md="6" align="end">
                  <span class="fontHeadDetail" v-if="items.installment_month === '0'">{{$t('DetailOrderBuyer.PaymentMethod')}} : {{ items.receipt[0].payType }}</span>
                  <span class="fontHeadDetail" v-else>{{$t('DetailOrderBuyer.PaymentMethod')}} : {{ items.receipt[0].payType }} ({{items.installment_month}}x @0%)</span><br>
                  <!-- <span class="fontHeadDetail">{{ bankName }}</span> -->
                </v-col>
              </v-row>
            </v-col>
            <v-col v-if="items.inet_relation_ship.length !== 0">
              <v-row class="pl-4 pr-3 pb-2 align-baseline">
                <v-col cols="12" class="pl-0 pt-4 pb-1">
                  <span style="font-size: 18px; font-weight: 700;">{{$t('DetailOrderBuyer.DiscountCoupon')}}</span>
                </v-col>
                <v-col cols="12" class="pl-0 pt-0">
                  <span style="font-size: 18px; font-weight: 700;">{{$t('DetailOrderBuyer.ListOf')}} {{items.inet_relation_ship.length}} {{$t('DetailOrderBuyer.Employees')}}</span>
                </v-col>
              </v-row>
              <v-card class="pa-2 py-4 custom-scroll" elevation="0" style="background: #F9FAFD; border-radius: 20px;  max-height: 435px; overflow-y: auto; overflow-x: hidden;">
                <v-row class="pb-1 pt-2"  v-for="(item, index) in items.inet_relation_ship" :key="index">
                  <v-col cols="12" class="px-4 py-0">
                    <v-col cols="12" class="pa-0">
                      <span style="font-size: 16px; font-weight: 400;">{{index + 1}}. {{$t('DetailOrderBuyer.Department')}}: <b>{{item.team}}</b></span>
                    </v-col>
                    <v-col cols="12" class="pa-0 pl-4">
                      <span style="font-size: 16px; font-weight: 400;">{{$t('DetailOrderBuyer.Company')}}: <b>{{item.company}}</b></span>
                    </v-col>
                    <v-col cols="12" class="pa-0 pl-4">
                      <span style="font-size: 16px; font-weight: 400;"><b>{{item.employee_one_id + ' ' + item.first_name_th + ' ' + item.last_name_th}} ({{ item.code }})</b></span>
                    </v-col>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
            <!-- <v-row no-gutters>
              <v-col v-if="items.transaction_status === 'Success'" cols="12" class="mt-4">
                <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#E6F5F3" small text-color="#27AB9C">ชำระเงินสำเร็จ</v-chip></p>
              </v-col>
              <v-col v-else-if="items.transaction_status === 'Cash'" cols="12" class="mt-4">
                <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#E6F5F3" small text-color="#27AB9C">ชำระเงินสด</v-chip></p>
              </v-col>
              <v-col v-else-if="items.transaction_status === 'Cancel'" cols="12" class="mt-4">
                <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#F7D9D9" small text-color="#D1392B">ยกเลิกสินค้า</v-chip></p>
              </v-col>
              <v-col v-else-if="items.transaction_status === 'Fail'" cols="12" class="mt-4">
                <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#FFECB3" small text-color="#FFB300">ชำระเงินไม่สำเร็จ</v-chip></p>
              </v-col>
              <v-col v-else cols="12" class="mt-4">
                <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#E5EFFF" small text-color="#1B5DD6">ยังไม่ชำระเงิน</v-chip></p>
              </v-col>
              <v-row v-if="items.transaction_status === 'Success' " no-gutters>
                <v-col cols="12" class="mt-4">
                  <span class="mr-2">คุณได้ชำระเงินเสร็จเรียบร้อยแล้ว ขอบคุณสำหรับการใช้บริการ</span>
                </v-col>
                <v-col cols="12" class="mt-5">
                  <span style="color: #27AB9C;"><b>&#8226; หลักฐานการชำระเงิน</b></span>
                </v-col>
                <v-col cols="11" class="mt-5">
                  <v-row no-gutters>
                    <v-col cols="3" sm="5">
                      <span>รหัสการชำระเงิน : </span>
                    </v-col>
                    <v-col cols="9" sm="7">
                      <span>{{ items.receipt[0].orderId }}</span>
                    </v-col>
                    <v-col cols="3" sm="5" class="mt-2">
                      <span>จำนวนเงิน : </span>
                    </v-col>
                    <v-col cols="9" sm="7" class="mt-2">
                      <span>{{ Number(items.receipt[0].TxnAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                    </v-col>
                    <v-col cols="3" sm="5" class="mt-2">
                      <span>วันและเวลาที่ทำรายการ : </span>
                    </v-col>
                    <v-col cols="9" sm="7" class="mt-2">
                      <span>{{new Date(items.receipt[0].created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })}}</span>
                    </v-col>
                    <v-col cols="3" sm="5" class="mt-2">
                      <span>Ref : </span>
                    </v-col>
                    <v-col cols="9" sm="7" class="mt-2">
                      <span>{{ items.receipt[0].orderIDRef }}</span>
                    </v-col>
                    <v-col cols="3" sm="5" class="mt-2">
                      <span>ธนาคาร : </span>
                    </v-col>
                    <v-col cols="9" sm="7" class="mt-2">
                      <span>{{ bankName }}</span>
                    </v-col>
                    <v-col cols="3" sm="5" class="mt-2">
                      <span>รูปแบบการชำระเงิน :</span>
                    </v-col>
                    <v-col cols="9" sm="7" class="mt-2">
                      <span>{{ items.receipt[0].payType }}</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
              <v-row v-else-if="items.transaction_status === 'Cancel' && dataRole.role === 'purchaser'" no-gutters>
                <v-col cols="12">
                  <span>คำสั่งซื้อสินค้าของคุณถูกยกเลิก</span>
                </v-col>
                <v-col cols="12" class="mt-5">
                  <span style="color: #27AB9C;"><b>&#8226; รายละเอียดใบสั่งซื้อที่ถูกยกเลิก</b></span>
                </v-col>
                <v-col cols="12" align="left">
                  <v-timeline dense>
                    <v-timeline-item v-for="(item,index) in items.approver_list" :key="index" fill-dot class="white--text mb-12" color="#27AB9C" small>
                      <template v-slot:icon>
                        <span>{{ index+1 }}</span>
                      </template>
                      <v-row no-gutters>
                        <v-col cols="12">
                          <span style="color: #27AB9C;"><b>ผู้อนุมัติ</b></span>
                        </v-col>
                        <v-col cols="12">
                          <span style="color: black;">สถานะ :</span>
                          <v-chip v-if="item.status === 'cancel'" class="ma-2" color="#F7D9D9" small text-color="#D1392B">ยกเลิกสินค้า</v-chip>
                          <v-chip v-else-if="item.status === 'pending'" class="ma-2" color="#FCF0DA" small text-color="#E9A016">รออนุมัติ</v-chip>
                          <v-chip v-else class="ma-2" color="#E6F5F3" small text-color="#27AB9C">อนุมัติ</v-chip>
                        </v-col>
                        <v-col cols="12">
                          <span style="color: black;">ผู้อนุมัติ : {{ item.approver_name }}({{ item.email }})</span>
                        </v-col>
                        <v-col v-if="item.time_approve === '-'" cols="12" class="mt-3">
                          <span style="color: black;">วันที่อนุมัติ : {{ item.time_approve }}</span>
                        </v-col>
                        <v-col v-else cols="12" class="mt-3">
                          <span style="color: black;">วันที่อนุมัติ : {{new Date(item.time_approve).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
                        </v-col>
                      </v-row>
                    </v-timeline-item>
                  </v-timeline>
                </v-col>
              </v-row>
              <v-row v-else-if="items.transaction_status === 'Cancel' && dataRole.role === 'ext_buyer'" no-gutters>
                <v-col cols="12" class="mt-4">
                  <span class="mr-2">คุณได้ทำการยกเลิกคำสั่งซื้อ หากต้องการซื้อสินค้าอีกครั้ง สามารถเข้าไปเลือกซื้อสินค้ากับเราได้เลย</span>
                </v-col>
              </v-row>
              <v-row v-else-if="items.transaction_status === 'Fail'" no-gutters>
                <v-col cols="12" class="mt-4">
                  <span class="mr-2">คุณชำระเงินไม่สำเร็จ กรุณาตรวจสอบการชำระเงินของคุณอีกครั้ง</span>
                  <v-btn class="white--text" color="#27AB9C" small @click="GoToPayment()">ชำระเงิน</v-btn>
                </v-col>
              </v-row>
              <v-row v-else-if="items.transaction_status === 'Not Paid'" no-gutters>
                <v-col cols="12" class="mt-4" v-if="items.tracking[0].status_tracking === 'Pick Up' || items.tracking[0].status_tracking === 'Not Paid'">
                  <span class="mr-2">คุณยังไม่ได้ทำการชำระเงิน กรุณาชำระเงินผ่านบริการทุกช่องทางของ <b>Not paidcom Payment</b> โดยสามารถกดชำระเงินได้ที่นี่</span>
                  <v-btn class="white--text" color="#27AB9C" small @click="GoToPayment()">ชำระเงิน</v-btn>
                </v-col>
              </v-row>
            </v-row> -->
          </v-container>
          <!-- </v-card> -->
        </v-col>
      </v-row>
    </v-container>
    <!-- Modal กรอกยกเลิกคำสั่งซื้อเมื่อยังไม่จ่ายเงิน -->
    <v-dialog v-model="modalInputReason" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ $t('DetailOrderBuyer.CancelOrder') }}</b></span>
              </v-col>
              <v-btn fab small @click="closemodalInputReason()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 15px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <!-- ข้อมูล order -->
                <!-- <v-row dense class="d-flex pa-4" style="background: #F9FAFD; border-radius: 8px;">
                  <v-col cols="12" md="12" sm="12" class="pt-3 pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">รหัสการสั่งซื้อ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{items.data_list[0].order_number}}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">ผู้ซื้อ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{items.buyer_name}}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">วันที่ทำรายการ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{new Date(items.created_at).toLocaleDateString('th-TH', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: 'numeric',
                      minute: 'numeric'
                    })}}น.</span>
                  </v-col>
                </v-row> -->
                <v-row>
                  <v-col cols="12">
                    <v-img src="@/assets/stepperNew/transport/cancelModalStep1.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="4"></v-img>
                  </v-col>
                  <v-col cols="6" class="d-flex justify-center">
                    <span style="font-size: 16px; font-weight: 700; color: #000000">{{$t('DetailOrderBuyer.CancelOrder')}}</span>
                  </v-col>
                  <v-col cols="6" class="d-flex justify-center">
                    <span style="font-size: 16px; font-weight: 700; color: #cccccc">{{$t('DetailOrderBuyer.refundAccount')}}</span>
                  </v-col>
                </v-row>
                <v-row dense class="mt-5">
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; color: #333333;">{{$t('DetailOrderBuyer.reasonCancel')}}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <v-textarea v-model="reason" :counter="250" maxLength="250" outlined :placeholder="$t('DetailOrderBuyer.reasonCancel')" style="border-radius: 8px; border: 1px solid #CCC;" height="160" hide-details></v-textarea>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="closemodalInputReason()">{{$t('DetailOrderBuyer.Cancel')}}</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" @click="openDialogConfirmCancel('no payment'); modalAwaitCancelOrder = true; modalInputReason = false" :disabled="reason !== '' ? false : true" height="40" class="white--text">{{$t('DetailOrderBuyer.Confirm')}}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Modal ยกเลิก order เมื่อจ่ายเงินแล้ว -->
    <v-dialog v-model="modalInputReasonRefund" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{$t('DetailOrderBuyer.CancelOrder')}}</b></span>
              </v-col>
              <v-btn fab small @click="closemodalInputReasonRefund()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <v-row v-if="cancelModalStep === 1">
                  <v-col cols="12" >
                    <v-img src="@/assets/stepperNew/transport/cancelModalStep1.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="4"></v-img>
                  </v-col>
                  <v-col cols="6" class="d-flex justify-center" v-if="cancelModalStep === 1">
                    <span style="font-size: 16px; font-weight: 700; color: #000000">{{$t('DetailOrderBuyer.CancelOrder')}}</span>
                  </v-col>
                  <v-col cols="6" class="d-flex justify-center" v-if="cancelModalStep === 1">
                    <span style="font-size: 16px; font-weight: 700;" :style="cancelModalStep === 2 ? 'color: #269afd' : 'color: #cccccc'">{{$t('DetailOrderBuyer.refundAccount')}}</span>
                  </v-col>
                </v-row>
                <v-row dense class="mt-5" v-if="cancelModalStep === 1">
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; color: #333333;">{{$t('DetailOrderBuyer.reasonCancel')}}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <v-textarea v-model="reasonRefund" :counter="250" maxLength="250" outlined :placeholder="$t('DetailOrderBuyer.reasonCancel')" style="border-radius: 8px; border: 1px solid #CCC;" height="160" hide-details></v-textarea>
                  </v-col>
                </v-row>
                <v-row dense class="mt-5" v-if="cancelModalStep === 2">
                  <v-col cols="12">
                    <v-img src="@/assets/stepperNew/transport/cancelModalStep2.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="4"></v-img>
                  </v-col>
                  <v-col cols="6" class="d-flex justify-center">
                    <span style="font-size: 16px; font-weight: 700; color: #000000">{{$t('DetailOrderBuyer.CancelOrder')}}</span>
                  </v-col>
                  <v-col cols="6" class="d-flex justify-center">
                    <span style="font-size: 16px; font-weight: 700;" :style="cancelModalStep === 2 ? 'color: #269afd' : 'color: #cccccc'">{{$t('DetailOrderBuyer.refundAccount')}}</span>
                  </v-col>
                  <v-col cols="12" class="d-flex justify-center">
                    <span v-if="urName === '' && bankNameRefund === '' && bankNumberRefund === ''" style="font-size: 16px; font-weight: 700; color: #cccccc">{{$t('DetailOrderBuyer.textAddAccount')}}</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 12 : 12" :class="MobileSize ? '' : 'pl-1'">
                    <v-col class="py-0 px-0">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ $t('DetailOrderBuyer.accountName') }} </span>
                    </v-col>
                    <v-col cols="12" class="px-0">
                      <v-text-field @keypress="CheckSpacebarOne($event)" v-model="urName" :placeholder="$t('DetailOrderBuyer.textEnterName')" dense outlined hide-details></v-text-field>
                    </v-col>
                  </v-col>
                  <v-col :cols="MobileSize ? 12 : 12" :class="MobileSize ? '' : 'pl-1'">
                    <v-col class="py-0 px-0">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ $t('DetailOrderBuyer.BankName') }} </span>
                    </v-col>
                    <v-col cols="12" class="px-0">
                      <v-select v-model="bankNameRefund" :items="listAccount" item-text="name" item-value="code" :menu-props="{ offsetY: false, maxHeight: '231px', maxWidth: '280px', overflowY: 'hidden' }" :placeholder="$t('DetailOrderBuyer.textEnterBank')" dense outlined hide-details></v-select>
                    </v-col>
                  </v-col>
                  <v-col :cols="MobileSize ? 12 : 12">
                    <v-col class="py-0 px-0">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ $t('DetailOrderBuyer.BankAccountNumber') }} </span>
                    </v-col>
                    <v-col cols="12" class="px-0">
                      <v-text-field
                        @keypress="CheckSpacebarOne($event)"
                        v-model="bankNumberRefund"
                        oninput="this.value = this.value.replace(/[^0-9\s]/g, '')"
                        :placeholder="$t('DetailOrderBuyer.textAccountNumber')"
                        dense
                        outlined
                        hide-details>
                      </v-text-field>
                    </v-col>
                  </v-col>
                </v-row>
                <!-- <v-row dense class="d-flex pa-4" style="background: #F9FAFD; border-radius: 8px;">
                  <v-col cols="12" md="12" sm="12" class="pt-3 pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">รหัสการสั่งซื้อ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{items.data_list[0].order_number}}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">ผู้ซื้อ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{items.buyer_name}}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="pb-3">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">วันที่ทำรายการ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{new Date(items.created_at).toLocaleDateString('th-TH', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: 'numeric',
                      minute: 'numeric'
                    })}}น.</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" :class="MobileSize ? 'ma-0 pa-0 align-center' : 'd-flex ma-0 pa-0 align-center'">
                    <v-col :cols="MobileSize ? 12 : 6" :class="MobileSize ? '' : 'pl-1'">
                      <v-col class="py-0 px-0">
                        <span style="font-size: 16px; font-weight: 600; color: #333333;">ชื่อบัญชีธนาคาร <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-text-field v-model="urName" placeholder="ระบุชื่อบัญชีธนาคาร" dense outlined hide-details></v-text-field>
                      </v-col>
                    </v-col>
                    <v-col :cols="MobileSize ? 12 : 6">
                      <v-col class="py-0 px-0">
                        <span style="font-size: 16px; font-weight: 600; color: #333333;">เบอร์โทรศัพท์ <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-text-field oninput="this.value = this.value.replace(/[^0-9\s]/g, '')" v-model="telNumber" placeholder="ระบุเบอร์โทรศัพท์" dense outlined hide-details></v-text-field>
                      </v-col>
                    </v-col>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" :class="MobileSize ? 'ma-0 align-center' : 'd-flex ma-0 align-center'">
                    <v-col :cols="MobileSize ? 12 : 6" :class="MobileSize ? '' : 'pl-1'">
                      <v-col class="py-0 px-0">
                        <span style="font-size: 16px; font-weight: 600; color: #333333;">ชื่อธนาคาร <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-select v-model="bankNameRefund" :items="listAccount" item-text="name" item-value="code" :menu-props="{ offsetY: false, maxHeight: '231px', maxWidth: '280px', overflowY: 'hidden' }" placeholder="ระบุธนาคาร" dense outlined hide-details></v-select>
                      </v-col>
                    </v-col>
                    <v-col :cols="MobileSize ? 12 : 6">
                      <v-col class="py-0 px-0">
                        <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขบัญชีธนาคาร <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-text-field
                          @keypress="CheckSpacebarOne($event)"
                          v-model="bankNumberRefund"
                          oninput="this.value = this.value.replace(/[^0-9\s]/g, '')"
                          placeholder="ระบุเลขบัญชีธนาคาร"
                          dense
                          outlined
                          hide-details>
                        </v-text-field>
                      </v-col>
                    </v-col>
                  </v-col>
                </v-row>
                <v-row dense class="mt-4">
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">เหตุผลยกเลิกคำสั่งซื้อ</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <v-textarea v-model="reasonRefund" :counter="250" maxLength="250" outlined placeholder="กรุณาระบุเหตุผลยกเลิกคำสั่งซื้อ" style="border-radius: 8px; border: 1px solid #CCC;" height="160" hide-details></v-textarea>
                  </v-col>
                </v-row> -->
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="closemodalInputReasonRefund()">{{$t('DetailOrderBuyer.Cancel')}}</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" @click="cancelModalStep === 1 ? openDialogConfirmCancel('payment') : openDialogConfirmCancelStep2();" :disabled="cancelModalStep === 1 ? reasonRefund === '' : bankNumberRefund === '' || bankNameRefund === '' || urName === ''" height="40" class="white--text">{{$t('DetailOrderBuyer.Confirm')}}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Modal ยกเลิก order เมื่อจ่ายเงินแล้ว เมื่อส่งคำขอไปแล้ว -->
    <v-dialog v-model="modalInputReasonDetail" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{$t('DetailOrderBuyer.CancelOrder')}}</b></span>
              </v-col>
              <v-btn fab small @click="modalInputReasonDetail = false" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <!-- ข้อมูล order -->
                <v-row dense class="d-flex pa-4" style="background: #F9FAFD; border-radius: 8px;">
                  <v-col cols="12" md="12" sm="12" class="pt-3 pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">{{$t('DetailOrderBuyer.OrderNumber')}} :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{items.data_list[0].order_number}}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">{{$t('DetailOrderBuyer.BuyerName')}} :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{items.buyer_name}}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">{{$t('DetailOrderBuyer.CreateAt')}} :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{new Date(timeStampCancel).toLocaleDateString('th-TH', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: 'numeric',
                      minute: 'numeric'
                    })}}น.</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span class="pr-3" style="font-size: 16px; font-weight: 600; color: #333333;">{{$t('DetailOrderBuyer.Status')}} :</span><v-chip :color="statusCancel === 'approve' ? '#F0F9EE' : statusCancel === 'waiting' ? '#fef3de' : '#FEE7E8'" :text-color="statusCancel === 'approve' ? 'green' : statusCancel === 'waiting' ? '#e9a016' : 'red'" style="font-weight: bold; font-size: 14px;">{{statusCancelorder(statusCancel)}}</v-chip>
                  </v-col>
                  <!-- <v-col cols="12" md="12" sm="12" :class="MobileSize ? 'ma-0 pa-0 align-center' : 'd-flex ma-0 pa-0 align-center'">
                    <v-col :cols="MobileSize ? 12 : 6" :class="MobileSize ? '' : 'pl-1'">
                      <v-col class="py-0 px-0">
                        <span style="font-size: 16px; font-weight: 600; color: #333333;">ชื่อบัญชีธนาคาร <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-text-field readonly v-model="urName" placeholder="ระบุชื่อบัญชีธนาคาร" dense outlined hide-details></v-text-field>
                      </v-col>
                    </v-col>
                    <v-col :cols="MobileSize ? 12 : 6">
                      <v-col class="py-0 px-0">
                        <span style="font-size: 16px; font-weight: 600; color: #333333;">เบอร์โทรศัพท์ <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-text-field readonly v-model="telNumber" placeholder="ระบุเบอร์โทรศัพท์" dense outlined hide-details></v-text-field>
                      </v-col>
                    </v-col>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" :class="MobileSize ? 'ma-0 align-center' : 'd-flex ma-0 align-center'">
                    <v-col :cols="MobileSize ? 12 : 6" :class="MobileSize ? '' : 'pl-1'">
                      <v-col class="py-0 px-0">
                        <span style="font-size: 16px; font-weight: 600; color: #333333;">ชื่อธนาคาร <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-select readonly v-model="bankNameRefund" :items="listAccount" item-text="name" item-value="code" :menu-props="{ offsetY: false, maxHeight: '231px', maxWidth: '280px', overflowY: 'hidden' }" placeholder="ระบุธนาคาร" dense outlined hide-details></v-select>
                      </v-col>
                    </v-col>
                    <v-col :cols="MobileSize ? 12 : 6">
                      <v-col class="py-0 px-0">
                        <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขบัญชีธนาคาร <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-text-field v-model="bankNumberRefund" readonly placeholder="ระบุเลขบัญชีธนาคาร" dense outlined hide-details></v-text-field>
                      </v-col>
                    </v-col>
                  </v-col> -->
                </v-row>
                <!-- <v-row dense class="mt-4">
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">เหตุผลยกเลิกคำสั่งซื้อ</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <v-textarea readonly v-model="reasonRefund" :counter="250" maxLength="250" outlined placeholder="กรุณาระบุเหตุผลยกเลิกคำสั่งซื้อ" style="border-radius: 8px; border: 1px solid #CCC;" height="160" hide-details></v-textarea>
                  </v-col>
                </v-row> -->
                <!-- เหตุผลอของร้านค้าเมื่อกดปฏิเสธคำขอยกเลิก order ดักจาก key reason_seller -->
                <v-row dense class="mt-4" v-if="statusCancel === 'reject'">
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">{{$t('DetailOrderBuyer.SellerReason')}}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <v-textarea readonly v-model="reasonSeller" :counter="250" maxLength="250" outlined :placeholder="$t('DetailOrderBuyer.PleaseEnterCancellationReason')" style="border-radius: 8px; border: 1px solid #CCC;" height="160" hide-details></v-textarea>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <!-- <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="closemodalInputReasonRefund()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" @click="openDialogConfirmCancel('payment')" :disabled="reasonRefund && bankNumberRefund && bankNameRefund && telNumber && urName !== '' ? false : true" height="40" class="white--text">ยืนยัน</v-btn>
        </v-card-actions> -->
      </v-card>
    </v-dialog>
    <v-dialog v-model="DialogQR" persistent :width="MobileSize ? '100%' : '640'">
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden; ">
          <v-card-text class="px-0">
            <div :style="MobileSize ? 'width: 100%' : 'width: 640px'" class="backgroundHead"
              style="position: absolute; height: 120px; ">
              <v-row style="height: 120px; ">
                <v-col style="text-align: center;" class="pt-4">
                  <span style="margin-left: 47px"
                    :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{$t('DetailOrderBuyer.ScanQRCodeForPayment')}}</b></span>
                </v-col>
                <v-btn fab small @click="closeDialogQR()" icon class="mt-3"><v-icon
                    color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '640px'"
                style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card class="d-flex justify-center mt-10 mb-9" elevation="0" width="100%" height="100%"
                style="background: #FFFFFF; border-radius: 20px;">
                <div style="text-align: center;">
                  <!-- <v-img height="280" width="280" style="margin-inline: auto;" :src="`${ImageQR}`" v-if="ImageQR !== ''"></v-img>
                  <v-btn color="#27AB9C" rounded width="125" height="40" class="white--text my-8" @click="saveQRCode()">บันทึกรูปภาพ</v-btn> -->
                  <v-col class="py-0">
                    <img id="qrcode" height="280" width="280" style="margin-inline: auto;" :src="`${ImageQR}`" v-if="ImageQR !== ''"/>
                  </v-col>
                  <v-col class="py-0">
                    <v-btn v-if="TypeOS !== 'iOS'" color="#27AB9C" rounded width="125" height="40" class="white--text my-8" @click="saveQRCode()">{{$t('DetailOrderBuyer.btnSaveImage')}}</v-btn>
                  </v-col>
                  <div>
                    <v-col>
                      <span style="font-size: 20px; font-weight: 700;">{{$t('DetailOrderBuyer.TotalPaymentAmount')}} : {{ Number(netPrice).toLocaleString(undefined, { minimumFractionDigits: 2 })}}
                        {{$t('DetailOrderBuyer.Baht')}}</span>
                    </v-col>
                    <v-col>
                      <span style="font-size: 14px; font-weight: 400;">{{$t('DetailOrderBuyer.referenceCode')}} {{Ref1}}</span>
                    </v-col>
                    <v-col class="py-0">
                      <span style="font-size: 14px; font-weight: 600; color: #A1A1A1;">{{$t('DetailOrderBuyer.CanMakePaymentByFollowingStep')}}</span>
                    </v-col>
                    <v-col class="text-left py-0">
                      <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">1. {{$t('DetailOrderBuyer.capture')}}<br><span style="color: red; font-weight: 700;">* {{$t('DetailOrderBuyer.caseiOS')}}</span> {{$t('DetailOrderBuyer.clickSave')}} <b>{{$t('DetailOrderBuyer.saveToApp')}}</b></span>
                    </v-col>
                    <v-col class="text-left py-0">
                      <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">2. {{$t('DetailOrderBuyer.selectMenuScan')}}</span>
                    </v-col>
                    <v-col class="text-left py-0">
                      <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">3. {{$t('DetailOrderBuyer.selectScreenshot')}}</span>
                    </v-col>
                  </div>
                </div>
              </v-card>
            </div>
          </v-card-text>
        </v-card>
    </v-dialog>
    <v-dialog v-model="dialogChooesPayType" persistent width="550">
      <v-card :height=" MobileSize || IpadSize ? '439':''" style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="75px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png')">
          <v-toolbar flat color="rgba(0, 0, 0, 0)">
            <v-spacer></v-spacer>
            <v-toolbar-title class="mt-5" style="font-size: 18px; font-weight: 700; color: #FFFFFF;">{{$t('DetailOrderBuyer.selectPayment')}}</v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn class="mt-3" color="#CCCCCC" icon
              @click="openDialogPayment()">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
        </v-img>
        <!-- <v-card-title style="place-content: center;" class="px-0">
          <span style="font-size: 20px;font-weight: 700; color: #FAAD14;">วิธีการชำระเงิน</span>
        </v-card-title> -->
        <v-card-text >
          <v-row dense no-gutters class="mb-10 mt-3">
            <!-- {{selected[0].payment_method[2]}} -->
            <!-- <v-radio-group v-model="radioPayment" row class="ma-0 pa-0" v-if="selected.length !== 0">
              <v-radio value="radio-qr" v-if="paymentMethods[0] === 'qrcode' || paymentMethods[1] === 'qrcode' || paymentMethods[2] === 'qrcode'"><template v-slot:label>
                  <span style="font-size: 16px;">QR Code</span>
                </template>
              </v-radio>
              <v-radio value="radio-credit" v-if="paymentMethods[0] === 'creditcard' || paymentMethods[1] === 'creditcard' || paymentMethods[2] === 'creditcard'"><template v-slot:label>
                  <span tyle="font-size: 16px;">Credit Card / Debit Card</span>
                </template>
              </v-radio>
              <v-radio value="radio-installment" v-if="paymentMethods[0] === 'installment' || paymentMethods[1] === 'installment' || paymentMethods[2] === 'installment'" >
                <template v-slot:label>
                  <span tyle="font-size: 16px;">Credit Card แบบผ่อนชำระ</span>
                </template>
              </v-radio>
            </v-radio-group> -->
            <!-- <v-radio-group v-model="radioPayment" row class="ma-0 pa-0" v-if="selected.length !== 0">
              <v-radio value="radio-qr" v-if="selected[0].payment_method[0] === 'qrcode' || selected[0].payment_method[1] === 'qrcode' || selected[0].payment_method[2] === 'qrcode'"><template v-slot:label>
                  <span style="font-size: 16px;">QR Code</span>
                </template>
              </v-radio>
              <v-radio value="radio-credit" v-if="selected[0].payment_method[0] === 'creditcard' || selected[0].payment_method[1] === 'creditcard' || selected[0].payment_method[2] === 'creditcard'"><template v-slot:label>
                  <span tyle="font-size: 16px;">Credit Card / Debit Card</span>
                </template>
              </v-radio>
              <v-radio value="radio-installment" v-if="selected[0].payment_method[0] === 'installment' || selected[0].payment_method[1] === 'installment' || selected[0].payment_method[2] === 'installment'" >
                <template v-slot:label>
                  <span tyle="font-size: 16px;">Credit Card แบบผ่อนชำระ</span>
                </template>
              </v-radio>
            </v-radio-group> -->
            <v-radio-group v-model="radioPayment" class="px-5">
              <v-card outlined :style="radioPayment === 'radio-qr' ? 'border-radius: 8px; border-color: #3EC6B6;' : 'border-radius: 8px;'" min-height="60" :min-width="MobileSize ? '' : 460" class="d-flex align-content-center mb-5">
                <v-radio color="#3EC6B6" value="radio-qr" class="pl-3"><template v-slot:label>
                    <v-avatar rounded class="mr-2 ml-3">
                      <v-img src="@/assets/QRIcon.png" width="30.86" height="30.86" contain></v-img>
                    </v-avatar>
                    <span style="font-size: 16px; font-weight: bold; color: black;">QR Code</span>
                  </template>
                </v-radio>
              </v-card>
              <v-card outlined :style="radioPayment === 'radio-credit' ? 'border-radius: 8px; border-color: #3EC6B6;' : 'border-radius: 8px;'" min-height="60" :min-width="MobileSize ? '' : 460" class="d-flex align-content-center">
                <v-radio color="#3EC6B6" value="radio-credit" class="pl-3"><template v-slot:label>
                  <v-avatar rounded class="mr-2 ml-3">
                    <v-img src="@/assets/CreditCardIcon.png" width="30.86" height="30.86" contain></v-img>
                  </v-avatar>
                  <span style="font-size: 16px; font-weight: bold; color: black;">Credit Card / Debit Card</span>
                </template>
              </v-radio>
              </v-card>
              <!-- <v-radio value="radio-installment">
                <template v-slot:label>
                  <span tyle="font-size: 16px;">Credit Card แบบผ่อนชำระ</span>
                </template>
              </v-radio> -->
            </v-radio-group>
          </v-row>
          <v-row no-gutters justify="center" class="mt-5" v-if="radioPayment === 'radio-installment'">
            <v-col :cols="MobileSize ? 6 : 4" class="pl-8">
              <span style="font-size: 16px;">{{$t('DetailOrderBuyer.installmentTerm')}}</span>
            </v-col>
            <v-col :cols="MobileSize ? 6 : 8" class="ma-0 pa-0" md="8" sm="6">
              <v-select style="border-radius: 8px;" outlined dense :label="$t('DetailOrderBuyer.selectTerm')" v-model="radioCreditTerm" :items="filteredCreditTerms" item-text="displayText" item-value="value" >
                <template v-slot:append>
                  <v-icon>mdi-chevron-down</v-icon>
                </template>
                <template v-slot:no-data>
                  <v-list-item>
                    <v-list-item-content class="text-center">
                      <v-list-item-title>{{$t('DetailOrderBuyer.InstallmentPaymentNotAvailableBecause')}} <span style="color: #27AB9C;">{{$t('DetailOrderBuyer.Min')}}</span> {{$t('DetailOrderBuyer.RequiredAmountHasNotBeenMet')}}</v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                </template>
              </v-select>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-text class="mt-10 py-5" style="background-color: #F5FCFB;">
          <v-row dense justify="center" class="px-10">
            <v-btn :width="MobileSize? '100':'100'" height="38" outlined rounded color="#27AB9C" class="mr-4"
              @click="openDialogPayment()">{{$t('DetailOrderBuyer.Cancel')}}</v-btn>
            <v-spacer></v-spacer>
            <v-btn elevation="0" :disabled="radioPayment === 'no' || radioPayment === 'radio-installment' && radioCreditTerm === ''" :width="MobileSize? '100':'100'" height="38" class="white--text " rounded color="#27AB9C"
              @click="confirmPayment()">{{$t('DetailOrderBuyer.Confirm')}}</v-btn>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogConfirm" width="424" persistent>
      <v-card :height=" MobileSize || IpadSize ? '400':''" style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="240px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn @click="dialogConfirm = false" color="#CCCCCC" icon>
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b></b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{$t('DetailOrderBuyer.ComfirmModal')}}</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize? '100':'156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogConfirm = false">{{$t('DetailOrderBuyer.Cancel')}}</v-btn>
              <v-btn :width="MobileSize? '100':'156'" height="38" class="white--text" rounded color="#27AB9C" @click="radioPayment === 'radio-qr' ? GetQRCode('cashPayment'): GetCC('cashPayment') ">{{$t('DetailOrderBuyer.Confirm')}}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Await Confirm Order -->
    <v-dialog v-model="modalAwaitCancelOrder" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeDialogConfirmCancel()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{$t('DetailOrderBuyer.cancelOrder')}}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{$t('DetailOrderBuyer.textcancel')}}</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{$t('DetailOrderBuyer.textThisOrder')}}</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeDialogConfirmCancel()">{{$t('DetailOrderBuyer.Cancel')}}</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="confirmCancel()">{{$t('DetailOrderBuyer.Confirm')}}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Cancel Order -->
    <v-dialog v-model="modalSuccessCancelOrder" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeSuccessCancelOrder()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{$t('DetailOrderBuyer.cancelSuccess')}}</b></p>
            <span v-if="cancelType === 'no payment'" style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{$t('DetailOrderBuyer.textHaveCancel')}}</span>
            <span v-if="cancelType === 'payment'" style="font-weight: 700; font-size: 16px; line-height: 24px; color: red;">*{{$t('DetailOrderBuyer.receivedRefund')}}</span>
            <span v-if="cancelType === 'payment'" style="font-weight: 700; font-size: 16px; line-height: 24px; color: red;"> {{ $t('DetailOrderBuyer.byDate') }}*</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeSuccessCancelOrder()">{{$t('DetailOrderBuyer.Confirm')}}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Modal Upload Slip -->
    <v-dialog v-model="dialogOpenDialogUploadSlip" :width="MobileSize ? '100%' : IpadSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{$t('DetailOrderBuyer.UploadSlip')}}</b></span>
              </v-col>
              <v-btn fab small @click="cancelUploadSlip()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <v-card-text style="text-align: center;" class="mt-6" v-if="base64_slip === ''">
                <!-- <span class="textUploadnameImage">(เพิ่มได้สูงสุด 5 รูปภาพ)</span> -->
                <v-row justify="center" class="pt-6" style="cursor: pointer;">
                  <v-col cols="12" align="center">
                    <v-card @click="UploadSlip()" width="700" height="363" elevation="0" style="border: 2px dashed rgba(0, 0, 0, 0.25); border-radius: 10px; box-sizing: border-box;">
                      <v-card-text>
                        <v-col cols="12" md="12" class="mb-6">
                          <v-row justify="center" class="pt-10">
                            <v-file-input
                              v-model="DataImage"
                              :items="DataImage"
                              accept="image/jpeg, image/jpg, image/png"
                              @change="UploadImageSlip()"
                              @click="event => event.target.value = null"
                              id="file_input_slip"
                              :clearable="false"
                              style="display:none"
                            >
                            </v-file-input>
                            <v-img
                              src="@/assets/ImageINET-Marketplace/ICONShop/cloud-upload.png"
                              width="143"
                              height="143"
                              contain
                            ></v-img>
                          </v-row>
                        </v-col>
                        <v-col cols="12" md="12" class="mt-2">
                          <v-row justify="center" align="center">
                            <v-col cols="12" md="12" style="text-align: center;">
                              <!-- <span class="textUpload">ลากรูปภาพของคุณมาที่นี่ เพื่ออัปโหลด</span><br /> -->
                              <span class="textUploadSelect">{{$t('DetailOrderBuyer.SelectImage')}}</span><br />
                              <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .JPEG, .PNG, .JPG)</span>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
              <v-card-text v-else>
                <v-row dense>
                  <v-col cols="12">
                    <v-row dense justify="center" class="pt-4">
                      <v-col cols="12" align="center" >
                        <v-img :src="base64_slip.path" height="100%" width="100%" max-height="600" max-width="300" contain>
                          <v-btn icon small style="float: right; background-color: #ff5252;">
                            <v-icon small color="white" dark @click="RemoveImageSlip()">mdi-close</v-icon>
                          </v-btn>
                        </v-img>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="cancelUploadSlip()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" height="40" class="white--text" :disabled="base64_slip === ''" @click="uploadSlipFile()">อัปโหลด</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Dialog Address -->
    <v-dialog v-model="DialogAddress" :width="MobileSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead"
            style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-6">
                <span
                  :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>เปลี่ยนที่อยู่ในการจัดส่งสินค้า</b></span>
              </v-col>
              <v-btn fab small @click="DialogAddress = false" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <div :class="MobileSize ? 'py-6 px-4' : 'py-12 px-10'">
                <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                <span :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'" >ที่อยู่ในการจัดส่งสินค้า</span>
                <div v-if="!MobileSize">
                  <v-card class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in dataAddress"
                    :key="index"
                    style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                    <v-row>
                      <v-col cols="1" class="align-self-center mt-2">
                        <v-radio-group v-model="addressDefaultID" class="mt-0 pt-0">
                          <v-radio color="#27AB9C" :value="item.id" :disabled="item.id === addressDefaultID" @click="changeAddressShipping(item)" style="color: #333333">
                          </v-radio>
                        </v-radio-group>
                      </v-col>
                      <div class="col-11 row">
                        <v-col class="pl-0 pb-0" cols="7">
                          <span style="font-size: 16px; font-weight: 600;">{{ item.first_name }} {{ item.last_name }}</span>
                          <span class="px-1" style="color: #EBEBEB;">|</span>
                          <span style="font-size: 16px; font-weight: 600;">{{ item.phone }}</span>
                        </v-col>
                        <v-col class="pl-0 pb-0" cols="5">
                          <v-row no-gutters justify="end">
                            <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                              @click="editAddress(item)"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                                style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                            <v-btn :disabled="dataAddress.length === 1 || (item.default_address === 'Y' || item.id === addressDefaultID)" color="red" text
                              style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                              @click="DeleteAddress(item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                                style="text-decoration-line: underline;">ลบ</span></v-btn>
                          </v-row>
                        </v-col>
                        <v-col class="pb-2 pl-0 pa-3 pt-0">
                          <span style="font-size: 16px;">{{ item.detail }} {{ item.sub_district }} {{ item.district }} {{
                            item.province }} {{ item.zip_code }}</span><br>
                            <span v-if="item.note_address" style="font-size: 16px; color: #333333;">หมายเหตุ : {{ item.note_address }}</span>
                        </v-col>
                      </div>
                    </v-row>
                  </v-card>
                </div>
                <div v-else>
                  <v-card class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in dataAddress"
                    :key="index"
                    style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                    <v-row>
                      <v-col  class="align-self-center mt-2">
                        <v-radio-group v-model="addressDefaultID" class="mt-0 pt-0">
                          <v-radio color="#27AB9C" :value="item.id" :disabled="item.id === addressDefaultID" @click="changeAddressShipping(item)" style="color: #333333">
                          </v-radio>
                        </v-radio-group>
                      </v-col>
                      <div class="row">
                        <v-col class="pl-0 pb-0 mt-5">
                          <v-row no-gutters justify="end">
                            <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                              @click="editAddress(item)"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                                style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                            <v-btn :disabled="dataAddress.length === 1 || (item.default_address === 'Y' || item.id === addressDefaultID)" color="red" text
                              style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                              @click="DeleteAddress(item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                                style="text-decoration-line: underline;">ลบ</span></v-btn>
                          </v-row>
                        </v-col>
                      </div>
                      <v-col class="pt-2" cols="12">
                          <span :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 16px; font-weight: 600;'" >{{ item.first_name }} {{ item.last_name }}</span>
                          <span class="px-1" style="color: #EBEBEB;">|</span>
                          <span :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 16px; font-weight: 600;'">{{ item.phone }}</span>
                        </v-col>
                      <v-col class="pb-2 px-2 pt-0">
                        <span :style="MobileSize ? 'font-size: 12px;' : 'font-size: 16px;'">{{ item.detail }} {{ item.sub_district }} {{ item.district }} {{
                          item.province }} {{ item.zip_code }}</span><br>
                          <span v-if="item.note_address" style="font-size: 12px; color: #333333;">หมายเหตุ : {{ item.note_address }}</span>
                      </v-col>
                    </v-row>
                  </v-card>
                </div>
                <div v-if="dataAddress.length === 0" style="text-align: -webkit-center;">
                  <v-img src="@/assets/emptypo.png" width="500" height="100%"></v-img>
                  <v-col class="pt-10 pb-0">
                    <span  :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'">ไม่มีที่อยู่ในการจัดส่งสินค้า</span>
                  </v-col>
                </div>
                <v-card v-if="dataAddress.length !== 10" class="mt-7" elevation="0"
                  style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="addAddress()">
                  <v-card-text class="py-2">
                    <v-row class="my-2 px-3 ">
                      <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                      <span class="pl-2"
                        style="font-weight: 500; font-size: 14px; color: #1B5DD6;">เพิ่มที่อยู่ใหม่</span>
                    </v-row>
                  </v-card-text>
                </v-card>
              </div>
            </v-card>
          </div>
        </v-card-text>
        <!-- <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="cancelChangeAddrss()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" height="40" class="white--text"
            @click="confirmAddress()">ยืนยัน</v-btn>
        </v-card-actions> -->
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogAwaitConfirm" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="240px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn color="#CCCCCC" icon
              @click="dialogAwaitConfirm = !dialogAwaitConfirm">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ลบที่อยู่จัดส่ง</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">คุณต้องการทำรายการนี้ ใช่
                หรือ ไม่</span>
            </v-card-text>
          <v-card-text>
            <v-row v-if="!MobileSize" dense justify="center">
              <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4"
                @click="CancelAwaitConfirm()">ยกเลิก</v-btn>
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C"
                @click="deleteAddress()">ตกลง</v-btn>
            </v-row>
            <v-row v-if="MobileSize" dense>
              <v-btn outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }" @click="CancelAwaitConfirm()">ยกเลิก</v-btn>
              <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }"
                @click="deleteAddress()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <ModalRefundProductBuyer ref="ModalRefundProductBuyer" />
    <ModalReviewProduct ref="ModalReviewProduct" />
    <EditModalAddress :EditAddressDetail="EditAddressDetail" :title="titleAddress" :page="page" />
  </div>
</template>

<script>
import { Encode } from '@/services'
import { Table } from 'ant-design-vue'
// import detailBuyerMockData from '@/components/UserProfile/detailBuyer.json'
export default {
  components: {
    'a-table': Table,
    EditModalAddress: () => import(/* webpackPrefetch: true */ '@/components/Modal/EditAddressProfile'),
    ModalRefundProductBuyer: () => import('@/components/Modal/RefundProductBuyerModal'),
    ModalReviewProduct: () => import('@/components/UserProfile/ModalReview/ReviewProduct'),
    OrderSummary: () => import('@/components/Card/OrderSummary.vue')
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      TypeOS: '',
      header: [
        // { text: 'รหัส SKU', value: 'sku', align: 'start', filterable: false, sortable: false, width: '15%', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รายละเอียดสินค้า', value: 'productdetails', sortable: false, align: 'start', width: '25%', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ราคาต่อชิ้น', value: 'revenue_default', align: 'start', filterable: false, sortable: false, width: '15%', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จำนวน', value: 'quantity', filterable: false, sortable: false, width: '15%', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ราคารวม', value: 'total_revenue_default', filterable: false, sortable: false, width: '15%', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'Amount', value: 'revenue_amount', filterable: false, sortable: false, align: 'start', width: '15%', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      Rules: {
        bookBankNo: [
          v => v.length > 10 || 'กรุณากรอกเลขบัญชีธนาคาร'
        ]
      },
      timeStampCancel: '',
      modalInputReasonDetail: false,
      detailCancel: [],
      statusCancel: '',
      cancelType: '',
      modalSuccessCancelOrder: false,
      listAccount: [],
      reason: '',
      reasonRefund: '',
      reasonSeller: '',
      urName: '',
      bankNameRefund: '',
      telNumber: '',
      bankNumberRefund: '',
      modalAwaitCancelOrder: false,
      modalInputReason: false,
      modalInputReasonRefund: false,
      trackingStatusFront: '',
      productService: [],
      productNormal: [],
      productFree: [],
      coupon: [],
      productType: '',
      ImageQR: '',
      radioCreditTerm: '',
      netPrice: '',
      Ref1: '',
      DialogQR: false,
      radioPayment: 'no',
      dialogChooesPayType: false,
      dialogConfirm: false,
      items: [],
      paymentNumber: {},
      statusStepper: 1,
      bankName: '',
      dataRole: '',
      trackingStatus: '',
      trackingPayment: '',
      trackingText: '',
      flashTrackingNo: '',
      flashTrackingData: {},
      receivedDate: '',
      sentDate: '',
      sentTime: '',
      step: 0,
      flashMCHID: process.env.VUE_APP_FLASH,
      checkAcceptProduct: [],
      mockupTracking: {},
      flashRoutes: [],
      dataReceived: '',
      dateCreateOrderStep1: '',
      dateCreateOrderStep2: '',
      dateCreateOrderStep3: '',
      dateCreateOrderStep4: '',
      dateCreateOrderCancel: '',
      itemsCreditTerm: [
        { value: '1', label: 'ไม่ผ่อนชำระ' },
        { value: '3', label: '3 เดือน' },
        { value: '6', label: '6 เดือน' },
        { value: '10', label: '10 เดือน' }
      ],
      CloseDialog: false,
      imageBase64: '',
      base64_slip: '',
      dialogOpenDialogUploadSlip: false,
      DataImage: '',
      datatoShowImage: '',
      dataAddress: [],
      fullname: '',
      telnumber: '',
      DialogAddress: false,
      EditAddressDetail: '',
      titleAddress: '',
      page: '',
      dialogAwaitConfirm: false,
      typeButton: '',
      addressDefaultID: '',
      downloadLink: '',
      urlTracking: '',
      trackingNoOutSource: '',
      poPDF: '',
      prPDF: '',
      soPDF: '',
      QTorder: '',
      documents: [],
      orderPlatformNumber: '',
      headersDetailProduct: [
        { text: 'รายละเอียดสินค้า', value: 'productdetails', width: '50', align: 'center', filterable: false, sortable: true, class: 'backgroundTable fontTable--text' },
        { text: 'ราคาต่อชิ้น', value: 'price', width: '50', align: 'center', filterable: true, sortable: true, class: 'backgroundTable fontTable--text' },
        { text: 'จำนวน', value: 'quantity', width: '50', align: 'center', filterable: true, sortable: true, class: 'backgroundTable fontTable--text' },
        { text: 'ราคารวม', value: 'net_price', width: '50', align: 'center', filterable: true, sortable: true, class: 'backgroundTable fontTable--text' }
      ],
      cancelModalStep: 1,
      itemsDoc: [
        { text: 'PO', value: '', file: '' },
        { text: 'PR', value: '', file: '' },
        { text: 'SO', value: '', file: '' }
      ]
    }
  },
  async created () {
    this.$EventBus.$on('getDetailPOBuyer', this.SwitchRole)
    this.$EventBus.$on('SentGetReview', this.getItemProduct)
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    this.TypeOS = this.detectOS()
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.paymentNumber = { payment_transaction_number: this.$route.query.orderNumber, role_user: 'ext_buyer' }
      await this.getItemProduct()
      await this.getOrderDocument(this.$route.query.orderNumber)
      await this.getDetailCancel()
    }
    localStorage.setItem('orderNumber', this.$route.query.orderNumber)
    this.orderPlatformNumber = this.items.order_platform_number === undefined || this.items.order_platform_number === null ? '-' : this.items.order_platform_number
    // console.log(this.orderPlatformNumber, 'this.orderPlatformNumber')
  },
  beforeDestroy () {
    this.$EventBus.$off('SentGetReview')
  },
  mounted () {
    this.$EventBus.$on('getAddressOrderDetail', this.getAddress)
  },
  computed: {
    // CheckPayment () {
    //   if (this.items.payment_method.length === 3) {
    //     return true
    //   } else {
    //     return false
    //   }
    // },
    filteredCreditTerms () {
      return this.itemsCreditTerm.map(item => {
        const correspondingInstallment = this.items.installment_method.find(
          installment => installment.month === item.value
        )
        return {
          ...item,
          displayText: correspondingInstallment ? `${item.label} - ${Number(correspondingInstallment.price).toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                })} บาท/เดือน` : item.label
        }
      }).filter(item => {
        // กรองตามเงื่อนไขที่ต้องการ
        const correspondingInstallment = this.items.installment_method.find(installment => installment.month === item.value)
        return correspondingInstallment && correspondingInstallment.price >= 500
      })
    },
    headers () {
      const headers = [
        // {
        //   title: 'รหัส SKU',
        //   dataIndex: 'sku',
        //   scopedSlots: { customRender: 'sku' },
        //   key: 'sku',
        //   align: 'center',
        //   width: '20%'
        // },
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '30%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'price',
          scopedSlots: { customRender: 'price' },
          key: 'price',
          align: 'center',
          width: '20%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          align: 'center',
          width: '15%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'net_price',
          scopedSlots: { customRender: 'net_price' },
          key: 'net_price',
          align: 'center',
          width: '20%'
        }
      ]
      return headers
    },
    headersMobile () {
      const headersMobile = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '75%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          align: 'center',
          width: '25%'
        }
      ]
      return headersMobile
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: `/pobuyerdetailMobile?orderNumber=${this.paymentNumber.payment_transaction_number}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/pobuyerdetail?orderNumber=${this.paymentNumber.payment_transaction_number}` }).catch(() => {})
      }
    }
  },
  methods: {
    linkToURLTracking (url) {
      this.urlTracking = url
      setTimeout(() => {
        document.getElementById('urlTracking').click()
      }, 200)
    },
    async changeAddressShipping (item) {
      this.DialogAddress = false
      this.$store.commit('openLoader')
      const data = {
        order_number: this.items.payment_transaction,
        address_id: item.id
      }
      await this.$store.dispatch('actionsChangeShippingAddress', data)
      const response = await this.$store.state.ModuleUser.stateChangeShippingAddress
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'success', title: 'เปลี่ยนที่อยู่จัดส่งสำเร็จ', showConfirmButton: false, timer: 1500 })
        await this.getItemProduct()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 1500 })
      }
    },
    async CancelAwaitConfirm () {
      this.dialogAwaitConfirm = false
    },
    addAddress () {
      const val = {
        building_name: '',
        default_address: '',
        detail: '',
        district: '',
        email: '',
        first_name: '',
        floor: '',
        house_no: '',
        id: '',
        last_name: '',
        moo_ban: '',
        moo_no: '',
        order_number: '',
        phone: '',
        province: '',
        room_no: '',
        soi: '',
        status: '',
        street: '',
        sub_district: '',
        user_id: '',
        yaek: '',
        zipcode: ''
      }
      this.reShippingData = true
      localStorage.setItem('AddressUserDetail', Encode.encode(val))
      this.EditAddressDetail = val
      this.titleAddress = 'เพิ่มที่อยู่ในการจัดส่งสินค้า'
      this.page = 'orderDetail'
      this.$EventBus.$emit('EditModalAddress')
    },
    editAddress (val) {
      this.$store.commit('openLoader')
      const editData = {
        building_name: val.building_name,
        default_address: val.default_address,
        detail: val.detail,
        district: val.district,
        email: val.email,
        first_name: val.first_name,
        floor: val.floor,
        house_no: val.house_no,
        id: val.id,
        last_name: val.last_name,
        moo_ban: val.moo_ban,
        moo_no: val.moo_no,
        order_number: val.order_number,
        phone: val.phone,
        province: val.province,
        room_no: val.room_no,
        soi: val.soi,
        status: val.status,
        street: val.street,
        sub_district: val.sub_district,
        user_id: val.user_id,
        yaek: val.yaek,
        zipcode: val.zip_code,
        note_address: val.note_address
      }
      // console.log(editData.default_address)
      if (editData.default_address === 'Y') {
        this.reShippingData = true
      }
      localStorage.setItem('AddressUserDetail', Encode.encode(editData))
      this.EditAddressDetail = editData
      this.titleAddress = 'แก้ไขที่อยู่ในการจัดส่งสินค้า'
      this.page = 'orderDetail'
      this.$EventBus.$emit('EditModalAddress')
    },
    async DeleteAddress (item) {
      this.dialogAwaitConfirm = true
      this.deleteAdressData = item
      this.typeButton = 'delete'
    },
    async deleteAddress () {
      const data = {
        id: this.deleteAdressData.id
      }
      await this.$store.dispatch('actionDeleteUserAddress', data)
      const res = await this.$store.state.ModuleUser.stateDeleteUserAddress
      if (res.message === 'Delete user address success') {
        // this.$swal.fire({ icon: 'success', title: 'ลบที่อยู่ในการจัดส่งสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.getAddress()
        this.deleteDialog = false
      } else {
        this.$swal.fire({ icon: 'error', title: 'ลบที่อยู่ในการจัดส่งสินค้าไม่สำเร็จ', showConfirmButton: false, timer: 1500 })
      }
      this.dialogAwaitConfirm = false
      this.dialogSuccess = true
    },
    async getAddress () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionListUserAddress')
      var userAddress = await this.$store.state.ModuleUser.stateListUserAddress
      if (userAddress.message === 'List user address success') {
        this.$store.commit('closeLoader')
        this.dataAddress = [...userAddress.data]
      } else {
        this.$store.commit('closeLoader')
        this.dataAddress = []
        if (userAddress.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        }
      }
    },
    async openChangeAddress (items) {
      this.addressDefaultID = ''
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionListUserAddress')
      var userAddress = await this.$store.state.ModuleUser.stateListUserAddress
      if (userAddress.message === 'List user address success') {
        this.$store.commit('closeLoader')
        this.dataAddress = [...userAddress.data]
        this.addressDefaultID = items.address_id
        this.DialogAddress = true
      } else {
        this.$store.commit('closeLoader')
        this.dataAddress = []
        if (userAddress.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        }
      }
    },
    RemoveImageSlip (index, val) {
      this.DataImage = ''
      this.base64_slip = ''
    },
    OpenUploadSlip () {
      if (this.datatoShowImage !== '') {
        this.base64_slip = this.datatoShowImage
      }
      this.dialogOpenDialogUploadSlip = true
    },
    cancelUploadSlip () {
      this.base64_slip = ''
      this.base64_slip = this.datatoShowImage
      this.dialogOpenDialogUploadSlip = false
    },
    UploadSlip () {
      document.getElementById('file_input_slip').click()
    },
    UploadImageSlip () {
      if (this.DataImage !== undefined || this.DataImage !== '') {
        const element = this.DataImage
        const imageSize = element.size / 1024 / 1024
        if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'image/webp') {
          const reader = new FileReader()
          reader.readAsDataURL(element)
          reader.onload = () => {
            var resultReader = reader.result
            var url = URL.createObjectURL(element)
            if (imageSize < 1) {
              this.base64_slip = {
                image_base64: resultReader.split(',')[1],
                path: url,
                name: this.DataImage.name,
                size: this.DataImage.size,
                type: 'base64'
              }
            } else {
              this.base64_slip = {
                image_base64: resultReader.split(',')[1],
                path: url,
                name: this.DataImage.name,
                size: this.DataImage.size,
                type: 'base64'
              }
            }
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    async uploadSlipFile () {
      this.$store.commit('openLoader')
      let data = []
      var dataArrayBase64 = []
      dataArrayBase64.push({
        image_base64: this.base64_slip === '' ? '' : this.base64_slip.image_base64,
        type: this.base64_slip === '' ? '' : this.base64_slip.type
      })
      // console.log('before upload ===>', dataArrayBase64)
      data = {
        payment_transaction_number: this.items.payment_transaction,
        base64_slip: dataArrayBase64
      }
      // console.log('before data upload ===>', data)
      await this.$store.dispatch('actionUploadSlipLotus', data)
      const response = await this.$store.state.ModuleShop.stateUploadSlipLotus
      if (response.message === 'Upload Slip Success') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          text: 'อัปโหลดสลิปสำเร็จ'
        })
        this.dialogOpenDialogUploadSlip = false
        await this.getItemProduct()
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ไม่สามารถอัปโหลดสลิปได้'
        })
        this.$store.commit('closeLoader')
      }
    },
    detectOS () {
      const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera
      if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        return 'iOS'
      }
      if (/android/i.test(userAgent)) {
        return 'Android'
      }
      return 'PC'
    },
    async addtoCart () {
      this.$store.commit('openLoader')
      var data = {
        order_number: this.paymentNumber.payment_transaction_number,
        role_user: 'ext_buyer',
        company_id: -1,
        com_perm_id: -1
      }
      await this.$store.dispatch('actionsRepeatOrder', data)
      const response = await this.$store.state.ModuleCart.stateRepeatOrder
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$router.push({ path: '/shoppingcart' }).catch(() => {})
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'ระบบขัดข้องไม่สามารถสั่งซื้อสินค้าอีกครั้งได้'
        })
      }
    },
    async CheckStockBeforeOpenModalPayment (orderNumber) {
      var messageCheckError = ''
      var i
      var data = {
        payment_transaction_number: this.orderPlatformNumber === '-' || this.orderPlatformNumber === undefined || this.orderPlatformNumber === '' ? orderNumber : this.orderPlatformNumber
      }
      await this.$store.dispatch('actionsCheckStockBeforePayment', data)
      const response = await this.$store.state.ModuleCart.stateCheckStockBeforePayment
      if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
        this.dialogChooesPayType = true
      } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
        for (i = 0; i < response.data.product_free.length; i++) {
          messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
        }
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม ได้แก่ ' + messageCheckError + ' กรุณาติดต่อเจ้าหน้าที่'
        })
      } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
        for (i = 0; i < response.data.product_list.length; i++) {
          messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
        }
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว ได้แก่ ' + messageCheckError + ' กรุณาติดต่อเจ้าหน้าที่'
        })
      } else {
        for (i = 0; i < response.data.product_list.length; i++) {
          messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
        }
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการซื้อ ได้แก่ ' + messageCheckError + ' กรุณาติดต่อเจ้าหน้าที่'
        })
      }
    },
    saveQRCode () {
      const image = document.getElementById('qrcode')
      // console.log(image)
      const link = document.createElement('a')
      link.href = image.src
      link.download = 'image.png'

      // Simulate a click on the link
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    copyClipboard () {
      const track = document.getElementById('trackingNumber')
      // Select the text field
      track.select()
      track.setSelectionRange(0, 99999) // For mobile devices

      // Copy the text inside the text field
      navigator.clipboard.writeText(track.value)
      this.$swal.fire({
        toast: true,
        showConfirmButton: false,
        timer: 1500,
        timerProgressBar: true,
        position: 'center',
        icon: 'success',
        title: 'คัดลอกสำเร็จ'
      })
    },
    backtoPOBuyer () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/pobuyerProfile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/pobuyerProfileMobile' }).catch(() => {})
      }
    },
    async GetETax (val) {
      // ของใหม่
      var data = {
        transactionCode: val.transaction_code
      }
      await this.$store.dispatch('ActionsGetETaxPDF', data)
      const response = await this.$store.state.ModuleCart.stateGetETaxPDF
      if (response.result === 'OK') {
        if (response.etaxResponse.status === 'OK') {
          var pdfUrl = ''
          if (response.etaxResponse.urlPdf !== undefined) {
            // this.pdfUrl = response.etaxResponse.urlPdf
            pdfUrl = response.etaxResponse.urlPdf
          } else {
            // this.pdfUrl = response.etaxResponse.pdfURL
            pdfUrl = response.etaxResponse.pdfURL
          }
          this.downloadLink = pdfUrl
          setTimeout(() => {
            document.getElementById('downloadLink').click()
          }, 500)
          // ใช้วิธีเปิดลิงก์ที่ทำงานได้ทุกแพลตฟอร์ม
          // const link = document.createElement('a')
          // link.href = pdfUrl
          // link.target = '_blank'
          // link.rel = 'noopener noreferrer'
          // document.body.appendChild(link)
          // link.click()
          // document.body.removeChild(link)
        }
      } else {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'ไม่พบเอกสารใบกำกับภาษี'
        })
      }
      // ของเก่า
      // var data = {
      //   transactionCode: val.transaction_code
      // }
      // await this.$store.dispatch('ActionsGetETaxPDF', data)
      // var response = await this.$store.state.ModuleCart.stateGetETaxPDF
      // if (response.result === 'OK') {
      //   if (response.etaxResponse.status === 'OK') {
      //     if (response.etaxResponse.urlPdf !== undefined) {
      //       window.open(`${response.etaxResponse.urlPdf}`, '_blank')
      //       // console.log('response', response.etaxResponse.urlPdf)
      //     } else {
      //       window.open(`${response.etaxResponse.pdfURL}`, '_blank')
      //     }
      //     // window.open(`${response.etaxResponse.pdfURL}`)
      //   }
      // } else {
      //   this.$swal.fire({
      //     toast: true,
      //     showConfirmButton: false,
      //     timer: 1500,
      //     timerProgressBar: true,
      //     icon: 'error',
      //     title: 'ไม่พบเอกสารใบกำกับภาษี'
      //   })
      // }
    },
    confirmPayment () {
      var dataRole = JSON.parse((localStorage.getItem('roleUser'))).role
      // console.log('dataRole', dataRole)
      if (dataRole === 'ext_buyer') {
        this.dialogChooesPayType = false
        this.dialogConfirm = true
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ไม่สามารถชำระเงินได้',
          text: 'สำหรับผู้ซื้อทั่วไปเท่านั้น'
        })
      }
    },
    async GetCC (paymentTypeData) {
      // console.log('GetCC', this.radioCreditTerm)
      this.$store.commit('openLoader')
      this.dialogConfirm = false
      var data
      var resCC
      var goLocalValue = window.location.href.startsWith('http://localhost:8080/') ? 'local' : ''
      if (this.radioPayment !== 'radio-installment') {
        this.radioCreditTerm = ''
      }
      data = {
        go_local: goLocalValue,
        payment_transaction_number: this.orderPlatformNumber === '-' || this.orderPlatformNumber === undefined || this.orderPlatformNumber === '' ? this.items.payment_transaction : this.orderPlatformNumber,
        term: this.radioCreditTerm ? this.radioCreditTerm : ''
      }
      // console.log('data', data)
      await this.$store.dispatch('actionsGetCCV2', data)
      resCC = await this.$store.state.ModuleCart.stateGetCCV2
      if (resCC.result === 'SUCCESS') {
        localStorage.removeItem('sale_order')
        localStorage.setItem('PaymentData', Encode.encode(resCC.data))
        this.$router.push('/RedirectPaymentPage').catch(() => {})
      } else if (resCC.message === 'ERROR ระบบ Payment มีปัญหาไม่สามารถส่งหรือรับข้อมูลได้') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ระบบ Payment มีปัญหา',
          text: 'ไม่สามารถชำระเงินได้'
        })
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ไม่สามารถชำระเงินได้'
        })
      }
      this.$store.commit('closeLoader')
    },
    GetQRCode (paymentTypeData) {
      // console.log('GetQRCode', paymentTypeData)
      const paymentType = paymentTypeData
      if (paymentType === 'cashPayment') {
        this.openDialogQR()
      }
    },
    closeDialogQR () {
      // this.checkOrderResult(this.items.payment_transaction)
      this.dialogConfirm = false
      this.CloseDialog = true
      this.DialogQR = false
      this.$store.commit('closeLoader')
    },
    async openDialogQR () {
      // console.log('openDialogQR')
      this.$store.commit('openLoader')
      let resQR = ''
      var data
      data = {
        payment_transaction_number: this.orderPlatformNumber === '-' || this.orderPlatformNumber === undefined || this.orderPlatformNumber === '' ? this.items.payment_transaction : this.orderPlatformNumber
      }
      await this.$store.dispatch('actionsGetQRCodeV2', data)
      resQR = await this.$store.state.ModuleCart.stateGetQRCodeV2
      if (resQR.result === 'SUCCESS') {
        localStorage.removeItem('sale_order')
        this.netPrice = await resQR.data.net_price
        this.Ref1 = await resQR.data.ref1
        this.Ref2 = await resQR.data.ref2
        this.imageBase64 = 'data:image/png;base64,' + resQR.data.img_base
        var QRCode = await resQR.data.img_base64
        setTimeout(() => {
          this.ImageQR = QRCode
          this.DialogQR = true
        }, 1000)
        this.$store.commit('closeLoader')
        this.checkOrderResult(this.items.payment_transaction)
      } else if (resQR.result === 'FAILED') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ERROR ไม่สามารถชำระเงินได้'
        })
        this.DialogQR = false
        this.dialogConfirm = false
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
        this.DialogQR = false
        this.dialogConfirm = false
        // }
        // this.$store.commit('closeLoader')
      }
    },
    async checkOrderResult (orderNumber) {
      const data = {
        payment_transaction_number: this.orderPlatformNumber === '-' || this.orderPlatformNumber === undefined || this.orderPlatformNumber === '' ? this.items.payment_transaction : this.orderPlatformNumber
      }
      // var value = orderNumber
      var value = this.orderPlatformNumber === '-' ? orderNumber : this.orderPlatformNumber
      const maxAttempts = 15
      let currentAttempt = 1
      while (currentAttempt <= maxAttempts) {
        await this.$store.dispatch('actionsCheckResultQRCodeV2', data)
        const resCheckQR = await this.$store.state.ModuleCart.stateCheckResultQRCodeV2
        if (this.CloseDialog === true) {
          break
        }
        if (resCheckQR.result === 'SUCCESS') {
          this.$router.push({ path: `/yourorder?id=${value}` }).catch(() => {})
          break
        }
        await new Promise(resolve => setTimeout(resolve, 10000))
        currentAttempt++
        if (currentAttempt === 15 && resCheckQR.result !== 'SUCCESS') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            text: 'การชำระเงินไม่เสร็จสมบูรณ์'
          })
          // this.$router.push({ path: '/' }).catch(() => {})
        }
      }
    },
    GoToMobily (Track) {
      window.open(Track)
    },
    async getItemProduct () {
      this.$store.commit('openLoader')
      // console.log('เพื่อซิลพี่ทำได้', this.paymentNumber)
      this.productNormal = []
      this.productService = []
      await this.$store.dispatch('actionOrderDetail', this.paymentNumber)
      var res = await this.$store.state.ModuleOrder.stateOrderDetailData
      if (res.message === 'Get detail order success') {
        this.$store.commit('closeLoader')
        this.items = res.data
        this.poPDF = res.data.PO_External
        this.prPDF = res.data.PR_External
        this.soPDF = res.data.SO_External
        this.QTorder = res.data.QT_order
        if (this.items.data_slip !== null) {
          if (this.items.data_slip.length !== 0) {
            this.datatoShowImage = ''
            this.datatoShowImage = {
              path: this.items.data_slip[0],
              image_base64: this.items.data_slip[0],
              type: 'url'
            }
            // this.base64_slip = {
            //   path: this.items.data_slip[0],
            //   image_base64: this.items.data_slip[0]
            // }
          }
        }
        if (this.items.coupon === null) {
          this.items.coupon = []
        }
        if (this.items.product_free === null) {
          this.items.product_free = []
        }
        this.productType = res.data.data_list[0].product_list
        this.productFree = res.data.product_free
        this.coupon = res.data.coupon
        if (this.productType.length !== 0) {
          this.productType.forEach(e => {
            // console.log('e', e)
            if (e.product_type !== 'service') {
              this.productNormal.push(e)
            } else {
              this.productService.push(e)
            }
          })
        }
        // console.log('productNormal', this.productNormal)
        // console.log('productService', this.productService)
        // console.log('productType', this.productType)
        // console.log('this.items', this.items) // 112233
        // mock data ถ้าข้อมูลหลังบ้านใช้ได้แล้วสามารถลบบรรทัดนี้ได้
        // this.items = detailBuyerMockData.data
        if (this.items.receipt.length !== 0) {
          if (this.items.receipt[0].bankNo === 'SCB') {
            this.bankName = 'ธนาคารไทยพาณิชย์ (SCB)'
          } else if (this.items.receipt[0].bankNo === 'BBL') {
            this.bankName = 'ธนาคารกรุงเทพ (BBL)'
          } else if (this.items.receipt[0].bankNo === 'KTB') {
            this.bankName = 'ธนาคารกรุงไทย (KTB)'
          } else if (this.items.receipt[0].bankNo === 'BAY') {
            this.bankName = 'ธนาคารกรุงศรีอยุธยา (BAY)'
          } else if (this.items.receipt[0].bankNo === 'KTC') {
            this.bankName = 'บริษัทบัตรกรุงไทย (KTC)'
          } else if (this.items.receipt[0].bankNo === 'CIMB') {
            this.bankName = 'ธนาคารซีไอเอ็มบี'
          } else {
            this.bankName = 'ธนาคารอื่นๆ'
          }
        } else {
          this.bankName = ''
        }
        // ต่อ Flash
        // var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        if (this.items.order_mobilyst_no !== '') {
          // this.flashTrackingNo = this.items.flash_pno
          // var data = {
          //   mchId: this.flashMCHID,
          //   token: onedata.user.access_token,
          //   pno: this.flashTrackingNo
          // }
          // await this.$store.dispatch('actionTrackingOrderFlash', data)
          // var response = await this.$store.state.ModuleOrder.stateTrackingOrderFlash
          var response = {
            code: 1,
            data: {
              customaryPno: null,
              eSignature: null,
              origPno: 'TH014276DR4A',
              pno: 'TH014276DR4A',
              returnedPno: null,
              routes: null,
              state: 0,
              stateChangeAt: null,
              stateText: '',
              ticketPickupId: null
            },
            message: 'success'
          }
          this.flashTrackingData = response.data
          if (this.flashTrackingData !== null) {
            this.mockupTracking = this.flashTrackingData
            if (this.flashTrackingData.routes === null) {
              this.step = 0
            } else {
              this.step = this.flashTrackingData.routes.length
            }
          } else {
            this.step = 0
          }
        } else {
          this.flashTrackingNo = ''
        }
        // ต่อ UPS
        // var val = {
        //   order_number: this.paymentNumber
        // }
        // await this.$store.dispatch('actionListOrder', val)
        // var responseUPS = await this.$store.state.ModuleOrder.stateListOrder
        // this.receivedDate = responseUPS.data.received_date
        // this.sentDate = responseUPS.data.sent_date
        // this.sentTime = responseUPS.data.sent_time
        // console.log('this.items', this.items.type_shipping)
        if (this.items.type_shipping === 'front') {
          // console.log('trackingStatus', this.trackingStatus)
          // if (this.)
          this.trackingStatus = 'Pick Up'
          this.trackingPayment = this.items.transaction_status
          this.trackingStatusFront = this.items.tracking[0].status_tracking
          // console.log('trackingPayment', this.trackingPayment, this.trackingStatus)
          this.dateCreateOrderStep4 = new Date(this.items.received_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        } else {
          this.trackingStatus = this.items.tracking[0].status_tracking
          this.dateCreateOrderStep4 = new Date(this.items.tracking[0].time_step_4).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        }
        // console.log('this.trackingStatus', this.trackingStatus)
        // this.trackingPayment = this.items.tracking[0].status_payment
        // console.log('this.dateCreateOrderStep4', this.dateCreateOrderStep4)
        this.dataReceived = new Date(this.items.received_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderStep1 = new Date(this.items.tracking[0].time_step_1).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderStep2 = new Date(this.items.tracking[0].time_step_2).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderStep3 = new Date(this.items.tracking[0].time_step_3).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderStepNew = new Date(this.items.tracking[0].time_step_new).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderCancel = new Date(this.items.tracking[0].time_cancel).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderCancel2 = new Date(this.items.tracking[0].time_cancel_2).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderCancel4 = new Date(this.items.tracking[0].time_cancel_4).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        // this.dateCreateOrderStep4 = new Date(this.items.tracking[0].time_step_4).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
        // console.log('dateCreateOrderStep2', this.items.tracking[0].time_step_2)
        if (this.items.tracking.length !== 0) {
          if (this.items.tracking[0].status_tracking === 'Not Paid') {
            this.trackingText = 'รอชำระเงิน'
          } else if (this.items.tracking[0].status_tracking === 'Success') {
            this.trackingText = 'คำสั่งซื้อที่ชำระเงินแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Not Sent') {
            this.trackingText = 'ชำระเงินแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Sent') {
            this.trackingText = 'จัดส่งแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Received') {
            this.trackingText = 'ได้รับสินค้าแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Not Received' && this.items.transportation_status === 'อยู่ระหว่างการขนส่ง') {
            this.trackingText = 'กำลังจัดส่ง'
          } else if (this.items.tracking[0].status_tracking === 'Not Received' && this.items.transportation_status === 'อยู่ระหว่างการตรวจสอบและยอมรับสินค้า') {
            this.trackingText = 'จัดส่งสำเร็จ'
          } else if (this.items.tracking[0].status_tracking === 'Cancel by approver') {
            this.trackingText = 'ยกเลิกโดยผู้อนุมัติ'
          } else if (this.items.tracking[0].status_tracking === 'Cancel' || this.items.transaction_status === 'Cancel') {
            this.trackingText = 'ยกเลิกคำสั่งซื้อ'
          } else if (this.items.tracking[0].status_tracking === 'Pick Up' && this.items.tracking[0].status_payment === 'Success') {
            this.trackingText = 'รอเข้ารับสินค้า'
          } else if (this.items.tracking[0].status_tracking === 'Pick Up' && this.items.tracking[0].status_payment === 'Not Paid') {
            this.trackingText = 'รอชำระเงิน'
          } else if (this.items.tracking[0].status_tracking === 'Pick Up' && this.items.tracking[0].status_payment === 'Cash') {
            this.trackingText = 'รอเข้ารับสินค้า'
          }
        } else {
          this.trackingText = ''
        }
        this.CheckAcceptProductData()
        // this.$store.commit('closeLoader')
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      } else {
        this.$store.commit('closeLoader')
        if (res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: res.message
          })
          if (this.MobileSize) {
            this.$router.push({ path: '/pobuyerProfileMobile' }).catch(() => {})
          } else {
            this.$router.push({ path: '/pobuyerProfile' })
          }
        }
      }
      // console.log('trackingStatus', this.trackingStatus)
    },
    // approveRefundProductSeller () {
    //   this.$refs.ModalRefundProductSeller.open(this.items, this.items.payment_transaction)
    // },
    refundProductBuyer (order) {
      this.$refs.ModalRefundProductBuyer.open(order, order.order_number, 'ext_buyer')
    },
    async CheckAcceptProductData () {
      // this.$store.commit('openLoader')
      var data = {
        payment_transaction_number: this.orderPlatformNumber === '-' || this.orderPlatformNumber === undefined || this.orderPlatformNumber === '' ? this.items.payment_transaction : this.orderPlatformNumber
      }
      await this.$store.dispatch('actionCheckAcceptProduct', data)
      var res = this.$store.state.ModuleOrder.stateCheckAcceptProduct
      if (res.result === 'SUCCESS') {
        this.checkAcceptProduct = res.data
        // console.log('checkAcceptProduct', this.checkAcceptProduct)
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 3000, timerProgressBar: true, icon: 'warning', text: res.message })
        this.checkAcceptProduct = []
        this.$router.push('/pobuyerProfile')
      }
    },
    SwitchRole () {
      this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.getItemProduct()
    },
    async acceptProduct (order) {
      // อาจจะมีการเพิ่มค่าที่ส่งไป api ถ้าหากมีแยกหลาย order
      //       {
      //     "payment_transaction_number": "OEM-20240101304",
      //     "order_number": "OEM-20240101304",
      //     "seller_shop_id": "",
      //     "role_user": "ext_buyer",
      //     "status": "accepted",
      //     "reason": ""
      // }
      var data = {
        payment_transaction_number: this.orderPlatformNumber === '-' || this.orderPlatformNumber === undefined || this.orderPlatformNumber === '' ? this.items.payment_transaction : this.orderPlatformNumber,
        order_number: order.order_number,
        role_user: 'ext_buyer',
        seller_shop_id: '',
        reason: '',
        status: 'accepted'
      }
      await this.$store.dispatch('actionAcceptProduct', data)
      var res = this.$store.state.ModuleOrder.stateAcceptProduct
      // console.log('res', res)
      if (res.message === 'Update status success.') {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ยืนยันการตรวจสอบและได้รับสินค้าแล้ว'
        })
        await this.getItemProduct()
        const dataForReview = {
          expired_review: '',
          order_number: order.order_number,
          status: 'waiting_review'
        }
        await this.$refs.ModalReviewProduct.open(dataForReview, order.order_number, 'create', 'ext_buyer')
        // if (this.MobileSize === false) {
        //   this.$router.push({ path: '/reviewBuyer' }).catch(() => {})
        // } else {
        //   this.$router.push({ path: '/reviewBuyerMobile' }).catch(() => {})
        // }
      } else {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR',
          text: `${res.message}`
        })
      }
    },
    openModalReviewProduct (order, index) {
      const actions = 'create'
      this.$refs.ModalReviewProduct.open(this.items.data_list[index].product_list, order.order_number, actions, 'ext_buyer')
    },
    openModalEditReviewProduct (order, index) {
      const actions = 'edit'
      this.$refs.ModalReviewProduct.open(this.items.data_list[index].product_list, order.order_number, actions, 'ext_buyer')
    },
    async GoToPayment () {
      const PaymentID = {
        payment_transaction_number: this.orderPlatformNumber === '-' || this.orderPlatformNumber === undefined || this.orderPlatformNumber === '' ? this.items.payment_transaction : this.orderPlatformNumber
      }
      await this.$store.dispatch('ActionGetPaymentPage', PaymentID)
      var response = this.$store.state.ModuleCart.stateGetPaymentPage
      await localStorage.setItem('PaymentData', Encode.encode(response))
      this.$router.push('/RedirectPaymentPage')
    },
    openDialogPayment () {
      this.dialogChooesPayType = false
      this.radioPayment = 'no'
    },
    gotoRefund () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/refundOrderBuyer' }).catch(() => {})
      } else {
        this.$router.push({ path: '/refundOrderBuyerMobile' }).catch(() => {})
      }
    },
    closemodalInputReason () {
      this.reason = ''
      this.modalInputReason = false
    },
    closemodalInputReasonRefund () {
      this.urName = ''
      this.bankNameRefund = ''
      this.telNumber = ''
      this.bankNumberRefund = ''
      this.reasonRefund = ''
      this.modalInputReasonRefund = false
      this.cancelModalStep = 1
      this.reason = ''
    },
    CheckSpacebarOne (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    async getAccountBank () {
      this.urName = ''
      this.bankNameRefund = ''
      this.telNumber = ''
      this.bankNumberRefund = ''
      this.reasonRefund = ''
      this.modalInputReasonRefund = true
      await this.$store.dispatch('actionsListBank')
      var response = await this.$store.state.ModuleShop.stateListBank
      if (response.code === 200) {
        this.listAccount = response.data
        // this.listAccount = response.data.map((item) => ({
        //   text: item.name
        // }))
        // console.log('see', this.listAccount)
      } else {
        this.listAccount = []
      }
    },
    async closeSuccessCancelOrder () {
      this.modalSuccessCancelOrder = false
      await this.getItemProduct()
      await this.getDetailCancel()
      // await this.getItemProductB2C()
      // await this.getOrderDocument()
    },
    async openDialogConfirmCancel (val) {
      this.cancelType = ''
      this.cancelType = val
      this.cancelModalStep = 2
      await this.getPaymentUsers()
      // this.modalInputReason = false
      // this.modalInputReasonRefund = false
      // this.modalAwaitCancelOrder = true
    },
    openDialogConfirmCancelStep2 () {
      this.modalInputReason = false
      this.modalAwaitCancelOrder = true
    },
    async openmodalInputReasonDetail () {
      this.getDetailCancel()
      await this.$store.dispatch('actionsListBank')
      var response = await this.$store.state.ModuleShop.stateListBank
      if (response.code === 200) {
        this.listAccount = response.data
        // this.listAccount = response.data.map((item) => ({
        //   text: item.name
        // }))
        // console.log('see', this.listAccount)
      } else {
        this.listAccount = []
      }
      this.modalInputReasonDetail = true
      // this.getAccountBank()
    },
    closeDialogConfirmCancel () {
      this.urName = ''
      this.bankNameRefund = ''
      this.telNumber = ''
      this.bankNumberRefund = ''
      this.reasonRefund = ''
      this.reason = ''
      this.modalAwaitCancelOrder = false
    },
    async confirmCancel () {
      this.modalAwaitCancelOrder = false
      if (this.cancelType === 'no payment') {
        this.$store.commit('openLoader')
        const data = {
          order_number: this.orderPlatformNumber === '-' || this.orderPlatformNumber === undefined || this.orderPlatformNumber === '' ? this.paymentNumber.payment_transaction_number : this.orderPlatformNumber,
          remark: this.reason
        }
        // console.log(data, 'data')
        await this.$store.dispatch('actionCancelOrder', data)
        var responseNopayment = this.$store.state.ModuleOrder.stateCancelOrder
        if (responseNopayment.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.modalSuccessCancelOrder = true
        } else {
          this.$store.commit('closeLoader')
          this.reason = ''
          this.$swal.fire({
            icon: 'error',
            text: responseNopayment.message,
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else if (this.cancelType === 'payment') {
        this.$store.commit('openLoader')
        var bankName = ''
        bankName = this.listAccount.filter(data => data.code === this.bankNameRefund)
        const data = {
          order_number: this.paymentNumber.payment_transaction_number,
          account_name: this.urName,
          phone: this.telNumber,
          bank_code: this.bankNameRefund,
          account_no: this.bankNumberRefund,
          remark: this.reasonRefund,
          bank_name: bankName[0].name
        }
        await this.$store.dispatch('actionCancelOrder', data)
        var response = this.$store.state.ModuleOrder.stateCancelOrder
        if (response.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.modalSuccessCancelOrder = true
        } else {
          this.cancelModalStep = 1
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            text: response.message,
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else {
        this.cancelModalStep = 1
        this.urName = ''
        this.bankNameRefund = ''
        this.telNumber = ''
        this.bankNumberRefund = ''
        this.reasonRefund = ''
        this.reason = ''
        this.$swal.fire({
          icon: 'error',
          text: 'ไม่สามารถยกออร์เดอร์ได้',
          showConfirmButton: false,
          timer: 1500
        })
      }
      this.modalAwaitCancelOrder = false
      this.modalInputReasonRefund = false
      // const shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      // var data = ''
      // data = {
      //   payment_transaction_number: this.itemOrder.payment_transaction,
      //   order_number: this.itemOrder.order_number,
      //   seller_shop_id: shopDetail.id,
      //   role_user: 'seller',
      //   status: this.getStatusOrder,
      //   reason: this.reason
      // }
      // await this.$store.dispatch('actionsAccecptProduct', data)
      // const resposnse = await this.$store.state.ModuleShop.stateAccecptProduct
      // if (resposnse.result === 'SUCCESS') {
      //   this.modalSuccessCancelOrder = true
      // } else {
      //   this.$swal.fire({
      //     icon: 'error',
      //     text: resposnse.message,
      //     showConfirmButton: false,
      //     timer: 1500
      //   })
      // }
    },
    async getDetailCancel () {
      this.$store.commit('openLoader')
      const data = {
        order_number: this.paymentNumber.payment_transaction_number
      }
      await this.$store.dispatch('actionCancelOrderDetails', data)
      var response = this.$store.state.ModuleOrder.stateCancelOrderDetails
      if (response.result === 'SUCCESS') {
        this.detailCancel = response.data
        // เช็คจาก key status
        if (this.detailCancel[0].status !== '') {
          // ให้เซ็ตค่า
          this.timeStampCancel = this.detailCancel[0].updated_at
          this.statusCancel = this.detailCancel[0].status
          this.urName = this.detailCancel[0].account_name
          this.bankNameRefund = this.detailCancel[0].bank_code
          this.telNumber = this.detailCancel[0].phone
          this.bankNumberRefund = this.detailCancel[0].account_no
          this.reasonRefund = this.detailCancel[0].remark_buyer
          this.reasonSeller = this.detailCancel[0].remark_seller
        } else {
          this.timeStampCancel = ''
          this.urName = ''
          this.bankNameRefund = ''
          this.telNumber = ''
          this.bankNumberRefund = ''
          this.reasonRefund = ''
        }
      } else {
        this.timeStampCancel = ''
        this.statusCancel = ''
        this.urName = ''
        this.bankNameRefund = ''
        this.telNumber = ''
        this.bankNumberRefund = ''
        this.reasonRefund = ''
      }
      this.$store.commit('closeLoader')
    },
    statusCancelorder (val) {
      if (val === 'approve') {
        return 'อนุมัติ'
      } else if (val === 'reject') {
        return 'ไม่อนุมัติ'
      } else {
        return 'รออนุมัติ'
      }
    },
    statusTransactionChipColor (val, valTransport) {
      if (val === 'Not Paid') {
        return '#FFF2E0'
      } else if (val === 'Success' && valTransport === 'อยู่ระหว่างดำเนินการ') {
        return '#EFFFF1'
      } else if (val === 'Success' && valTransport === 'ยืนยันการรับสินค้าแล้ว') {
        return '#EFFFF1'
      } else if (val === 'Success' && valTransport === 'รอขนส่งเข้ารับพัสดุ') {
        return '#FFF2E0'
      } else if (val === 'Success' && valTransport === 'ผู้ส่งกำลังเตรียมพัสดุ') {
        return '#FFF2E0'
      } else if (val === 'Success' && valTransport === 'พัสดุอยู่ระหว่างการขนส่ง' && this.trackingStatus === 'Not Sent') {
        return '#d7e2f6'
      } else if (val === 'Success' && valTransport === 'พัสดุอยู่ระหว่างการขนส่ง' && this.trackingStatus !== 'Not Sent') {
        return '#F4F2FF'
      } else if (val === 'Success' && valTransport === 'รอการตรวจสอบและยอมรับสินค้า') {
        return '#EFFFF1'
      } else if (val === 'Cancel' && valTransport === 'ยกเลิกคำสั่งซื้อ') {
        return '#ffefef'
      } else if (val === 'Waiting_Cancel' && valTransport === 'อยู่ระหว่างดำเนินการ') {
        return '#ffefef'
      } else if (val === 'Success' && valTransport === 'การจัดส่งสำเร็จ') {
        return '#EFFFF1'
      } else if (val === 'Cancel' && valTransport === 'อยู่ระหว่างดำเนินการ') {
        return '#ffefef'
      }
    },
    statusTransactionTextColor (val, valTransport) {
      if (val === 'Not Paid') {
        return '#FAAD14'
      } else if (val === 'Success' && valTransport === 'อยู่ระหว่างดำเนินการ') {
        return '#52C41A'
      } else if (val === 'Success' && valTransport === 'ยืนยันการรับสินค้าแล้ว') {
        return '#52C41A'
      } else if (val === 'Success' && valTransport === 'รอขนส่งเข้ารับพัสดุ') {
        return '#FAAD14'
      } else if (val === 'Success' && valTransport === 'ผู้ส่งกำลังเตรียมพัสดุ') {
        return '#FAAD14'
      } else if (val === 'Success' && valTransport === 'พัสดุอยู่ระหว่างการขนส่ง' && this.trackingStatus === 'Not Sent') {
        return '#1c3d77'
      } else if (val === 'Success' && valTransport === 'พัสดุอยู่ระหว่างการขนส่ง' && this.trackingStatus !== 'Not Sent') {
        return '#9747FF'
      } else if (val === 'Success' && valTransport === 'รอการตรวจสอบและยอมรับสินค้า') {
        return '#52C41A'
      } else if (val === 'Cancel' && valTransport === 'ยกเลิกคำสั่งซื้อ') {
        return '#F5222D'
      } else if (val === 'Waiting_Cancel' && valTransport === 'อยู่ระหว่างดำเนินการ') {
        return '#F5222D'
      } else if (val === 'Success' && valTransport === 'การจัดส่งสำเร็จ') {
        return '#52C41A'
      } else if (val === 'Cancel' && valTransport === 'อยู่ระหว่างดำเนินการ') {
        return '#F5222D'
      }
    },
    statusTransactionText (val, valTransport) {
      if (val === 'Not Paid') {
        return 'รอชำระเงิน'
      } else if (val === 'Success' && valTransport === 'อยู่ระหว่างดำเนินการ') {
        return 'ชำระเงินสำเร็จ'
      } else if (val === 'Success' && valTransport === 'ยืนยันการรับสินค้าแล้ว') {
        return 'จัดส่งสินค้าสำเร็จ'
      } else if (val === 'Success' && valTransport === 'รอขนส่งเข้ารับพัสดุ') {
        return 'รอขนส่งเข้ารับพัสดุ'
      } else if (val === 'Success' && valTransport === 'ผู้ส่งกำลังเตรียมพัสดุ') {
        return 'ผู้ส่งกำลังเตรียมพัสดุ'
      } else if (val === 'Success' && valTransport === 'พัสดุอยู่ระหว่างการขนส่ง' && this.trackingStatus === 'Not Sent') {
        return 'พัสดุเข้าระบบ'
      } else if (val === 'Success' && valTransport === 'พัสดุอยู่ระหว่างการขนส่ง' && this.trackingStatus !== 'Not Sent') {
        return 'กำลังจัดส่งสินค้า'
      } else if (val === 'Success' && valTransport === 'รอการตรวจสอบและยอมรับสินค้า') {
        return 'จัดส่งสินค้าสำเร็จ'
      } else if (val === 'Cancel' && valTransport === 'ยกเลิกคำสั่งซื้อ') {
        return 'ยกเลิกคำสั่งซื้อ'
      } else if (val === 'Waiting_Cancel' && valTransport === 'อยู่ระหว่างดำเนินการ') {
        return 'ยกเลิกคำสั่งซื้อ'
      } else if (val === 'Success' && valTransport === 'การจัดส่งสำเร็จ') {
        return 'จัดส่งสินค้าสำเร็จ'
      } else if (val === 'Cancel' && valTransport === 'อยู่ระหว่างดำเนินการ') {
        return 'ยกเลิกคำสั่งซื้อ'
      }
    },
    async getOrderDocument (orderNumber) {
      this.$store.commit('openLoader')
      var data = {
        orderNumber: orderNumber
      }
      // console.log('data', data)
      await this.$store.dispatch('actionGetOrderDocument', data)
      var res = await this.$store.state.ModuleOrder.stateGetOrderDocument
      if (res.message === 'Get order document successfully') {
        // console.log('itemsDoc', this.itemsDoc)
        this.documents = res.data
        this.itemsDoc[0].value = this.documents.PoNumber === null ? '' : this.documents.PoNumber
        this.itemsDoc[1].value = this.documents.PrNumber === null ? '' : this.documents.PrNumber
        this.itemsDoc[2].value = this.documents.SoNumber === null ? '' : this.documents.SoNumber
        this.itemsDoc[0].file = this.documents.poFile
        this.itemsDoc[1].file = this.documents.prFile
        this.itemsDoc[2].file = this.documents.soFile
      } else {
        this.documents = []
      }
      this.$store.commit('closeLoader')
      // console.log(' this.documents', this.documents)
    },
    async getPaymentUsers () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionGetPaymentUsers')
      var res = await this.$store.state.ModuleOrder.stateGetPaymentUsers
      if (res.code === 200) {
        // console.log(res.data[0], 'res')
        this.bankNumberRefund = res.data[0].bank_code === '-' ? '' : res.data[0].bank_no
        this.urName = res.data[0].bank_user_name === '-' ? '' : res.data[0].bank_user_name
        this.bankNameRefund = res.data[0].bank_no === '' ? '' : res.data[0].bank_code
      }
      this.$store.commit('closeLoader')
    },
    async reviewProduct (orderNumber) {
      const dataForReview = {
        expired_review: '',
        order_number: orderNumber,
        status: 'waiting_review'
      }
      await this.$refs.ModalReviewProduct.open(dataForReview, orderNumber, 'create', 'ext_buyer')
    }
  }
}
</script>
<style scoped>

.couponIMGDesk{
  background-image: url('../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: contain;
  padding: 1%;
  box-shadow: 5px 5px 5px 0px gray;
  /* height: 200px; */
  /* width: 250px; */
}
.couponIMGMobile{
  background-image: url('../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: contain;
  /* padding: 1%; */
  /* width: 100%; */
  box-shadow: 5px 5px 5px 0px gray;
}
.couponIMG{
  background-image: url('../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: cover;
  padding: 1%;
  box-shadow: 5px 5px 5px 0px gray;
}
</style>
<style lang="css" scoped>
/* .v-application .mb-12 {
    margin-bottom: 12px !important;
} */
/* ::v-deep .ant-table-thead {
  display: none;
} */
::v-deep .ant-table-pagination {
  display: none;
}
.notPay {
  color: #FAAD14;
}
.Pay {
  color: #52C41A;
}
.notReceived {
  color: #1B5DD6;
}
.received {
  color: #16D2A5;
}
.borderSty {
  border-bottom: solid 1px #F2F2F2;
}
.sizeIMG {
  height: 24px;
  width: 24px;
}
.sizeIMGMobile {
  height: 18px;
  width: 18px;
}
.imageshow {
  width: 80px;
  height: 80px;
}
.imageshowMobile {
  width: 60px;
  height: 60px;
  /* cursor: pointer; */
}
.bgShippingUPS {
  background-color: #F3F5F7;
}
.fontHeight {
  /* padding-top: 20px; */
  padding-bottom: 15px;
}
.fontHeightMobile {
  /* padding-top: 20px; */
  padding-bottom: 18px;
}
.fontHeadDetail {
  font-size: 18px;
  font-weight: 700;
}
.fontStatus {
  font-size: 14px;
  font-weight: 400;
}
.fontActive {
  font-size: 14px;
  font-weight: 700;
  /* color: #27AB9C; */
  color: #269afd;
}
.fontInactive {
  color: #BDE7D9;
  font-weight: 700;
  font-size: 14px;
}
.fontPass {
  color: #333333;
  font-weight: 700;
  font-size: 14px;
}
.fontSizeDetailUp {
  font-size: 16px;
  font-weight: 600;
}
.fontSizeStepOrder {
  font-size: 10px;
}
.fontSizeTotalPrice {
  font-size: 16px;
  font-weight: 400;
}
.fontSizeTotalPriceNumber {
  font-size: 16px;
  font-weight: 700;
}
.fontSizeDiscount {
  font-size: 16px;
  color: red;
}
.fontSizeDiscountBold {
  font-size: 16px;
  color: red;
  font-weight: 700;
}
.fontSizeTotalPriceMobile {
  font-size: 16px;
}
.fontSizeAddressDetail {
  font-size: 16px;
}
.buttonFontSize {
  font-size: 14px;
  font-weight: normal;
}
.buttonFontSizeMobile {
  font-size: 11px;
  font-weight: normal;
  white-space: normal;
}
.captionSku {
  font-size: 14px;
  font-weight: 700;
}
.fontSizeTitle {
  font-size: 21px;
}
.fontSizeTitleMobile {
  font-size: 18px;
}
.fontSizeDetail {
  font-size: 14px;
}
.fontSizeDetailMobile {
  font-size: 14px;
  /* font-weight: 700; */
}
.fontDetailMobile {
  font-size: 12px;
}
.fontSizeTotalPriceMobile {
  font-size: 14px;
}
.DetailsProductFrontMobile {
  font-size: 12px;
}
.fontpast {
  font-size: 14px;
  font-weight: 700;
}
.ant-card-bordered {
  border: 0px solid #e8e8e8;
}
</style>
<style scoped>
.custom-scroll::-webkit-scrollbar {
  width: 10px;
  -webkit-overflow-scrolling: touch;
  -webkit-appearance: none;
}

.custom-scroll::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 10px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background: #27AB9C;
  border-radius: 10px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: #23998C;
  -webkit-overflow-scrolling: touch;
}

.five-step-transport {
  display: flex;
  justify-content: space-around;
}
</style>
