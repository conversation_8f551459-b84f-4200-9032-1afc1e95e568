<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">รายชื่อผู้ใช้งานในระบบ</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> รายชื่อผู้ใช้งานในระบบ</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาจากรายชื่อผู้ใช้งานในระบบ" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="12">
            <v-row class="my-3">
              <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''">
                <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="listBusinessAdmin.length !== 0 && (!MobileSize && !IpadSize)">รายชื่อผู้ใช้งานในระบบทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="listBusinessAdmin.length !== 0 && (MobileSize || IpadSize)">รายชื่อผู้ใช้งานในระบบทั้งหมด {{ showCountRequest }} รายการ</span>
              </v-col>
            </v-row>
            <v-row dense no-gutters class="mt-2" v-if="!MobileSize">
              <v-col cols="12" class="py-0 mb-0">
                <v-tabs left @change="handleTabChange" style="margin-bottom: 0; padding-bottom: 0;" type="card">
                  <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
                  <v-tab :key="0" style="margin: 5px 2px; background-color: #e0f7fa; color: #56acd6; border-radius: 5px; cursor: pointer;">ทั้งหมด</v-tab>
                  <v-tab :key="1" style="margin: 5px 2px; background-color: #e8f5e9; color: #55b78f; border-radius: 5px; cursor: pointer;">ผู้ใช้งานในระบบ</v-tab>
                  <v-tab :key="2" style="margin: 5px 2px; background-color: #ffebee; color: #f4675d; border-radius: 5px; cursor: pointer;">ผู้ใช้ถูกปิดการใช้งาน</v-tab>
                </v-tabs>
              </v-col>
            </v-row>
            <v-row dense no-gutters class="mt-2" v-else>
              <v-col cols="12" class="py-0 mb-0">
                <v-tabs left @change="handleTabChange" style="margin-bottom: 0; padding-bottom: 0;" type="card">
                  <v-tab :key="0" style="margin: 5px 2px; background-color: #e0f7fa; font-size:12px !important; color: #56acd6; border-radius: 5px; cursor: pointer;">ทั้งหมด</v-tab>
                  <v-tab :key="1" style="margin: 5px 2px; background-color: #e8f5e9; font-size:12px !important; color: #55b78f; border-radius: 5px; cursor: pointer;">ผู้ใช้งานในระบบ</v-tab>
                  <v-tab :key="2" style="margin: 5px 2px; background-color: #ffebee; font-size:12px !important; color: #f4675d; border-radius: 5px; cursor: pointer;">ผู้ใช้ถูกปิดการใช้งาน</v-tab>
                </v-tabs>
              </v-col>
            </v-row>
            <!-- <v-row dense no-gutters class="mt-2" v-else>
              <v-col cols="12" class="py-0 mb-0">
                <a-tabs @change="handleTabChange">
                  <a-tab-pane :key="0"><span slot="tab" style="padding: 4px; background-color: #e0f7fa; color: #56acd6; border-radius: 5px; cursor: pointer;">ทั้งหมด</span></a-tab-pane>
                  <a-tab-pane :key="1"><span slot="tab" style="padding: 4px; background-color: #e8f5e9; color: #55b78f; border-radius: 5px; cursor: pointer;">ผู้ใช้งานในระบบ</span></a-tab-pane>
                  <a-tab-pane :key="2"><span slot="tab" style="padding: 4px; background-color: #ffebee; color: #f4675d; border-radius: 5px; cursor: pointer;">ผู้ใช้ถูกปิดการใช้งาน</span></a-tab-pane>
                </a-tabs>
              </v-col>
            </v-row> -->
            <div v-if="keyTable === 0">
              <v-row dense>
              <v-col cols="12">
                <v-data-table
                :headers="headers"
                :items="listBusinessAdmin"
                :search="search"
                style="width:100%; white-space: nowrap;"
                height="100%"
                :page.sync="page"
                @pagination="countRequest"
                no-results-text="ไม่พบชื่อผู้ใช้งานของระบบ"
                no-data-text="ไม่มีชื่อชื่อผู้ใช้งานของระบบ"
                :update:items-per-page="itemsPerPage"
                :items-per-page="10"
                class="elevation-1 mt-4 user-table"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                >
                  <template v-slot:[`item.indexOfUser`]="{ item }">
                    {{
                      listBusinessAdmin.map(function(x) {
                        return x.id;
                      }).indexOf(item.id) + 1
                    }}
                  </template>
                  <template v-slot:[`item.status`]="{ item }">
                    <div style="display: flex; justify-content: flex-start; gap: 5px;">
                    <v-icon v-if="item.status === 'active'" color="#55b78f" x-small>mdi-brightness-1</v-icon>
                    <v-icon v-else-if="item.status === 'inactive'" color="#f4675d" x-small>mdi-brightness-1</v-icon> {{ item.status }}
                  </div>
                  </template>
                  <template v-slot:[`item.fullname`]="{ item }">
                    {{ item.first_name_th + ' ' + item.last_name_th }}
                  </template>
                  <template v-slot:[`item.fullname_eng`]="{ item }">
                    {{ item.first_name_en + ' ' + item.last_name_en }}
                  </template>
                  <template v-slot:[`item.department_name`]="{ item }">
                    {{ item.department_name !== null ? item.department_name : '-' }}
                  </template>
                  <template v-slot:[`item.access_token`]="{ item }">
                    <v-btn color="#27AB9C" outlined @click="OpenDialogShowData(item.email)">ดู Token</v-btn>
                    <!-- {{ item.access_token !== null ? item.access_token : '-' }} -->
                  </template>
                  <template v-slot:[`item.id`]="{ item }">
                    <v-btn color="#27AB9C" outlined @click="couponUser(item)">ดูคูปอง</v-btn>
                    <!-- {{ item.access_token !== null ? item.access_token : '-' }} -->
                  </template>
                  <template v-slot:[`item.manage`]="{ item }">
                    <v-btn v-if="item" plain @click="openEditStatusDialog(item)"><v-icon color="#27AB9C">mdi-dots-vertical</v-icon></v-btn>
                </template>
                </v-data-table>
                <v-dialog v-model="modalShowData" :width="MobileSize ? '100%' : '50%'" persistent>
                  <v-card >
                      <v-card-title class="text-h5 grey lighten-2">
                        <span>Access Token</span>
                        <v-btn text color="#b1afae" style="margin-left: auto" @click="handleClick()"><v-icon>mdi-content-copy</v-icon>คัดลอก</v-btn>

                        <v-tooltip
                          v-model="showText"
                          top
                        >
                          <template v-slot:activator="{ on, attrs }">
                            <p
                              icon
                              v-bind="attrs"
                              v-on="on"
                            >
                            </p>
                          </template>
                          <span>คัดลอกสำเร็จ</span>
                        </v-tooltip>
                      </v-card-title>

                      <v-card-text class="pt-4">
                        <div v-for="(item , index) in tokenSearch" :key="index">
                          <span>{{ item.access_token }}</span>
                        </div>
                      </v-card-text>

                      <v-divider></v-divider>

                      <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn
                          color="primary"
                          text
                          @click="modalShowData = false"
                        >
                          ปิด
                        </v-btn>
                      </v-card-actions>
                  </v-card>
                </v-dialog>
              </v-col>
            </v-row>
            </div>
            <div v-else-if="keyTable === 1">
              <v-row dense>
              <v-col cols="12">
                <v-data-table
                :headers="headers"
                :items="activeBusinessAdmin"
                :search="search"
                style="width:100%; white-space: nowrap;"
                height="100%"
                :page.sync="page"
                @pagination="countRequest"
                no-results-text="ไม่พบชื่อผู้ใช้งานของระบบ"
                no-data-text="ไม่มีชื่อชื่อผู้ใช้งานของระบบ"
                :update:items-per-page="itemsPerPage"
                :items-per-page="10"
                class="elevation-1 mt-4 user-table"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                >
                  <template v-slot:[`item.indexOfUser`]="{ item }">
                    {{
                      listBusinessAdmin.map(function(x) {
                        return x.id;
                      }).indexOf(item.id) + 1
                    }}
                  </template>
                  <template v-slot:[`item.status`]="{ item }">
                    <div style="display: flex; justify-content: flex-start; gap: 5px;">
                    <v-icon v-if="item.status === 'active'" color="#55b78f" x-small>mdi-brightness-1</v-icon>
                    <v-icon v-else-if="item.status === 'inactive'" color="#f4675d" x-small>mdi-brightness-1</v-icon> {{ item.status }}
                  </div>
                  </template>
                  <template v-slot:[`item.fullname`]="{ item }">
                    {{ item.first_name_th + ' ' + item.last_name_th }}
                  </template>
                  <template v-slot:[`item.fullname_eng`]="{ item }">
                    {{ item.first_name_en + ' ' + item.last_name_en }}
                  </template>
                  <template v-slot:[`item.department_name`]="{ item }">
                    {{ item.department_name !== null ? item.department_name : '-' }}
                  </template>
                  <template v-slot:[`item.access_token`]="{ item }">
                    <v-btn color="#27AB9C" outlined @click="OpenDialogShowData(item.email)">ดู Token</v-btn>
                    <!-- {{ item.access_token !== null ? item.access_token : '-' }} -->
                  </template>
                  <template v-slot:[`item.id`]="{ item }">
                    <v-btn color="#27AB9C" outlined @click="couponUser(item)">ดูคูปอง</v-btn>
                    <!-- {{ item.access_token !== null ? item.access_token : '-' }} -->
                  </template>
                  <template v-slot:[`item.manage`]="{ item }">
                    <v-btn v-if="item" plain @click="openEditStatusDialog(item)"><v-icon color="#27AB9C">mdi-dots-vertical</v-icon></v-btn>
                </template>
                </v-data-table>
                <v-dialog v-model="modalShowData" :width="MobileSize ? '100%' : '50%'" persistent>
                  <v-card >
                      <v-card-title class="text-h5 grey lighten-2">
                        <span>Access Token</span>
                        <v-btn text color="#b1afae" style="margin-left: auto" @click="handleClick()"><v-icon>mdi-content-copy</v-icon>คัดลอก</v-btn>

                        <v-tooltip
                          v-model="showText"
                          top
                        >
                          <template v-slot:activator="{ on, attrs }">
                            <p
                              icon
                              v-bind="attrs"
                              v-on="on"
                            >
                            </p>
                          </template>
                          <span>คัดลอกสำเร็จ</span>
                        </v-tooltip>
                      </v-card-title>

                      <v-card-text class="pt-4">
                        <div v-for="(item , index) in tokenSearch" :key="index">
                          <span>{{ item.access_token }}</span>
                        </div>
                      </v-card-text>

                      <v-divider></v-divider>

                      <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn
                          color="primary"
                          text
                          @click="modalShowData = false"
                        >
                          ปิด
                        </v-btn>
                      </v-card-actions>
                  </v-card>
                </v-dialog>
              </v-col>
            </v-row>
            </div>
            <div v-else-if="keyTable === 2">
              <v-row dense>
              <v-col cols="12">
                <v-data-table
                :headers="headers"
                :items="inactiveBusinessAdmin"
                :search="search"
                style="width:100%; white-space: nowrap;"
                height="100%"
                :page.sync="page"
                @pagination="countRequest"
                no-results-text="ไม่พบชื่อผู้ใช้งานของระบบ"
                no-data-text="ไม่มีชื่อชื่อผู้ใช้งานของระบบ"
                :update:items-per-page="itemsPerPage"
                :items-per-page="10"
                class="elevation-1 mt-4 user-table"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                >
                  <template v-slot:[`item.indexOfUser`]="{ item }">
                    {{
                      listBusinessAdmin.map(function(x) {
                        return x.id;
                      }).indexOf(item.id) + 1
                    }}
                  </template>
                  <template v-slot:[`item.status`]="{ item }">
                    <div style="display: flex; justify-content: flex-start; gap: 5px;">
                    <v-icon v-if="item.status === 'active'" color="#55b78f" x-small>mdi-brightness-1</v-icon>
                    <v-icon v-else-if="item.status === 'inactive'" color="#f4675d" x-small>mdi-brightness-1</v-icon> {{ item.status }}
                  </div>
                  </template>
                  <template v-slot:[`item.fullname`]="{ item }">
                    {{ item.first_name_th + ' ' + item.last_name_th }}
                  </template>
                  <template v-slot:[`item.fullname_eng`]="{ item }">
                    {{ item.first_name_en + ' ' + item.last_name_en }}
                  </template>
                  <template v-slot:[`item.department_name`]="{ item }">
                    {{ item.department_name !== null ? item.department_name : '-' }}
                  </template>
                  <template v-slot:[`item.access_token`]="{ item }">
                    <v-btn color="#27AB9C" outlined @click="OpenDialogShowData(item.email)">ดู Token</v-btn>
                    <!-- {{ item.access_token !== null ? item.access_token : '-' }} -->
                  </template>
                  <template v-slot:[`item.id`]="{ item }">
                    <v-btn color="#27AB9C" outlined @click="couponUser(item)">ดูคูปอง</v-btn>
                    <!-- {{ item.access_token !== null ? item.access_token : '-' }} -->
                  </template>
                  <template v-slot:[`item.manage`]="{ item }">
                    <v-btn v-if="item" plain @click="openEditStatusDialog(item)"><v-icon color="#27AB9C">mdi-dots-vertical</v-icon></v-btn>
                </template>
                </v-data-table>
                <v-dialog v-model="modalShowData" :width="MobileSize ? '100%' : '50%'" persistent>
                  <v-card >
                      <v-card-title class="text-h5 grey lighten-2">
                        <span>Access Token</span>
                        <v-btn text color="#b1afae" style="margin-left: auto" @click="handleClick()"><v-icon>mdi-content-copy</v-icon>คัดลอก</v-btn>

                        <v-tooltip
                          v-model="showText"
                          top
                        >
                          <template v-slot:activator="{ on, attrs }">
                            <p
                              icon
                              v-bind="attrs"
                              v-on="on"
                            >
                            </p>
                          </template>
                          <span>คัดลอกสำเร็จ</span>
                        </v-tooltip>
                      </v-card-title>

                      <v-card-text class="pt-4">
                        <div v-for="(item , index) in tokenSearch" :key="index">
                          <span>{{ item.access_token }}</span>
                        </div>
                      </v-card-text>

                      <v-divider></v-divider>

                      <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn
                          color="primary"
                          text
                          @click="modalShowData = false"
                        >
                          ปิด
                        </v-btn>
                      </v-card-actions>
                  </v-card>
                </v-dialog>
              </v-col>
            </v-row>
            </div>
          </v-col>
        </v-row>
      </v-card-text>
      <!-- <CreateAdminModal ref="CreateAdminModal" />
      <DetailAdminModal ref="DetailAdminModal" /> -->
    </v-card>
    <v-dialog v-model='openEditStatus' :width="MobileSize ? '70%' : '40%'" persistent @keydown.esc="openEditStatus = false">
        <v-card min-height='100%'>
          <v-app-bar flat color="primary">
            <v-toolbar-title v-if="!MobileSize" style="font-size: 20px; color: white;">แก้ไขสถานะผู้ใช้</v-toolbar-title>
            <v-toolbar-title v-else style="font-size: 16px; color: white;">แก้ไขสถานะผู้ใช้</v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn plain fab small @click='openEditStatus = false' icon><v-icon color='white'>mdi-close</v-icon></v-btn>
          </v-app-bar>
          <v-container>
              <v-row dense class="mx-2 mt-1" width="100%">
                <v-col cols="12">
                  <v-text-field v-model="selectedItem.name" outlined label="ชื่อ-สกุล" readonly></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-text-field v-model="selectedItem.status" outlined label="สถานะ" readonly></v-text-field>
                </v-col>
              </v-row>
            <v-card-actions>
              <v-row v-if="!MobileSize" dense class='d-flex justify-center mt-1' style="padding: 0 20%; gap: 10%;" width="100%">
                <v-btn class="ml-2" text color="primary" small :style="{ flex: '1' }" @click="openEditStatus = false"><span style="font-size: 12px;">Cancel</span></v-btn>
                <v-btn class="white--text ml-2" color="primary" small :style="{ flex: '1' }" @click="editStatus(selectedItem.id,selectedItem.status)"><span style="font-size: 12px;">{{ textStatus }}</span></v-btn>
              </v-row>
              <v-row v-else dense class='d-flex justify-center mt-1' style="padding: 0 10%; gap: 10%;" width="100%">
                <v-btn class="ml-2" text color="primary" small :style="{ flex: '1' }" @click="openEditStatus = false"><span style="font-size: 12px;">Cancel</span></v-btn>
                <v-btn class="white--text ml-2" color="primary" small :style="{ flex: '1' }" @click="editStatus(selectedItem.id,selectedItem.status)"><span style="font-size: 12px;">{{ textStatus }}</span></v-btn>
              </v-row>
            </v-card-actions>
          </v-container>
        </v-card>
      </v-dialog>
      <v-dialog v-model='openSuccess' :width="MobileSize ? '60%' : '30%'" persistent @keydown.esc="openSuccess = false">
        <v-card min-height='100%'>
          <v-img height="100%" :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn plain fab small @click='openSuccess = false' icon><v-icon color='#BABABA'>mdi-close</v-icon></v-btn>
          </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
                บันทึกสำเร็จ
            </v-card-text>
            <v-card-actions>
              <v-row dense class='d-flex justify-center' style="padding: 0 25%;">
                <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="openSuccess = !openSuccess">ตกลง</v-btn>
              </v-row>
            </v-card-actions>
          </v-container>
        </v-card>
      </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  components: {
    // CreateAdminModal: () => import('@/components/AdminPanit/AdminManage/CreateAdminPanitModal'),
    // DetailAdminModal: () => import('@/components/AdminPanit/AdminManage/DetailAdminPanitModal')
    // EditAdminModal: () => import('@/components/AdminPanit/AdminManage/EditAdminPanitModal')
  },
  data () {
    return {
      search: '',
      listBusinessAdmin: [],
      showCountRequest: 0,
      itemsPerPage: 10,
      page: 1,
      isSuperAdmin: null,
      modalShowData: false,
      tokenSearch: {},
      searchToken: '',
      showText: false,
      keyTable: 0,
      openEditStatus: false,
      openSuccess: false,
      selectedItem: [],
      statusAPI: '',
      textStatus: '',
      headers: [
        { text: 'ลำดับ', value: 'indexOfUser', width: '20', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', value: 'status', width: '60', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'อีเมล', value: 'email', width: '80', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ - นามสกุล', value: 'fullname', width: '120', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ - นามสกุล (en)', value: 'fullname_eng', width: '170', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'Username ONE', value: 'username_oneid', width: '170', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ตำแหน่ง', value: 'department_name', width: '120', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'คูปอง', value: 'id', width: '120', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'Token', value: 'access_token', width: '120', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'manage', sortable: false, class: 'backgroundTable fontTable--text', fixed: true, right: true }
      ]
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    activeBusinessAdmin () {
      return this.listBusinessAdmin.filter(item => item.status === 'active')
    },
    inactiveBusinessAdmin () {
      return this.listBusinessAdmin.filter(item => item.status === 'inactive')
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/adminUserWebMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'adminUserWeb')
        this.$router.push({ path: '/adminUserWeb' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    // this.$EventBus.$on('createAdminPanitSuccess', this.getShopData)
    // this.$EventBus.$on('listBusinessAdmindeleteAdminPanitSuccess', this.getShopData)
    // this.$EventBus.$on('editAdminPanitSuccess', this.getShopData)
    if (localStorage.getItem('oneData') !== null) {
      this.getShopData()
    //   this.AuthorityUser()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  beforeDestroy () {
    // this.$EventBus.$off('createAdminPanitSuccess')
    // this.$EventBus.$off('deleteAdminPanitSuccess')
    // this.$EventBus.$off('editAdminPanitSuccess')
  },
  methods: {
    async OpenDialogShowData (email) {
      this.$store.commit('openLoader')
      this.searchToken = { search: email }
      await this.$store.dispatch('actionsGetToken', this.searchToken)
      var response = await this.$store.state.ModuleAdminManage.stateGetToken
      this.tokenSearch = response.data
      this.modalShowData = true
      this.$store.commit('closeLoader')
    },
    handleClick () {
      this.copyToken()
      this.showText = !this.showText
    },
    copyToken () {
      const token = this.tokenSearch.length > 0 ? this.tokenSearch[0].access_token : ''
      if (token) {
        navigator.clipboard.writeText(token).then(() => {
          // console.log('Copy Token Success:', token)
        }).catch(err => {
          console.error('Copy Token Fail:', err)
        })
      }
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    // async AuthorityUser () {
    //   await this.$store.dispatch('actionsAuthorityUser')
    //   var response = await this.$store.state.ModuleUser.stateAuthorityUser
    //   if (response.message === 'Get user detail success') {
    //     if (response.data.current_role_user.super_admin_platform === true) {
    //       this.isSuperAdmin = true
    //     } else {
    //       this.isSuperAdmin = false
    //     }
    //   }
    // },
    handleTabChange (key) {
      this.keyTable = key
    },
    openEditStatusDialog (item) {
      this.selectedItem = { ...item }
      this.selectedItem.name = item.first_name_th + ' ' + item.last_name_th
      if (this.selectedItem.status === 'active') {
        this.textStatus = 'inactive'
      } else if (this.selectedItem.status === 'inactive') {
        this.textStatus = 'active'
      }
      this.openEditStatus = true
    },
    async editStatus () {
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.selectedItem.status === 'active') {
        this.statusAPI = 'delete-user'
      } else if (this.selectedItem.status === 'inactive') {
        this.statusAPI = 'active-user'
      }
      const data = {
        user_id: this.selectedItem.id
      }
      // console.log('data', data)
      try {
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}admin/${this.statusAPI}`,
          data: data,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'POST'
        })
        this.getShopData()
        this.$store.commit('closeLoader')
        this.openEditStatus = false
        this.openSuccess = true
      } catch (error) {
        alert('Error. Please try again.')
        this.$store.commit('closeLoader')
        this.openEditStatus = false
      }
    },
    async getShopData () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsUserInSystem')
      var response = await this.$store.state.ModuleAdminManage.stateUserInSystem
      // console.log('list business ====>', response)
      if (response.message === 'Data List User') {
        this.$store.commit('closeLoader')
        this.listBusinessAdmin = await [...response.data]
        this.listBusinessAdmin.forEach((element) => {
          element.fullname = element.first_name_th + ' ' + element.last_name_th
          element.fullname_eng = element.first_name_en + ' ' + element.last_name_en
        })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    async couponUser (val) {
      var fullname = val.first_name_th + ' ' + val.last_name_th
      console.log(fullname)
      if (!this.MobileSize) {
        this.$router.push({ path: `/AdminCouponUser?userId=${val.id}&name=${fullname}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/AdminCouponUserMobile?userId=${val.id}&name=${fullname}` }).catch(() => {})
      }
    }
  }
}
</script>

<style>
.v-tabs:not(.v-tabs--vertical):not(.v-tabs--right) > .v-slide-group--is-overflowing.v-tabs-bar--is-mobile:not(.v-slide-group--has-affixes) .v-slide-group__prev {
  display: none !important;
}
</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}

</style>

<style lang="scss" scoped>
.user-table {
::v-deep table {
  tbody {
  tr {
      td:nth-child(10) {
      position: sticky !important;
      position: -webkit-sticky !important;
      right: 0;
      z-index: 10;
      background: white;
      }
  }
  }
  thead {
  tr {
      th {
        white-space: nowrap;
      }
      th:nth-child(1) {
      position: sticky !important;
      position: -webkit-sticky !important;
      right: 0;
      z-index: 10;
      background: white;
      }
  }
  }
  thead {
  tr {
      th:nth-child(10) {
      z-index: 11;
      background: white;
      position: sticky !important;
      position: -webkit-sticky !important;
      right: 0;
      }
  }
  }
}}
</style>
