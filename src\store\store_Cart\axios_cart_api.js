import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}
const GetTokenNoBearer = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}
export default {
  // add to cart
  async AddToCart (val) {
    const auth = await GetToken()
    try {
      // var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/add_to_cart`, val, auth)
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/add_to_cart_b2b`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // update cart
  async UpdateCart (val) {
    const auth = await GetToken()
    try {
      // var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/update_cart`, val, auth)
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/update_to_cart_b2b`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // delete all item cart
  async DeleteAllProductCart (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/delete_to_cart_b2b`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // get detail cart
  async DetailCart (val) {
    // console.log('AxiosDetailCart', val)
    const auth = await GetToken()
    try {
      // var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_cart`, val, auth)
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_cart_b2b_v2`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // get cart
  async GetCart (val) {
    const auth = await GetToken()
    try {
      // var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_cart`, val, auth)
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_cart_b2b`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetCartV2 (val) {
    const auth = await GetToken()
    try {
      // var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_cart`, val, auth)
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_cart_b2b_v2`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetOrderEdit (val) {
    const auth = await GetToken()
    try {
      // var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_cart`, val, auth)
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_order_for_edit`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EstimateEdit (val) {
    const auth = await GetToken()
    try {
      // var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_cart`, val, auth)
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/estimate_order_for_edit`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // GetCartSpecialPrice
  async GetCartSpecialPrice (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_cart_special_price`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // create order
  async CreateOrder (val) {
    const auth = await GetToken()
    try {
      // var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_order`, val, auth)
      // var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_order_eprocurement`, val, auth)
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_order_b2b`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // create order special price
  async CreateOrderSpecialprice (val) {
    const auth = await GetToken()
    try {
      // var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_order`, val, auth)
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_order_special_price`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // CreateInvoiceAddressPurchaser
  async CreateInvoiceAddressPurchaser (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_invoice_address`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // confirmOrderLogin add Taxaddress sync CreateOrder
  async CreateOrderSyncTaxaddress (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/invoice_relate_cart`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetCompanyPurchaser (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_company_purchaser`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetAdminData (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_admin_data`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateQuManual (val) {
    // const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_qu_dummy`, val)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // get Payment page
  async GetPaymentPage (val) {
    const auth = await GetToken()
    try {
      // var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_payment_page`, val, auth)
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_payment_page`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetPaymentPageB2B (val) {
    const auth = await GetToken()
    try {
      // var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_payment_page`, val, auth)
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}b2b/link_to_payment`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Local Storage
  async LocalstorageDetailCart (val) {
    try {
      // var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/frontend_localstorage_detail_cart`, data)
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}localstoragecart/frontendLocalStorageDetailCart`, val)
      // console.log('API GET RES')
      return response.data
    } catch (error) {
      // console.log('API GET ERR')
      return error.response.data
    }
  },
  async LocalstorageGetCart (val) {
    try {
      // var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/frontend_localstorage_get_cart`, data)
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}localstoragecart/frontendLocalStorageGetCart`, val)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async LocalstorageCreateOrder (val) {
    try {
      // var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/frontend_localstorage_create_order`, val)
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}localstoragecart/frontendLocalStorageCreateOrder`, val)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateSentDataPPL (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_PAPERLESS}external_service/api/v1/post_external_data`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // check data change when add to cart local
  async LocalstorageCheckAddToCart (val) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}localstoragecart/frontendLocalStorageCheckAddToCart`, val)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // PreviewQU
  async PreviewQU (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/qu/preview_qu`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetETaxPDF (val) {
    const auth = await GetTokenNoBearer()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}etax/document_r2`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PayCashInStore (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/pay_cash_in_store`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDocumentType (data) {
    // const auth = await GetToken()
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const setHeader = {
      headers: {
        Authorization: `Bearer ${oneData.user.access_token}`,
        'client-id': `${process.env.VUE_APP_PR_PORTAL_CLIENT_ID_GET_DOC}`,
        'client-secret': `${process.env.VUE_APP_PR_PORTAL_CLIENT_SECRET_GET_DOC}`
      }
    }
    try {
      var response = await axios.get(`${process.env.VUE_APP_API_PR_PORTAL}api/document_type/api/v1/get_document_type_external?tax_id=${data.tax_id}`, setHeader)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListItemCodePr (data) {
    // const auth = await GetToken()
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const setHeader = {
      headers: {
        Authorization: `Bearer ${oneData.user.access_token}`,
        secretKey: `${process.env.VUE_APP_PR_PORTAL_SECRET_KEY}`
      }
    }
    try {
      // var response = await axios.get(`${process.env.VUE_APP_API_PR_PORTAL}api/item_erp/api/v1/show_itemerp?tax_id=${data}`, auth)
      var response = await axios.get(`${process.env.VUE_APP_API_PR_PORTAL}api/item_erp/api/v1/show_itemerp_external?tax_id=${data}`, setHeader)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetQRCode (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}payment/getqrcode`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetQRCodeV2 (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}payment/getqrcodev2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CheckResultQRCode (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}payment/checkresultqrcode`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CheckResultQRCodeV2 (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}payment/checkresultqrcodev2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ResponseFromTDCP (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}payment/responsefromtdcp`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetCC (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}payment/getCredit`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetCCV2 (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}payment/getCreditV2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EstimateCost (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/estimate_cost`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async listEstimateCostEdit (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_estimate_order_for_edit`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async editOrderQT (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_order_QT`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetCompanyAddress (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}getCompanyAddress`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SetDefaultCompanyAddress (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}setDefaultCompanyAddress`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteCompanyAddress (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}deleteCompanyAddress`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetReviewQuotation (data) {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/gen_qt_external_from_cart?role_user=${data.role_user}&company_id=${data.company_id}&com_perm_id=${data.com_perm_id}&seller_shop_id=${data.seller_shop_id}&token=${data.token}&customer_id=${data.customer_id}&invoice_id=${data.invoice_id}&product_free=${data.product_free}`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CheckStockBeforePayment (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/ckeck_stock_before_payment`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CheckStockBeforeCreateOrder (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/ckeck_stock_before_create_order`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}coupon/readyV2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UseCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}coupon/use`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EWTH (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/import_pv_ap`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ReCheck (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}coupon/recheck`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async RepeatOrder (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/repeat_order`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CalculateQT (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/calculate_QT`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchInetRelation (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/search_inet_relation`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CheckInetRelation (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/check_inet_relation`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchCouponPlatform (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/search_coupon_platform`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListPlatformCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}platform/coupon/readyV2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SendEWHT (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}withholding/sendewht`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetQRCodeB2B (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}payment/getqrcodeb2b`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CheckResultB2B (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}payment/checkresultb2b`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetCCB2B (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}payment/getcreditb2b`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async RedirectWHT () {
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END2}withholding/redirectwithewht`)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
