<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">แดชบอร์ด User Active</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>แดชบอร์ด User Active</v-card-title>
      <v-row v-if="MobileSize || IpadSize">
        <v-col cols="12">
          <v-row class="mx-1">
            <!-- Total User -->
            <v-col cols="12">
                <v-card class="cardUserActive" width="100%" outlined style="background-color: #2bc08f;">
                <v-col cols="12">
                    <v-row no-gutters class="format_box">
                    <p class="titleRespons">Total User</p>
                    <p class="dataUserActiveMobile">{{ showUserActive.total_user | formatNumber  }}</p>
                    </v-row>
                </v-col>
                </v-card>
            </v-col>
          </v-row>
        <v-row class="mx-1">
          <!-- active users today -->
          <v-col cols="6">
            <v-card class="cardUserActive" width="100%" outlined style="background-color: #5daee5;">
              <v-col cols="12">
                <v-row no-gutters class="format_box">
                  <p class="titleRespons">Today</p>
                  <p class="dataUserActiveMobile">{{ showUserActive.active_users_last_to_days | formatNumber }}</p>
                </v-row>
              </v-col>
            </v-card>
          </v-col>
          <!-- active users last 7 days -->
          <v-col cols="6">
            <v-card class="cardUserActive" width="100%" outlined style="background-color: #fdcf71;">
              <v-col cols="12">
                <v-row no-gutters class="format_box">
                  <p class="titleRespons">7 Day</p>
                  <p class="dataUserActiveMobile">{{ showUserActive.active_users_last_7_days | formatNumber }}</p>
                </v-row>
              </v-col>
            </v-card>
          </v-col>
        </v-row>
        <v-row class="mx-1">
          <!-- active_users_last_14_days -->
          <v-col cols="6">
            <v-card class="cardUserActive" width="100%" outlined style="background-color: #ef947b;">
              <v-col cols="12">
                <v-row no-gutters class="format_box">
                  <p class="titleRespons">14 Day</p>
                  <p class="dataUserActiveMobile">{{ showUserActive.active_users_last_14_days | formatNumber }}</p>
                </v-row>
              </v-col>
            </v-card>
          </v-col>
          <!-- active_users_last_30_days -->
          <v-col cols="6">
            <v-card class="cardUserActive" width="100%" outlined style="background-color: #e05470;">
              <v-col cols="12">
                <v-row no-gutters class="format_box">
                    <p class="titleRespons">30 Day</p>
                    <p class="dataUserActiveMobile">{{ showUserActive.active_users_last_30_days | formatNumber }}</p>
                </v-row>
              </v-col>
            </v-card>
          </v-col>
          <!-- active_users_last_90_days -->
          <v-col cols="6">
            <v-card class="cardUserActive" width="100%" outlined style="background-color: #cc0066;">
              <v-col cols="12">
                <v-row no-gutters class="format_box">
                  <p class="titleRespons">90 Day</p>
                  <p class="dataUserActiveMobile">{{ showUserActive.active_users_last_90_days | formatNumber }}</p>
                </v-row>
              </v-col>
            </v-card>
          </v-col>
        </v-row>
      </v-col>
      </v-row>
      <v-row v-else-if="IpadProSize">
        <v-col cols="12">
          <v-row class="mx-1">
            <!-- Total User -->
            <v-col cols="6">
              <v-card class="cardUserActive" width="100%" outlined style="background-color: #2bc08f;">
                <v-col cols="12">
                  <v-row no-gutters class="format_box">
                    <p class="titleRespons">Total User</p>
                    <p class="dataUserActiveMobile">{{ showUserActive.total_user | formatNumber }}</p>
                  </v-row>
                </v-col>
              </v-card>
            </v-col>
            <!-- active users today -->
            <v-col cols="6">
              <v-card class="cardUserActive" width="100%" outlined style="background-color: #5daee5;">
                <v-col cols="12">
                  <v-row no-gutters class="format_box">
                    <p class="titleRespons">Today</p>
                    <p class="dataUserActiveMobile">{{ showUserActive.active_users_last_to_days | formatNumber }}</p>
                  </v-row>
                </v-col>
              </v-card>
            </v-col>
        </v-row>
        <v-row class="mx-1">
          <!-- active users last 7 days -->
          <v-col cols="3">
            <v-card class="cardUserActive" width="100%" outlined style="background-color: #fdcf71;">
              <v-col cols="12">
                <v-row no-gutters class="format_box">
                  <p class="titleRespons">7 Day</p>
                  <p class="dataUserActiveMobile">{{ showUserActive.active_users_last_7_days | formatNumber }}</p>
                </v-row>
              </v-col>
            </v-card>
          </v-col>
          <!-- active_users_last_14_days -->
          <v-col cols="3">
            <v-card class="cardUserActive" width="100%" outlined style="background-color: #ef947b;">
              <v-col cols="12">
                <v-row no-gutters class="format_box">
                  <p class="titleRespons">14 Day</p>
                  <p class="dataUserActiveMobile">{{ showUserActive.active_users_last_14_days | formatNumber }}</p>
                </v-row>
              </v-col>
            </v-card>
          </v-col>
          <!-- active_users_last_30_days -->
          <v-col cols="3">
            <v-card class="cardUserActive" width="100%" outlined style="background-color: #e05470;">
              <v-col cols="12">
                  <v-row no-gutters class="format_box">
                    <p class="titleRespons">30 Day</p>
                    <p class="dataUserActiveMobile">{{ showUserActive.active_users_last_30_days | formatNumber }}</p>
                  </v-row>
              </v-col>
            </v-card>
          </v-col>
          <!-- active_users_last_90_days -->
          <v-col cols="3">
            <v-card class="cardUserActive" width="100%" outlined style="background-color: #cc0066;">
              <v-col cols="12">
                <v-row no-gutters class="format_box">
                  <p class="titleRespons">90 Day</p>
                  <p class="dataUserActiveMobile">{{ showUserActive.active_users_last_90_days | formatNumber }}</p>
                </v-row>
              </v-col>
            </v-card>
          </v-col>
        </v-row>
        </v-col>
      </v-row>
      <v-row v-else>
        <!-- Total User -->
        <v-col cols="6" class="Total_User">
          <v-card class="cardUserActive" width="100%" outlined style="background-color: #2bc08f;">
            <v-col cols="12">
                <v-row no-gutters class="format_box">
                  <p class="titleDes">Total User</p>
                  <p class="dataUserActive">{{ showUserActive.total_user | formatNumber }}</p>
                </v-row>
            </v-col>
          </v-card>
        </v-col>
        <!-- active users today -->
        <v-col cols="6" class="today">
          <v-card class="cardUserActive" width="100%" outlined style="background-color: #5daee5;">
            <v-col cols="12">
                <v-row no-gutters class="format_box">
                <p class="titleDes">Today</p>
                <p class="dataUserActive">{{ showUserActive.active_users_last_to_days | formatNumber }}</p>
                </v-row>
            </v-col>
          </v-card>
        </v-col>
      <!-- active users last 7 days -->
        <v-col class="last_7_days">
          <v-card class="cardUserActive" width="100%" outlined style="background-color: #fdcf71;">
            <v-col cols="12">
                <v-row no-gutters class="format_box">
                <p class="titleDes">7 Day</p>
                <p class="dataUserActive">{{ showUserActive.active_users_last_7_days | formatNumber }}</p>
                </v-row>
            </v-col>
          </v-card>
        </v-col>
        <!-- active_users_last_14_days -->
        <v-col class="last_14_days">
          <v-card class="cardUserActive" width="100%" outlined style="background-color: #ef947b;">
            <v-col cols="12">
                <v-row no-gutters class="format_box">
                <p class="titleDes">14 Day</p>
                <p class="dataUserActive">{{ showUserActive.active_users_last_14_days | formatNumber }}</p>
                </v-row>
            </v-col>
          </v-card>
        </v-col>
        <!-- active_users_last_30_days -->
        <v-col class="last_30_days">
          <v-card class="cardUserActive" width="100%" outlined style="background-color: #e05470;">
            <v-col cols="12">
                <v-row no-gutters class="format_box">
                <p class="titleDes">30 Day</p>
                <p class="dataUserActive">{{ showUserActive.active_users_last_30_days | formatNumber }}</p>
                </v-row>
            </v-col>
          </v-card>
        </v-col>
        <!-- active_users_last_90_days -->
        <v-col class="last_90_days">
          <v-card class="cardUserActive" width="100%" outlined style="background-color: #cc0066;">
            <v-col cols="12">
                <v-row no-gutters class="format_box">
                <p class="titleDes">90 Day</p>
                <p class="dataUserActive">{{ showUserActive.active_users_last_90_days | formatNumber }}</p>
                </v-row>
            </v-col>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12">
          <v-card-title style="font-weight: 700; color: #38b2a4;">User Last Login 90 Day</v-card-title>
          <v-col cols="12" class="d-flex align-center">
            <v-text-field v-model="search" placeholder="ค้นหาชื่อผู้ใช้งานในระบบ" outlined rounded dense hide-details>
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
            <v-spacer></v-spacer>
            <v-btn style="border-radius: 20px; background: #27AB9C; padding: 1vw; font-size: small;" @click="getExcel()">
              <v-icon style="color: #fff">mdi-microsoft-excel</v-icon><br>
            <span style="color: #fff">{{ MobileSize || IpadSize ? 'User 90 days' : 'Export User 90 days' }}</span>
            </v-btn>
          </v-col>
          <v-col cols="12">
            <v-data-table
            :headers="headers"
            :items="showUserActive.user_active_90_days"
            :search="search"
            style="width:100%; text-align: center; white-space: nowrap;"
            height="100%"
            @pagination="countRequest"
            no-results-text="ไม่พบรายการผู้ใช้งานระบบ"
            no-data-text="ไม่พบรายการผู้ใช้งานระบบ"
            :items-per-page="10"
            class="elevation-1 mt-4"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            >
                <template v-slot:[`item.social_type`]="{ item }">
                  <span v-if="item.social_type" color="#27AB9C" > {{ item.social_type}}</span>
                  <span v-else>-</span>
                </template>
                <template v-slot:[`item.username_oneid`]="{ item }">
                  <span v-if="item.username_oneid" color="#27AB9C" > {{ item.username_oneid}}</span>
                  <span v-else>-</span>
                </template>
                <template v-slot:[`item.social_id`]="{ item }">
                  <span v-if="item.social_id" color="#27AB9C" > {{ item.social_id}}</span>
                  <span v-else>-</span>
                </template>
                <template v-slot:[`item.lineUserId`]="{ item }">
                  <div v-if="MobileSize">
                    <div v-if="item.lineUserId" color="#27AB9C">
                      <v-tooltip top>
                        <template v-slot:activator="{ on, attrs }">
                          <span v-bind="attrs" v-on="on"> {{ substringLineUserId(item.lineUserId) }}</span>
                        </template>
                        <span>{{ item.lineUserId }}</span>
                      </v-tooltip>
                    </div>
                    <span v-else>-</span>
                  </div>
                  <div v-else>
                    <span v-if="item.lineUserId" color="#27AB9C" > {{ item.lineUserId }}</span>
                    <span v-else>-</span>
                  </div>
                </template>
                <template v-slot:[`item.created_at`]="{ item }">
                  <span v-if="item.created_at" color="#27AB9C" > {{new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
                  <span v-else>-</span>
                </template>
                <template v-slot:[`item.updated_at`]="{ item }">
                  <span v-if="item.updated_at" color="#27AB9C" > {{new Date(item.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
                  <span v-else>-</span>
                </template>
            </v-data-table>
          </v-col>
        </v-col>
      </v-row>
  </v-card>
</v-container>
</template>

<script>
import Vue from 'vue'
import { Decode } from '@/services'
export default {
  data () {
    return {
      showUserActive: {},
      search: '',
      headers: [
        { text: 'social_type', value: 'social_type', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'username_oneid', value: 'username_oneid', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'social_id', value: 'social_id', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'lineUserId', value: 'lineUserId', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'created_at', value: 'created_at', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'updated_at', value: 'updated_at', sortable: false, class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardUserActiveMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'dashboardUserActive')
        this.$router.push({ path: '/dashboardUserActive' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    if (localStorage.getItem('oneData') !== null) {
      this.getUserActive()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.formatSold()
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async getUserActive () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsUserActive')
      var response = this.$store.state.ModuleAdminManage.stateUserActive
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.showUserActive = response.data
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          html: '<h3>ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่</h3>'
        })
        this.showUserActive = {}
      }
    //   console.log('showOrder---->', this.showUserActive)
    },
    substringLineUserId (lineUserId) {
      return lineUserId.length > 15 ? lineUserId.substring(0, 15) + '...' : lineUserId
    },
    formatSold () {
      Vue.filter('formatNumber', function (value) {
        if (!value) return 0
        if (value >= 1000) {
          return (value / 1000).toFixed(2) + ' K'
        }
        return value.toString()
      })
    },
    async getExcel () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}exports/users_active`,
        data: this.exportExcelBody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'user_active_90_days.xlsx')
        // fileLink.setAttribute('download', 'report.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    }
  }
}
</script>

<style scoped>
.format_box {
  display: flex;
  flex-direction: column;
  text-align: center;
}
.last_7_days, .last_14_days, .last_30_days, .last_90_days .Cards{
  border-radius: 10px !important;
  max-width: 100% !important;
  width: 100% !important;
}
.theme--light.v-data-table > .v-data-table__wrapper > table > thead > tr:last-child > th {
  white-space: nowrap !important;
  text-align: center !important;
}
.v-data-table > .v-data-table__wrapper > table > tbody > tr > td {
  text-align: center !important;
}
.cardUserActive {
  border-radius: 1vw !important;
}
.titleRespons {
  color: #fff;
  font-weight: 600;
}
.titleDes {
  color: #fff;
  font-weight: 600;
}
.dataUserActive {
  color: #535353;
  font-size: x-large;
  font-weight: 600;
  background-color: #fff;
  border-radius: 5vw;
}
.dataUserActiveMobile {
  color: #535353;
  font-size: larger;
  font-weight: 600;
  background-color: #fff;
  border-radius: 5vw;
}
</style>
