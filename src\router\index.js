import Vue from 'vue'
import VueGtm from '@gtm-support/vue2-gtm'
import VueRouter from 'vue-router'
import Vue<PERSON>eta from 'vue-meta'
// import store from '@/store/index.js'

Vue.use(VueRouter)
Vue.use(VueMeta, {
  keyName: 'metaInfo', // the component option name that vue-meta looks for meta info on.
  attribute: 'data-vue-meta', // the attribute name vue-meta adds to the tags it observes
  ssrAttribute: 'data-vue-meta-server-rendered', // the attribute name that lets vue-meta know that meta info has already been server-rendered
  tagIDKeyName: 'vmid' // the property name that vue-meta uses to determine whether to overwrite or append a tag
})

const routes = [
  { path: '/RedirectPaymentPage', component: () => import('@/components/Cart/RedirectPayment') },
  { path: '/redirect', component: () => import('@/views/redirect') },
  { path: '/redirectSSO', component: () => import('@/views/redirectSSO') },
  { path: '/shared_token_go', component: () => import('@/views/redirectGO') },
  { path: '/api/backend/api/shared_token_product_oneauthen', component: () => import('@/views/redirectOneAuthen') },
  { path: '/share/:data', name: 'share', component: () => import(/* webpackPrefetch: true */ '@/views/RedirectURL') },
  { path: '/linkShop/:data', name: 'linkShop', component: () => import(/* webpackPrefetch: true */ '@/views/RedirectShopURL') },
  { path: '/linkGroup/:data', name: 'linkGroup', component: () => import(/* webpackPrefetch: true */ '@/views/RedirectGroupURL') },
  { path: '/linkAffiliate/:data', name: 'linkAffiliate', component: () => import(/* webpackPrefetch: true */ '@/views/RedirectAffiliateURL') },
  // { path: '/link/s/:data', name: 'linkAffiliate', component: () => import(/* webpackPrefetch: true */ '@/views/RedirectAffiliateURL') },
  { path: '/linkToApp', name: 'linkToApp', component: () => import(/* webpackPrefetch: true */ '@/views/RedirectHomeURL') },
  { path: '/PMCC/:id', name: 'PMCC', component: () => import('@/views/CarbonCopyPayment') },
  { path: '/NGC/google', component: () => import('@/views/google') },
  { path: '/NGC/line', component: () => import('@/views/line') },
  { path: '/NGC/facebook', component: () => import('@/views/facebook') },
  { path: '/registerLineOA', component: () => import('@/views/registerLineOA') },
  { path: '/room', component: () => import('@/components/Shop/LiveStreamShop/Room') },
  { path: '/watch', component: () => import('@/views/liveStreaming/watch') },
  { path: '/shopRegister', name: 'shopRegister', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/Shop/ShopRegister/shopRegister.vue') },
  { path: '/shopRegisterMobile', name: 'shopRegisterMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/Shop/ShopRegister/shopRegister.vue') },
  { path: '/infoRegisterShop', name: 'infoRegisterShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/Shop/ShopRegister/InfoRegisterShop.vue') },
  { path: '/infoRegisterShopMobile', name: 'infoRegisterShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/Shop/ShopRegister/InfoRegisterShop.vue') },
  {
    path: '/',
    name: 'Home',
    component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "home-chunk" */ '@/views/HomeUI'),
    children: [
      { path: '/404', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "home-chunk" */ '@/views/404') },
      { path: '/System-offline', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "home-chunk" */ '@/views/Renovations') },
      { path: '/', name: 'HomeProduct', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "home-chunk" */ '@/views/HomeProductUI') },
      { path: '/AllCoupons', name: 'AllCoupons', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "home-chunk" */ '@/components/Home/CouponHome/AllCouponHome') },
      { path: '/otpLogin', name: 'otpLogin', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "home-chunk" */ '@/components/Home/LoginWithOTP/OtpLogin') },
      { path: '/otpVerificationLogin', name: 'otpVerification', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "home-chunk" */ '@/components/Home/LoginWithOTP/OtpVerificationLogin') },
      { path: '/Login', name: 'LoginMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "home-chunk" */ '@/components/Home/LoginMobile') },
      { path: '/ForgotPassword', name: 'ForgotPassword', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "home-chunk" */ '@/components/Home/ForgotPassword/ForgotPasswordPage') },
      { path: '/Register', name: 'RegisterMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "home-chunk" */ '@/components/Home/RegisterMobile') },
      { path: '/ListProduct/:data', name: 'ListProduct', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "listproduct-chunk" */ '@/views/ListProductUI') },
      { path: '/ListVoucher/:data', name: 'ListVoucher', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "listproduct-chunk" */ '@/views/ListVoucherUI') },
      { path: '/AllCouponInShop/:data', name: 'AllCouponInShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "cupon-chunk" */ '@/components/Shop/AllCouponInShop') },
      { path: '/ListProductShop/:data', name: 'ListProductShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "listproduct-chunk" */ '@/views/ListProductShop') },
      { path: '/ListShopProduct/:data', name: 'ListShopProductUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "listproduct-chunk" */ '@/components/Shop/ListProductUI') },
      { path: '/DetailProduct/:data', name: 'DetailProduct', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "detailProduct-chunk" */ '@/views/DetailProductUI') },
      { path: '/ManageShop', name: 'ManageShop', component: () => import('@/views/CreateShop') },
      { path: '/MyShop', name: 'MyShop', component: () => import('@/views/MyShop') },
      { path: '/shoppage/:data', name: 'ShoppageUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "shopHomePage-chunk" */ '@/components/Shop/ShopHomepageUI.vue') },
      { path: '/ViewArticle', name: 'ViewArticle', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "shopHomePage-chunk" */ '@/components/Shop/ViewArticle.vue') },
      { path: '/GroupShoppage/:data', name: 'GroupShoppage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "shopHomePage-chunk" */ '@/components/Shop/GroupShopHomepage.vue') },
      { path: '/allShop', name: 'allShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "shopHomePage-chunk" */ '@/components/Home/ShopAll/AllShop.vue') },
      { path: '/buyAgain', name: 'buyAgain', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "shopHomePage-chunk" */ '@/components/Home/ShopAll/BuyAgain.vue') },
      { path: '/search/:data', name: 'search', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "search-chunk" */ '@/components/Search/SearchResultUI.vue') },
      { path: '/searchImage', name: 'searchImage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "search-chunk" */ '@/components/Search/SearchResultImageUI.vue') },
      { path: '/searchProduct/:data', name: 'searchProduct', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "search-chunk" */ '@/components/Search/SearchResaultProductUI.vue') },
      { path: '/AboutPanit', name: 'AboutPanit', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "shopHomePage-chunk" */ '@/components/AboutPanit/AboutPanit.vue') },
      { path: '/userprofileMobile', name: 'userprofileMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/views/UserProfileMobileUI') },
      // { path: '/userprofileDetail', name: 'userprofileDetail', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/NewUserProfileUI') },
      { path: '/userprofileDetail', name: 'userprofileDetail', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/userProfileNewUI') },
      { path: '/favoriteMobile', name: 'favoriteMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Favorite') },
      { path: '/addressProfileMobile', name: 'addressProfileMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/AddressProfile') },
      { path: '/pobuyerProfileMobile', name: 'pobuyerProfileMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/views/POBuyerProfileUI') },
      { path: '/pobuyerdetailapproveMobile', name: 'pobuyerdetailapproveMobileui', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/POBuyerDetailApproveUI') },
      { path: '/pobuyerdetailMobile', name: 'pobuyerdetailMobileui', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/POBuyerDetailUI') },
      { path: '/ClickBuyerAffiliateMobile', name: 'ClickBuyerAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/clickAffiliateBuyer') },
      { path: '/orderedBuyerAffilateMobile', name: 'orderedBuyerAffilateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/reportOrderAffiliate') },
      { path: '/SharedBuyerAffiliateMobile', name: 'SharedBuyerAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/DashboardBuyerAffiliate') },
      { path: '/tackingbuyerMobile', name: 'tackingbuyerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/views/TackingBuyerMobile') },
      { path: '/refundProductMobile', name: 'refundProductMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/views/RefundProductProfile') },
      { path: '/refundDetailMobile', name: 'refundDetailMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/RefundProductProfile/RefundProductDetail') },
      { path: '/reviewBuyerMobile', name: 'reviewBuyerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/ReviewListBuyer') },
      { path: '/Quotation', name: 'Quotation', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "Quotation-chunk" */ '@/components/PurchaseOrder/PurchaseOrder') },
      { path: '/QuotationEdit', name: 'QuotationEdit', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "Quotation-chunk" */ '@/components/PurchaseOrder/PurchaseOrderEdit') },
      { path: '/QuotationAllMobile', name: 'QuotationAllMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "shop-chunk" */ '@/components/Quotation/QuotationSeller/ListQuotationSeller') },
      { path: '/QuotationDetailMobile', name: 'QuotationDetailMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "shop-chunk" */ '@/components/Quotation/QuotationSeller/DetailQuotationSeller') },
      { path: '/pobuyerProfileRecordMobile', name: 'pobuyerProfileRecordMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/views/POBuyerProfileRecordUI') },
      { path: '/pobuyerProfileRecordSalesMobile', name: 'pobuyerProfileRecordSalesMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/views/POBuyerProfileRecordSalesUI') },
      { path: '/policy', name: 'policy', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "policy-chunk" */ '@/views/PolicyMain') },
      { path: '/termsofuse', name: 'termsofuse', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "policy-chunk" */ '@/views/TermsofUse') },
      { path: '/ChatAll', name: 'chatall', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "policy-chunk" */ '@/components/library/CallChatMe/callChatMeAll') },
      { path: '/withdrawAffiliateMobile', name: 'withdrawAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/withdrawAffiliate') },
      { path: '/manageFlashSaleCreateMobile', name: 'manageFlashSaleCreateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/Shop/ManageFlashSale/ManageFlashSaleCreate') },
      { path: '/refundOrderBuyerMobile', name: 'refundOrderBuyerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/RefundOrderBuyer') },
      { path: '/Popup', name: 'Popup', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/Home/Popup.vue') },
      { path: '/CreatePartnerMobile', name: 'CreatePartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/CreatePartner.vue') },
      { path: '/allGroupShop', name: 'allGroupShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "shopHomePage-chunk" */ '@/components/Home/ShopAll/AllGroupShop.vue') },
      { path: '/EditPartnerMobile', name: 'EditPartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/EditPartner.vue') },
      { path: '/adminGroupShopBGManageMobile', name: 'adminGroupShopBGManageMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/AdminPanit/AdminManage/adminGroupShopBGManage.vue') },
      { path: '/customBannerMobile', name: 'customBannerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/AdminPanit/AdminManage/customBanner.vue') },
      { path: '/PartnerBillingMobile', name: 'PartnerBillingMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/PartnerBilling.vue') },
      { path: '/paymentListMobile', name: 'paymentListMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/MarketplacePartner/paymentList.vue') },
      { path: '/AdminReachMobile', name: 'AdminReachMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminReach.vue') },
      { path: '/managePositionBussinessMobile', name: 'managePositionBussinessMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/managePositionBussiness') },
      { path: '/live', name: 'live', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Home/Live/Live.vue') },
      { path: '/AllProductCategory', name: 'AllProductCategory', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "home-chunk" */ '@/components/Category/AllProductCategory.vue') },
      { path: '/DetailServiceCoupon', name: 'DetailServiceCoupon', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "shopHomePage-chunk" */ '@/components/Home/HomeCoupon/DetailServiceCoupon.vue') },
      { path: '/ManageRegisterShopMobile', name: 'ManageRegisterShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageRegisterShop.vue') },
      { path: '/ManageRegisterShopDetailMobile', name: 'ManageRegisterShopDetailMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageRegisterShopDetail.vue') },
      { path: '/ManageRegisterInfoPartnerMobile', name: 'ManageRegisterInfoPartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageRegisterInfoPartner.vue') },
      { path: '/ManageRegisterInfoPartnerDetailMobile', name: 'ManageRegisterInfoPartnerDetailMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageRegisterInfoPartnerDetail.vue') },
      { path: '/ManageRegisterInfoShopMobile', name: 'ManageRegisterInfoShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageRegisterInfoShop.vue') },
      { path: '/ManageRegisterInfoShopDetailMobile', name: 'ManageRegisterInfoShopDetailMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageRegisterInfoShopDetail.vue') },
      {
        path: '/userprofile',
        component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '../views/UserProfileUI'),
        children: [
          // { path: '/userprofile', name: 'userprofileUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/NewUserProfileUI') },
          { path: '/userprofile', name: 'userprofileUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/userProfileNewUI') },
          // { path: '/FriendGetFriends', name: 'FriendGetFriends', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/FriendGetFriends') },
          { path: '/favorite', name: 'favorite', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Favorite') },
          { path: '/MyCouponsAndPoints', name: 'MyCouponsAndPoints', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Coupons/Coupons') },
          { path: '/buyerAffiliate', name: 'buyerAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/views/Affiliate/Buyer/buyerAffiliate') },
          { path: '/SharedBuyerAffiliate', name: 'SharedBuyerAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/DashboardBuyerAffiliate') },
          { path: '/ClickBuyerAffiliate', name: 'ClickBuyerAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/clickAffiliateBuyer') },
          { path: '/orderedBuyerAffilate', name: 'orderedBuyerAffilate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/reportOrderAffiliate') },
          { path: '/buyerDetail', name: 'buyerDetail', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/buyerDetail') },
          { path: '/socailMediaDetail', name: 'socailMediaDetail', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/socailMediaDetail') },
          { path: '/confirmDetail', name: 'confirmDetail', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/confirmDetail') },
          { path: '/productAffiliate', name: 'productAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/productAffiliate') },
          { path: '/editPay', name: 'editPay', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/editPay') },
          { path: '/editSocail', name: 'editSocail', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/editSocail') },
          { path: '/consentAffiliate', name: 'consentAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/consentAffiliate') },
          { path: '/ProductCardAffiliateUI', name: 'ProductCardAffiliateUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/Card/ProductCardAffiliateUI') },
          { path: '/ProductCardResponsiveAffiliate', name: 'ProductCardResponsiveAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/Card/ProductCardResponsiveAffiliate') },
          { path: '/showShopSellerAffiliate', name: 'showShopSellerAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/showShopSellerAffiliate') },
          { path: '/showProductSellerJoinAffiliate', name: 'showProductSellerJoinAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/showProductSellerJoinAffiliate') },
          { path: '/addressProfile', name: 'addressProfile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/AddressProfile') },
          { path: '/pobuyerProfile', name: 'pobuyeruiProfile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/views/POBuyerProfileUI') },
          { path: '/pobuyerProfileRecord', name: 'pobuyeruiProfileRecord', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/views/POBuyerProfileRecordUI') },
          { path: '/pobuyerProfileRecordSales', name: 'pobuyeruiProfileRecordSales', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/views/POBuyerProfileRecordSalesUI') },
          { path: '/tackingbuyer', name: 'tackingbuyer', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/views/TackingBuyer') },
          { path: '/pobuyerdetailapprove', name: 'pobuyerdetailapproveui', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/POBuyerDetailApproveUI') },
          { path: '/pobuyerprofilepointallshop', name: 'pobuyerprofilepointallshop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/POBuyerProfilePointAllShop') },
          { path: '/pobuyerdetail', name: 'pobuyerdetailui', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/POBuyerDetailUI') },
          { path: '/creditProfile', name: 'creditProfile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/CreditProfile') },
          { path: '/changePassword', name: 'changePassword', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/ChangePassword') },
          { path: '/refundProduct', name: 'refundProduct', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/views/RefundProductProfile') },
          { path: '/refundDetail', name: 'refundDetail', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/RefundProductProfile/RefundProductDetail') },
          { path: '/ListOrderBySales', name: 'ListOrderBySales', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/ListOrderBySales') },
          { path: '/DetailDueDateSaleOrderBySale', name: 'DetailDueDateSaleOrderBySale', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/DetailDueDateSaleOrderBySale') },
          { path: '/DetailOrderSalesNoJV', name: 'DetailOrderSalesNoJV', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/DetailOrderBySale') },
          { path: '/reviewBuyer', name: 'reviewBuyer', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/ReviewListBuyer') },
          { path: '/ChatAll', name: 'chatall', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "policy-chunk" */ '@/components/library/CallChatMe/callChatMeAll') },
          { path: '/withdrawAffiliate', name: 'withdrawAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/withdrawAffiliate') },
          { path: '/refundOrderBuyer', name: 'refundOrderBuyer', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/RefundOrderBuyer') },
          { path: '/BankAccountUser', name: 'BankAccountUser', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/BankAccountUser') }
        ]
      },
      { path: '/search-shop/:data', name: 'search-shop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "search-chunk" */ '@/components/Search/AllShopSearchUI.vue') },
      // Mobile Shop Homepage //////////////////////////////////////////////////////////////
      { path: '/sellerMobile', name: 'sellerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '../views/SellerShopMobileUI') },
      { path: '/sellerShopMobile', name: 'sellerMobileUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ShopProduct/ShopProductUI') },
      { path: '/sellersMobile', name: 'sellersMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/FirstTimeSellerMobile') },
      { path: '/manageproductMobile', name: 'manageproductMobileUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/CreateProductUI') },
      { path: '/ReportsellerMobile', name: 'ReportsellerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Quotation/ReportSeller') },
      { path: '/posellerMobile', name: 'posellerMobileUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/POSellerUI') },
      { path: '/SettingShopMibile', name: 'SettingShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SettingShop') },
      { path: '/posellerDetailMobile', name: 'posellerDetailMobileUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Quotation/POSellerDetailUIV2') },
      { path: '/CurierMobile', name: 'CurierMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Curier/Curier') },
      { path: '/designShopMobile', name: 'designShopMobileUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SettingShop') },
      { path: '/inventoryMobile', name: 'inventoryMobileUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/InventoryUI') },
      { path: '/DownloadFilesMobile', name: 'DownloadFilesMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/DownloadFileMobile') },
      { path: '/TackingorderMobile', name: 'TackingorderMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Tackingorder') },
      { path: '/createShopMobile', name: 'createShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/CreateShop/CreateShop') },
      { path: '/stepCreateShopMobile', name: 'stepCreateShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/CreateShop/stepCreateShop') },
      { path: '/SettingPartnerRequestMobile', name: 'SettingPartnerRequestMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/SettingPartnerRequest') },
      { path: '/SettingTierMobile', name: 'SettingTierMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/SettingTier') },
      { path: '/QuotationSettingSellerMobile', name: 'QuotationSettingSellerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/QuotationSettingSeller') },
      { path: '/dashboardMobile', name: 'dashboardMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/NewDashboard') },
      { path: '/revenueMobile', name: 'revenueMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/RevenueUI') },
      { path: '/returnMobile', name: 'returnMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/ReturnUI') },
      { path: '/MyCouponsAndPointsMobile', name: 'MyCouponsAndPointsMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/UserProfile/Coupons/Coupons') },
      { path: '/buyerAffiliateMobile', name: 'buyerAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/views/Affiliate/Buyer/buyerAffiliate') },
      { path: '/returnDetailMobile', name: 'returnDetailMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/return/returnDetail') },
      { path: '/EtaxCredentailMobile', name: 'EtaxCredentailMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/EtaxCredential') },
      { path: '/MerchantShopMobile', name: 'MerchantShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Merchant/Merchant') },
      { path: '/ManagaCuponMobile', mname: 'ManagaCuponMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Merchant/ManageCupon') },
      { path: '/sellerlistCreditOrderMobile', name: 'sellerlistCreditOrderMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/CreditTerm/ListCreditOrder') },
      { path: '/sellerlistCreditTermMobile', name: 'creditTermMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/CreditTerm/ListCreditTerm') },
      { path: '/sellerInvoicePDFMobile', name: 'sellerInvoicePDFMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/CreditTerm/InvoicePDF') },
      { path: '/manageCommentsMobile', name: 'manageCommentsMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageComments/SellerManageComment') },
      { path: '/specialPriceMobile', name: 'specialPriceMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Partner/SpecialPrice') },
      { path: '/partnerSellerMobile', name: 'partnerSellerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Partner/PartnerShop') },
      { path: '/dashboardAdminMobile', name: 'dashboardAdminMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/views/DashboardAdminUI') },
      { path: '/dashboardShopAdminMobile', name: 'dashboardShopAdminMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/DashboardForAdmin/DashboardShopAdminMobile') },
      { path: '/dashboardJVMobile', name: 'dashboardJVMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/DashboardJV/DashboardJVMobile') },
      { path: '/dashboardAdminForGPMobile', name: 'dashboardAdminForGPMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/views/MobileUI/DashboardForGPUI') },
      { path: '/listShopPositionMobile', name: 'listShopPositionMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SetPermission/Position/listTablePosition') },
      { path: '/listShopUserMobile', name: 'listShopUserMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SetPermission/User/listTableUser') },
      { path: '/AdminPanitMobile', name: 'AdminPanitMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/views/AdminPanit/HomeMobile') },
      { path: '/ListRequestNewTermMobile', name: 'ListRequestNewTermMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/CreditTerm/ListRequestNewTerm') },
      { path: '/TransactionMobile', name: 'TransactionMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/views/AdminPanit/Transaction') },
      { path: '/adminPanitManageMobile', name: 'adminPanitManageMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminPanit/AdminManage/AdminPanitManage') },
      { path: '/adminShopManageMobile', name: 'adminShopManageMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminPanit/AdminManage/AdminShopManage') },
      { path: '/AllShopTopTenMobile', name: 'AllShopTopTenMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Transaction/AllPageShopTopTen') },
      { path: '/companyMobile', name: 'companyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '../views/Company/HomeMobile.vue') },
      { path: '/detailCompanyMobile', name: 'detailCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/DetailCompany') },
      { path: '/ManageAddressCompanyMobile', name: 'ManageAddressCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/ManageTaxInvoice') },
      { path: '/ManagePositionMobile', name: 'ManagePositionMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/BuyerManage/ManagePositionCompany') },
      { path: '/ManageCompanyPostionUserMobile', name: 'ManageCompanyPostionUserMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/BuyerManage/ManagePositionUserCompany') },
      { path: '/PartnerMobile', name: 'PartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Partner/Partner_shop') },
      { path: '/ReportMobile', name: 'ReportMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/ReportUI') },
      { path: '/CompanyCouposAndPointsMobile', name: 'CompanyCouposAndPointsMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Coupons/CompanyCouposAndPoints') },
      { path: '/orderCompanyMobile', name: 'orderCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Order/Order') },
      { path: '/listApprovePositionMobile', name: 'listApprovePositionMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManageApprover/ListApprovePosition') },
      { path: '/detailPositionMobile', name: 'detailPositionMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManageApprover/DetailPosition') },
      { path: '/listApproveCompanyMobile', name: 'listApproveCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/ListApproveCompany') },
      { path: '/detailApproveCompanyMobile', name: 'detailApproveCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/DetailApproveCompany') },
      { path: '/ManageBuyerApproveMobile', name: 'ManageBuyerApproveMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManageBuyerApprove/ManageBuyer') },
      { path: '/listApproveOrderMobile', name: 'listApproveOrderMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManageApproveOrder/ListApproveOrder') },
      { path: '/DetailApproveOrderMobile', name: 'DetailApproveOrderMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManageApproveOrder/DetailApproveOrder') },
      { path: '/listApproveMobile', name: 'ListApproveMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManageApproveOrder/ListApprove') },
      { path: '/DetailListApproveMobile', name: 'DetailListApproveMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManageApproveOrder/DetailApprove') },
      { path: '/specialPriceBuyerRequestMobile', name: 'specialPriceBuyerRequestMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/SpecialPrice/SpecialPriceBuyerRequest') },
      { path: '/companyListCreditOrderMobile', name: 'listCreaditOrderMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/CreditTerm/ListCreditOrder') },
      { path: '/companyListCreditTermMobile', name: 'listCreaditTermMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/CreditTerm/ListCreditTerm') },
      { path: '/invoicePDFMobile', name: 'invoicePDFMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/CreditTerm/InvoicePDF') },
      { path: '/QUCompanyMobile', name: 'QUCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManagePO/ListManagePO') },
      { path: '/TackingCompanyMobile', name: 'TackingCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Tacking/TackingCompany') },
      { path: '/refundCompanyMobile', name: 'refundCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/RefundCompany/RefundProductCompany') },
      { path: '/reviewCompanyMobile', name: 'reviewCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Review/ReviewPageCompany') },
      { path: '/orderDetailCompanyMobile', name: 'orderDetailCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Order/OrderDetailV2') },
      { path: '/refundDetailCompanyMobile', name: 'refundDetailCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/RefundCompany/RefundProductDetailCompany') },
      { path: '/manageCompanyMobile', name: 'manageCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/CreateCompany') },
      { path: '/QUCompanyDetailMobile', name: 'QUCompanyDetailMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManagePO/DetailPOCompany') },
      { path: '/EditShopMobile', name: 'EditShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/EditShop') },
      { path: '/editShopAdminMobile', name: 'editShopAdminMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/EditShopAdmin') },
      { path: '/orderRecordCompanyMobile', name: 'orderRecordCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Order/OrderRecord') },
      { path: '/adminBusinessManageMobile', name: 'adminBusinessManageMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminBusinessManage') },
      { path: '/adminUserWebMobile', name: 'adminUserWebMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminUserWeb') },
      { path: '/sellerdashboardMobile', name: 'sellerdashboardMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/SellerDashboard/SellerDashboardMobile') },
      { path: '/DashboardSaleOrderMobile', name: 'DashboardSaleOrderMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/DashboardSaleOrder/DashboardSaleOrderMobile') },
      { path: '/ShippingReportMobile', name: 'ShippingReportMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ShippingReport/index') },
      { path: '/eTaxAdminMobile', name: 'eTaxAdminMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/eTaxAdmin') },
      { path: '/stockAdminMobile', name: 'stockAdminMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/StockAdmin') },
      { path: '/listSalesMobile', name: 'listSalesMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/listSales') },
      { path: '/listCustomerSalesMobile', name: 'listCustomerSalesMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/listCustomerOfSales') },
      { path: '/listQuotationSalesMobile', name: 'listQuotationSalesMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/QuotationSalesOrder') },
      { path: '/DetailQuotationSalesMobile', name: 'DetailQuotationSalesMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/DetailQuotationSalesOrder') },
      { path: '/listCustomerGroupSalesMobile', name: 'listCustomerGroupSalesMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/listCustomerGroupSales') },
      { path: '/listCustomerSaleOrderMobile', name: 'listCustomerSaleOrderMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/listCustomerSaleOrder') },
      { path: '/CouponSalesOrderMobile', name: 'CouponSalesOrderMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/CouponSalesOrder') },
      { path: '/ListOrderCustomerMobile', name: 'ListOrderCustomerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/listOrderCustomer') },
      { path: '/DetailOrderCustomerMobile', name: 'DetailOrderCustomerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/DetailOrderCustomer') },
      { path: '/ManageSalesApprovalMobile', name: 'ManageSalesApprovalMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/ManageSalesApproval') },
      { path: '/ManageSalesApprovalDetailMobile', name: 'ManageSalesApprovalDetailMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/ManageSalesApprovalDetail') },
      { path: '/orderSalesMobile', name: 'orderSalesMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/orderSales') },
      { path: '/DetailDueDateSaleOrderMobile', name: 'DetailDueDateSaleOrderMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/DetailDueDateSaleOrder') },
      { path: '/DetailDueDateCustomerMobile', name: 'DetailDueDateCustomerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/DetailDueDateCustomer') },
      { path: '/DetailOrderSalesMobile', name: 'DetailOrderSalesMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/DetailOrderSales') },
      { path: '/DetailOrderSalesNoJVMobile', name: 'DetailOrderSalesNoJVMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/DetailOrderBySale') },
      { path: '/listApproveSalesMobile', name: 'listApproveSalesMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/listApproveSales') },
      { path: '/sellerAffiliateMobile', name: 'sellerAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/Affiliate/Shop/sellerAffiliate') },
      { path: '/ERPPartnerMobile', name: 'ERPPartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ERPPartner/ERPPartner') },
      { path: '/ListUserJoinAffiliateMobile', name: 'ListUserJoinAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Affiliate/ListUserJoin') },
      { path: '/manageSaleApproveMobile', name: 'manageSaleApproveMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/ManageSaleApprove') },
      { path: '/DetailListApproveSalesMobile', name: 'DetailListApproveSalesMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/DetailListApproveSales') },
      { path: '/manageCouponMobile', name: 'manageCouponMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Voucher/manageCoupon') },
      { path: '/setPointMobile', name: 'setPointMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SetPoint/setPoint') },
      { path: '/allpointfromcostomerMobile', name: 'allpointfromcostomerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SetPoint/allPointFromCostomer') },
      { path: '/bundleDealMobile', name: 'bundleDealMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/BundleDeal/BundleDeal') },
      { path: '/createbundleDealMobile', name: 'createbundleDealMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/BundleDeal/CreateBundleDeal') },
      { path: '/coupondiscountMobile', name: 'coupondiscountMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Voucher/CouponDiscount') },
      { path: '/couponfreeproductMobile', name: 'couponfreeproductMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Voucher/CouponfreeProduct') },
      { path: '/couponfreeshippingMobile', name: 'couponfreeshippingMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Voucher/CouponfreeShipping') },
      { path: '/ListOrderBySalesMobile', name: 'ListOrderBySalesMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/ListOrderBySales') },
      { path: '/DetailDueDateSaleOrderBySaleMobile', name: 'DetailDueDateSaleOrderBySaleMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/DetailDueDateSaleOrderBySale') },
      { path: '/eWHTCompanyMobile', name: 'eWHTCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/eWHT') },
      { path: '/buyerAffiliateMobile', name: 'buyerAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/views/Affiliate/Buyer/buyerAffiliate') },
      { path: '/buyerDetailMobile', name: 'buyerDetailMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/buyerDetail') },
      { path: '/socailMediaDetailMobile', name: 'socailMediaDetailMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/socailMediaDetail') },
      { path: '/confirmDetailMobile', name: 'confirmDetailMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/confirmDetail') },
      { path: '/productAffiliateMobile', name: 'productAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/productAffiliate') },
      { path: '/editPayMobile', name: 'editPayMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/editPayMobile') },
      { path: '/editSocailMobile', name: 'editSocailMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/editSocail') },
      { path: '/consentAffiliateMobile', name: 'consentAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/consentAffiliateMobile') },
      { path: '/ProductCardAffiliateUIMobile', name: 'ProductCardAffiliateUIMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/Card/ProductCardAffiliateUI') },
      { path: '/ProductCardResponsiveAffiliateMobile', name: 'ProductCardResponsiveAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/Card/ProductCardResponsiveAffiliate') },
      { path: '/showShopSellerAffiliateMobile', name: 'showShopSellerAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/showShopSellerAffiliateMobile') },
      { path: '/dashboardShopAffiliateMobile', name: 'dashboardShopAffiliateMobile', component: () => import('@/components/Shop/DashboardShopAffiliate/DashboardShopAffiliate') },
      { path: '/showProductSellerJoinAffiliateMobile', name: 'showProductSellerJoinAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/UserProfile/Affiliate/showProductSellerJoinAffiliate') },
      { path: '/SearchOrderMobile', name: 'SearchOrderMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminPanit/Order/SearchOrder') },
      { path: '/CreateOrderJVERPMobile', name: 'CreateOrderJVERPMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminPanit/Order/CreateOrderJVERP') },
      { path: '/orderJVMobile', name: 'orderJVMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/AdminPanit/Order/OrderJV') },
      { path: '/adminBannerManageMobile', name: 'adminBannerManageMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminBannerManage') },
      { path: '/groupStoreMobile', name: 'groupStoreMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/groupStore') },
      { path: '/manageGroupStoreMobile/:data', name: 'manageGroupStoreMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageGropStore') },
      { path: '/userJoinAffiliateMobile', name: 'userJoinAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/userJoinAffiliate') },
      { path: '/reportCommissionAffiliateAdminMobile', name: 'reportCommissionAffiliateAdminMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/ReportCommission') },
      { path: '/showShopUserJoinAffiliateMobile', name: 'showShopUserJoinAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/showShopUserJoinAffiliate') },
      { path: '/userJoinAffiliateMobile', name: 'userJoinAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/userJoinAffiliateMobile') },
      { path: '/sellerJoinAffiliateMobile', name: 'sellerJoinAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/sellerJoinAffiliate') },
      { path: '/userJoinSellerAffiliateMobile', name: 'userJoinSellerAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/userJoinSellerAffiliate') },
      { path: '/listUserJoinSellerAffiliateMobile', name: 'listUserJoinSellerAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/listUserJoinSellerAffiliate') },
      { path: '/reportCommissionAffiliateAdminMobile', name: 'reportCommissionAffiliateAdminMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/ReportCommission') },
      { path: '/reportCommissionAffiliateDetailAdminMobile', name: 'reportCommissionAffiliateDetailAdminMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/ReportCommissionShopDetail') },
      { path: '/dashboardUserActiveMobile', name: 'dashboardUserActiveMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/Company/DashboardAdmin/DashboardUserActive') },
      { path: '/dashboardStatusShopMobile', name: 'dashboardStatusShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminPanit/AdminManage/DashboardStatusShop') },
      { path: '/reportCommissionAffiliateUserDetailAdminMobile', name: 'reportCommissionAffiliateUserDetailAdminMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/ReportCommissionUserDetail') },
      { path: '/listAttorneyMobile', name: 'listAttorneyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/listAttorney') },
      { path: '/listProductsAffiliateMobile', name: 'listProductsAffiliateMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/listProductsAffiliate') },
      { path: '/ManageCategoryMobile', name: 'ManageCategoryMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageCategory') },
      { path: '/AttorneyMobile', name: 'AttorneyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/Shop/Attorney') },
      { path: '/detailbusinesssidMobile', name: 'detailbusinesssidMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/views/Business/DetailBusinessIdUI') },
      { path: '/detailbusinesssidMobileMenu', name: 'detailbusinesssidMobileMenu', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/views/Business/HomeMobile') },
      { path: '/manageUserShopMobile', name: 'manageUserShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageUserShop') },
      { path: '/manageUserListMobile', name: 'manageUserListMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageUserList') },
      { path: '/manageListStoreMobile', name: 'manageListStoreMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageListStore') },
      { path: '/manageUserMobile', name: 'manageUserMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/Business/manageUser.vue') },
      { path: '/manageCompanyShopMobile', name: 'manageCompanyShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/Business/manageCompanyShop.vue') },
      { path: '/detailOrderCompanyMobile', name: 'detailOrderCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/DetailOrderCompany') },
      { path: '/detailOrderShopMobile', name: 'detailOrderShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/DetailOrderShop') },
      { path: '/listOrderCompanyMobile', name: 'listOrderCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/ListOrderCompany') },
      { path: '/listOrderShopMobile', name: 'listOrderShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/ListOrderShop') },
      { path: '/ManageERPMobile', name: 'ManageERPMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/AdminPanit/AdminManage/ManageERP') },
      { path: '/manageShippingMobile', name: 'manageShippingMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/ManageShipping/index') },
      { path: '/showDetailCompanyMobile', name: 'showDetailCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/detailCompany') },
      { path: '/detailUsersCompanyMobile', name: 'detailUsersCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/detailUsersCompany.vue') },
      { path: '/detailUsersShopsMobile', name: 'detailUsersShopsMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/detailUsersShops.vue') },
      { path: '/detailShopMobile', name: 'detailShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/detailShop.vue') },
      { path: '/managePositionComapny&BussinessMobile', name: 'managePositionComapny&BussinessMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/managePosition') },
      { path: '/detailPositionCompanyShopMobile', name: 'detailPositionCompanyShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/detailPosition') },
      { path: '/manageUserCompanyMobile', name: 'manageUserCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageUserCompany') },
      { path: '/DashboardProductShopMobile', name: 'DashboardProductShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/AdminPanit/AdminManage/DashboardProductShop') },
      { path: '/manageArticleMobile', name: 'manageArticleMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Article/manageArticle.vue') },
      { path: '/detailArticleMobile', name: 'detailArticleMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Article/detailArticle.vue') },
      { path: '/createArticleMobile', name: 'createArticleMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Article/createArticle.vue') },
      { path: '/editArticleMobile', name: 'editArticleMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Article/editArticle.vue') },
      { path: '/ManageUserListCompanyMobile', name: 'ManageUserListCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/AdminPanit/AdminManage/ManageUserListCompany') },
      { path: '/createBussinessBranchShopMobile', name: 'createBussinessBranchShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/CreateBussinessBranchShop.vue') },
      // { path: '/createBussinessBranchCompanyMobile', name: 'createBussinessBranchCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/CreateBussinessBranchCompany.vue') },
      { path: '/manageOrderShippingMobile', name: 'manageOrderShippingMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/ManageShipping/ManageOrderShipping') },
      { path: '/managePositionShopMobile', name: 'managePositionShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/managePositionShop.vue') },
      { path: '/managePositionCompanyMobile', name: 'managePositionCompanyMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/managePositionCompany.vue') },
      { path: '/createbusinesssidMobile', name: 'createbusinesssiduiMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/views/Business/CreateBusinessIdUI') },
      { path: '/ViewArticleMobile', name: 'ViewArticleMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Shop/ViewArticle.vue') },
      { path: '/dashboardTransportMobile', name: 'dashboardTransportMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/DashboardTransport') },
      { path: '/pendingOrderStageMobile', name: 'pendingOrderStageMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/PendingOrderStage') },
      { path: '/pendingOrderInfoMobile', name: 'pendingOrderInfoMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/PendingOrderInfo') },
      { path: '/manageFlashSaleMobile', name: 'manageFlashSaleMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/Shop/ManageFlashSale/ManageFlashSale') },
      { path: '/ManageQTExternalMobile', name: 'ManageQTExternalMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageQTExternal/ManageQTExternal') },
      { path: '/ManageQTJVMobile', name: 'ManageQTJVMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageQTExternal/ManageQTJV') },
      { path: '/DashboardWithdrawMoneyShopMobile', name: 'DashboardWithdrawMoneyShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/WithdrawMoney/DashboardWithdrawMoneyShop.vue') },
      { path: '/manageFlashSaleEditMobile', name: 'manageFlashSaleEditMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageFlashSale/ManageFlashSaleEdit') },
      { path: '/adminFlashsaleManageMobile', name: 'adminFlashsaleManageMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/adminFlashsaleManage.vue') },
      { path: '/EditImagesFlashSaleMobile', name: 'EditImagesFlashSaleMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/EditImagesFlashSale.vue') },
      { path: '/EditGroupShopLandingImageMobile', name: 'EditGroupShopLandingImageMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/EditGroupShopLandingImage.vue') },
      { path: '/AdminManagePopupMobile', name: 'AdminManagePopupMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminManagePopup.vue') },
      { path: '/PopupMobile', name: 'PopupMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/Home/Popup.vue') },
      { path: '/AdminManageProductPartnerMobile', name: 'AdminManageProductPartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/MarketplacePartner/AdminManageProductPartner.vue') },
      { path: '/AdminManageShopPartnerMobile', name: 'AdminManageShopPartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/MarketplacePartner/AdminManageShopPartner.vue') },
      { path: '/partnerShopInfoMobile', name: 'partnerShopInfoMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/partnerShopInfo.vue') },
      { path: '/serviceProductPartnerMobile', name: 'serviceProductPartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/serviceProductPartner.vue') },
      { path: '/shopConnectedMobile', name: 'shopConnectedMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/shopConnected.vue') },
      { path: '/partnerOrderListMobile', name: 'partnerOrderListMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/partnerOrderList.vue') },
      { path: '/dashboardPartnerMobile', name: 'dashboardPartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/dashboardPartner.vue') },
      { path: '/DetailProductShopPartnerMobile', name: 'DetailProductShopPartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/MarketplacePartner/DetailProductShopPartner.vue') },
      { path: '/DetailShopPartnerMobile', name: 'DetailShopPartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/MarketplacePartner/DetailShopPartner.vue') },
      { path: '/createServicePartnerMobile', name: 'createServicePartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business//createServicePartner.vue') },
      { path: '/updateServicePartnerMobile', name: 'updateServicePartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business//updateServicePartner') },
      { path: '/ShopJoinPartnerDetailsMobile', name: 'ShopJoinPartnerDetailsMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Shop/MarketplacePartner/ShopJoinPartnerDetails.vue') },
      { path: '/ShopPartnerDetailsMobile', name: 'ShopPartnerDetailsMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Shop/MarketplacePartner/ShopPartnerDetails.vue') },
      { path: '/orderListPartnerMobile', name: 'orderListPartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Shop/MarketplacePartner/orderListPartner') },
      { path: '/DetailOrderProductShopMobile', name: 'DetailOrderProductShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Shop/MarketplacePartner/DetailOrderProductShop') },
      { path: '/createShippingMobile', name: 'createShippingMobile', component: () => import('@/components/iShip/CreateShipping') },
      { path: '/adminManageCouponMobile', name: 'adminManageCouponMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/AdminPanit/ManagePromotion/adminManageCoupon') },
      { path: '/discountCouponMobile', name: 'discountCouponMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/AdminPanit/ManagePromotion/discountCoupon') },
      { path: '/freeShippingCouponMobile', name: 'freeShippingCouponMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/AdminPanit/ManagePromotion/freeShippingCoupon') },
      { path: '/editFreeShippingCouponMobile', name: 'editFreeShippingCouponMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/AdminPanit/ManagePromotion/editFreeShippingCoupon') },
      { path: '/editDiscountCouponMobile', name: 'editDiscountCouponMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/components/AdminPanit/ManagePromotion/editDiscountCoupon') },
      { path: '/ManageShopAccountMobile', name: 'ManageShopAccountMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageShopAccount.vue') },
      { path: '/ConfirmPartnerPackageMobile', name: 'ConfirmPartnerPackageMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/MarketplacePartner/ConfirmPartnerPackage.vue') },
      { path: '/PaymentPackageMobile', name: 'PaymentPackageMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/MarketplacePartner/PaymentPackage.vue') },
      { path: '/DetailOrderProductPartnerMobile', name: 'DetailOrderProductPartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Business/DetailOrderProductPartner') },
      { path: '/paymentPartnerMobile', name: 'paymentPartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Shop/MarketplacePartner/paymentPartner') },
      // { path: '/successPaymentPartnerMobile', name: 'successPaymentPartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/MarketplacePartner/successPaymentPartner.vue') },
      // { path: '/DetailAllShopDashboardMobile', name: 'DetailAllShopDashboardMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/DashboardPartnerTab/DetailAllShopDashboard.vue') },
      // { path: '/PackageSeminarMobile', name: 'PackageSeminarMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/PackageSeminar.vue') },
      { path: '/PackageUserManualAndSeminarMobile', name: 'PackageUserManualAndSeminarMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/PackageUserManualAndSeminar.vue') },
      { path: '/manageShippingSellerMobile', name: 'manageShippingSellerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ShippingReport/manageShippingSeller') },
      { path: '/AdminAddUserManualAndSeminarLinkMobile', name: 'AdminAddUserManualAndSeminarLinkMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/MarketplacePartner/AdminAddUserManualAndSeminarLink.vue') },
      { path: '/ManageCategoryProductMobile', name: 'ManageCategoryProductMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ShopProduct/ManageCategoryProduct') },
      { path: '/ListDeliveryMobile', name: 'ListDeliveryMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageDelivery/ListDelivery.vue') },
      { path: '/ManageDeliveryMobile', name: 'ManageDeliveryMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageDelivery/ManageDelivery.vue') },
      { path: '/hostMobile', name: 'hostMobile', component: () => import('@/views/liveStreaming/sellerIiveSteam') },
      { path: '/PreviewDetailPartnerMobile', name: 'PreviewDetailPartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Business/PreviewDetailPartner') },
      { path: '/AdminCouponUserMobile', name: 'AdminCouponUserMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminCouponUser') },
      { path: '/DetailDeliveryMobile', name: 'DetailDeliveryMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageDelivery/DetailDelivery.vue') },
      { path: '/ManageSalesPartnerApprovalMobile', name: 'ManageSalesPartnerApprovalMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/ManageSalesApproval') },
      { path: '/ManageSalesPartnerApprovalDetailMobile', name: 'ManageSalesPartnerApprovalDetailMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/ManageSalesApprovalDetail') },
      { path: '/manageSalePartnerApproveMobile', name: 'manageSalePartnerApproveMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/ManageSaleApprove') },
      { path: '/listApproveSalesPartnerMobile', name: 'listApproveSalesPartnerMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/listApproveSales') },
      { path: '/ListApprovePartnerShopMobile', name: 'ListApprovePartnerShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Partner/ListApprovePartnerShop') },
      { path: '/DetailApprovePartnerShopMobile', name: 'DetailApprovePartnerShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Partner/DetailApprovePartnerShop') },
      { path: '/DetailServiceCouponMobile', name: 'DetailServiceCouponMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "shopHomePage-chunk" */ '@/components/Home/HomeCoupon/DetailServiceCoupon.vue') },
      { path: '/ReceiveItemsMobile', name: 'ReceiveItemsMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/ReceiveItems/ReceiveItems.vue') },
      { path: '/ReceiveItemsDetailsMobile', name: 'ReceiveItemsDetailsMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/ReceiveItems/ReceiveItemsDetails.vue') },
      { path: '/ManageTagShopMobile', name: 'ManageTagShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageTag/ManageTagShop.vue') },
      { path: '/ManageTagProductShopMobile', name: 'ManageTagProductShopMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageTag/ManageTagProductShop.vue') },
      { path: '/BusinessManageMobile', name: 'BusinessManageMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminBusinessManageV2') },
      { path: '/ProfileTraceabilityMobile', name: 'ProfileTraceabilityMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ProfileTraceability/ProfileTraceability.vue') },
      { path: '/FormCreateShop', name: 'FormCreateShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/PopOver/FormCreateShop.vue') },
      { path: '/CreateProfileTraceabilityMobile', name: 'CreateProfileTraceabilityMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/Shop/ProfileTraceability/CreateProfileTraceability.vue') },
      { path: '/DetailProfileTraceabilityMobile', name: 'DetailProfileTraceabilityMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/Shop/ProfileTraceability/DetailProfileTraceability.vue') },
      { path: '/AdminListOrderCancelMobile', name: 'AdminListOrderCancelMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminListOrderCancel') },
      { path: '/UpdateShippingAddressMobile', name: 'UpdateShippingAddressMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/ManageShipping/UpdateShippingAddress') },
      { path: '/adminManageNotificationMobile', name: 'adminManageNotificationMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/ManagePromotion/adminManageNotification') },
      { path: '/DashboardAdminSearchMobile', name: 'DashboardAdminSearchMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/DashboardForAdmin/DashboardAdminSearch.vue') },
      // { path: '/dashboardOTOPMobile', name: 'dashboardOTOPMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/DashboardForAdmin/DashboardOTOP.vue') },
      { path: '/BankAccountUserMobile', name: 'BankAccountUserMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/UserProfile/BankAccountUser') },
      { path: '/dashboardOTOP', name: 'dashboardOTOP', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/NewDashboard/DashboardOTOP.vue') },
      { path: '/dashboardOTOPMobile', name: 'dashboardOTOPMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/NewDashboard/DashboardOTOP.vue') },
      { path: '/recommentProductManageMobile', name: 'recommentProductManageMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/RecommentProductManage') },
      { path: '/paymentManageMobile', name: 'paymentManageMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/PaymentManage') },
      // { path: '/dashboardOTOPMobile', name: 'dashboardOTOPMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/DashboardForAdmin/DashboardOTOP.vue') },
      // { path: '/dashboardOTOPMobile', name: 'dashboardOTOPMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/DashboardAdmin/DashboardOTOP') },
      // { path: '/FriendGetFriendsMobile', name: 'FriendGetFriendsMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/UserProfile/FriendGetFriends.vue') },
      { path: '/ShopPaymentManageMobile', name: 'ShopPaymentManageMobile', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/AdminPanit/AdminManage/ShopPaymentManage.vue') },
      {
        path: '/sellers',
        // component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '../views/SellerShopUI'),
        component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '../views/SellerShopUI(Backup)'),
        children: [
          { path: '/sellers', name: 'sellerUIs', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/FirstTimeSeller') },
          { path: '/seller', name: 'sellerUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ShopProduct/ShopProductUI') },
          { path: '/manageproduct', name: 'manageproductUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/CreateProductUI') },
          { path: '/shop', name: 'shopUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/CreateShop') },
          { path: '/poseller', name: 'posellerUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/POSellerUI') },
          { path: '/Reportseller', name: 'Reportseller', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Quotation/ReportSeller') },
          { path: '/ShippingReport', name: 'ShippingReport', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ShippingReport/index') },
          { path: '/manageShippingSeller', name: 'manageShippingSeller', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ShippingReport/manageShippingSeller') },
          { path: '/Curier', name: 'Curier', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Curier/Curier') },
          { path: '/POSellerDetail', name: 'POSellerDetailUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Quotation/POSellerDetailUIV2') },
          { path: '/orderdetailseller', name: 'orderdetailseller', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/OrderDetailSeller') },
          { path: '/inventory', name: 'inventoryUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/InventoryUI') },
          { path: '/createShipping', name: 'createShipping', component: () => import('@/components/iShip/CreateShipping') },
          { path: '/divideorder/:id', name: 'designShopUI', component: () => import('@/components/iShip/DivideOrder') },
          { path: '/QuotationAll', name: 'QuotationAll', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Quotation/QuotationSeller/ListQuotationSeller') },
          { path: '/QuotationDetail', name: 'QuotationDetail', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Quotation/QuotationSeller/DetailQuotationSeller') },
          { path: '/designShop', name: 'designShopUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SettingShop') },
          { path: '/EditShop', name: 'EditShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/EditShop') },
          { path: '/dashboard', name: 'dashboardUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/NewDashboard') },
          { path: '/sellerdashboard', name: 'sellerdashboard', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/SellerDashboard/SellerDashboard') },
          { path: '/DashboardSaleOrder', name: 'DashboardSaleOrder', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/DashboardSaleOrder/DashboardSaleOrder') },
          { path: '/return', name: 'returnUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/ReturnUI') },
          { path: '/returnDetail', name: 'returndetailUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/return/returnDetail') },
          { path: '/DownloadFiles', name: 'DownloadFiles', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/DownloadFile') },
          { path: '/Tackingorder', name: 'Tackingorder', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Tackingorder') },
          { path: '/coupondiscount', name: 'coupondiscount', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Voucher/CouponDiscount') },
          { path: '/couponfreeproduct', name: 'couponfreeproduct', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Voucher/CouponfreeProduct') },
          { path: '/couponfreeshipping', name: 'couponfreeshipping', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Voucher/CouponfreeShipping') },
          { path: '/manageCoupon', name: 'manageCoupon', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Voucher/manageCoupon') },
          { path: '/partnerSeller', name: 'partnerSeller', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Partner/PartnerShop') },
          { path: '/revenue', name: 'RevenueUI', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/RevenueUI') },
          { path: '/createShop', name: 'createShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/CreateShop') },
          { path: '/stepCreateShop', name: 'stepCreateShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/CreateShop/stepCreateShop') },
          { path: '/manageComments', name: 'manageComments', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageComments/SellerManageComment') },
          { path: '/listShopPosition', name: 'listShopPosition', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SetPermission/Position/listTablePosition') },
          { path: '/setPoint', name: 'setPoint', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SetPoint/setPoint') },
          { path: '/allpointfromcostomer', name: 'allpointfromcostomer', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SetPoint/allPointFromCostomer') },
          { path: '/bundleDeal', name: 'bundleDeal', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/BundleDeal/BundleDeal') },
          { path: '/createbundleDeal', name: 'createbundleDeal', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/BundleDeal/CreateBundleDeal') },
          { path: '/listShopUser', name: 'listShopUser', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SetPermission/User/listTableUser') },
          { path: '/ReviewSeller', name: 'ReviewSeller', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/ReviewSellerUI') },
          { path: '/SettingPartnerRequest', name: 'SettingPartnerRequest', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/SettingPartnerRequest') },
          { path: '/SettingTier', name: 'SettingTier', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/SettingTier') },
          { path: '/QuotationSettingSeller', name: 'QuotationSettingSeller', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/QuotationSettingSeller') },
          { path: '/specialPrice', name: 'specialPrice', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Partner/SpecialPrice') },
          { path: '/sellerlistCreditOrder', name: 'sellerlistCreditOrder', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/CreditTerm/ListCreditOrder') },
          { path: '/sellerlistCreditTerm', name: 'creditTerm', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/CreditTerm/ListCreditTerm') },
          { path: '/sellerInvoicePDF', name: 'sellerInvoicePDF', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/CreditTerm/InvoicePDF') },
          { path: '/ListRequestNewTerm', name: 'ListRequestNewTerm', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/CreditTerm/ListRequestNewTerm') },
          { path: '/EtaxCredentail', name: 'EtaxCredentail', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/EtaxCredential') },
          { path: '/MerchantShop', name: 'MerchantShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Merchant/Merchant') },
          { path: '/ManagaCupon', mname: 'ManagaCupon', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Merchant/ManageCupon') },
          { path: '/ReportSeller', name: 'ReportSeller', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Quotation/ReportSeller') },
          { path: '/QuotationSetting', name: 'QuotationSetting', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Quotation/QuotationSeller/SetQT') },
          { path: '/listSales', name: 'listSales', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/listSales') },
          { path: '/listCustomerSaleOrder', name: 'listCustomerSaleOrder', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/listCustomerSaleOrder') },
          { path: '/CouponSalesOrder', name: 'CouponSalesOrder', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/CouponSalesOrder') },
          { path: '/ListOrderCustomer', name: 'ListOrderCustomer', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/listOrderCustomer') },
          { path: '/DetailOrderCustomer', name: 'DetailOrderCustomer', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/DetailOrderCustomer') },
          { path: '/listCustomerSales', name: 'listCustomerSales', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/listCustomerOfSales') },
          { path: '/listCustomerGroupSales', name: 'listCustomerGroupSales', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/listCustomerGroupSales') },
          { path: '/ManageSalesApproval', name: 'ManageSalesApproval', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/ManageSalesApproval') },
          { path: '/ManageSalesApprovalDetail', name: 'ManageSalesApprovalDetail', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/ManageSalesApprovalDetail') },

          { path: '/ManageSalesPartnerApproval', name: 'ManageSalesPartnerApproval', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/ManageSalesApproval') },
          { path: '/ManageSalesPartnerApprovalDetail', name: 'ManageSalesPartnerApprovalDetail', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/ManageSalesApprovalDetail') },
          { path: '/manageSalePartnerApprove', name: 'manageSalePartnerApprove', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/ManageSaleApprove') },
          { path: '/listApproveSalesPartner', name: 'listApproveSalesPartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/listApproveSales') },
          { path: '/ListApprovePartnerShop', name: 'ListApprovePartnerShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Partner/ListApprovePartnerShop') },
          { path: '/DetailApprovePartnerShop', name: 'DetailApprovePartnerShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Partner/DetailApprovePartnerShop') },

          { path: '/listQuotationSales', name: 'listQuotationSales', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/QuotationSalesOrder') },
          { path: '/DetailQuotationSales', name: 'DetailQuotationSales', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/DetailQuotationSalesOrder') },
          { path: '/orderSales', name: 'orderSales', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/orderSales') },
          { path: '/DetailDueDateSaleOrder', name: 'DetailDueDateSaleOrder', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/DetailDueDateSaleOrder') },
          { path: '/DetailDueDateCustomer', name: 'DetailDueDateCustomer', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/DetailDueDateCustomer') },
          { path: '/DetailOrderSales', name: 'DetailOrderSales', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/DetailOrderSales') },
          { path: '/manageSaleApprove', name: 'manageSaleApprove', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/ManageSaleApprove') },
          { path: '/listApproveSales', name: 'listApproveSales', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/listApproveSales') },
          { path: '/sellerAffiliate', name: 'sellerAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/Affiliate/Shop/sellerAffiliate') },
          { path: '/ERPPartner', name: 'ERPPartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ERPPartner/ERPPartner') },
          { path: '/ListUserJoinAffiliate', name: 'ListUserJoinAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Affiliate/ListUserJoin') },
          { path: '/DetailListApproveSales', name: 'DetailListApproveSales', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/SalesOrder/DetailListApproveSales') },
          { path: '/Attorney', name: 'Attorney', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Attorney') },
          { path: '/listAttorney', name: 'listAttorney', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/listAttorney') },
          { path: '/DashboardShopAffiliate', name: 'DashboardShopAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/DashboardShopAffiliate/DashboardShopAffiliate') },
          { path: '/manageArticle', name: 'manageArticle', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Article/manageArticle.vue') },
          { path: '/detailArticle', name: 'detailArticle', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Article/detailArticle.vue') },
          { path: '/createArticle', name: 'createArticle', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Article/createArticle.vue') },
          { path: '/editArticle', name: 'editArticle', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/Article/editArticle.vue') },
          { path: '/manageFlashSale', name: 'manageFlashSale', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageFlashSale/ManageFlashSale') },
          { path: '/ManageQTExternal', name: 'ManageQTExternal', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageQTExternal/ManageQTExternal') },
          { path: '/ManageQTJV', name: 'ManageQTJV', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageQTExternal/ManageQTJV') },
          { path: '/manageFlashSaleCreate', name: 'manageFlashSaleCreate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageFlashSale/ManageFlashSaleCreate') },
          { path: '/manageFlashSaleEdit', name: 'manageFlashSaleEdit', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageFlashSale/ManageFlashSaleEdit') },
          { path: '/testFlashSale', name: 'testFlashSale', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageFlashSale/testFlashSale') },
          { path: '/testFlashSale', name: 'testFlashSale', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageFlashSale/testFlashSale') },
          { path: '/DashboardWithdrawMoneyShop', name: 'DashboardWithdrawMoneyShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/WithdrawMoney/DashboardWithdrawMoneyShop.vue') },
          { path: '/refundPage', name: 'refundPage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Quotation/refundPage') },
          { path: '/ShopJoinPartnerDetails', name: 'ShopJoinPartnerDetails', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/MarketplacePartner/ShopJoinPartnerDetails.vue') },
          { path: '/ShopPartnerDetails', name: 'ShopPartnerDetails', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/MarketplacePartner/ShopPartnerDetails.vue') },
          { path: '/orderListPartner', name: 'orderListPartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/MarketplacePartner/orderListPartner') },
          { path: '/DetailOrderProductShop', name: 'DetailOrderProductShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/MarketplacePartner/DetailOrderProductShop') },
          { path: '/ManageShopAccount', name: 'ManageShopAccount', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageShopAccount.vue') },
          { path: '/ConfirmPartnerPackage', name: 'ConfirmPartnerPackage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/MarketplacePartner/ConfirmPartnerPackage.vue') },
          { path: '/PaymentPackage', name: 'PaymentPackage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/MarketplacePartner/PaymentPackage.vue') },
          { path: '/paymentPartner', name: 'paymentPartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/MarketplacePartner/paymentPartner') },
          { path: '/paymentList', name: 'paymentList', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/MarketplacePartner/paymentList.vue') },
          { path: '/host', name: 'host', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/views/liveStreaming/sellerIiveSteam') },
          // { path: '/host', component: () => import('@/views/liveStreaming/host') },
          { path: '/host', component: () => import('@/views/liveStreaming/sellerIiveSteam') },
          { path: '/ManageCategoryProduct', name: 'ManageCategoryProduct', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ShopProduct/ManageCategoryProduct') },
          // { path: '/host', name: 'host', component: () => import('@/views/liveStreaming/sellerIiveSteam') },
          { path: '/ManageCategoryProduct', name: 'ManageCategoryProduct', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ShopProduct/ManageCategoryProduct') },
          // { path: '/successPaymentPartner', name: 'successPaymentPartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/MarketplacePartner/successPaymentPartner.vue') }
          { path: '/ListDelivery', name: 'ListDelivery', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageDelivery/ListDelivery.vue') },
          { path: '/ManageDelivery', name: 'ManageDelivery', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageDelivery/ManageDelivery.vue') },
          { path: '/DetailDelivery', name: 'DetailDelivery', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageDelivery/DetailDelivery.vue') },
          { path: '/ManageTagShop', name: 'ManageTagShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageTag/ManageTagShop.vue') },
          { path: '/ManageTagProductShop', name: 'ManageTagProductShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ManageTag/ManageTagProductShop.vue') },
          { path: '/ProfileTraceability', name: 'ProfileTraceability', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ProfileTraceability/ProfileTraceability.vue') },
          { path: '/CreateProfileTraceability', name: 'CreateProfileTraceability', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/ProfileTraceability/CreateProfileTraceability.vue') },
          { path: '/DetailProfileTraceability', name: 'DetailProfileTraceability', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/Shop/ProfileTraceability/DetailProfileTraceability.vue') }
        ]
      },
      { path: '/QuotationSale', name: 'QuotationSetting', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Quotation/QuotationSeller/QTCheckout') },
      { path: '/QuotationVendor', name: 'QuotationVendor', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Quotation/QuotationSeller/QTVendor') },
      { path: '/createbusinesssid', name: 'createbusinesssidui', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/views/Business/CreateBusinessIdUI') },
      // { path: '/detailbusinesssid', name: 'detailbusinesssidui', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/views/Business/DetailBusinessIdUIBackUp') },
      { path: '/shoppingcart', name: 'shoppingcartui', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "checkout-chunk" */ '@/views/Cart/ShoppingCartUI') },
      { path: '/checkout', name: 'checkoutui', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "checkout-chunk" */ '@/views/Cart/CheckoutUI') },
      { path: '/yourorder', name: 'yourorder', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "checkout-chunk" */ '@/components/Cart/YourOrder') },
      { path: '/successPaymentPartner', name: 'successPaymentPartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Shop/MarketplacePartner/successPaymentPartner.vue') },
      { path: '/checkoutSaleOrder', name: 'checkoutSaleOrder', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "checkout-chunk" */ '@/views/Cart/CheckoutSaleOrder') },
      { path: '/checkoutExt', name: 'checkoutExt', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "checkout-chunk" */ '@/views/Cart/CheckoutExt') },
      { path: '/pobuyer', name: 'pobuyer', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "userprofile-chunk" */ '@/views/POBuyerUI') },
      {
        path: '/detailbusinesssid',
        component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/views/Business/Home'),
        children: [
          { path: '/detailbusinesssid', name: 'detailbusinesssidui', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/views/Business/DetailBusinessIdUI') },
          // { path: '/createbusinesssid', name: 'createbusinesssidui', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/views/Business/CreateBusinessIdUI') },
          { path: '/managePositionComapny&Bussiness', name: 'managePositionComapny&Bussiness', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/managePosition') },
          { path: '/manageUser', name: 'manageUser', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/manageUser.vue') },
          { path: '/manageCompanyShop', name: 'manageCompanyShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/manageCompanyShop.vue') },
          { path: '/listOrderCompany', name: 'listOrderCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/ListOrderCompany') },
          { path: '/listOrderShop', name: 'listOrderShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/ListOrderShop.vue') },
          { path: '/detailOrderShop', name: 'detailOrderShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/DetailOrderShop.vue') },
          { path: '/detailOrderCompany', name: 'detailOrderCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/DetailOrderCompany') },
          { path: '/showDetailCompany', name: 'showDetailCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/detailCompany') },
          { path: '/detailUsersCompany', name: 'detailUsersCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/detailUsersCompany.vue') },
          { path: '/detailUsersShops', name: 'detailUsersShops', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/detailUsersShops.vue') },
          { path: '/detailShop', name: 'detailShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/detailShop.vue') },
          { path: '/detailPositionCompanyShop', name: 'detailPositionCompanyShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/detailPosition.vue') },
          { path: '/createBussinessBranchShop', name: 'createBussinessBranchShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/CreateBussinessBranchShop.vue') },
          { path: '/managePositionCompany', name: 'managePositionCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/managePositionCompany.vue') },
          // { path: '/createBussinessBranchCompany', name: 'createBussinessBranchCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/CreateBussinessBranchCompany.vue') },
          { path: '/managePositionShop', name: 'managePositionShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/managePositionShop.vue') },
          { path: '/partnerShopInfo', name: 'partnerShopInfo', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/partnerShopInfo.vue') },
          { path: '/serviceProductPartner', name: 'serviceProductPartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/serviceProductPartner.vue') },
          { path: '/shopConnected', name: 'shopConnected', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/shopConnected.vue') },
          { path: '/partnerOrderList', name: 'partnerOrderList', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/partnerOrderList.vue') },
          { path: '/DetailOrderProductPartner', name: 'DetailOrderProductPartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "sellers-chunk" */ '@/components/Business/DetailOrderProductPartner') },
          { path: '/dashboardPartner', name: 'dashboardPartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/dashboardPartner.vue') },
          { path: '/CreatePartner', name: 'CreatePartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/CreatePartner.vue') },
          { path: '/EditPartner', name: 'EditPartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/EditPartner.vue') },
          { path: '/createServicePartner', name: 'createServicePartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/createServicePartner.vue') },
          { path: '/updateServicePartner', name: 'updateServicePartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/updateServicePartner.vue') },
          { path: '/PartnerBilling', name: 'PartnerBilling', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/PartnerBilling.vue') },
          // { path: '/DetailAllShopDashboard', name: 'DetailAllShopDashboard', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/DashboardPartnerTab/DetailAllShopDashboard.vue') },
          // { path: '/PackageSeminar', name: 'PackageSeminar', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/PackageSeminar.vue') },
          { path: '/PackageUserManualAndSeminar', name: 'PackageUserManualAndSeminar', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/PackageUserManualAndSeminar.vue') },
          { path: '/PreviewDetailPartner', name: 'PreviewDetailPartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "businesss-chunk" */ '@/components/Business/PreviewDetailPartner.vue') }
        ]
      },
      // {
      //   path: '/approved',
      //   component: () => import('@/views/Approved'),
      //   children: [
      //     { path: '/approved', name: 'approved', component: () => import('@/components/Approved/Approved') },
      //     { path: '/approveDetail', name: 'approveDetail', component: () => import('@/components/Approved/ApproveDetail') }
      //   ]
      // },
      {
        path: '/Company',
        component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/views/Company/Home'),
        children: [
          { path: '/Company', name: 'Company', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/Company') },
          { path: '/manageCompany', name: 'manageCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/CreateCompany') },
          { path: '/detailCompany', name: 'detailCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/DetailCompany') },
          { path: '/RenewOrder', name: 'RenewOrder', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Order/RenewOrder') },
          { path: '/ChangeAndRenewOrder', name: 'ChangeAndRenewOrder', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Order/ChangeAndRenewOrder') },
          { path: '/Change', name: 'Change', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Order/Change') },
          { path: '/QUCompany', name: 'QUCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManagePO/ListManagePO') },
          { path: '/QUCompanyDetail', name: 'QUCompanyDetail', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManagePO/DetailPOCompany') },
          { path: '/Partner', name: 'Partner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/Partner/Partner_shop') },
          { path: '/TackingCompany', name: 'TackingCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Tacking/TackingCompany') },
          { path: '/ManagePosition', name: 'ManagePosition', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/BuyerManage/ManagePositionCompany') },
          { path: '/ManageAddressCompany', name: 'ManageAddressCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/ManageTaxInvoice') },
          { path: '/ManageCompanyPostionUser', name: 'ManageCompanyPostionUser', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/BuyerManage/ManagePositionUserCompany') },
          { path: '/orderCompany', name: 'orderCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Order/Order') },
          { path: '/orderRecordCompany', name: 'orderRecordCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Order/OrderRecord') },
          { path: '/orderDetailCompany', name: 'orderDetailCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Order/OrderDetailV2') },
          { path: '/reviewCompany', name: 'reviewCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Review/ReviewPageCompany') },
          { path: '/refundCompany', name: 'refundCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/RefundCompany/RefundProductCompany') },
          { path: '/refundDetailCompany', name: 'refundDetailCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/RefundCompany/RefundProductDetailCompany') },
          { path: '/Report', name: 'Report', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/views/ReportUI') },
          { path: '/CompanyCouposAndPoints', name: 'CompanyCouposAndPoints', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Coupons/CompanyCouposAndPoints') },
          { path: '/specialPriceBuyerRequest', name: 'specialPriceBuyerRequest', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/SpecialPrice/SpecialPriceBuyerRequest') },
          { path: '/companyListCreditOrder', name: 'listCreaditOrder', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/CreditTerm/ListCreditOrder') },
          { path: '/companyListCreditTerm', name: 'listCreaditTerm', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/CreditTerm/ListCreditTerm') },
          { path: '/invoicePDF', name: 'invoicePDF', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/CreditTerm/InvoicePDF') },
          { path: '/ManageBuyerApprove', name: 'ManageBuyerApprove', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManageBuyerApprove/ManageBuyer') },
          { path: '/listApprovePosition', name: 'listApprovePosition', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManageApprover/ListApprovalType') },
          { path: '/ManageApprovalPosition', name: 'ManageApprovalPosition', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManageApprover/ManageApprovalPosition') },
          { path: '/listApproveSequence', name: 'listApproveSequence', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManageApprover/ListApprovalSequence') },
          { path: '/listApproveCompany', name: 'listApproveCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/ListApproveCompany') },
          { path: '/detailApproveCompany', name: 'detailApproveCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/DetailApproveCompany') },
          { path: '/detailPosition', name: 'detailPosition', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManageApprover/DetailPosition') },
          { path: '/listApproveOrder', name: 'listApproveOrder', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManageApproveOrder/ListApproveOrder') },
          { path: '/DetailApproveOrder', name: 'DetailApproveOrder', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManageApproveOrder/DetailApproveOrder') },
          { path: '/listApprove', name: 'ListApprove', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManageApproveOrder/ListApprove') },
          { path: '/DetailListApprove', name: 'DetailListApprove', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/ManageApproveOrder/DetailApprove') },
          { path: '/eWHTCompany', name: 'eWHTCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/eWHT') },
          { path: '/ReceiveItems', name: 'ReceiveItems', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/ReceiveItems/ReceiveItems.vue') },
          { path: '/ReceiveItemsDetails', name: 'ReceiveItemsDetails', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/ReceiveItems/ReceiveItemsDetails.vue') }
        ]
      },
      {
        path: '/dashboardShopAdmin',
        component: () => import('@/views/AdminPanit/Home'),
        children: [
          { path: '/dashboardShopAdmin', name: 'dashboardShopAdmin', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/DashboardForAdmin/DashboardShopAdmin') },
          { path: '/dashboardAdmin', name: 'dashboardAdmin', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/views/DashboardAdminUI') },
          { path: '/dashboardJV', name: 'dashboardJV', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/DashboardJV/DashboardJV') },
          { path: '/Transaction', name: 'Transaction', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/views/AdminPanit/Transaction') },
          { path: '/AllShopTopTen', name: 'AllShopTopTen', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Transaction/AllPageShopTopTen') },
          { path: '/dashboardAdminForGP', name: 'dashboardAdminForGP', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/views/DashboardForGPUI') },
          { path: '/adminPanitManage', name: 'adminPanitManage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminPanitManage') },
          { path: '/manageGracz', name: 'manageGracz', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageGracz') },
          { path: '/adminShopManage', name: 'adminShopManage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminShopManage') },
          { path: '/editShopAdmin', name: 'editShopAdmin', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/EditShopAdmin') },
          { path: '/adminBusinessManage', name: 'adminBusinessManage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminBusinessManage') },
          { path: '/adminBannerManage', name: 'adminBannerManage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminBannerManage') },
          { path: '/BigBannerEdit', name: 'BigBannerEdit', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/BannerManage/BigBannerEdit') },
          { path: '/BannerAEdit', name: 'BannerAEdit', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/BannerManage/BannerAEdit') },
          { path: '/BannerBEdit', name: 'BannerBEdit', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/BannerManage/BannerBEdit') },
          { path: '/BannerCEdit', name: 'BannerCEdit', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/BannerManage/BannerCEdit') },
          { path: '/adminUserWeb', name: 'adminUserWeb', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminUserWeb') },
          { path: '/eTaxAdmin', name: 'eTaxAdmin', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/eTaxAdmin') },
          { path: '/groupStore', name: 'groupStore', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/groupStore') },
          { path: '/manageGroupStore/:data', name: 'manageGroupStore', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageGropStore') },
          { path: '/stockAdmin', name: 'stockAdmin', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/StockAdmin') },
          { path: '/SearchOrder', name: 'SearchOrder', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Order/SearchOrder') },
          { path: '/CreateOrderJVERP', name: 'CreateOrderJVERP', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Order/CreateOrderJVERP') },
          { path: '/orderJV', name: 'orderJV', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Order/OrderJV') },
          { path: '/userJoinAffiliate', name: 'userJoinAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/userJoinAffiliate') },
          { path: '/reportCommissionAffiliateAdmin', name: 'reportCommissionAffiliateAdmin', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/ReportCommission') },
          { path: '/showShopUserJoinAffiliate', name: 'showShopUserJoinAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/showShopUserJoinAffiliate') },
          { path: '/sellerJoinAffiliate', name: 'sellerJoinAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/sellerJoinAffiliate') },
          { path: '/userJoinSellerAffiliate', name: 'userJoinSellerAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/userJoinSellerAffiliate') },
          { path: '/listUserJoinSellerAffiliate', name: 'listUserJoinSellerAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/listUserJoinSellerAffiliate') },
          { path: '/reportCommissionAffiliateAdmin', name: 'reportCommissionAffiliateAdmin', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/ReportCommission') },
          { path: '/reportCommissionAffiliateDetailAdmin', name: 'reportCommissionAffiliateDetailAdmin', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/ReportCommissionShopDetail') },
          { path: '/dashboardUserActive', name: 'dashboardUserActive', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/DashboardAdmin/DashboardUserActive') },
          { path: '/dashboardStatusShop', name: 'dashboardStatusShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminPanit/AdminManage/DashboardStatusShop') },
          { path: '/listProductsAffiliate', name: 'listProductsAffiliate', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/listProductsAffiliate') },
          { path: '/reportCommissionAffiliateUserDetailAdmin', name: 'reportCommissionAffiliateUserDetailAdmin', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/Affiliate/ReportCommissionUserDetail') },
          { path: '/ManageCategory', name: 'ManageCategory', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageCategory') },
          { path: '/manageUserShop', name: 'manageUserShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageUserShop') },
          { path: '/manageUserList', name: 'manageUserList', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageUserList') },
          { path: '/manageListStore', name: 'manageListStore', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageListStore') },
          { path: '/ManageERP', name: 'ManageERP', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageERP') },
          { path: '/manageShipping', name: 'manageShipping', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/ManageShipping/index') },
          { path: '/manageOrderShipping', name: 'manageOrderShipping', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/ManageShipping/ManageOrderShipping') },
          { path: '/manageUserCompany', name: 'manageUserCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageUserCompany') },
          { path: '/DashboardProductShop', name: 'DashboardProductShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/DashboardProductShop') },
          { path: '/ManageUserListCompany', name: 'ManageUserListCompany', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageUserListCompany') },
          { path: '/dashboardTransport', name: 'dashboardTransport', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/DashboardTransport') },
          { path: '/pendingOrderStage', name: 'pendingOrderStage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/PendingOrderStage') },
          { path: '/pendingOrderInfo', name: 'pendingOrderInfo', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/PendingOrderInfo') },
          { path: '/adminFlashsaleManage', name: 'adminFlashsaleManage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/adminFlashsaleManage.vue') },
          { path: '/EditImagesFlashSale', name: 'EditImagesFlashSale', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/EditImagesFlashSale.vue') },
          { path: '/EditGroupShopLandingImage', name: 'EditGroupShopLandingImage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/EditGroupShopLandingImage.vue') },
          { path: '/AdminManagePopup', name: 'AdminManagePopup', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminManagePopup.vue') },
          { path: '/AdminManageProductPartner', name: 'AdminManageProductPartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/MarketplacePartner/AdminManageProductPartner.vue') },
          { path: '/AdminManageShopPartner', name: 'AdminManageShopPartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/MarketplacePartner/AdminManageShopPartner.vue') },
          { path: '/DetailProductShopPartner', name: 'DetailProductShopPartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/MarketplacePartner/DetailProductShopPartner.vue') },
          { path: '/DetailShopPartner', name: 'DetailShopPartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/MarketplacePartner/DetailShopPartner.vue') },
          { path: '/adminManageCoupon', name: 'adminManageCoupon', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/ManagePromotion/adminManageCoupon') },
          { path: '/discountCoupon', name: 'discountCoupon', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/ManagePromotion/discountCoupon') },
          { path: '/freeShippingCoupon', name: 'freeShippingCoupon', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/ManagePromotion/freeShippingCoupon') },
          { path: '/editFreeShippingCoupon', name: 'editFreeShippingCoupon', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/ManagePromotion/editFreeShippingCoupon') },
          { path: '/editDiscountCoupon', name: 'editDiscountCoupon', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/ManagePromotion/editDiscountCoupon') },
          { path: '/adminGroupShopBGManage', name: 'adminGroupShopBGManage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/adminGroupShopBGManage') },
          { path: '/customBanner', name: 'customBanner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/customBanner') },
          { path: '/AdminReach', name: 'AdminReach', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminReach') },
          { path: '/managePositionBussiness', name: 'managePositionBussiness', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/managePositionBussiness') },
          { path: '/AdminAddUserManualAndSeminarLink', name: 'AdminAddUserManualAndSeminarLink', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/MarketplacePartner/AdminAddUserManualAndSeminarLink.vue') },
          { path: '/AdminCouponUser', name: 'AdminCouponUser', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminCouponUser.vue') },
          { path: '/adminManageNotification', name: 'adminManageNotification', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/ManagePromotion/adminManageNotification') },
          { path: '/BusinessManage', name: 'BusinessManage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminBusinessManageV2') },
          { path: '/ManageRegisterShop', name: 'ManageRegisterShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageRegisterShop') },
          { path: '/ManageRegisterShopDetail', name: 'ManageRegisterShopDetail', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageRegisterShopDetail') },
          { path: '/AdminListOrderCancel', name: 'AdminListOrderCancel', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/AdminListOrderCancel') },
          { path: '/DashboardAdminSearch', name: 'DashboardAdminSearch', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/DashboardForAdmin/DashboardAdminSearch.vue') },
          { path: '/UpdateShippingAddress', name: 'UpdateShippingAddress', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/ManageShipping/UpdateShippingAddress') },
          { path: '/recommentProductManage', name: 'recommentProductManage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/RecommentProductManage') },
          { path: '/paymentManage', name: 'paymentManage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/PaymentManage') },
          { path: '/ShopPaymentManage', name: 'ShopPaymentManage', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ShopPaymentManage.vue') },
          // { path: '/dashboardOTOP', name: 'dashboardOTOP', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminManage/DashboardForAdmin/DashboardOTOP.vue') }
          // { path: '/dashboardOTOP', name: 'dashboardOTOP', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "company-chunk" */ '@/components/AdminManage/Company/DashboardAdmin/DashboardOTOP') }
          { path: '/ManageRegisterInfoPartner', name: 'ManageRegisterInfoPartner', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageRegisterInfoPartner.vue') },
          { path: '/ManageRegisterInfoPartnerDetail', name: 'ManageRegisterInfoPartnerDetail', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageRegisterInfoPartnerDetail.vue') },
          { path: '/ManageRegisterInfoShop', name: 'ManageRegisterInfoShop', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageRegisterInfoShop.vue') },
          { path: '/ManageRegisterInfoShopDetail', name: 'ManageRegisterInfoShopDetail', component: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "adminPanit-chunk" */ '@/components/AdminPanit/AdminManage/ManageRegisterInfoShopDetail.vue') }

        ]
      }
    ]
  },
  {
    path: '/regisMorhpromt',
    component: () => import('@/views/RegisterMorhpromt/register'),
    children: [
      { path: '/regisMorhpromt', name: 'regisMorhpromt', component: () => import('@/components/RegisterMorhpromt/register') },
      { path: '/otp', name: 'otp', component: () => import('@/components/RegisterMorhpromt/OTP') },
      { path: '/findTax', name: 'findTax', component: () => import('@/components/RegisterMorhpromt/FindTaxID') },
      { path: '/Registerbuyer', name: 'Registerbuyer', component: () => import('@/components/PopOver/Registerbuyer') },
      { path: '/ListRegister', name: 'ListRegister', component: () => import('@/components/RegisterMorhpromt/ListRegister') }
    ]
  },
  { path: '*', component: () => import('@/views/404') }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

router.beforeEach((to, from, next) => {
  // Add meta tags dynamically based on the route
  // keep previous route
  // if (!store.state.ModuleGlobal.initialRoute) {
  //   store.commit('setInitialRoute', to)
  // }
  // store.commit('setPreviousRoute', from)
  const metaTags = to.metaInfo || []
  metaTags.forEach((tag) => {
    const tagElement = document.createElement('meta')
    Object.keys(tag).forEach((key) => {
      tagElement.setAttribute(key, tag[key])
    })
    document.head.appendChild(tagElement)
  })

  // Remove old meta tags
  const head = document.head
  Array.from(head.children).forEach((child) => {
    if (child.tagName === 'META' && !metaTags.find((tag) => tag.name === child.name)) {
      head.removeChild(child)
    }
  })

  next()
})

Vue.use(VueGtm, {
  id: 'GTM-KG3476F', // Your GTM single container ID, array of container ids ['GTM-xxxxxx', 'GTM-yyyyyy'] or array of objects [{id: 'GTM-xxxxxx', queryParams: { gtm_auth: 'abc123', gtm_preview: 'env-4', gtm_cookies_win: 'x'}}, {id: 'GTM-yyyyyy', queryParams: {gtm_auth: 'abc234', gtm_preview: 'env-5', gtm_cookies_win: 'x'}}], // Your GTM single container ID or array of container ids ['GTM-xxxxxx', 'GTM-yyyyyy']
  queryParams: {
    // Add URL query string when loading gtm.js with GTM ID (required when using custom environments)
    gtm_auth: 'ZwqHb0tQWN5GKqcWv7pXsg',
    gtm_preview: 'env-1',
    gtm_cookies_win: 'x'
  },
  defer: false, // Script can be set to `defer` to speed up page load at the cost of less accurate results (in case visitor leaves before script is loaded, which is unlikely but possible). Defaults to false, so the script is loaded `async` by default
  compatibility: false, // Will add `async` and `defer` to the script tag to not block requests for old browsers that do not support `async`
  nonce: '2726c7f26c', // Will add `nonce` to the script tag
  enabled: true, // defaults to true. Plugin can be disabled by setting this to false for Ex: enabled: !!GDPR_Cookie (optional)
  debug: true, // Whether or not display console logs debugs (optional)
  loadScript: true, // Whether or not to load the GTM Script (Helpful if you are including GTM manually, but need the dataLayer functionality in your components) (optional)
  vueRouter: router, // Pass the router instance to automatically sync with router (optional)
  ignoredViews: ['homepage'], // Don't trigger events for specified router names (case insensitive) (optional)
  trackOnNextTick: false // Whether or not call trackView in Vue.nextTick
})

export default router
