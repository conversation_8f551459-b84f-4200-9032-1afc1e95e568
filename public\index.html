<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta http-equiv="Content-Security-Policy" content="script-src 'self' https://static.cloudflareinsights.com;"> -->
    <!-- <meta name="viewport" content="width=device-width,initial-scale=1.0"> -->
	<meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name=keywords content="NexGenCommerce, inet, nexgencommerce.one.th, nex, Nex, gen, Gen, Commerce, INET, ตลาดออนไลน์, NexGenCommerce, nexgencommerce, ngc, NGC, NexGen Commerce, NexGen, ngc.one.th, สินค้าไทย, สินค้าคุณภาพ, ร้านค้าน่าเชื่อถือ, ค่าส่งถูก, สินค้าและบริการ, B2B2C, ออก E-tax invoice ได้, GP ต่ำ, แหล่งเงินทุนสำหรับผู้ประกอบการ, Digital Transformation, Affiliate">
    <meta name=author content="NexGenCommerce">
    <meta name="robots" content="index,follow">
    <meta name=description content="Nex Gen Commerce ซื้อขายออนไลน์ สินค้าและบริการ B2B2C เพิ่มโอกาสทางการขาย ต่อยอดธุรกิจ ส่งเสริมธุรกิจไทย ออก e-Tax invoice 'GP ต่ำ' ค่าส่งถูก Affiliate">
    <meta name="apple-itunes-app" content="app-id=com.inet.nexgenshop">
    <link rel="shortcut icon" href=/ngc_Logo.ico>
	<link rel='shortcut icon' href="<%= VUE_APP_DOMAIN %>ngc_Logo.ico">
	<link rel='icon' type="image/x-icon" href="<%= VUE_APP_DOMAIN %>ngc_Logo.ico">
	<link rel='apple-touch-icon' href="<%= VUE_APP_DOMAIN %>ngc_Logo.ico">
	<!-- <link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet"> -->

	<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="0">
    <!-- <title>INET-Marketplace</title> -->
		<title>
			<%= htmlWebpackPlugin.options.title %>
	    </title>
		<!-- Google tag (gtag.js) -->
		<!-- <script async src="https://www.googletagmanager.com/gtag/js?id=G-RZJD7V4KHK"></script>
		<script>
			window.dataLayer = window.dataLayer || [];
			function gtag(){dataLayer.push(arguments);}
			gtag('js', new Date());

			gtag('config', 'G-RZJD7V4KHK');
		</script> -->
        <script defer charset="utf-8" src="https://static.line-scdn.net/liff/edge/versions/2.22.3/sdk.js"></script>
    <!-- <?php
        $URL = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
        if ( strpos( $URL, "DetailProduct" ) >= 0 ){

            $G_DB_HOST = "**********";
            $G_DB_NAME = "epro_b2b";
            $G_DB_USER = "admin";
            $G_DB_PORT = 3306;
            $G_DB_PASS = "password";
            $G_DATABASE_CONN = new mysqli($G_DB_HOST, $G_DB_USER, $G_DB_PASS, $G_DB_NAME, $G_DB_PORT);
            if ($G_DATABASE_CONN->connect_errno){
            }else{

                if ( strpos( $URL, "DetailProduct" ) !== false ){
                    $exploded_URL = explode("-", $URL);

                    $iidd = $exploded_URL[ count($exploded_URL) - 1 ];
                    if (is_numeric($iidd)){

                        $stmt = $G_DATABASE_CONN->prepare("SELECT msp.name, msp.short_description, iv.media_path FROM ms_product msp LEFT JOIN ms_product_image_vdo iv ON msp.id = iv.product_id WHERE msp.id = ? AND iv.media_type = 'image'");

                        if (!$stmt){
                            return;
                        }

                        $stmt->bind_param("i", $iidd);
                        $stmt->execute();
                        $stmt->bind_result($name, $short_description, $media_path);

                        if ( $stmt->fetch() ){
                            $urlPath = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
                            echo '<meta property="og:url" content="' . $urlPath . '">';
                            echo '<meta property="og:type" content="website">';
                            echo '<meta property="og:title" content="' . $name . '">';
                            echo '<meta property="og:site_name" content="' . $name . '">';
                            if (isset($media_path)){
                                $media_path = preg_replace("/https:\/\/s3gw.inet.co.th:8082\/e-pro-b2b-uat/", "https://devinet-eprocurement.one.th/static",$media_path);
                                $media_path = preg_replace("/https:\/\/s3gw.inet.co.th:8082\/e-pro-b2b-dev/", "https://devinet-eprocurement.one.th/static",$media_path);

                                echo '<meta property="og:image" content="' . $media_path . '">';
                                echo '<meta property="og:image:url" content="' . $media_path . '">';
                                echo '<meta property="og:image:secure_url" content="' . $media_path . '">';
                                echo '<meta property="og:image:width" content="1200">';
                                echo '<meta property="og:image:height" content="630">';
                            }
                            echo '<meta property="og:description" content="' . $short_description . '">';
                        }
                    }
                } else if ( strpos( $URL, "share" ) !== false ) {
                    $exploded_URL = explode("-", $URL);

                    $iidd = $exploded_URL[ count($exploded_URL) - 1 ];
                    if (is_numeric($iidd)){

                        $stmt = $G_DATABASE_CONN->prepare("SELECT msp.name, msp.short_description, iv.media_path FROM ms_product msp LEFT JOIN ms_product_image_vdo iv ON msp.id = iv.product_id WHERE msp.id = ? AND iv.media_type = 'image'");

                        if (!$stmt){
                            return;
                        }

                        $stmt->bind_param("i", $iidd);
                        $stmt->execute();
                        $stmt->bind_result($name, $short_description, $media_path);

                        if ( $stmt->fetch() ){
                            $urlPath = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
                            echo '<meta property="og:url" content="' . $urlPath . '">';
                            echo '<meta property="og:type" content="website">';
                            echo '<meta property="og:title" content="' . $name . '">';
                            echo '<meta property="og:site_name" content="' . $name . '">';
                            if (isset($media_path)){
                                $media_path = preg_replace("/https:\/\/s3gw.inet.co.th:8082\/e-pro-b2b-uat/", "https://devinet-eprocurement.one.th/static",$media_path);
                                $media_path = preg_replace("/https:\/\/s3gw.inet.co.th:8082\/e-pro-b2b-dev/", "https://devinet-eprocurement.one.th/static",$media_path);

                                echo '<meta property="og:image" content="' . $media_path . '">';
                                echo '<meta property="og:image:url" content="' . $media_path . '">';
                                echo '<meta property="og:image:secure_url" content="' . $media_path . '">';
                                echo '<meta property="og:image:width" content="1200">';
                                echo '<meta property="og:image:height" content="630">';
                            }
                            echo '<meta property="og:description" content="' . $short_description . '">';
                        }
                    }
                } else if (strpos( $URL, "shoppage" ) !== false ) {
                    $exploded_URL = explode("-", $URL);
                    $iidd = $exploded_URL[ count($exploded_URL) - 1 ];
                    if (is_numeric($iidd)){

                        $stmt = $G_DATABASE_CONN->prepare("SELECT ss.name_th, ss.shop_description, ss.path_logo FROM seller_shop ss WHERE ss.id = ?");

                        if (!$stmt){
                            return;
                        }

                        $stmt->bind_param("i", $iidd);
                        $stmt->execute();
                        $stmt->bind_result($name_th, $shop_description, $media_path);

                        if ( $stmt->fetch() ){
                            $urlPath = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
                            echo '<meta property="og:url" content="' . $urlPath . '">';
                            echo '<meta property="og:type" content="website">';
                            echo '<meta property="og:title" content="เลือกซื้อสินค้าจากร้าน ' . $name_th . '">';
                            echo '<meta property="og:site_name" content="' . $name_th . '">';
                            if (isset($media_path)){
                                $media_path = preg_replace("/https:\/\/s3gw.inet.co.th:8082\/e-pro-b2b-uat/", "https://devinet-eprocurement.one.th/static",$media_path);
                                $media_path = preg_replace("/https:\/\/s3gw.inet.co.th:8082\/e-pro-b2b-dev/", "https://devinet-eprocurement.one.th/static",$media_path);
                               
                                echo '<meta property="og:image" content="' . $media_path . '">';
                                echo '<meta property="og:image:url" content="' . $media_path . '">';
                                echo '<meta property="og:image:secure_url" content="' . $media_path . '">';
                                echo '<meta property="og:image:width" content="1200">';
                                echo '<meta property="og:image:height" content="630">';
                            }
                            if (!is_null($shop_description)){
                                echo '<meta property="og:description" content="' . $shop_description . '">';
                            } else {
                                echo '<meta property="og:description" content="เลือกซื้อสินค้าคุณภาพจากร้าน ' . $name_th . ' ได้เลยที่ Nex Gen Commerce ซื้อขายออนไลน์ สินค้าและบริการ B2B2C เพิ่มโอกาสทางการขาย ต่อยอดธุรกิจ ส่งเสริมธุรกิจไทย ออก e-Tax invoice GP ต่ำ ค่าส่งถูก Affiliate">';
                            }
                            
                        }
                    }
                } else if (strpos( $URL, "GroupShoppage" ) !== false ) {
                    $exploded_URL = explode("-", $URL);
                    $iidd = $exploded_URL[ count($exploded_URL) - 1 ];
                    if (is_numeric($iidd)){

                        $stmt = $G_DATABASE_CONN->prepare("SELECT ss.group_name, ss.group_shop_description, ss.group_shop_media_path FROM group_seller_shop ss WHERE ss.id = ?");

                        if (!$stmt){
                            return;
                        }

                        $stmt->bind_param("i", $iidd);
                        $stmt->execute();
                        $stmt->bind_result($group_name, $group_shop_description, $group_shop_media_path);

                        if ( $stmt->fetch() ) {
                            $urlPath = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
                            echo '<meta property="og:url" content="' . $urlPath . '">';
                            echo '<meta property="og:type" content="website">';
                            echo '<meta property="og:title" content="เลือกสินค้าจากกลุ่มร้าน ' . $group_name . '">';
                            echo '<meta property="og:site_name" content="' . $group_name . '">';
                            if (isset($group_shop_media_path)){
                                $group_shop_media_path = preg_replace("/https:\/\/s3gw.inet.co.th:8082\/e-pro-b2b-uat/", "https://devinet-eprocurement.one.th/static",$group_shop_media_path);
                                $group_shop_media_path = preg_replace("/https:\/\/s3gw.inet.co.th:8082\/e-pro-b2b-dev/", "https://devinet-eprocurement.one.th/static",$group_shop_media_path);
                               
                                echo '<meta property="og:image" content="' . $group_shop_media_path . '">';
                                echo '<meta property="og:image:url" content="' . $group_shop_media_path . '">';
                                echo '<meta property="og:image:secure_url" content="' . $group_shop_media_path . '">';
                                echo '<meta property="og:image:width" content="1200">';
                                echo '<meta property="og:image:height" content="630">';
                            }
                            if (!is_null($shop_description)){
                                echo '<meta property="og:description" content="' . $group_shop_description . '">';
                            } else {
                                echo '<meta property="og:description" content="เลือกซื้อสินค้าคุณภาพจากกลุ่ม ' . $group_name . ' ได้เลยที่ Nex Gen Commerce ซื้อขายออนไลน์ สินค้าและบริการ B2B2C เพิ่มโอกาสทางการขาย ต่อยอดธุรกิจ ส่งเสริมธุรกิจไทย ออก e-Tax invoice GP ต่ำ ค่าส่งถูก Affiliate">';
                            }
                            
                        }
                    }
                } else {
                  $urlPath = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
                  echo '<meta property="og:url" content="' . $urlPath . '">';
                  echo '<meta property="og:type" content="website">';
                  echo '<meta property="og:image" content="<%= VUE_APP_DOMAIN %>img/ngc_logo.52dd8912.png">';
                  echo '<meta property="og:image:url" content="<%= VUE_APP_DOMAIN %>img/ngc_logo.52dd8912.png">';
                  echo '<meta property="og:image:secure_url" content="<%= VUE_APP_DOMAIN %>img/ngc_logo.52dd8912.png">';
                  echo '<meta property="og:title" content="NexGenCommerce | ซื้อขายออนไลน์ ส่งเสริมธุรกิจไทย">';
                  echo '<meta property="og:description" content="Nex Gen Commerce ซื้อขายออนไลน์ สินค้าและบริการ B2B2C เพิ่มโอกาสทางการขาย ต่อยอดธุรกิจ ส่งเสริมธุรกิจไทย ออก e-Tax invoice GP ต่ำ ค่าส่งถูก Affiliate">';
                }
            }
        }
    ?> -->
    <script async type="application/ld+json">
        {
          "@type": "Organization",
          "@context": "http://schema.org/",
          "name": "NexGenCommerce",
          "description": "Nex Gen Commerce ซื้อขายออนไลน์ สินค้าและบริการ B2B2C เพิ่มโอกาสทางการขาย ต่อยอดธุรกิจ ส่งเสริมธุรกิจไทย ออก e-Tax invoice 'GP ต่ำ' ค่าส่งถูก Affiliate",
          "url": "<%= VUE_APP_DOMAIN %>",
          "logo": "<%= VUE_APP_DOMAIN %>img/ngc_logo.52dd8912.png"
        }
    </script>
  </head>
  <body>
		<!-- <link href="https://cdnjs.cloudflare.com/ajax/libs/MaterialDesign-Webfont/5.9.55/css/materialdesignicons.min.css" rel="stylesheet"> -->
		<!-- Google tag (gtag.js) -->
		<!-- <script async src="https://www.googletagmanager.com/gtag/js?id=G-7NQ65YQ2S2"></script>
		<script>
		  window.dataLayer = window.dataLayer || [];
		  function gtag(){dataLayer.push(arguments);}
		  gtag('js', new Date());

		  gtag('config', 'G-7NQ65YQ2S2');
		</script> -->
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@5.x/css/materialdesignicons.min.css" rel="stylesheet" rel="preload" as="style">
  </body>
</html>
<script>
</script>

