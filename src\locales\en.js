export default {
  default: 'en',
  SystemBar: {
    DownloadApp: 'Download the App',
    ContactUs: 'Contact Us',
    Help: 'Customer Service',
    OpenShop: 'Open a Shop',
    RegisterPartner: 'Register as a Partner'
  },
  DialogDownloadApp: {
    ForBuyer: '(For Buyer)',
    ForSeller: '(For Seller)',
    textDownload: 'Download the app now by'
  },
  AppBar: {
    Search: 'Search for products and shops',
    Register: 'Sign Up',
    or: 'or',
    Login: 'Login',
    SearchNoJV: 'Search Products',
    SearchJV: 'Search Products in Shop',
    ViewCart: 'View your carts',
    NoCart: 'There are no products in your cart.',
    sku: 'SKU',
    Quantity: 'quantity'
  },
  Notification: {
    notification: 'Notification',
    noNotification: 'No Notification',
    textNoNoti: 'Notifications will appear here when you receive messages.',
    AllNotifications: 'All Notifications',
    ReadAll: 'Read All',
    New: 'New',
    Before: 'Before'
  },
  TextHome: {
    textRecommend: 'Recommend!',
    textProduct: 'Products for you',
    ViewAllProducts: 'View All Products',
    textSearch: 'Please enter a search term.'
  },
  Footer: {
    Account: 'Account',
    Chat: 'Chat',
    Shop: 'Shop',
    Payment: 'Payment',
    Shipping: 'Shipping',
    CustomerService: 'Customer Service',
    Detail: {
      Account: 'Keep your user account safe with One ID.',
      Chat: 'You can chat with sellers anytime via our One Chat system.',
      Shop: 'Select the items you desire from our website.',
      Payment: 'Make a payment using your credit card via Thaidotcom payment.',
      Shipping: 'We ensure fast and safe delivery of your products directly to you.',
      CustomerService: 'Our efficient after-sales service ensures your confidence in our products.'
    },
    HeadOffice: 'Head Office',
    HelpCenter: 'Help Center',
    Categories: 'Categories',
    PaymentMethods: 'Payment Methods',
    FollowUs: 'Follow Us',
    aboutAs: 'About As',
    Certification: 'Certification',
    DetailAddress: {
      Company: 'Nextgen Shop Co., Ltd.',
      INET: 'A subsidiary of Internet Thailand Public Company Limited',
      Address: '1768 Thai Summit Tower, 16th Floor, New Petchburi Road, Bangkapi, Huai Khwang, Bangkok 10310',
      Phone: 'Phone: 02-257-7155 ext. 5',
      Email: 'Email: <EMAIL>'
    },
    DetailAbout: {
      Policy: 'Policy',
      TermsOfUse: 'Terms of Use'
    },
    DetailPayment: {
      Payment: 'Thai Dotcom Payment'
    }
  },
  Menu: {
    Language: 'Language',
    Profile: 'My Account',
    Orders: 'My Orders',
    Select: 'Select Role',
    DetailSelect: {
      Buyer: 'Buyer',
      Purchaser: 'Purchaser',
      Seller: 'Seller',
      Sales: 'Sales',
      Admin: 'Admin',
      Approver: 'Approver',
      AdminSystem: 'Admin NGC'
    },
    BusinessEntity: 'Business Info',
    Company: 'Company Info',
    Shops: 'Shops',
    ShopRegister: 'Create Shop',
    Logout: 'Log Out'
  },
  Headers: {
    Home: 'Home Page',
    RecommendedProducts: 'Recommended Products',
    RecommendedForYou: 'Recommended for You',
    Detail: {
      Categories: 'Product Categories',
      Category: 'Categories',
      AllCategories: 'All Categories',
      Price: 'Price'
    },
    ProductText: {
      Popular: 'Popular Products',
      FlashSale: 'Flash Sale',
      NoCategory: 'No Product Category',
      NoProduct: 'No Products Available',
      NewProducts: 'New Products',
      BestSeller: 'Best Sellers Products',
      SameShop: 'Products from the Same Shop',
      All: 'All Products',
      SameCategory: 'Products from the Same Category',
      RelatedCategory: 'Related Categories Products',
      Product: 'Product',
      Products: 'Products'
    },
    HomeText: {
      RecommendedProducts: 'Recommended Products',
      NewProducts: 'New Products',
      BestSeller: 'Best Sellers Products',
      NoRecommendedProducts: 'No recommended products available yet',
      NoBestSellers: 'No best-selling products available yet',
      NoNewArrivals: 'No new arrival products available yet',
      ViewAll: 'View All',
      Shops: 'Shops',
      AllShops: 'All Shops'
    }
  },
  userProfileNewUI: {
    UserProfile: 'My Information',
    MyAccount: 'My Account',
    Edit: 'Edit',
    Cancel: 'Cancel',
    Save: 'Save',
    Confirm: 'Confirm',
    PersonalInformation: 'Personal Information',
    FirstName: 'Name',
    LastName: 'Surname',
    Phone: 'Phone Number',
    Email: 'Email',
    EnterFirstName: 'Enter Name',
    EnterLastName: 'Enter Surname',
    EnterPhone: 'Enter Phone',
    EnterEmail: 'Enter Email',
    BankName: 'Bank Name',
    BankAccountNumber: 'Bank Account Number',
    BankAccountName: 'Bank Account Name',
    EditUserProfile: 'Edit My Information',
    CheckedInformation: 'Please confirm that all information is correct before proceeding.',
    OnlyLetterEnter: 'Only letters can be enter',
    PleaseEnterName: 'Please enter your name',
    NotEnterMoreName: 'Do not enter more than 30 characters in your name',
    PleaseEnterSurName: 'Please enter your surname',
    NotEnterMoreSurName: 'Do not enter more than 30 characters in your surname',
    NoUsername: 'No Username',
    NoFullname: 'No Name - Surname',
    PleaseAddImage: 'Please add only image files (JPEG, JPG, PNG)',
    UnableProceed: 'Unable to proceed',
    EditSuccess: 'Information updated successfully.',
    SuccessEditYourData: 'You have successfully updated your information.'
  },
  ShoppingCartUI: {
    ShoppingCart: 'Shopping Cart',
    Home: 'Home',
    NoProduct: "Don't have product in your cart",
    GoShop: 'Back to store',
    GoBuy: "Let's go shopping",
    RemoveSelectItems: 'Remove Selected Item',
    Store: 'Store',
    StoreNosell: 'Store not ready',
    DetailProduct: 'Product Details',
    UnitPrice: 'Unit Price',
    Quantity: 'Quantity',
    TotalPrice: 'Total Price',
    CannotPurchaseTogether: "Services and general products can't be purchased together",
    Delete: 'Delete',
    NotReady: 'not ready',
    SoldOut: 'sold out',
    ProductCannotReduce: "Product quantity can't be reduced",
    item: 'item',
    items: 'items',
    DueMinimumOrder: 'Due to the minimum order quantity of',
    ItemLeftTH: '',
    ItemLeftEN: 'left',
    PreOrder: 'Pre-order',
    StoreDiscountCoupon: 'Apply Shop Coupon',
    StoreDiscountPoint: 'Use Seller Points',
    GeneralProduct: 'General Products',
    B2BProduct: 'B2B',
    ProductCannotIncrease: "Product quantity can't be increased",
    DueMaximumOrder: 'Due to the maximum order quantity of',
    Free: 'free',
    StoreDiscount: 'Use store discount, reduce',
    ShippingDiscount: 'Use shipping discount, reduce',
    PointsDiscount: 'Use points discount',
    SystemDiscountCoupon: 'Nexgen Discount Coupon',
    UseSystemDiscount: 'Nexgen Discount',
    Discount: 'discount',
    OrderSummary: 'Order Summary',
    PriceExcludingVAT: 'VAT-exclusive items',
    VAT: 'VAT',
    PriceIncludingVAT: 'VAT-inclusive items',
    Baht: 'baht',
    Month: 'month',
    TotalPriceAll: 'Total Amount',
    StoreDiscountSummary: 'Seller Discount',
    ShippingDiscountSummary: 'Shipping Discount',
    PointsDiscountSummary: 'Point Discount',
    SystemDiscountSummary: 'Nexgen Discount',
    PriceAfterDiscount: 'Subtotal',
    PartnerDiscount: 'Partner Discount',
    CustomerDiscount: 'Customer Discount',
    PriceAfterPartnerDiscount: 'Price (after partner discount)',
    ProductPriceIncludingDiscount: 'Product Price (including discount)',
    ThisPriceStandardPriceVary: 'This price is a standard rate - the actual price may vary depending on',
    DependingProductDestination: 'the item/destination. The delivery staff will contact you',
    PointsEarnedThisTime: 'Nexgen Poins Earned',
    Points: 'Points',
    OrderProducts: 'Checkout',
    DeleteProduct: 'Delete product',
    HaveDeletedProduct: 'You have deleted product',
    Cancel: 'Cancel',
    Confirm: 'Confirm',
    ShippingFee: 'Shipping Fee',
    ConfirmTransaction: 'Confirm this transaction?',
    TotalPointsEarnedThisTime: 'Nexgen Poins Earned All',
    PointsEarned: 'Points received',
    TotalPointsEarned: 'Total Points',
    LargeProduct: '*Large product',
    ProductChanged: '*Product has changed, please remove from cart',
    OutStock: 'Out of stock',
    InsufficientStock: 'Insufficient stock',
    AddProductCart: 'Please add product to cart',
    UnableProceed: 'Unable to proceed',
    CartAddedMaximum: 'The cart can only be added to a maximum of 150 items',
    NotConditionsDiscountProductPriceLessMinimumPrice: 'Does not meet the conditions for using the discount because the product price is less than the minimum price specified by the coupon',
    CouponOutStock: "This coupon can't be used because this coupon is out of stock",
    CouponReserved: "This coupon can't be used because this coupon has been reserved",
    ServerError: 'Server Error',
    ChangedRemoveCartAddItem: 'This has changed. Please remove from cart and re-add the item',
    NoStockRemoveCart: 'No stock. Please remove from cart',
    InsufficientStockRemoveCart: 'Insufficient stock. Please remove from car',
    ProductID: 'Product ID',
    WantDeleteItem: 'Do you want to delete the item?',
    QuantityLessSpecified: 'Quantity less than specified',
    ChangedRemoveCart: 'This has changed. Please remove from cart',
    CartRemoveSuccess: 'Cart has been removed successfully',
    OK: 'Confirm',
    LessSpecifiedQuantity: 'Less than specified quantity',
    MoreSpecifiedQuantity: 'More than specified quantity',
    CannotIncreaseQuantityItemsSpecifiedQuantity: "Can't increase the quantity of items because you have already added the specified quantity",
    LargeShippingOnlyItem: 'Since this item uses the Large shipping method, you can only order 1 item at a time',
    TotalPriceLess: 'The total price should not be less than 1 baht',
    PleaseSelect: 'Please select',
    NewAgain: 'New again',
    UsePoints: 'Use points',
    PointsScore: 'Points',
    NoShopIDNGS: 'There is no ShopID in NGS',
    NoRecommendedItems: 'There are no recommended items'
  },
  Coupon: {
    Coupon: 'Coupon',
    Coupons: 'Coupons',
    GeneralDiscount: 'General shop Discount Coupon',
    NoGeneralDiscount: 'General shop coupons are not available yet.',
    StoreSpecific: 'Shop-only Coupon',
    NoStoreSpecific: 'No Shop-Only Coupons Available yet.',
    ForStore: 'For shop',
    Only: 'Only',
    Conditions: 'Coupon Conditions',
    PromotionType: 'Promotion Type',
    ShippingDiscount: 'Shipping Discount',
    ProductDiscount: 'Product Price Discount',
    UsageRight: 'Coupon Usage Rights',
    Usable: 'Can be used',
    Rights: 'Rights',
    MaxDiscount: 'Maximum Discount',
    MaxReduce: 'Maximum Discount ',
    Currency: ' Baht',
    Unlimited: 'Unlimited',
    ParticipatingStores: 'Participating Stores',
    AllStores: 'All Participating Stores',
    Shop: 'Shop',
    ValidEverywhere: 'Valid at All Stores',
    MinSpend: 'Min. Spend',
    MaxSpend: 'Up to',
    ValidUntil: 'Valid Until',
    ValidFrom: 'Valid From',
    ValidOn: 'Valid On',
    NoExpiry: 'No Expiration Date',
    ViewConditions: 'View Conditions',
    Claim: 'Claim',
    Claimed: 'Claimed',
    ClaimAll: 'Claim All',
    ShowMore: 'Show More',
    ShowLess: 'Show Less'
  },
  CheckOut: {
    ShippingAddress: 'Shipping Address',
    ChangeAddress: 'Change Address',
    Notation: 'Notation',
    IncompleteAddress: 'Address incomplete. Please choose others address.',
    AddAddress: 'Add Address',
    OrderList: 'Order List',
    QuotationSample: 'Quotation Sample',
    ShopName: 'Store',
    UseCouponShop: 'Use Coupon Shop',
    GeneralProduct: 'General Products',
    ServiceProduct: 'Service Products',
    ApplyStoreVoucher: 'Apply Store Voucher',
    ApplyStoreVoucherMobile: 'Apply Store Voucher',
    FreeShipping: 'Free Shipping',
    FreeShippingMobile: 'Free Shipping',
    UseStorePoint: 'Use Seller Points',
    Free: 'Free',
    Discount: 'Discount',
    AddressTaxInvoiceIssuance: 'Address for tax invoice issuance',
    ReceiveTaxInvoice: 'Receive a tax invoice',
    NotReceiveTaxInvoice: 'Not receive a tax invoice',
    BranchCode: 'Branch code',
    TaxNumber: 'Tax Number',
    Address: 'Address',
    ShippingType: 'Shipping Type',
    PickUpAStore: 'Pick up at the store',
    ProductDelivery: 'Product delivery',
    ChooseShipping: 'Choose Shipping',
    ChooseOtherShipping: 'Choose Other Shipping',
    SentByStore: 'Sent By Store',
    HouseNumber: 'House Number',
    SubDistrict: 'SubDistrict',
    District: 'District',
    Province: 'Province',
    ReceivingDate: 'Receiving Date',
    ReceivingTime: 'Receiving Time',
    UnitPrice: 'Unit price',
    Quantity: 'Quantity',
    TotalPrice: 'Total Price',
    PricePerMonth: 'THB/month',
    SKU: 'SKU',
    SystemDiscountCoupon: 'Nexgen Discount Coupon',
    UseSystemDiscountCoupon: 'Nexgen Discount',
    DiscountCoupon: 'Discount',
    DiscountShipping: 'Free Shipping',
    Relationships: 'Relationships',
    RelationshipsCoupon: 'Partnership Coupon',
    ChangeRelationshipsCoupon: 'Change Relationships Coupon',
    PriceExcludingVAT: 'VAT-exclusive items',
    VAT: 'VAT',
    Baht: 'Baht',
    PriceIncludingVAT: 'Total (Incl. VAT)',
    SummaryOrdered: 'Order Summary',
    CouponDiscountSeller: 'Seller Discount',
    CouponDiscountNexgen: 'Nexgen Discount',
    NexgenPointDiscount: 'Point Discount',
    SubTotal: 'Subtotal',
    ShippingFee: 'Shipping Fee',
    ShippingCostDesicription1: 'This price is a standard rate - the actual price may vary depending on',
    ShippingCostDesicription2: 'the item/destination. The delivery staff will contact you',
    ShippingDiscountSeller: 'Shipping Discount Seller',
    ShippingDiscountNexgen: 'Shipping Discount Nexgen',
    CreditCardInstallmentOption: 'Credit Card with Installment Option',
    SelectPaymentMethod: 'Payment Method',
    InstallmentPaymentNotAvailableBecause: 'Installment payment is not available because the',
    RequiredAmountHasNotBeenMet: 'required amount has not been met',
    SelectInstallmentPeriod: 'Select Installment Period',
    Krungthai: 'Krungthai (KTC)',
    PointsEarned: 'Points earned this time',
    Point: 'Point',
    TotalPriceAll: 'Total Price',
    ConfirmOrder: 'Confirm Order',
    ComfirmModal: 'Do you want to proceed with this transaction? Yes or No',
    Confirm: 'Confirm',
    Cancel: 'Cancel',
    PleaseWaitSellerApproveOrder: 'Please wait for the seller to approve your order',
    OrderConfirmationCompleted: 'Order confirmation completed',
    ShippingAddressDeletedSuccessfully: 'Shipping address deleted successfully',
    YourOrderHasBeenPlacedSuccessfully: 'Your order has been placed successfully',
    YouHaveSuccessfullyDeletedTheShippingAddress: 'You have successfully deleted the shipping address',
    GoOrderListPage: 'Go to Order List Page',
    GoToHomePage: 'Go to Home Page',
    GoQuotationPage: 'Go to Quotation Page',
    ChangeShippingAddress: 'Change Shipping Address',
    Edit: 'Edit',
    Delete: 'Delete',
    NoAddress: 'No Shipping Address',
    SelectShippingMethod: 'Select Shipping Method',
    StartingShippingFee: 'Starting Shipping Fee',
    ChangeBillingAddressInvoice: 'Change Billing Address for Invoice',
    TaxInvoiceAddress: 'Tax Invoice Address',
    Individual: 'Individual',
    JuristicPerson: 'Juristic Person',
    DefaultAddress: 'Default Address',
    ScanQRCodeMakePayment: 'Pay the store by scanning the QR Code',
    ScanQRCodeToPay: 'Pay by scanning the QR Code',
    SaveImage: 'Save Image',
    TotalPaymentAmount: 'Total Payment Amount',
    CanMakePaymentByFollowingStep: 'You can make a payment by following these steps (for mobile payment)',
    SaveImageStep1: 'Tap the "Save Image" button or take a screenshot',
    ForIOSDevices: 'For iOS devices',
    TakeScreenshot: 'Take a screenshot or tap and hold the image',
    TapTheButton: ', then tap the button',
    OpenYourBankingApp: 'Open your banking app and select the "Scan QR Code" menu',
    SelectScreenshotScanTheQRCode: 'Select the screenshot and scan the QR Code',
    RefID: 'Reference Code',
    RequestQuotationEdit: 'Request Quotation Edit',
    EditCouponDisscription: 'Discount coupons cannot be applied to the revised quotation until it is approved by the seller',
    EditCouponConfirm: '"Confirm to proceed or not"',
    DeleteTaxInvoiceAddress: 'Delete Tax Invoice Address',
    ConfirmDelectTaxInvoiceAddress: 'Do you want to proceed with this action? Yes or No',
    DeleteAddressSuccessfully: 'Delete Address Successfully',
    YouHaveSuccessfullyDeletedTaxInvoiceAddress: 'You have successfully deleted the tax invoice address',
    TotalPointsEarnedInThisTransaction: 'Total points earned in this transaction',
    Store: 'Store',
    PointEarned: 'Point Earned',
    TotalPoint: 'Total Point',
    HomePage: 'Home Page',
    ShoppingCart: 'Shopping Cart',
    OperatingBudget: 'Operating Budget',
    investmentBudget: 'Investment Budget',
    regularExpenditureBudget: 'Regular Expenditure Budget',
    COGS: 'COGS',
    SGnA: 'SG&A',
    RnD: 'R&D',
    Month: 'Month',
    PleaseEnterYourFullName: 'Please enter your full name',
    PleaseEnterYourPhoneNumber: 'Please enter your phone number',
    PleaseEnterValidPhoneNumber: 'Please enter a valid phone number',
    PleasEnterYourPosition: 'Please enter your position',
    PleaseEnterYourEmailAddress: 'Please enter your email address',
    PleaseEnterValidEmailAddress: 'Please enter a valid email address',
    ProductDetails: 'Product Details',
    RedeemPoints: 'Redeem',
    Points: 'Points',
    TheCartHasBeenUpdated: 'The cart has been updated',
    PleaseEnterTheShippingAddress: 'Please enter the shipping address',
    ToViewsampleQuotation: 'to view the sample quotation',
    PleaseEnterTheAddressForTheTaxTnvoice: 'Please enter the address for the tax invoice',
    ToViewSampleQuotation: 'to view the sample quotation',
    UnavailableSampleQuotation: 'The system is unavailable to view the sample quotation',
    FailedToDeleteTheShippingAddress: 'Failed to delete the shipping address',
    SetAsDefaultAddressSuccessfully: 'Set as default address successfully',
    FailedToSetAsDefaultAddress: 'Failed to set as default address',
    AddShippingAddress: 'Add Shipping Address',
    EditShippingAddress: 'Edit Shipping Address',
    SelectAddressFirst: 'Please select a shipping address first, or complete/add a shipping address before proceeding',
    Phone: 'Telephone',
    SystemErrorHasOccurredPleaseContactSupport: 'A system error has occurred please contact support',
    EditTaxInvoiceAddress: 'Edit Tax Invoice Address',
    AddTaxInvoiceAddress: 'Add Tax Invoice Address',
    PleaseSelectShippingMethodStoreDelivery: "Please select a shipping method for the store's delivery",
    NoTaxInvoiceAddressAvailable: 'No tax invoice address available',
    SomeItemsAreNotEligibleForTheFreeGiftDueToInsufficientQuantity: 'Some items are not eligible for the free gift due to insufficient quantity:',
    PleaseRemoveTheseItemsFromYourCart: 'Please remove these items from your cart',
    ShoppingCartNotFound: 'Shopping Cart Not Found',
    SomeItemsAreNoLongerAvailableInTheSystem: 'Some items are no longer available in the system:',
    PleaseRemoveThemFromYourCart: 'Please remove them from your cart',
    SomeItemsAreNotAvailableInSufficientQuantityForPurchase: 'Some items are not available in sufficient quantity for purchase:',
    TheTotalPriceMustBeGreaterThanZerobaht: 'The total price must be greater than 0 baht',
    PleaseEnterDiscountThatDoesNotExceedTheProductPricel: 'Please enter a discount that does not exceed the product price',
    YouHaveNotAddedAnAddressYet: 'You have not added an address yet',
    Incompleteinformation: 'Incomplete information',
    PleaseConfirmYourOrderAgain: 'Please confirm your order again',
    WeightOver50kilograms: 'The total weight of your items exceeds 50 kilograms. If you would like to place an order, please contact our staff. Thank you.',
    UserPermissionsChange: 'Please check again, as there has been a change in user permissions',
    TheCouponCanBeUsed: 'The coupon can be used',
    TheCouponCannotBeUsed: 'The coupon cannot be used',
    Paymentincomplete: 'Payment Incomplete',
    ModalShippingIMG: require('@/assets/shippingbannermini_en.png'),
    ShippingBanner: require('@/assets/shippingbannermini_en.png'),
    NoteToStore: 'Note to Store (Not Required)'
  },
  register: {
    Register: 'Register',
    SignUp: 'Join',
    SignIn: 'Sign In',
    Username: 'Username',
    EnterUsername: 'Enter Username',
    ValidateUsername1: 'Please enter your username',
    ValidateUsername2: 'Please enter only letters and numbers',
    ValidateUsername3: 'Please enter at least 6 characters',
    Password: 'Password',
    ValidatePassword1: 'Please enter your password',
    ValidatePassword2: 'Must contain at least 1 letter and 1 number',
    ValidatePassword3: 'Must contain at least 1 letter',
    ValidatePassword4: 'Must contain at least 1 number',
    ValidatePassword5: 'Must be at least 8 characters long',
    TooltipPassword1: 'Must contain both letters and at least one number. Special characters are allowed.',
    TooltipPassword2: 'Must be at least 8 characters long',
    EnterPassword: 'Enter Password',
    ConfirmPassword: 'Confirm Password',
    ValidateConfirmPassword1: 'Passwords must match',
    EnterConfirmPassword: 'Enter Confirm Password',
    Email: 'Email',
    EnterEmail: 'Enter Email',
    ValidateEmail1: 'Please enter your email',
    ValidateEmail2: 'Please enter a valid email',
    ValidateEmail3: 'Please use English letters only',
    ValidateEmail4: 'No spaces allowed',
    Phone: 'Phone Number',
    EnterPhone: 'Enter Phone Number',
    ValidatePhone1: 'Please enter your phone number',
    Accept: 'Accept',
    ServiceOneId: 'Terms Of Service ONE ID',
    And: 'and',
    Policy: 'Privacy Policy',
    AlreadyAccount: 'Already have an account?',
    OTP: 'Request OTP',
    EnterOTP: 'Enter OTP',
    SendOTPPhone: 'An OTP has been sent to',
    WrongOTP: 'Incorrect OTP. Please try again.',
    OTPrequestAgain: 'You can request another OTP within',
    SendOTPButton: 'Send OTP',
    LeavePage: 'Leave Page',
    ExitPage: 'Are you sure you want to leave the page?',
    Cancel: 'Cancel',
    Confirm: 'Confirm',
    EasyOneID: 'Confirm new account with Easy OneID',
    OneIDSuccess: 'This phone number can’t be used with another',
    OtherAccount: 'account after registration.',
    OTPCorrect: 'OTP verification successful!',
    RegisterFail1: 'This username is already in use. Please choose another username.',
    RegisterFail2: 'Missing parameter. Please check your information and try again.',
    RegisterFail3: 'This phone number is already in use.',
    AlreadyUseEmail: 'This email is already in use. Please use another email.'
  },
  Login: {
    Welcome: 'Welcome to the platform',
    ForgotPassword: 'Forgot Password?',
    Login: 'Login',
    LoginOne: 'ONE ID Test Login',
    Or: 'or',
    HaveAccount: 'Don’t have an account?',
    Register: 'Register',
    LoginFail1: 'Incorrect username or password.',
    LoginFail2: 'Username or password cannot be empty.',
    LoginFail3: 'A system error has occurred. Please contact support.',
    LoginWithPhone: 'Sign in with phone number'
  },
  OTPPage: {
    LoginWithOTP: 'Sign in with phone number',
    DescribeOTP: 'An OTP will be sent to your phone number.',
    PhoneOTP: 'Phone Number',
    EnterPhone: 'Enter Phone Number',
    ValidateOTP1: 'Please enter your phone number',
    ValidateOTP2: 'Please enter a 10-digit phone number',
    OTPNotRegister1: 'This phone number is not registered.',
    OTPNotRegister2: 'Do you want to register?',
    OTPFail: 'We’re unable to complete your request.',
    yes: 'Yes',
    no: 'No',
    OTPSend: 'An OTP has been sent to',
    ReferenceCode: 'Reference Code',
    ConfirmOTP: 'Confirm OTP'
  },
  ForgotPassword: {
    PhoneType: 'Phone Number',
    headers: 'Did you forget your password?',
    describe: 'We will send you an email to reset your password.',
    send: 'Send',
    describeOTP: 'An OTP will be sent to your phone number.',
    sendOTP: 'Send OTP',
    newPassword: 'New Password',
    enterNewPassword: 'Enter New Password',
    confirmNewPassword: 'Confirm New Password',
    EnterConfirmPassword: 'Enter Confirm Password',
    resetEmailSuccess: 'An email has been sent to reset your password!',
    resetOTPSuccess: 'OTP verification successful!',
    detailReset: 'Please check your email.'
  },
  ProductPage: {
    AllReviews: 'All Reviews',
    ShippingFee: 'Shipping Fee',
    Wholesale: 'Wholesale',
    Like: 'Like',
    Share: 'Share',
    ViewShippingFee: 'View Shipping Fee',
    ShowMore: 'Show More',
    AddToCart: 'Add to Cart',
    BuyNow: 'Buy Now',
    MinOrderLimit: 'Minimum order quantity for this product is',
    Unit: 'unit(s)',
    AndUp: 'unit(s) and up',
    MaxOrderLimit: 'and the maximum order limit is',
    PerOrder: 'per order',
    ViewStore: 'View Shop',
    ProductDetails: 'Product Description',
    ProductReviews: 'Product Reviews',
    SameStoreProducts: 'From The Same Shop',
    RecommendedProducts: 'You May Also Like',
    ViewAll: 'View All Products',
    OverallRating: 'Product Ratings',
    ReviewCount: 'Reviews',
    All: 'All',
    OutOfStock: 'Out of Stock',
    InStock: 'Ready to Ship',
    PreOrder: 'Pre-order',
    ContactSupport: 'Contact Support',
    SpecialDiscount: 'Special Discount',
    Discount: 'Discount',
    Note: 'Note',
    TotalPreOrderItems: 'Total Pre-order Items',
    TotalItems: 'Total Items',
    PayType: 'Pay Type',
    SelectPayType: 'Select Pay Type',
    Price: 'Price',
    NoProductDetails: 'No product details available',
    Options: 'Options',
    SellerResponse: 'Seller Response',
    TimeIndicator: 'hr.', // or just leave as 'น.' if matching locale is preferred
    NoProductComments: 'No product reviews for this item yet',
    ServiceProduct: 'Service Product',
    Chat: 'Chat',
    MyLink: 'My Link',
    CopyLink: 'Copy Link',
    WholesalePrice: 'Wholesale Price',
    AndAbove: 'and up',
    ShippingCost: 'Shipping Cost',
    ShareProduct: 'Share Product',
    Sku: 'SKU Number',
    SelectATB: 'Please select product variation first',
    AddToCartComplete: 'Item has been added to your shopping cart'
  },
  ShopPage: {
    Shop: 'Shop',
    PendingApproval: 'Waiting for Approval',
    StoreRating: 'Shop Rating',
    NoRatingYet: 'No rating yet',
    AllProductsList: 'All Product Listings',
    AllProducts: 'All Products',
    RecommendedPromotion: 'Recommended',
    // RecommendedPromotion: 'Recommended promotions',
    NewArrival: 'New Products',
    DiscountedProducts: 'Discounted',
    // DiscountedProducts: 'Discounted Products',
    BestSellers: 'Best-Selling',
    // BestSellers: 'Best-Selling Products',
    Recommended: 'Recommended Products',
    GeneralProducts: 'General Products',
    OutOfStock: 'Out of Stock',
    All: 'All',
    SameStoreProducts: 'Products from the same shop',
    AboutStore: 'About the Shop',
    RegisteredOnly: 'Products for Registered Partners Only',
    RegisterAsPartner: 'Register as a Partner',
    StoreNotFound: 'Shop Not Found',
    PleaseCheckStore: 'Please recheck the shop you are looking for',
    ContactStore: 'Please contact the shop',
    NoProductYet: 'This shop has no products yet',
    RequestSubmitted: 'Your request has been submitted. Please wait for shop approval.',
    ExternalOrder: 'External Purchase',
    OrContactSupport1: 'or contact support',
    ClickIfInterested: 'If you are interested, click here',
    CustomerCode: 'Customer Code',
    SelectCustomerInfo: 'Select Customer Info',
    InternalOrder: 'Internal Purchase',
    StoreCoupon: 'Shop Discount Code',
    ViewAll: 'View All',
    CouponConditions: 'Coupon Usage Conditions',
    NoExpiry: 'No Expiration Date',
    Used: 'Used',
    Coupon: 'Coupon',
    Discount: 'Discount',
    Freebie: 'Free Gift',
    SaveCode: 'Save Code',
    SavedCode: 'Code Saved',
    Minimum: 'Minimum',
    THB: 'THB',
    FreeShippingCode: 'Free Shipping Code',
    AllProductsCategory: 'All Products / All Categories',
    Title: 'Home Page',
    Category: 'Category',
    Type: 'Type',
    Price: 'Price',
    NoProduct: 'No Product Yet',
    SortBy: 'Sort By',
    Filter: 'Filter',
    Article: 'Article',
    ReadMore: 'Read More',
    NoArticles: 'No Articles in Shop',
    CustomerTypeList: 'Customer Types List',
    Company: 'Company',
    CompanyOrder: 'Orders for Company Organizations',
    Individual: 'Individual',
    PersonalOrder: 'Orders for Individuals',
    AllCoupons: 'All Coupons',
    ValidUntil: 'Valid Until',
    DiscountCodeConditions: 'Discount Code Conditions',
    PromotionType: 'Promotion Type',
    ProductDiscount: 'Product Discount',
    ShippingDiscount: 'Shipping Discount',
    CouponEligibility: 'Coupon Eligibility',
    Unlimited: 'Unlimited',
    DiscountCodeDetails: 'Discount Code Details',
    FreebieDetails: 'Free Gift Details',
    OrderList: 'Order List',
    ProductName: 'Product Name',
    Options: 'Options',
    Quantity: 'Quantity',
    FreeGift: 'Free Gift',
    CustomerList: 'Customer List',
    AddCustomerInfo: 'Add Customer Info',
    FindNamePartner: 'Find Name Partner',
    AddInfo: 'Add Info',
    SelectAddress: 'Select Shipping Address',
    AllAddresses: 'All Addresses',
    ItemList: 'Item List',
    AddNewAddress: 'Add New Address',
    PartnerRequest: 'Request to Become Partner',
    StoreName: 'Shop Name',
    PartnerRequestDetails: 'Partner Request Details',
    Email: 'E-mail',
    EmailNote: '*Used for notifications to partners',
    EmailNote2: 'Used for sale notifications',
    CustomerGroup: 'Customer Group',
    SpecifyGroup: 'Specify Customer Group',
    RequiredDocuments: 'Documents to Upload',
    UploadNote: '(Supports .pdf files up to 5 MB)',
    UploadFile: 'Upload File',
    DropFileHere: 'Drop your file here',
    OrChooseFile: 'Or choose from your computer',
    FileNote: '(.PDF files up to 5 MB)',
    Cancel: 'Cancel',
    SubmitRequest: 'Submit Request',
    CreateQuotation: 'Create Quotation',
    Budget: 'Budget',
    DeductBudget: 'Deduct Budget',
    Confirm: 'Confirm',
    DocumentSample: 'Sample Document',
    MustRegisterToView: 'You must register as a partner with the seller to view products and services',
    OrContactSupport: 'Or contact support',
    ConfirmPartnerRequest: 'Do you want to request to be a partner with this Shop?',
    PartnerRequestSent: 'Partner Request Submitted',
    AlreadyRequested: 'You have already submitted a partner request',
    Clear: 'Clear',
    ConfirmAction: 'Confirm'
  },
  AllShopsPage: {
    Home: 'Home Page',
    AllShops: 'All Shops',
    AllShop: 'All Shop',
    Shop: 'Shop',
    SearchResults: 'Search Results',
    SearchShops: 'Search by shop name',
    NoShop: 'No shop available',
    ShopNotFound: 'Shop not found',
    ShopSearchEmpty: 'No shop matched your search. Please check again.'
  },
  YourOrderPage: {
    banner: require('@/assets/new_yourorder_en.png'),
    PaymentInformation: 'Payment Information',
    OrderID: 'Order ID',
    TotalAmount: 'Total Amount',
    NexgenDiscountCoupon: 'Nexgen Discount Coupon',
    SellerDiscount: 'Seller Discount',
    NexgenPoinsDiscount: 'Nexgen Points Discount',
    SystemCoupon: 'System Coupon (Free Shipping)',
    StoreCoupon: 'Store Coupon (Free Shipping)',
    DateTimeofPayment: 'Date & Time of Payment',
    PaymentMethod: 'Payment Method',
    PaymentResult: 'Payment Result',
    OrderDetails: 'Order Details',
    Store: 'Store',
    ProductDetails: 'Product Details',
    UnitPrice: 'Unit Price',
    Quantity: 'Quantity',
    TotalPrice: 'Total Price',
    ShippingAddress: 'Shipping Address',
    InvoiceAddress: 'Tax Invoice Address',
    PickupDate: 'Pickup Date',
    PickupTime: 'Pickup Time',
    ShippingNew: require('@/assets/shipping_new_en.png'),
    PickUpNew: require('@/assets/pickup_new_en.png'),
    Baht: 'baht',
    PaymentConfirmed: 'Payment Confirmed',
    Freegift: 'Free Gift',
    DiscountCode: 'Discount Code',
    ListOf: 'List of',
    employees: 'employees',
    Department: 'Department',
    Company: 'Company',
    SubDistrict: 'Sub-district',
    District: 'District',
    Province: 'Province',
    Time: 'AM/PM',
    GeneralProducts: 'General Products',
    ServiceProduct: 'Service Product',
    GotoOrderList: 'Go to Order List',
    GotoHomepage: 'Go to Homepage',
    Paymentincomplete: 'Payment Incomplete',
    NoAddress: 'You haven’t added your address yet.',
    Incompleteinformation: 'Incomplete information.',
    ConfirmOrder: 'Please confirm your order again.',
    OverWeight: 'The total weight of your items exceeds 50 kilograms. To proceed with your order, please contact our staff. Thank you.',
    Sku: 'This SKU',
    WeightProblem: 'has an issue with the weight. Please contact our staff.',
    UserPermission: 'Your permissions have changed. Please refresh or check your access.'
  },
  ListCoupon: {
    Header: 'Discount Coupon',
    HeaderSystemCode: 'Nexgen Discount Coupon',
    HeaderSystemCodeShipping: 'Nexgen Shipping Coupon',
    PlaceHolder: 'Search Coupon Discount',
    GeneralDiscount: 'General Discount',
    ShippingDiscount: 'Shipping Discount',
    Min: 'Min',
    Max: 'Max',
    Validfrom: 'Valid from',
    Onwards: 'onwards',
    SelectCoupon: 'Select Coupon',
    FreeGiftCoupon: 'Free Gift Coupon',
    ShowMoreCoupons: 'Show More Coupons',
    ForStore: 'For',
    Only: 'Only',
    NoCoupon: 'No Store Discount Coupon Available',
    NoCouponSystem: 'No Nexgen Discount Coupon Available',
    CouponNotFound: 'Coupon not found or not currently valid',
    PointError: 'Point system error. Please try again later',
    PleaseSelect: 'Please select a discount first.',
    NotExceed1: 'The amount of discount points you can use',
    NotExceed2: 'cannot exceed 25% of the total price.',
    CannotUse: 'This coupon cannot be used because the usage conditions are not met',
    errorMessage1: 'Coupon not found',
    errorMessage2: 'Coupon not currently valid',
    errorMessage3: 'Data has been changed.',
    errorMessage4: 'Please Do you want to save or discard the changes?',
    errorMessage5: 'Please check the discount code again. The entered details do not meet the code’s conditions.',
    errorMessage6: 'The product price does not meet the minimum requirement.',
    errorMessage7: 'This discount cannot be used for in-store pickup.',
    errorMessage8: 'Coupon collected successfully.',
    errorMessage9: 'Unable to collect coupon.',
    errorMessage10: 'Code not found',
    errorMessage11: 'The code',
    errorMessage12: 'already exists. Please check again.',
    errorMessage13: 'Please check the information again.',
    Balance: 'Balance',
    Save: 'Save'
  },
  AllProductCategory: {
    HomePage: 'Home Page',
    Categories: 'Product Categories'
  },
  SearchPage: {
    HomePage: 'Home Page',
    RelatedShops: 'Shops related to',
    All: 'All',
    Shop: 'Shop',
    OtherShops: 'Other Shops',
    ViewShop: 'View Shop',
    SearchKeyword: 'Search keyword:',
    NoResults: 'No search results found',
    CheckSpelling: 'Please check if the spelling is correct',
    TrySimilarWords: 'Or try using similar words',
    TryAgain: 'Please try searching again',
    SearchResults: 'Search Results',
    list: 'list',
    Store: 'Shop'
  },
  SelectPrice: {
    Price: 'Price',
    All: 'All',
    Low: 'Price: Low to High',
    High: 'Price: High to Low',
    Higher: 'High to Low',
    Lower: 'Low to High',
    AllCategories: 'All Categories',
    NotFoundTag: 'No products found in the product tag you selected.',
    NotTag: 'There are no product tags listed.',
    TagProduct: 'Tag Product'
  },
  BankAccountPage: {
    header: 'Bank Account',
    headerModal: 'My Account',
    index: 'Account No. ',
    addAccount: 'Add Account',
    editAccount: 'Edit',
    editAccountModal: 'Edit Account',
    deleteAccount: 'Delete',
    overTenAccount: 'Cannot add more than 10 accounts',
    accountName: 'Name',
    accountNameModal: 'Account Name',
    PlaceHolderAccountName: 'Enter account name',
    validateAccountName1: 'Please enter account name',
    validateAccountName2: 'Only letters can be enter',
    bankName: 'Bank',
    bankNameModal: 'Bank Name',
    PlaceHolderBankName: 'Enter bank name',
    validateBankName1: 'Please select bank name',
    accountNumber: 'Number',
    accountNumberModal: 'Account Number',
    PlaceHolderAccountNumber: 'Enter account number',
    validateAccountNumber1: 'Please enter account number',
    validateAccountNumber2: 'Account number must be more than 9 digits',
    accountDefault: 'Set as default account',
    addAwait: 'Add',
    editAwait: 'Edit',
    deleteAwait: 'Delete',
    MyAccount: ' my account',
    Proceed: 'Do you want to proceed with this action?',
    Review: 'Have you reviewed the information?',
    Success: ' successful',
    DetailSuccess1: 'You have successfully ',
    DetailSuccess2: ' your account',
    NoFound: 'No account found.',
    Press: 'You can press '
  },
  FavoritePage: {
    header: 'Favorite items',
    PlaceHolderName: 'Search for products',
    PleceHolderCategory: 'Search for categories',
    Type: 'Type',
    Category: 'Category',
    Wishlist: 'Your wishlist contains',
    list: 'items',
    TypeList: {
      All: 'All',
      GeneralProducts: 'General Products',
      ServiceProduct: 'Service Product'
    },
    NoProduct1: 'No favorite items yet. ',
    NoProduct2: 'You can tap ',
    NoProduct3: 'Shop Now'
  },
  CouponProfile: {
    header: 'My Coupons and Points',
    Condition: 'Point conditions are subject to the store’s terms.',
    headerTable: 'Reward points from purchases at participating stores',
    subHeaderTable: {
      Shop: 'Shop Name',
      getPoint: 'Point-to-order ratio',
      usePoint: 'Points Used',
      detailTable: 'Detail',
      detail: {
        OrderList: 'Order List',
        ProductPrice: 'Product Price',
        PointsEarned: 'Points Earned',
        PointsUsed: 'Points Used',
        total: 'Total',
        ViewDetails: 'View Details'
      },
      Baht: 'Baht',
      Per: 'Per',
      Point: 'Point',
      NumberOfRows: 'Number of Rows'
    },
    Coupon: {
      CanUse: 'Valid code',
      CannotUse: 'Invalid/Expired',
      CouponCard: {
        CannotUseWith: 'Not combinable',
        Minimum: 'Minimum spend',
        Maximum: 'Maximum discount',
        ValidUntil: 'Valid until',
        NoExpirationDate: 'No expiration date',
        Used: 'Used',
        Remaining: 'Remaining',
        ViewConditions: 'Conditions',
        Coupons: 'Coupon',
        Discount: 'Discount',
        FreeGift: 'Bonus'
      }
    },
    ConditionModal: {
      header: 'Coupon Usage Conditions',
      PromotionType: 'Promotion Type',
      PromotionTypeList: {
        ProductPriceDiscount: 'Product Price Discount',
        ShippingDiscount: 'Shipping Discount',
        FreeGiftCoupon: 'Free Gift Coupon'
      },
      CouponEligibilityList: {
        CouponEligibility: 'Coupon Eligibility',
        CanBeUsed: 'Can be used',
        Times: 'times'
      },
      AvailableCreditLimit: 'Available Credit Limit',
      MaximumDiscount: 'Maximum Discount',
      Unlimited: 'Unlimited',
      ParticipatingStores: 'Participating Stores',
      ListOfAll: 'List of all',
      Stores: 'stores',
      ValidAtAllStores: 'Valid at all stores',
      ShowLess: 'Show Less',
      ShowMore: 'Show More',
      DiscountCodeDetails: 'Discount Code Details',
      FreeGiftDetails: 'Free Gift Details',
      OrderList: 'Order List',
      ProductName: 'Product Name',
      Attribute: 'Attribute',
      Amount: 'Amount',
      FreeGift: 'Free Gift',
      GoBack: 'Go Back',
      NoCouponsAvailable: 'No coupons available'
    }
  },
  AddressProfilePage: {
    headerPage: 'Shipping Address',
    headerInvoice: 'Tax Invoice Address',
    AddAddressBtn: 'Add Address',
    OverAddress: 'Cannot add more than 10 addresses',
    OverAddressInvoice: 'Cannot add more than 5 addresses',
    SetDefaultAddress: 'Set as default address',
    SuccessSetDefaultAddress: 'Set as default address successfully',
    FailedSetDefaultAddress: 'Failed to set as default address',
    Individual: 'Individual',
    LegalEntity: 'Legal Entity',
    remark: 'Remark',
    BranchNo: 'Branch No.',
    TaxID: 'Tax ID',
    NotHaveAddress: 'You don’t have any address yet',
    Click: 'You can click ',
    ShippingPurpose: ' For shipping purposes',
    TaxInvoicePurpose: ' For tax invoice purposes',
    ShippingAddressModal: {
      TitleAddAddress: 'Add Shipping Address',
      TitleEditAddress: 'Edit Shipping Address',
      RecipientInformation: 'Recipient Information',
      FirstName: 'First Name',
      PlaceHolderFirstName: 'Enter first name',
      LastName: 'Last Name',
      PlaceHolderLastName: 'Enter last name',
      Phone: 'Phone',
      PlaceHolderPhone: 'Enter phone number',
      Email: 'Email',
      PlaceHolderEmail: 'Enter email',
      HouseNo: 'House No.',
      PlaceHolderHouseNo: 'Enter house number',
      RoomNo: 'Room No.',
      PlaceHolderRoomNo: 'Enter room number',
      Floor: 'Floor',
      PlaceHolderFloor: 'Enter floor',
      BuildingName: 'Building Name',
      PlaceHolderBuildingName: 'Building,Apartment,Condominium Name',
      Village: 'Village',
      PlaceHolderVillage: 'village name',
      VillageNo: 'Village No.',
      PlaceHolderVillageNo: 'Enter village number',
      Alley: 'Alley',
      PlaceHolderAlley: 'Enter alley',
      Junction: 'Junction',
      PlaceHolderJunction: 'Enter junction',
      Road: 'Road',
      PlaceHolderRoad: 'Road Name',
      SubDistrict: 'Sub-district',
      PlaceHolderSubDistrict: 'Enter Sub-district',
      District: 'District',
      PlaceHolderDistrict: 'Enter District',
      Province: 'Province',
      PlaceHolderProvince: 'Enter Province',
      Zipcode: 'Zip Code',
      PlaceHolderZipCode: 'Enter Zip Code',
      PlaceHolderRemark: 'Enter remark',
      error1: 'Invalid information',
      error2: 'Please enter recipient\'s first name',
      error3: 'Maximum 20 characters',
      error4: 'Please enter recipient\'s last name',
      error5: 'Maximum 30 characters',
      error6: 'Please enter phone number',
      error7: 'Phone number should start with 0',
      error8: 'Phone number must be 10 digits',
      error9: 'Please enter email address',
      error10: 'Please enter a valid email address',
      error11: 'Thai characters are not allowed',
      error12: 'Special characters are not allowed',
      error13: 'Email must not contain spaces',
      error14: 'Please specify house number',
      error15: 'Please enter correct information',
      error16: 'Incorrect information provided',
      error17: 'Numbers only',
      error18: 'Maximum 120 characters',
      error19: 'Invalid input',
      error20: 'Invalid input. Please check your information and try again.',
      error21: 'Please select sub-district, district, province, and postal code.',
      error22: '(Sub-district, District, Province, Postal Code)',
      Success1: 'Address added successfully',
      Success2: 'Address edited successfully',
      Success3: 'You have successfully added an address',
      Success4: 'You have successfully edited an address'
    },
    TaxInvoiceAddressModal: {
      TitleAddAddressInvoice: 'Add Tax Invoice Address',
      TitleEditAddressInvoice: 'Edit Tax Invoice Address',
      GeneralInformation: 'General Information',
      TaxInvoiceInformation: 'Tax Invoice Information',
      SameAddress: 'Use shipping address',
      TaxInvoiceName: 'Name for Tax Invoice',
      BranchNo: 'Branch No.',
      enterCode: '(Please enter code 00000 if this is the head office)',
      TaxInvoiceNo: 'Tax Identification Number',
      TaxInvoiceEmail: 'Taxpayer Email',
      TaxInvoiceAddress: 'Billing Address for Tax Invoice',
      error2: 'Please select information',
      error3: 'Please enter the name for the tax invoice',
      error4: 'Tax invoice name must not exceed 100 characters',
      error5: "Please enter the recipient's Tax ID",
      error6: 'Tax ID must be 13 digits',
      error7: 'Invalid Tax ID',
      error8: 'Please specify the address',
      error9: 'Please enter email address',
      error10: 'Please enter a valid email address',
      error11: 'Taxpayer email must not exceed 256 characters',
      error12: 'Please enter phone number',
      error13: 'Phone number should start with 0',
      error14: 'Phone number must be 10 digits',
      error15: 'Please enter branch No.',
      error16: 'Branch No. must be 5 digits',
      error17: 'Please select',
      error18: 'No shipping address available',
      error19: 'Please set a default shipping address first',
      error20: 'Please check your information and try again.',
      error21: 'Please enter all required information.',
      error22: 'Please check the information again',
      error23: 'Please select (Individual or Legal Entity)',
      Success1: 'Address added successfully',
      Success2: 'Address edited successfully',
      Success3: 'You have successfully added an address',
      Success4: 'You have successfully edited an address'
    },
    deleteAddress: {
      TitleShippingAddress: 'Delete Shipping Address',
      TitleTaxInvoiceAddress: 'Delete Tax Invoice Address',
      DeleteShippingAddressSuccess: 'Delete Shipping Address Successfully',
      DeleteShippingAddressSuccess1: 'You have successfully deleted the shipping address',
      DeleteTaxInvoiceAddressSuccess: 'Delete Address Successfully',
      DeleteTaxInvoiceAddressSuccess1: 'You have successfully deleted the tax invoice address'
    }
  },
  ListPoint: {
    StoreDiscountPoint: 'Store Discount Points',
    Points: 'points',
    RedeemPoints: 'Use Points',
    PointUsageRules: 'Terms of Point Use',
    MaximumUsablePoints: 'maximum usable points',
    Baht: 'baht',
    Spend: 'spend',
    Earn: 'earn',
    PointUsageMustNotExceed: 'point usage must not exceed',
    OrderTotal: 'of the order total',
    Cancel: 'Cancel',
    Confirm: 'Confirm',
    NoDocument: 'no document',
    CompanyCertificateCopy: 'company certificate copy',
    CompanyCertificateCopyDocument: 'company certificate copy document',
    PointError: 'Point error. Please try again later.',
    SystemError: 'System error. Please contact support.',
    PleaseSelectDiscount: 'Please select a discount first.',
    PointExceedLimit: 'Points can be used for up to 25% of the order total'
  },
  ListCode: {
    Title: 'Partnership Coupon',
    SearchPlaceholder: 'Search Coupon Discount',
    EmployeeList: 'Employee List',
    ListName: 'Person',
    Department: 'Department',
    Company: 'Company',
    EmployeeName: 'Employee name',
    NotAdded: 'No code has been added',
    Cancel: 'Cancel',
    Confirm: 'Confirm',
    Delete: 'Delete',
    TitleConfirm: 'Changes Detected',
    Description: 'Would you like to save or discard the changes?',
    Save: 'Save',
    InvalidCouponCondition: 'Please verify the coupon details again. The conditions do not match.',
    NotFound: 'Coupon code not found',
    AlreadyUsed_Prefix: 'Coupon code',
    AlreadyUsed_Suffix: 'has already been used. Please check again.',
    InvalidInput: 'Please check the input again',
    PointError: 'Points error, please try again later',
    SystemError: 'System error, please contact support'
  },
  withdrawAffiliate: {
    WithdrawHistory: 'Withdrawal History',
    ConfirmTitle: 'Do you want to withdraw the money?',
    Cancel: 'Cancel',
    Confirm: 'Confirm',
    TopEarning: 'Top Earning',
    TopProduct: 'Top Product',
    SearchPlaceholder: 'Search',
    NoData: 'No records found',
    Views: 'Views',
    Orders: 'Orders',
    TotalSales: 'Total Sales',
    Commission: 'Commission',
    Times: 'times',
    Items: 'items',
    Baht: 'baht',
    Withdraw: 'Withdraw',
    FeeOnly: '5 Baht fee',
    MinWithdrawNote: 'Minimum 20 Baht',
    EKYCWarning: 'You must complete eKYC verification before you can withdraw.',
    EKYCWarning1: 'You must complete eKYC verification',
    EKYCWarning2: 'before you can withdraw.',
    OrderNumber: 'Order Number',
    Shop: 'Shop',
    OrderAffiliate: 'Order Affiliate',
    TotalAffiliate: 'Total Affiliate (Baht)',
    TimeTransfer: 'Time Transfer',
    TransferAffiliate: 'Transfer Status',
    SystemError: 'System error, please contact support',
    NoWithdrawData: 'No Affiliate withdrawal data available'
  },
  clickAffiliateBuyer: {
    Title: 'Click Report',
    ClickTime: 'Click Time',
    ClickTimePlaceholder: 'Search by click time',
    ClickCode: 'Click Code',
    ClickCodePlaceholder: 'Search by click code',
    Region: 'Region',
    RegionPlaceholder: 'Search by region',
    SubId: 'Sub_id',
    SubIdPlaceholder: 'Search by sub_id',
    Clear: 'Clear',
    TotalResult: 'Total',
    Items: 'items',
    Export: 'Export',
    NoClickData: 'No click report found',
    EmptyMessage: 'You have no click reports',
    Cancel: 'Cancel',
    Confirm: 'Confirm',
    Reference: 'Reference'
  },
  ReviewListBuyer: {
    title: 'Review',
    notEvaluation: 'Not yet evaluated',
    assessed: 'Evaluated',
    searchOrder: 'Search by order ID',
    orderNot: 'List of all orders not yet evaluated',
    orderSuccess: 'List of all orders evaluated',
    order: 'order',
    orderNotFound: 'No satisfaction evaluation data found',
    noResultsText: 'No customer satisfaction data found for the searched product',
    noDataText: 'No customer satisfaction data is available in the table',
    numberRows: 'Number of rows',
    status1: 'satisfaction already evaluated',
    status2: 'waiting for your feedback',
    btnDetail: 'Detail',
    btnReview: 'Give Evaluation',
    noOrderEvaluated: "You don't have any satisfaction evaluations yet",
    thOrder: 'order number',
    thStatus: 'status',
    thExpired: 'expired review',
    thManage: 'manage',
    textDialog: 'Please evaluate your satisfaction with the product',
    titleSKU: 'SKU',
    titleEdit: 'Edit',
    titleReviewEdit: 'Write your satisfaction evaluation',
    titleDateEdit: 'Satisfaction evaluation date',
    titleDateSuccess: 'Edit satisfaction evaluation date',
    titleReviewSuccess: 'Review',
    noImage: 'No Image',
    uploadImage: 'Upload your image here',
    fixedSizeImage: '(upload up to 6 images max size 6 MB each, file types: .png, .jpeg, .jpg)',
    uploadVideo: 'Upload your video here',
    fixedSizeVideo: '(upload up to 1 video max size 20 MB each, file types: .mp4)',
    anonymous: 'Anonymous',
    btnCancel: 'Cancel',
    btnSuccess: 'Save'
  },
  Affiliate: {
    AffiliateShop: 'Affiliate Shops',
    AffiliateShopDescription: 'Shops participating in the Affiliate',
    SearchShop: 'Search Shop Name',
    AllShops: 'All',
    JoinAffiliate: 'Join Affiliate',
    WaitingForApproval: 'Waiting for approval',
    NotJoined: 'Not joined the Affiliate',
    CancelRequest: 'Cancel',
    List: 'List',
    NoShopFound: 'No shop found',
    NoAffiliateShop: 'No Affiliate shops available',
    UpTo: 'Up to',
    NotSpecified: 'Not specified',
    StartDate: 'Start date',
    EndDate: 'End date',
    Unlimited: 'Unlimited',
    ViewProducts: 'View products',
    NoItems: 'No items yet',
    NotJoinedAffiliate: 'Not Joined the Affiliate',
    JoinAllAffiliateShops: 'Join all Affiliate shops',
    ConfirmShopNameBeforeJoining: 'Please verify the shop name before confirming to join the Affiliate',
    Confirm: 'Confirm',
    CancelSelection: 'Cancel selection',
    ConfirmShopNameBeforeCancelAll: 'Please verify the shop name before cancelling all Affiliate participation',
    AllShopsAffiliate: 'All Affiliate Shops',
    JoinAll: 'Join All Affiliates',
    WaitingAll: 'All Pending Approvals',
    RejectAll: 'Not joined all Affiliates',
    DataTable: {
      RowCount: 'Number of rows',
      ShopName: 'Shop Name',
      OfferDuration: 'Offer Duration',
      CommissionRate: 'Commission Rate',
      Status: 'Status',
      Note: 'Note',
      Action: 'Action'
    },
    WaitingApproval: 'Waiting for shop approval',
    JoinSuccess: 'Successfully joined Affiliate',
    ProductList: 'Product List',
    Shop: 'Shop',
    SearchProductName: 'Search product name',
    AllAffiliateProducts: 'All Affiliate products',
    NoAffiliateProductFound: 'No Affiliate products found',
    SelectAllOnPage: 'Select all products on this page',
    Select: 'Select',
    GetAllLinks: 'Get all links at once',
    ValidateSelectionBeforeGetLink: 'Please verify your selected Affiliate products before getting all links at once',
    ConfirmCancelAllSelection: 'Confirm cancel all selections',
    ConfirmCancelAllMessage: 'Are you sure you want to cancel the selection of all products',
    Product: {
      FDAApproved: 'FDA Certified',
      Sold: 'Sold',
      K: 'K',
      Unit: 'pcs',
      ContactSupport: 'Contact Support',
      CommissionRate: 'Commission Rate',
      GetLink: 'Get Link',
      ProductOfferLink: 'Product Offer Link',
      PleaseCopyShortLink: 'Please copy the short link',
      CopyLink: 'Copy Link',
      Close: 'Close',
      CopySuccess: 'Link copied successfully',
      CopyError: 'An error occurred. Unable to get the link',
      MultipleProductLinks: 'Multiple Product Links.xlsx'
    },
    ProductListTitle: 'Affiliate Product List',
    SystemError: 'A system error occurred. Please contact support.'
  },
  PaymentSettings: {
    Title: 'Payment Settings',
    AccountType: 'Account Type',
    PaymentInfo: 'Payment Information',
    AccountName: 'Account Name',
    BankName: 'Bank Name',
    BankBranch: 'Bank Branch Name',
    BankNumber: 'Bank Account Number',
    BankBookImage: 'Bank Book Image',
    Upload: 'Upload',
    TaxInfo: 'Tax Information',
    TaxId: 'Taxpayer Identification Number',
    AddressCard: 'Address as per ID card',
    AddressNo: 'House No.',
    RoomNo: 'Room No.',
    Floor: 'Floor',
    Building: 'Building',
    Village: 'Village',
    Moo: 'Moo',
    Soi: 'Alley/Lane',
    Intersection: 'Intersection',
    Road: 'Road',
    SubDistrict: 'Subdistrict',
    District: 'District',
    Province: 'Province',
    PostalCode: 'Postal Code',
    IsAccountType: {
      Savings: 'Savings Account',
      FixedDeposit: 'Fixed Deposit Account'
    },
    PaymentForm: {
      SelectAccountType: 'Select Account Type',
      EnterAccountName: 'Enter Account Name',
      SelectBankName: 'Select Bank Name',
      EnterBankBranch: 'Enter Bank Branch Name',
      EnterBankNumber: 'Enter Bank Account Number',
      EnterTaxId: 'Enter Taxpayer Identification Number',
      EnterAddressNo: 'Enter House Number',
      EnterRoomNo: 'Enter Room Number',
      EnterFloor: 'Enter Floor',
      EnterBuilding: 'Building, Apartment, or Condominium Name',
      EnterVillage: 'Village Name',
      EnterMoo: 'Enter Moo',
      EnterSoi: 'Enter Alley/Lane',
      EnterIntersection: 'Enter Intersection',
      EnterRoad: 'Enter Road Name',
      EnterSubDistrict: 'Enter Subdistrict',
      EnterDistrict: 'Enter District',
      EnterProvince: 'Enter Province',
      EnterPostalCode: 'Enter Postal Code'
    },
    Validation: {
      SelectAccountType: 'Please select account type',
      EnterAccountName: 'Please enter account name',
      SelectBankName: 'Please select bank name',
      EnterBankBranch: 'Please enter bank branch name',
      EnterBankNumber: 'Please enter bank account number',
      NumbersOnly: 'Please enter numbers only',
      EnterTaxId: 'Please enter taxpayer identification number',
      TaxIdLength: 'Tax ID must be 13 digits and numbers only',
      InvalidTaxId: 'Invalid taxpayer identification number',
      EnterAddressNo: 'Please enter house number',
      EnterValidData: 'Please enter valid information',
      InvalidInput: 'Invalid input',
      DigitsOnly: 'Digits only',
      MaxCharacters: 'Maximum 120 characters allowed'
    },
    UpdateSuccess: 'Updated successfully',
    FileSizeExceeded: 'File size exceeds 5 MB',
    InvalidFileType: 'Only JPEG, PNG, JPG formats are allowed',
    FileTooLarge: 'Please upload an image smaller than 5 MB',
    AllowOnlyImage: 'Only image files with jpeg/jpg/png are allowed',
    Edit: 'Edit',
    Cancel: 'Cancel',
    Save: 'Save',
    Close: 'Close',
    InvalidInfo: 'Invalid information'
  },
  SocialMediaSettings: {
    Title: 'Social Media Account Settings',
    Facebook: 'Facebook',
    TikTok: 'TikTok',
    Youtube: 'Youtube',
    Instagram: 'Instagram',
    Line: 'Line',
    EnterLink: 'Enter link',
    UpdateSuccess: 'Update successful',
    UpdateFailed: 'Update failed',
    RequireAtLeastOne: 'Please enter at least one social media account',
    Edit: 'Edit',
    Cancel: 'Cancel',
    Save: 'Save'
  },
  MenuBuyer: {
    titleUserProfile: 'User Information',
    menuMyProfile: 'My Information',
    menuBankAccount: 'Bank Account',
    menuShippingAddress: 'Shipping Address',
    menuFavoriteProduct: 'Favorite items',
    menuCouponPoint: 'My Coupons and Points',
    titleManageOrders: 'Order Management',
    menuMyOrders: 'Order list',
    menuProfileRecord: 'My purchase',
    menuReview: 'Review',
    titleAffiliate: 'Affiliate Program',
    menuShopSeller: 'Affiliate Shops',
    menuProductAffiliate: 'Affiliate Product List',
    menuEditPay: 'Payment Settings',
    menuEditSocial: 'Social Settings',
    menuMyShared: 'Dashboard Affiliate',
    menuClick: 'Click Report',
    menuOrderAffiliate: 'Order Report',
    menuWithdraw: 'Withdrawal History',
    titleChat: 'Chat',
    menuMyChat: 'My Chat',
    titleManageSales: 'Manage Sales Order',
    breadcrumbHome: 'Home'
  },
  PobuyerRecord: {
    title: 'My purchase',
    search: 'Search for product name or shop name',
    viewShop: 'View Shop',
    orderDate: 'Order Date',
    quantity: 'Quantity',
    orderSummary: 'Order Summary',
    addCart: 'Add To Cart',
    reorder: 'Reorder',
    discount: 'Discount',
    noOrder: 'There are no orders yet',
    textAddCart: 'Added to cart',
    textHaveProduct: 'This item is already in your cart',
    textIncomplete: 'Information is incomplete',
    textInsert: 'Quantity must be at least 1',
    textNotFoundAttribute: 'Please select a product option first',
    textUnable: 'Unable to proceed',
    textReAdd: 'This item is already in your cart, but it has been updated. Please remove it from your cart and add it again',
    textAddOnly: 'You can add up to 30 items to the cart only'
  },
  MyOrders: {
    title: 'Order list',
    search: 'Search by order ID and shop name',
    orderStatus: 'Order Status',
    statusAll: 'All',
    statusSuccess: 'Success',
    statusCancel: 'Cancel',
    statusPending: 'Pending',
    statusApprove: 'Approve',
    statusFail: 'Fail',
    statusNotPaid: 'Not Paid',
    orderDate: 'Order Date',
    formatDate: 'DD/MM/YYYY',
    btnCancel: 'Cancel',
    btnConfirm: 'Confirm',
    btnSave: 'Save',
    shop: 'Shop',
    cancelOrder: 'Cancel Order',
    payment: 'Payment',
    review: 'Reviews',
    received: 'Order Received',
    reorder: 'Reorder',
    totalOrder: 'Total Items',
    paymentSuccess: 'Payment Successful',
    pickedUp: 'Parcel Registered',
    shippingInprogress: 'Shipping in Progress',
    delivered: 'Delivered',
    pendingPayment: 'Pending Payment',
    generalProduct: 'General Product',
    serviceProduct: 'Service Product',
    attribute: 'Attribute',
    quantity: 'Quantity',
    price: 'Price',
    totalPrice: 'Total Price',
    seeMore: 'See More',
    freeGift: 'Free Gift',
    noOrderAt: "You don't have any orders",
    noOrder: "You don't have any orders",
    selectPayment: 'Select Payment',
    installmentTerm: 'Installment Term',
    selectTerm: 'Select installment term',
    scanQR: 'Scan QR Code to Pay',
    btnSaveImage: 'Save Image',
    paymentAmount: 'Payment Amount',
    referenceCode: 'Reference Code',
    stepMobile: 'You can make the payment by following these steps (for mobile payments)',
    capture: 'Press the "Save Image" button or take a screenshot',
    caseiOS: 'In case of iOS',
    clickSave: 'Take a screenshot, or press and hold the image, then tap the button',
    saveToApp: 'Save to Photos app',
    selectMenuScan: 'Open your banking app and select the Scan QR Code menu',
    selectScreenshot: 'Select the screenshot to scan the QR Code',
    refundAccount: 'Refund account',
    reasonCancel: 'Reason for order cancellation',
    reasonCancelPlaceholder: 'Please specify the reason for cancelling the order',
    textAddAccount: 'No account information found. Please add an account to receive refunds',
    accountName: 'Bank account name',
    textEnterName: 'Enter the bank account name',
    bankName: 'Bank name',
    textEnterBank: 'Please select your bank',
    accountNumber: 'Bank account number',
    textAccountNumber: 'Enter the bank account number',
    textcancel: 'Are you sure you want to cancel',
    textThisOrder: 'this order',
    cancelSuccess: 'The order has been successfully cancelled',
    textHaveCancel: 'You have successfully cancelled the order',
    receivedRefund: '*you will receive a refund at 10:00 PM.',
    byDate: 'by the date approved by the store',
    image: 'Image',
    productName: 'Product Name',
    thCreateAt: 'Create at',
    thTransactionNumber: 'Transaction number',
    thTransactionStatus: 'Transaction status',
    thReceipt: 'Receipt number',
    thPaymentIcon: 'Quotation',
    thInvoice: 'Invoice',
    thTransactionCode: 'Transaction code',
    thPaidDate: 'Paid date time',
    thPayment: 'Payment',
    thShop: 'Shop name',
    thTransportation: 'Transportation status',
    thNotReceipt: 'Not received',
    thReceived: 'Received',
    UnableCancel: 'Unable to cancel the order',
    textError: 'System error: Unable to reorder at this time',
    textCopy: 'Copied successfully',
    textConfirmReceipt: 'Confirmed inspection and receipt of the goods',
    textPlsSelect: 'Please select the order date',
    textNotSend: 'The item is being shipped',
    textSaveSuccess: 'Data saved successfully',
    textNotFoundInvoice: 'Tax invoice not found',
    textNotFree: 'Some items are not sufficient to qualify for the free gift, including: ',
    textContact: ' please contact the staff',
    textNoDataProduct: 'Some items no longer exist in the system, including: ',
    textNotEnough: 'Some items are insufficient for purchase, including: ',
    textUnablePayment: 'Unable to complete the payment',
    textForGeneral: 'For general customers only',
    textErrorPayment: 'There is a problem with the payment system',
    textPaymentError: 'ERROR: Unable to process payment',
    Fail: 'A system error has occurred. Please contact support.',
    Paymentincomplete: 'Payment Incomplete',
    textConfirmPay: 'Do you want to proceed with this action? Yes or No?',
    ConfirmReceipt: 'Delivery confirmed'
  },
  DetailOrderBuyer: {
    OrderDetails: 'Order Details',
    CancelOrder: 'Cancel Order',
    TrackCancellationRequest: 'Track Cancellation Request',
    ItemReceived: 'Item received',
    ProductReview: 'Product Review',
    OrderAgain: 'Order Again',
    UploadSlip: 'Upload Slip',
    OrderStatus: 'Order Status',
    OrderNumber: 'Order ID',
    NewOrder: 'New Order',
    PaymentSuccessful: 'Order Paid',
    EnterInformation: 'Enter Information',
    PreparingForShipment: 'Preparing to ship',
    OutForDelivery: 'Order Shipped Out',
    DeliverySuccessful: 'Delivery',
    OrdersWithCompletedPayment: 'Orders with completed payment',
    OrderHasBeenShipped: 'Order has been shipped',
    BuyerReviewedAndRequestedReturn: 'Buyer reviewed and requested a return',
    WaitingForRefundApproval: 'Waiting for refund approval',
    RefundSuccessful: 'Refund Successful',
    WaitingForPickup: 'Waiting for pickup',
    ReceivedSuccessfully: 'Received Successfully',
    ToReceived: 'To be received',
    DataNotFound: 'Data not found',
    ShippingMethod: 'Shipping Options',
    DocumentDetails: 'Document Details',
    QuotationNumber: 'Quotation Number',
    PurchaseOrderNumber: 'Purchase Order Number (PO NO.)',
    ReceiptNumber: 'Receipt Number',
    PRNumber: 'Purchase Requisition Number (PR) :',
    SONumber: 'Sale Order Number',
    PickupAddress: 'Pickup Address',
    ShippingAddress: 'Shipping Address',
    PickUpAtStore: 'Pick Up At Store',
    Remarks: 'Remarks',
    TaxInvoiceDeliveryAddress: 'Tax Invoice Address',
    OrderList: 'Order List',
    GeneralProducts: 'General Products',
    ServiceProducts: 'Service Products',
    FreeGift: 'Free Gift',
    Payment: 'Payment',
    PriceExcludingVAT: 'Price Excluding VAT',
    VAT: 'VAT',
    PriceIncludingVAT: 'Price Including VAT',
    StoreDiscountSummary: 'Store Discount Summary',
    ShippingDiscountSummary: 'Shipping Discount Summary',
    PointsDiscountSummary: 'Points Discount Summary',
    SystemDiscountSummary: 'System Discount Summary',
    SubTotal: 'Subtotal',
    ShippingFee: 'Shipping Fee',
    ShippingDiscountSeller: 'Shipping Discount Seller',
    ShippingDiscountNexgen: 'Shipping Discount Nexgen',
    TotalPriceAll: 'Total Price',
    PendingPayment: 'Pending Payment',
    PaymentSuccessfully: 'Payment Successful',
    PaymentMethod: 'Payment Method',
    YourOrderHasBeenCancelled: 'Your order has been cancelled',
    CancelledOrderDetails: 'Cancelled Order Details',
    Approver: 'Approver',
    Approve: 'Approve',
    NotApprove: 'Not Approve',
    WaitingForApproval: 'Waiting Approve',
    ApprovalDate: 'Approval Date',
    YourPaymentWasUnsuccessfulPleaseCheckYourPaymentAnd: 'Your payment was unsuccessful. Please check your payment and try again.',
    Payment2: 'Payment',
    YouHaveNotCompletedPayment: 'You have not completed your payment. Please make a payment via Thaidotcom Payment. You can proceed with the payment here',
    DiscountCoupon: 'Discount Coupon',
    ListOf: 'List of',
    Employees: 'Employees',
    Department: 'Department',
    Company: 'Company',
    PurchaseDate: 'Date',
    PaymentDate: 'Date',
    PreparingShipmentDate: 'Preparing Shipment Date',
    Date: 'On',
    WaitingConfirmation: 'Waiting for confirmation',
    ShippingDate: 'Shipping Date',
    ReturnDate: 'Return Date',
    CancelDate: 'Cancel Date',
    RefundDate: 'Refund Date',
    CashPayment: 'Cash Payment',
    ReceivingDate: 'Receiving Date',
    PRNumber2: 'Purchase Requisition Number (PR)',
    RemarksToSeller: 'Remarks to Seller',
    Baht: 'Baht',
    refundAccount: 'Refund account',
    reasonCancel: 'Reason for order cancellation',
    Cancel: 'Cancel',
    Confirm: 'Confirm',
    textAddAccount: 'No account information found. Please add an account to receive refunds',
    accountName: 'Bank account name',
    textEnterName: 'Enter the bank account name',
    BankName: 'Bank Name',
    textEnterBank: 'Please select your bank',
    BankAccountNumber: 'Bank account number',
    textAccountNumber: 'Enter the bank account number',
    BuyerName: 'Buyer Name',
    CancelOrderSuccess: 'You have successfully cancelled the order',
    CreateAt: 'Create at',
    Status: 'Status',
    SellerReason: "Seller's Reason",
    PleaseEnterCancellationReason: 'Please enter the reason for cancellation',
    ScanQRCodeForPayment: 'Scan QR Code for Payment',
    btnSaveImage: 'Save Image',
    TotalPaymentAmount: 'Total Payment Amount',
    referenceCode: 'Reference Code',
    CanMakePaymentByFollowingStep: 'You can make a payment by following these steps (for mobile payment)',
    capture: 'Press the "Save Image" button or take a screenshot',
    caseiOS: 'In case of iOS',
    clickSave: 'Take a screenshot, or press and hold the image, then tap the button',
    saveToApp: 'Save to Photos app',
    selectMenuScan: 'Open your banking app and select the Scan QR Code menu',
    selectScreenshot: 'Select the screenshot to scan the QR Code',
    selectPayment: 'Select Payment',
    installmentTerm: 'Installment Term',
    selectTerm: 'Select installment term',
    InstallmentPaymentNotAvailableBecause: 'Installment payment is not available because the',
    Min: 'Min',
    Max: 'Max',
    RequiredAmountHasNotBeenMet: 'required amount has not been met',
    ComfirmModal: 'Do you want to proceed with this transaction? Yes or No',
    textcancel: 'Are you sure you want to cancel',
    textThisOrder: 'this order',
    cancelSuccess: 'The order has been successfully cancelled',
    textHaveCancel: 'You have successfully cancelled the order',
    receivedRefund: '*you will receive a refund at 10:00 PM.',
    byDate: 'by the date approved by the store',
    SelectImage: 'Select Image',
    FileExtensions: 'File Extensions: .JPEG, .PNG, .JPG',
    ChangeShippingAddress: 'Change Shipping Address',
    Edit: 'Edit',
    Delete: 'Delete',
    Notation: 'Notation',
    NoAddress: 'No shipping address',
    AddAddress: 'Add New Address',
    DeleteShippingAddress: 'Delete Shipping Address',
    textDeleteShippingAddress: 'Do you want to proceed with this transaction? Yes or No',
    ProductDetails: 'Product Details',
    UnitPrice: 'Unit Price',
    Quantity: 'Quantity',
    TotalPrice: 'Total Price',
    Amount: 'Amount',
    validateAccountNumber1: 'Please enter account number',
    NoInstallment: 'No Installment',
    Month: 'Month',
    PricePerMonth: 'Price per month',
    ShippingAddressChanged: 'Shipping address changed successfully',
    AddShippingAddress: 'Add Shipping Address',
    EditShippingAddress: 'Edit Shipping Address',
    FailedToDeleteTheShippingAddress: 'Failed to delete the shipping address',
    AllowOnlyImage: 'Only image files',
    UploadSlipSuccess: 'Upload Slip Success',
    CanNotUploadSlip: 'Can not upload slip',
    SystemErrorUnableToPlaceTheOrderAgain: 'System error: Unable to place the order again',
    SomeItemsRequirementsForFreeGift: 'Some items do not meet the requirements for the free gift, including: ',
    PleaseContactCustomerSupport: ' Please contact customer support',
    PleaseRemoveTheseItemsFromYourCart: 'Please remove these items from your cart',
    ShoppingCartNotFound: 'Shopping Cart Not Found',
    SomeItemsAreNoLongerAvailableInTheSystem: 'Some items are no longer available in the system:',
    PleaseRemoveThemFromYourCart: 'Please remove them from your cart',
    SomeItemsAreNotAvailableInSufficientQuantityForPurchase: 'Some items are not available in sufficient quantity for purchase:',
    TheTotalPriceMustBeGreaterThanZerobaht: 'The total price must be greater than 0 baht',
    PleaseEnterDiscountThatDoesNotExceedTheProductPricel: 'Please enter a discount that does not exceed the product price',
    UnableCancelTheOrder: 'Unable to cancel the order',
    PendingPayments: 'Pending Payment',
    ItemDelivered: 'Item delivered',
    SellerPreparingTheParcel: 'Seller preparing the parcel',
    SellerPreparingTheParcel2: 'Seller preparing the parcel',
    pickedUp: 'Parcel Registered',
    shippingInprogress: 'Shipping in Progress'
  },
  reportOrderAffiliate: {
    PageTitle: 'Order Report',
    OrderID: 'Order ID',
    OrderDate: 'Order Date',
    OrderStatus: 'Order Status',
    SearchOrderDate: 'Search by Order Date',
    Cancel: 'Cancel',
    Confirm: 'Confirm',
    Clear: 'Clear',
    Search: 'Search',
    Total: 'Total',
    Items: 'items',
    NoData: 'No order report found',
    View: 'View',
    NoOrders: 'You have no order reports yet',
    OrderDetailTitle: 'Order Details',
    ProductName: 'Product Name',
    ProductType: 'Product Type',
    ProductAttribute: 'Product Attribute',
    ProductPrice: 'Price',
    Quantity: 'Quantity (pcs)',
    CommissionPercent: 'Commission (%)',
    CommissionBaht: 'Commission (Baht)',
    CommissionValue: 'Commission Amount (Baht)',
    TaxDeducted: 'Tax Deducted (Baht)',
    NetCommission: 'Net Commission (Baht)',
    OrderStatusTitle: 'Order Status',
    TotalCommission: 'Total Commission (Baht)',
    Detail: 'Details',
    All: 'All',
    Success: 'Payment Successful',
    NotPaid: 'Not Paid',
    CancelStatus: 'Order Cancelled',
    Fail: 'Payment Failed',
    Pending: 'Pending',
    Approve: 'Billed',
    Credit: 'Credit Payment',
    DataIncomplete: 'Incomplete Data',
    Refund: 'Refunded'
  },
  DashboardAffiliate: {
    titleWeb: 'Dashboard',
    titleMobile: 'Dashboard Affiliate',
    yearly: 'Yearly',
    monthly: 'Monthly',
    daily: 'Daily',
    year: 'Year',
    month: 'Month',
    day: 'Day',
    display: 'Display',
    titleSelectMonth: 'Select Month',
    formatDate: 'DD/MM/YYYY',
    btnCancel: 'Cancel',
    btnConfirm: 'Confirm',
    btnSave: 'Save',
    filter: 'Filter',
    clsValue: 'Clear',
    selectShop: 'Select Shop',
    allLinks: 'All Links',
    incomeInfo: 'Income Information',
    commission: 'Commission List per Link',
    topClick: 'Top 10 Most Clicked Products',
    topSeller: 'Top 10 Best Seller Products',
    clickInfo: 'Click Information',
    incomeGraph: 'Income Graph',
    theCommission: 'Commission',
    clickGraph: 'Click Graph',
    clicking: 'Clicking',
    totalCommission: 'Total Commissions',
    unit: 'baht',
    totalClicks: 'Total Clicks',
    unitClick: 'times',
    unitOrder: 'Items',
    noClick: 'No click records',
    numberClicks: 'Number of Clicks',
    totalPeriods: 'Total Across All Periods',
    noPurchase: 'No purchase records',
    commissionFee: 'Commission Fee',
    items: 'items',
    productList: 'Product List',
    AllProductsList: 'All Product List',
    productType: 'Product Type',
    attribute: 'Product Attributes',
    price: 'Price',
    commissionIncome: 'Commission Income',
    orderDetail: 'Order Details',
    productName: 'Product Name',
    quantity: 'Quantity',
    taxDeduction: 'Tax Deduction',
    Jan: 'January',
    Feb: 'February',
    Mar: 'March',
    Apr: 'April',
    May: 'May',
    Jun: 'June',
    Jul: 'July',
    Aug: 'August',
    Sep: 'September',
    Oct: 'October',
    Nov: 'November',
    Dec: 'December',
    link: 'Link',
    status: 'Status',
    thPrice: 'Price (baht)',
    thComProduct: 'Product Commission',
    thComPrice: 'Commission (baht)',
    attributeList: 'Attribute List',
    quantitySold: 'Quantity Sold (items)',
    influPayment: 'Commission Income (baht)',
    textSeries: 'Commission Amount',
    textSeriesClick: 'Number of Clicks',
    allShop: 'All',
    typeLanguage: 'en-EN',
    linkStatusAc: 'Active',
    linkStatusIn: 'In Active',
    excelCommission: 'Commission Table',
    excelTopProducts: 'Top 10 Products',
    textLinkCopy: 'Link copied successfully'
  },
  ProductCard: {
    AlreadySold: ' ',
    Sold: 'Sold',
    OutOfStock: 'Out of Stock',
    ContactSupport: 'Contact Support'
  },
  ModalMenu: {
    titleSeller: 'My Company',
    textSearchSeller: 'Search by Company Name',
    unitOrder: 'Items',
    addCompany: 'Add Company',
    noData: 'No company found in the table',
    noResults: 'Company not found in the table',
    numberRows: 'Number of rows',
    selectCompany: 'Select Company',
    shopList: 'Shop List',
    myShop: 'My Shops',
    textSearchName: 'Search by Shop Name',
    noDataShop: 'No shop found in the table',
    noResultsShop: 'Shop not found in the table',
    selectShop: 'Select Shop',
    positionCompany: 'Company Position',
    myPosition: "My company's position",
    noDataPosition: 'No position records found in the table',
    noResultsPosition: 'No job positions found in the table',
    list: 'List',
    selectPosition: 'Select Position',
    listBusiness: 'List of Business',
    myBusiness: 'My Business',
    regisBusiness: 'Register Business',
    noDataCor: 'My business is not listed in the table',
    noResultsCor: 'No business found in the table',
    selectCor: 'Select Business',
    textSearchCor: 'Search by business name',
    saleOrderFormat: 'Sale Order format',
    orderForCus: '* Ordering for the customer',
    orderInet: '* Internal ordering at INET',
    general: 'General',
    externalCus: '* Orders for external customers (companies or individuals)',
    listPartner: 'List of partners',
    myPartners: 'My partners',
    textSearchPartners: 'Search by partner company name',
    noDataPartner: 'No partners of my company are found in the table',
    noResultsPartner: 'No partner company listed in the table',
    selectPartner: 'Select Partner',
    no: 'No.',
    companyName: 'Company Name',
    shopName: 'Shop Name',
    partnerName: 'Partner Name',
    nameCor: 'Name of business',
    notPermission: 'You do not have this permission in the system',
    textContact: ' please contact the staff',
    noID: 'No Business ID',
    createCompany: 'Please create a company',
    createComSuccess: 'Company created successfully',
    noDataYourCor: 'No information found for your legal entity',
    role: 'role : ',
    company: '   Company : ',
    plsLogin: 'Please log in again',
    channelNot: 'Channel not found. Please check and try again',
    shopNot: 'Shop not found. Please check and try again',
    employNot: 'Employee information not found. Please check and try again',
    shopNo: 'Shop not found',
    noAccess: 'Not access this function',
    noBus: 'Your business entity information was not found',
    noRegis: 'You have not yet registered a business account. Would you like to sign up?',
    regis: 'Register',
    myPurchaser: 'My Purchaser',
    selectPurchaser: 'Select Purchaser',
    textSearchPurchaser: 'Search by Purchaser Name'
  },
  ProductCardFlashSale: {
    Sold: 'Sold'
  },
  GroupShopHomepage: {
    TitleAllShops: 'All Shops',
    TitleShop: 'Shop',
    NoProduct: 'No Products',
    SearchByShopName: 'Search by Shop Name',
    Province: 'Province',
    Category: 'Category',
    AllFound: 'All Found',
    NoShops: 'No Shops',
    NoItemsInGroup: 'This Shop Group Has No Items Yet',
    ShopNotFound: 'Shop Not Found',
    ShopSearchNotFound: 'No Shops Found. Please Check Again.',
    Home: 'Home',
    ShopType: 'Shop Type',
    Product: 'Product',
    Shop: 'Shop',
    All: 'All',
    CopyLinkSuccess: 'Link Copied Successfully'
  },
  CheckName: {
    Save: 'Save',
    Name: 'Name',
    EnterName: 'Enter Name',
    LastName: 'Last Name',
    EnterLastName: 'Enter Last Name',
    Phone: 'Phone Number',
    EnterPhone: 'Enter Phone Number',
    SaveData: 'Save Data',
    YouHaveMadeChanges: 'You have made changes to your personal information',
    DoYouWantProceedTransaction: 'Do you want to proceed with this transaction? Yes or No',
    Cancel: 'Cancel',
    Confirm: 'Confirm',
    SavedSuccessfully: 'Saved Successfully',
    SuccessfullyUpdatedYourInformation: 'You have successfully updated your personal information',
    
  }
}
