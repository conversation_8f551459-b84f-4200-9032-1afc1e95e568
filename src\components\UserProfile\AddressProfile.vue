<template>
  <v-container class="pa-2">
    <!-- Modal Comfirm before Delete -->
    <v-dialog v-model="deleteDialog" :style="MobileSize ? 'z-index: 16000004' : ''" persistent width="424">
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="deleteDialog = !deleteDialog"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4" v-if="TypeAddress === 'address'"><b>ลบที่อยู่จัดส่ง</b></p>
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4" v-if="TypeAddress === 'invoice'"><b>ลบที่อยู่ใบกำกับภาษี</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="deleteDialog = !deleteDialog">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="deleteAddress()" v-if="TypeAddress === 'address'">ตกลง</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="DeleteInvoice()" v-if="TypeAddress === 'invoice'">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Modal Success Delete Address -->
    <v-dialog v-model="ModalSuccessDelete" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="CloseModalSuccessDelete()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ลบที่อยู่จัดส่งเสร็จสิ้น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการลบที่อยู่จัดส่งเรียบร้อย</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="CloseModalSuccessDelete()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogDeleteSuccessTax" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogDeleteSuccessTax = !dialogDeleteSuccessTax"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 20px; line-height: 24px; color: #333333;" class="my-4"><b>ลบที่อยู่เสร็จสิ้น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการลบที่อยู่ในการออกใบกำกับภาษีเรียบร้อย</span><br/>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center" v-show="!MobileSize">
            <v-btn width="156" height="38" dark rounded color="#27AB9C" @click="dialogDeleteSuccessTax = !dialogDeleteSuccessTax">ตกลง</v-btn>
            </v-row>
            <v-row dense justify="center" v-show="MobileSize">
            <v-btn height="38" dark rounded color="#27AB9C" :style="{ flex: '1' }" @click="dialogDeleteSuccessTax = !dialogDeleteSuccessTax">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-card width="100%" min-height="700" class="mb-4" elevation="0">
      <v-card-text>
        <!-- section ที่อยู่ในการจัดส่งสินค้า -->
        <div>
          <v-row dense>
            <v-col cols="12" class="d-flex">
              <span style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" class="mr-auto pt-2" v-if="!MobileSize">ที่อยู่ในการจัดส่งสินค้า</span>
              <span style="font-weight: bold; font-size: 16px; line-height: 32px; color: #333333;" class="mr-auto pt-2" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUser()">mdi-chevron-left</v-icon> ที่อยู่ในการจัดส่งสินค้า</span>
              <v-btn v-if="userdetail.length === 1 && userdetail[0].zip_code === ''" color="#27AB9C" rounded width="126" height="40" dark @click="editAddress(userdetail[0])" class="ml-auto"><v-icon left>mdi-plus-circle-outline</v-icon>เพิ่มที่อยู่</v-btn>
              <v-btn v-else-if="userdetail.length !== 10" color="#27AB9C" rounded width="126" height="40" dark @click="addAddress()" class="ml-auto"><v-icon left>mdi-plus-circle-outline</v-icon>เพิ่มที่อยู่</v-btn>
              <span v-else class="pt-1" style="font-size: 16px; font-weight: 500; color: #989898; line-height: 32px;"><v-icon size="20" color="#989898" class="pr-2">mdi-information-outline</v-icon>ไม่สามารถเพิ่มที่อยู่ได้ (เพิ่มได้สูงสุด 10 รายการ)</span>
            </v-col>
          </v-row>
          <!-- <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">ที่อยู่ในการจัดส่งสินค้า</v-card-title>
          <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUser()">mdi-chevron-left</v-icon> ที่อยู่ในการจัดส่งสินค้า</v-card-title>
          <v-row dense class="mt-2 mb-7 mr-4">
            <v-col cols="12" md="12" class="px-6">
              <v-row justify="end">
                <v-btn color="#27AB9C" dark @click="addAddress()"><v-icon left>mdi-plus-circle-outline</v-icon>เพิ่มที่อยู่ใหม่</v-btn>
              </v-row>
            </v-col>
          </v-row> -->
          <v-row dense class="mt-9" v-if="userdetail.length !== 0">
            <v-col cols="12" class="pt-2" v-for="(item, index) in userdetail" :key="index">
              <v-card min-height="149" elevation="0" outlined :style="item.default_address === 'Y' ? 'border-color: #27AB9C' : 'border-color: #C4C4C4'">
              <!-- <v-card elevation="0" outlined style="border-color: #C4C4C4;"> -->
                <v-row class="pa-4">
                  <v-col cols="12" md="12">
                    <v-row dense>
                      <!-- action -->
                      <v-col cols="12" md="12" class="d-flex">
                        <div class="mr-auto">
                          <span style="font-weight: 700; font-size: 18px;">{{ item.first_name }} {{ item.last_name }}</span> <span style="color: #EBEBEB;">|</span> <span style="color: #333333; font-weight: 700; font-size: 18px;">{{ item.phone }}</span>
                        </div>
                        <div class="ml-auto">
                          <div v-if="!MobileSize">
                            <v-btn color="#27AB9C" height="20" width="56" text elevation="0" @click="editAddress(item)"><v-icon color="#27AB9C" size="20">mdi-pencil-outline</v-icon><span v-if="!MobileSize">แก้ไข</span></v-btn>
                            <v-btn v-if="userdetail.length !== 1" height="20" width="41" text :disabled="item.default_address === 'Y'" color="#ff0303" elevation="0" @click="openDeleteDialog(item, 'address')"><v-icon size="20" color="#ff0303">mdi-delete-outline</v-icon><span v-if="!MobileSize">ลบ</span></v-btn>
                          </div>
                          <v-row dense v-else>
                            <v-col cols="6">
                              <v-btn outlined color="#27AB9C" height="30" :width="!MobileSize ? '56' : '30'" :icon="!MobileSize ? false : true" elevation="0" @click="editAddress(item)"><v-icon color="#27AB9C" size="20">mdi-pencil-outline</v-icon><span v-if="!MobileSize">แก้ไข</span></v-btn>
                            </v-col>
                            <v-col cols="6">
                              <v-btn outlined v-if="userdetail.length !== 1" :icon="!MobileSize ? false : true" height="30" :width="!MobileSize ? '41' : '30'" :disabled="item.default_address === 'Y'" color="#ff0303" elevation="0" @click="openDeleteDialog(item, 'address')"><v-icon size="20" color="#ff0303">mdi-delete-outline</v-icon><span v-if="!MobileSize">ลบ</span></v-btn>
                            </v-col>
                          </v-row>
                        </div>
                        <!-- <v-row justify="end" class="mt-1 mr-1">
                          <v-btn color="#F2F2F2" fab x-small elevation="0" @click="editAddress(item)"><v-icon color="#A1A1A1">mdi-pencil</v-icon></v-btn>
                          <v-btn :disabled="userdetail.length === 1 || item.default_address === 'Y'" color="#F2F2F2" fab x-small class="ml-2" elevation="0" @click="openDeleteDialog(item)"><v-icon color="#A1A1A1">mdi-delete-outline</v-icon></v-btn>
                        </v-row> -->
                      </v-col>
                      <!-- ชื่อ-นามสกุล -->
                      <!-- <v-col cols="12" md="12" class="pl-5 pb-0">
                        <span style="font-weight: 700; font-size: 16px;">{{ item.first_name }} {{ item.last_name }}</span>
                      </v-col> -->
                      <!-- หมายเลขโทรศัพท์ -->
                      <!-- <v-col cols="12" md="12" class="pl-5 pb-0">
                        <span style="color: #333333; fo nt-weight: 400; font-size: 14px;">{{ item.phone }}</span>
                      </v-col> -->
                      <!-- ที่อยู่ -->
                      <v-col cols="12" md="12">
                        <span v-snip="3" style="color: #333333; font-weight: 400; font-size: 14px;">{{ item.detail }} {{ item.sub_district }} {{ item.district }} {{ item.province }} {{ item.zip_code }}</span>
                        <span v-if="item.note_address">หมายเหตุ : {{ item.note_address }}</span>
                      </v-col>
                      <!-- ตั้งค่าที่อยู่เริ่มต้น -->
                      <v-col cols="12" md="12">
                        <v-radio-group v-model="item.default_address">
                          <v-radio
                            color="#27AB9C"
                            label="ตั้งค่าเป็นที่อยู่เริ่มต้น"
                            value="Y"
                            :disabled="item.default_address === 'Y' ? true : false"
                            @click="setDefaultAdress(item)"
                            style="color: #333333"
                          ></v-radio>
                        </v-radio-group>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
          <v-row justify="center" dense class="pt-12" v-else>
            <v-col cols="12" class="pt-12" align="center">
              <v-img :src="require('@/assets/ImageINET-Marketplace/ICONProfile/NotAddress.png')" height="100%" width="100%" max-height="143" max-width="318" class="mb-6"></v-img>
              <p style="font-size: 18px; font-weight: 600; line-height: 26px; color: #636363;">ยังไม่มีที่อยู่ของคุณ</p>
              <span style="font-size: 16px; font-weight: 400; line-height: 26px; color: #9A9A9A;">สามารถกด</span><span style="font-size: 18px; font-weight: 600; line-height: 26px; color: #1B5DD6; text-decoration: underline;">เพิ่มที่อยู่</span><span style="font-size: 16px; font-weight: 400; line-height: 26px; color: #9A9A9A;">เพื่อใช้ในการจัดส่งสินค้า</span>
            </v-col>
          </v-row>
        </div>
        <!-- section ที่อยู่ในการออกใบกำกับภาษี  -->
        <div :class="MobileSize ? 'pt-4' : IpadSize ? 'pt-6' : 'pt-8'">
          <v-row dense>
            <v-col cols="12" class="d-flex">
              <span style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" class="mr-auto pt-2" v-if="!MobileSize">ที่อยู่ในการออกใบกำกับภาษี</span>
              <span style="font-weight: bold; font-size: 16px; line-height: 32px; color: #333333;" class="mr-auto pt-2" v-else>ที่อยู่ในการออกใบกำกับภาษี</span>
              <v-btn v-if="userInvoiceAddress.length !== 5" color="#27AB9C" rounded width="126" height="40" dark @click="manageTaxAddress('add', '')" class="ml-auto"><v-icon left>mdi-plus-circle-outline</v-icon>เพิ่มที่อยู่</v-btn>
              <span v-else class="pt-1" style="font-size: 16px; font-weight: 500; color: #989898; line-height: 32px;"><v-icon size="20" color="#989898" class="pr-2">mdi-information-outline</v-icon>ไม่สามารถเพิ่มที่อยู่ได้ (เพิ่มได้สูงสุด 5 รายการ)</span>
            </v-col>
          </v-row>
          <v-row dense class="mt-9" v-if="userInvoiceAddress.length !== 0">
            <v-col cols="12" class="pt-2" v-for="(item, index) in userInvoiceAddress" :key="index">
              <v-card min-height="149" elevation="0" outlined :style="item.default_invoice === 'Y' ? 'border-color: #27AB9C' : 'border-color: #C4C4C4'">
              <!-- <v-card elevation="0" outlined style="border-color: #C4C4C4;"> -->
                <v-row class="pa-4">
                  <v-col cols="12" md="12">
                    <v-row dense>
                      <!-- action -->
                      <v-col cols="12" md="12" class="d-flex">
                        <div class="mr-auto">
                          <span style="font-weight: 700; font-size: 18px;">{{ item.name }}</span><br v-if="MobileSize"> <span style="color: #EBEBEB;" v-if="!MobileSize">|</span> <span style="font-size: 18px; font-weight: 600; color: #27AB9C;">{{ item.tax_type === 'Personal' ? 'บุคคลธรรมดา' : 'นิติบุคคล' }}</span><br/>
                          <span v-if="item.tax_type !== 'Personal'" style="font-weight: 400; font-size: 18px;">รหัสสาขา : {{ item.branch_id !== '' ? item.branch_id : '-' }}</span><br/>
                          <span style="color: #333333; font-weight: 400; font-size: 18px; line-height: 32px;">เลขประจำตัวผู้เสียภาษี: </span><span style="color: #333333; font-weight: 700; font-size: 18px; line-height: 32px;">{{ item.tax_id }}</span>
                        </div>
                        <div class="ml-auto">
                          <div v-if="!MobileSize">
                            <v-btn color="#27AB9C" height="20" :width="!MobileSize ? '56' : '20'" :icon="!MobileSize ? false : true" text elevation="0" @click="manageTaxAddress('edit', item)"><v-icon color="#27AB9C" size="20">mdi-pencil-outline</v-icon><span v-if="!MobileSize">แก้ไข</span></v-btn>
                            <v-btn v-if="userInvoiceAddress.length !== 1" :icon="!MobileSize ? false : true" height="20" :width="!MobileSize ? '41' : '20'" text :disabled="item.default_invoice === 'Y'" color="#9A9A9A" elevation="0" @click="openDeleteDialog(item, 'invoice')"><v-icon size="20" color="#9A9A9A">mdi-delete-outline</v-icon><span v-if="!MobileSize">ลบ</span></v-btn>
                          </div>
                          <v-row dense v-else>
                            <v-col cols="6">
                              <v-btn outlined color="#27AB9C" height="30" :width="!MobileSize ? '56' : '30'" :icon="!MobileSize ? false : true" elevation="0" @click="manageTaxAddress('edit', item)"><v-icon color="#27AB9C" size="20">mdi-pencil-outline</v-icon><span v-if="!MobileSize">แก้ไข</span></v-btn>
                            </v-col>
                            <v-col cols="6">
                              <v-btn outlined v-if="userInvoiceAddress.length !== 1" :icon="!MobileSize ? false : true" height="30" :width="!MobileSize ? '41' : '30'" :disabled="item.default_invoice === 'Y'" color="#9A9A9A" elevation="0" @click="openDeleteDialog(item, 'invoice')"><v-icon size="20" color="#9A9A9A">mdi-delete-outline</v-icon><span v-if="!MobileSize">ลบ</span></v-btn>
                            </v-col>
                          </v-row>
                        </div>
                        <!-- <v-row justify="end" class="mt-1 mr-1">
                          <v-btn color="#F2F2F2" fab x-small elevation="0" @click="editAddress(item)"><v-icon color="#A1A1A1">mdi-pencil</v-icon></v-btn>
                          <v-btn :disabled="userdetail.length === 1 || item.default_address === 'Y'" color="#F2F2F2" fab x-small class="ml-2" elevation="0" @click="openDeleteDialog(item)"><v-icon color="#A1A1A1">mdi-delete-outline</v-icon></v-btn>
                        </v-row> -->
                      </v-col>
                      <!-- ชื่อ-นามสกุล -->
                      <!-- <v-col cols="12" md="12" class="pl-5 pb-0">
                        <span style="font-weight: 700; font-size: 16px;">{{ item.first_name }} {{ item.last_name }}</span>
                      </v-col> -->
                      <!-- หมายเลขโทรศัพท์ -->
                      <!-- <v-col cols="12" md="12" class="pl-5 pb-0">
                        <span style="color: #333333; font-weight: 400; font-size: 14px;">{{ item.phone }}</span>
                      </v-col> -->
                      <!-- ที่อยู่ -->
                      <v-col cols="12" md="12">
                        <span v-snip="3" style="color: #333333; font-weight: 400; font-size: 14px;">{{ item.address }} {{ item.sub_district }} {{ item.district }} {{ item.province }} {{ item.postal_code }}</span>
                      </v-col>
                      <!-- ตั้งค่าที่อยู่เริ่มต้น -->
                      <v-col cols="12" md="12">
                        <v-radio-group v-model="item.default_invoice">
                          <v-radio
                            color="#27AB9C"
                            label="ตั้งค่าเป็นที่อยู่เริ่มต้น"
                            value="Y"
                            :disabled="item.default_invoice === 'Y' ? true : false"
                            @click="setDefaultAddressInvoice(item)"
                            style="color: #333333"
                          ></v-radio>
                        </v-radio-group>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
          <v-row justify="center" dense class="pt-6" v-else>
            <v-col cols="12" class="pt-6" align="center">
              <v-img :src="require('@/assets/ImageINET-Marketplace/ICONProfile/NotAddress.png')" height="100%" width="100%" max-height="143" max-width="318" class="mb-6"></v-img>
              <p style="font-size: 18px; font-weight: 600; line-height: 26px; color: #636363;">ยังไม่มีที่อยู่ของคุณ</p>
              <span style="font-size: 16px; font-weight: 400; line-height: 26px; color: #9A9A9A;">สามารถกด</span><span style="font-size: 18px; font-weight: 600; line-height: 26px; color: #1B5DD6; text-decoration: underline;">เพิ่มที่อยู่</span><span style="font-size: 16px; font-weight: 400; line-height: 26px; color: #9A9A9A;">เพื่อใช้ในการออกใบกำกับภาษี</span>
            </v-col>
          </v-row>
        </div>
      </v-card-text>
    </v-card>
    <ModalTaxAddress ref="ModalTaxAddress" :title="titleTaxAddress" :page="page"/>
    <EditModalAddress :EditAddressDetail="EditAddressDetail" :title="titleAddress" :page="page" />
  </v-container>
</template>
<script>
import { Decode, Encode } from '@/services'
export default {
  components: {
    // ModalAddress: () => import('../Modal/AddressProfile'),
    ModalTaxAddress: () => import('@/components/Cart/ModalAddress/TaxInvoiceAddress'),
    EditModalAddress: () => import('../Modal/EditAddressProfile.vue')
  },
  data () {
    return {
      dialogDeleteSuccessTax: false,
      OldId: '',
      deleteAddressInvoiceData: [],
      TypeAddress: '',
      titleTaxAddress: '',
      ModalSuccessDelete: false,
      deleteDialog: false,
      EditAddressDetail: [],
      AddressTex: '',
      userdetail: [],
      userInvoiceAddress: [],
      hidden: false,
      userShop: [],
      oneUserType: '',
      BindAccountdialog: false,
      CheckDataTex: false,
      usernameOne: '',
      passwordOne: '',
      telnumber: '',
      lazy: false,
      radioGroup: '',
      titleAddress: '',
      deleteAdressData: '',
      page: '',
      Rules: {
        username: [
          v => !!v || 'กรุณากรอกชื่อผู้ใช้',
          v => /^[A-Za-z0-9]+$/.test(v) || 'กรอกได้เฉพาะตัวอักษรและตัวเลขเท่านั้น'
        ],
        password: [
          v => !!v || 'กรุณากรอกรหัสผ่าน'
        ]
      }
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/addressProfileMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/addressProfile' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  mounted () {
    this.$EventBus.$on('EditAddressComplete', (data) => { this.getAddress(data) })
    this.$EventBus.$on('getAddressTex', this.getAddressTex)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getAddressTex')
    })
  },
  async created () {
    this.$EventBus.$emit('changeNavAccount')
    this.$EventBus.$emit('checkpage', this.$route.name)
    this.$EventBus.$emit('main', this.$route.name)
    if (localStorage.getItem('oneData') === null) {
      localStorage.removeItem('oneData')
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (onedata.user !== undefined) {
        this.checkAddress()
        this.getAddress()
        this.getAddressTex()
        this.oneUserType = onedata.user.type_user
      } else {
        localStorage.removeItem('oneData')
        this.$router.push({ path: '/' }).catch(() => {})
      }
    }
  },
  beforeDestroy () {
    this.$EventBus.$off('EditAddressComplete')
  },
  methods: {
    manageTaxAddress (key, data) {
      this.page = 'userdetail'
      if (key === 'add') {
        this.titleTaxAddress = 'เพิ่มที่อยู่ในการออกใบกำกับภาษี'
        this.$refs.ModalTaxAddress.open()
      } else if (key === 'edit') {
        const editData = {
          id: data.id,
          user_id: data.user_id,
          company_id: data.company_id,
          tax_type: data.tax_type,
          tax_id: data.tax_id,
          buyer_one_id: data.buyer_one_id,
          name: data.name,
          email: data.email,
          address: data.address,
          postal_code: data.postal_code,
          province: data.province,
          district: data.district,
          sub_district: data.sub_district,
          role: data.role,
          default_invoice: data.default_invoice,
          branch_id: data.branch_id
        }
        // console.log(editData)
        localStorage.setItem('InvoiceAddress', Encode.encode(editData))
        this.titleTaxAddress = 'แก้ไขที่อยู่ในการออกใบกำกับภาษี'
        this.$refs.ModalTaxAddress.open()
      }
      //   var address = this.userdetailAddress.filter(e => e.default_address === 'default')
      //   this.$refs.ModalTaxAddress.open(key, data, address[0], '', 'userProfile')
    },
    async setDefaultAddressInvoice (item) {
      const data = {
        old_id: this.OldId !== '' ? this.OldId : 0,
        new_id: item.id
      }
      await this.$store.dispatch('actionsSetDefaultInvoice', data)
      var res = await this.$store.state.ModuleUser.stateSetDefaultInvoice
      if (res.result === 'SUCCESS') {
        this.$swal.fire({ icon: 'success', title: '<h5>ตั้งค่าเป็นที่อยู่เริ่มต้นแล้ว</h5>', showConfirmButton: false, timer: 1500 })
      } else {
        this.$swal.fire({ icon: 'error', title: '<h5>ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ</5>', showConfirmButton: false, timer: 1500 })
      }
      this.getAddressTex()
    },
    async DeleteInvoice () {
      this.$store.commit('openLoader')
      this.deleteDialog = false
      const data = {
        invoice_id: this.deleteAdressData.id
      }
      await this.$store.dispatch('actionsDeleteInvoice', data)
      var res = await this.$store.state.ModuleUser.stateDeleteInvoice
      if (res.result === 'SUCCESS') {
        // this.$swal.fire({ icon: 'success', title: 'ลบที่อยู่สำเร็จ', showConfirmButton: false, timer: 1500 })
        this.dialogDeleteSuccessTax = true
        this.getAddressTex()
      }
      this.$store.commit('closeLoader')
    },
    CloseModalSuccessDelete () {
      this.getAddress()
      this.ModalSuccessDelete = false
    },
    backtoUser () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
    },
    async checkAddress () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('GetUserAddress', data)
      this.propsAddress = await this.$store.state.ModuleManageShop.GetUserAddress
      localStorage.setItem('AddressData', Encode.encode(this.propsAddress[0].address_data))
      if (dataRole.role === 'ext_buyer') {
        if (this.propsAddress[0].address_data[0].status === 'N') {
          localStorage.setItem('AddressUserDetail', Encode.encode(this.propsAddress[0].address_data[0]))
          this.EditAddressDetail = this.propsAddress[0].address_data[0]
          this.titleAddress = 'แก้ไขอยู่ในการจัดส่งสินค้า'
          this.page = 'addressProfile'
          this.$EventBus.$emit('EditModalAddress')
        }
      }
    },
    async getAddress () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionListUserAddress')
      var userdetail = await this.$store.state.ModuleUser.stateListUserAddress
      if (userdetail.message === 'List user address success') {
        this.$store.commit('closeLoader')
        this.userdetail = [...userdetail.data]
        this.userdetail.forEach(element => {
          var x = element.phone.replace(/\D/g, '').match(/(\d{0,3})(\d{0,3})(\d{0,4})/)
          this.telnumber = !x[2] ? x[1] : x[1] + '-' + x[2] + (x[3] ? '-' + x[3] : '')
          if (element.first_name === '' && element.last_name === '') {
            this.fullname = '-'
          } else {
            this.fullname = element.first_name + ' ' + element.last_name
          }
        })
      } else {
        this.$store.commit('closeLoader')
        this.userdetail = []
        if (userdetail.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        }
      }
    },
    openDeleteDialog (item, type) {
      this.deleteDialog = !this.deleteDialog
      this.TypeAddress = type
      this.deleteAdressData = item
    },
    async deleteAddress () {
      this.$store.commit('openLoader')
      this.deleteDialog = false
      const data = {
        id: this.deleteAdressData.id
      }
      await this.$store.dispatch('actionDeleteUserAddress', data)
      const res = await this.$store.state.ModuleUser.stateDeleteUserAddress
      if (res.message === 'Delete user address success') {
        // this.$swal.fire({ icon: 'success', title: 'ลบที่อยู่ในการจัดส่งสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.$store.commit('closeLoader')
        this.ModalSuccessDelete = true
      } else {
        this.$swal.fire({ icon: 'error', title: 'ลบที่อยู่ในการจัดส่งสินค้าไม่สำเร็จ', showConfirmButton: false, timer: 1500 })
      }
    },
    addAddress () {
      const val = {
        building_name: '',
        default_address: '',
        detail: '',
        district: '',
        email: '',
        first_name: '',
        floor: '',
        house_no: '',
        id: '',
        last_name: '',
        moo_ban: '',
        moo_no: '',
        order_number: '',
        phone: '',
        province: '',
        room_no: '',
        soi: '',
        status: '',
        street: '',
        sub_district: '',
        user_id: '',
        yaek: '',
        zipcode: ''
      }
      localStorage.setItem('AddressUserDetail', Encode.encode(val))
      this.EditAddressDetail = val
      this.titleAddress = 'เพิ่มที่อยู่ในการจัดส่งสินค้า'
      this.page = 'addressProfile'
      this.$EventBus.$emit('EditModalAddress')
    },
    editAddress (val) {
      this.$store.commit('openLoader')
      const editData = {
        building_name: val.building_name,
        default_address: val.default_address,
        detail: val.detail,
        district: val.district,
        email: val.email,
        first_name: val.first_name,
        floor: val.floor,
        house_no: val.house_no,
        id: val.id,
        last_name: val.last_name,
        moo_ban: val.moo_ban,
        moo_no: val.moo_no,
        order_number: val.order_number,
        phone: val.phone,
        province: val.province,
        room_no: val.room_no,
        soi: val.soi,
        status: val.status,
        street: val.street,
        sub_district: val.sub_district,
        user_id: val.user_id,
        yaek: val.yaek,
        zipcode: val.zip_code,
        note_address: val.note_address
      }
      localStorage.setItem('AddressUserDetail', Encode.encode(editData))
      this.EditAddressDetail = editData
      this.titleAddress = 'แก้ไขอยู่ในการจัดส่งสินค้า'
      this.page = 'addressProfile'
      this.$EventBus.$emit('EditModalAddress')
    },
    async setDefaultAdress (item) {
      const data = {
        id: item.id,
        default_address: item.default_address
      }
      await this.$store.dispatch('actionDefaultUserAddress', data)
      var res = await this.$store.state.ModuleUser.stateSetDefaultUserAddress
      if (res.message === 'Update default address success') {
        this.$swal.fire({ icon: 'success', title: '<h5>ตั้งค่าเป็นที่อยู่เริ่มต้นแล้ว</h5>', showConfirmButton: false, timer: 1500 })
        this.userdetail = [...res.data]
      } else {
        this.$swal.fire({ icon: 'error', title: '<h5>ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ</5>', showConfirmButton: false, timer: 1500 })
      }
      // this.getAddress()
    },
    async getAddressTex () {
      this.$store.commit('openLoader')
      // await this.$store.dispatch('actionUPSListUserAddressTex')
      // const responeAddressTex = await this.$store.state.UPSModuleUser.stateUPSListUserAddressTex
      // if (responeAddressTex.result === 'Success') {
      //   if (responeAddressTex.data.length === 0) {
      //     this.userInvoiceAddress = []
      //     this.CheckDataTex = false
      //   } else {
      //     this.userInvoiceAddress = await responeAddressTex.data
      //     this.CheckDataTex = true
      //   }
      // }
      this.userInvoiceAddress = []
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // var companyId = ''
      // if (dataRole.role !== 'ext_buyer') {
      //   companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      // }
      const data = {
        user_id: onedata.user.user_id,
        company_id: ''
      }
      await this.$store.dispatch('actionsGetAllInvoiceAddress', data)
      var res = await this.$store.state.ModuleUser.stateGetAllInvoiceAddress
      if (res.message === 'Success') {
        this.userInvoiceAddress = res.data
        this.userInvoiceAddress.forEach(element => {
          if (element.default_invoice === 'Y') {
            this.OldId = element.id
          }
        })
      }
      this.$store.commit('closeLoader')
    },
    editAddressTex (val) {
      localStorage.setItem('AddressTexUserDetail', Encode.encode(val))
      this.EditAddressDetail = val
      this.$EventBus.$emit('EditModalAddressTex')
    },
    BindAccount () {
      this.$swal.fire({
        text: `คุณต้องการผูกบัญชี ${this.userdetail.email} กับ One ID จริงหรือไม่?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ยอมรับ',
        cancelButtonText: 'ไม่ยอมรับ'
      }).then(async (result) => {
        if (result.isConfirmed) {
          this.BindAccountdialog = true
          // const { value: password } = await this.$swal.fire({
          //   // text: 'กรุณาใส่รหัสผ่านเพื่อยืนยันผู้ใช้บัญชีนี้',
          //   showCancelButton: true,
          //   confirmButtonColor: '#3085d6',
          //   cancelButtonColor: '#d33',
          //   confirmButtonText: 'ตกลง',
          //   cancelButtonText: 'ยกเลิก',
          //   input: 'password',
          //   inputLabel: 'ใส่รหัสผ่านเพื่อยืนยันผู้ใช้บัญชีนี้',
          //   inputPlaceholder: 'กรุณาใส่รหัสผ่าน',
          //   inputValidator: (value) => {
          //     if (!value) {
          //       return 'กรุณาใส่รหัสผ่าน'
          //     }
          //   }
          // })
          // if (password) {
          //   var data = {
          //     username: this.userdetail.email,
          //     password: password
          //   }
          //   await this.$store.dispatch('actionBindAccount', data)
          //   var response = await this.$store.state.ModuleUser.stateBindAccount
          //   if (response.result === 'SUCCESS') {
          //     this.$swal.fire({ title: 'ผูกบัญชีสำเร็จ!', text: 'กรุณา Login บัญชีใหม่เพื่อใช้งาน', icon: 'success', timer: 3000, showConfirmButton: false })
          //   } else {
          //     this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
          //   }
          // }
        }
      })
    }
  }
}
</script>
<style scoped>
.f-right {
  float: right;
}
/* .v-btn:hover {
  color: #017541 !important;
}
.v-icon:hover {
  color: #27AB9C !important;
} */
</style>
