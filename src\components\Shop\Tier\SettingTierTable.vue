<template>
  <v-container style="min-height:650px" :class="MobileSize ? 'mt-3' : 'mx-0'">
    <v-card width="100%" height="100%" elevation="0" :class="MobileSize ? 'px-2' : '' ">
      <v-row dense class="mb-4">
        <v-col cols="12" class="mt-3" :class="MobileSize ? 'px-5' : 'px-0'">
          <v-row class="mx-0" v-if="!MobileSize">
            <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">ตั้งค่ากลุ่มคู่ค้า (Tier)</v-card-title>
          </v-row>
          <v-row v-else>
            <v-card-title class="pl-2" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> ตั้งค่ากลุ่มคู่ค้า (Tier)</v-card-title>
          </v-row>
        </v-col>
        <v-col cols="12" class="pb-10" :class="MobileSize ? 'px-2' : 'px-3'">
          <v-row dense class="px-0">
            <v-col cols="12" :class="MobileSize|| IpadProSize || IpadSize ? 'pb-0' : ''">
              <a-tabs @change="getTierListReturn" class="px-2 py-0 changeBorderbottom">
                <a-tab-pane :key="0"><span slot="tab" style="font-size: 18px;">ทั้งหมด <v-chip small text-color="#27AB9C" color="rgba(39, 171, 156, 0.10)">{{ tierList.count_all }}</v-chip></span></a-tab-pane>
                <a-tab-pane :key="1"><span slot="tab" style="font-size: 18px;">กำลังใช้งาน <v-chip small text-color="#52C41A" color="#F0FEE8">{{ tierList.count_active }}</v-chip></span></a-tab-pane>
                <a-tab-pane :key="2"><span slot="tab" style="font-size: 18px;">ยกเลิก <v-chip small text-color="#F5222D" color="rgba(245, 34, 45, 0.10)">{{ tierList.count_inactive }}</v-chip></span></a-tab-pane>
                <template v-slot:tabBarExtraContent>
                  <v-switch v-if="!MobileSize && !IpadSize" :ripple="false" class="float-left hide-background-hover" :class="MobileSize ? 'pb-5' : IpadProSize || IpadSize ? 'pb-4' : 'pb-4'" v-model="tierList.on_off" value="yes" :label="tierList.on_off === 'yes' ? 'เปิดการขอเป็นคู่ค้า' : 'ปิดการขอเป็นคู่ค้า'" color="#52C41A" style="font-size: 14px !important;" inset hide-details @click="turnOnOffRequessPartner()"></v-switch>
                </template>
              </a-tabs>
            </v-col>
          </v-row>
          <!-- <v-container :class="MobileSize || IpadProSize || IpadSize ? '' : 'mt-3'" class="mx-0"> -->
          <v-row dense class="px-0 pb-6 pt-2">
            <v-col v-if="DataTable.length !== 0" cols="12" md="4" sm="12" class="pt-3">
              <span v-if="StateStatus == 0" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;">รายการกลุ่มคู่ค้าทั้งหมด {{ showCountOrder }} รายการ</span>
              <span v-if="StateStatus == 1" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;">รายการกลุ่มคู่ค้ากำลังใช้งานทั้งหมด {{ showCountOrder }} รายการ</span>
              <span v-if="StateStatus == 2" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;">รายการกลุ่มคู่ค้ายกเลิกทั้งหมด {{ showCountOrder }} รายการ</span>
            </v-col>
            <v-col cols="12" md="4" sm="12" v-if="DataTable.length !== 0">
              <v-text-field v-model="search" style="border-radius: 8px;" append-icon="mdi-magnify" placeholder="ค้นหาจากชื่อกลุ่มคู่ค้า" v-if="DataTable" outlined dense hide-details></v-text-field>
            </v-col>
            <v-col cols="12" :md="DataTable.length !== 0 ? '4' : '12'" sm="12" align="end" class="mt-1" v-if="!MobileSize && !IpadSize">
              <v-btn :block="MobileSize" rounded class="pt-0" width="153" height="40" :class="MobileSize ? 'mt-0': 'mt-0 '" color="#27AB9C" dark @click="addTier()"><v-icon left>mdi-plus</v-icon>เพิ่มกลุ่มคู่ค้า</v-btn>
            </v-col>
            <v-col v-if="MobileSize || IpadSize" :cols="MobileSize || IpadProSize || IpadSize ? 12 : 6" align="end" class="mb-0 pt-0" :class="IpadProSize || IpadSize ? 'pt-2' : 'px-2'">
              <v-switch v-if="MobileSize || IpadSize" :ripple="false" class="float-left hide-background-hover mt-2 pt-0" :class="MobileSize ? 'pb-5' : IpadProSize || IpadSize ? '' : 'ml-14'" v-model="tierList.on_off" value="yes" label="เปิด-ปิดการขอเป็นคู่ค้า" color="#52C41A" style="font-size: 14px !important;" inset hide-details @click="turnOnOffRequessPartner()"></v-switch>
              <v-btn :block="MobileSize" rounded width="153" height="40" class="pt-0" :class="MobileSize ? 'mt-6': 'mt-0 '" color="#27AB9C" dark @click="addTier()"><v-icon left>mdi-plus</v-icon>เพิ่มกลุ่มคู่ค้า</v-btn>
            </v-col>
          </v-row>
          <v-data-table v-if="DataTable.length !== 0" v-model="selected" @pagination="countCompany" :page.sync="page" :headers="headers" :items="DataTable" :footer-props="{'items-per-page-text':'จำนวนแถว'}" :search="search" item-key="referance_id" color="blue" class="elevation-1 px-0" no-results-text="ไม่พบกลุ่มคู่ค้าที่ค้นหา">
            <template v-slot:[`item.tier_name`]="{ item }">
              {{item.tier_name}}
            </template>
            <template v-slot:[`item.created_at`]="{ item }">
              {{new Date(item.created_at).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}
            </template>
            <template v-slot:[`item.status`]="{ item }">
              <span v-if="item.status === 'active'">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">กำลังใช้งาน</v-chip>
              </span>
              <span v-else-if="item.status === 'inactive'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ยกเลิก</v-chip>
              </span>
            </template>
            <template v-slot:[`item.manage`]="{ item }">
              <v-btn text rounded color="#27AB9C" small @click="openDetail(item)">
                <v-icon class="pr-1" color="27AB9C" small>mdi-file-document-outline</v-icon>
                <b>รายละเอียด</b><v-icon small>mdi-chevron-right</v-icon>
              </v-btn>
            </template>
          </v-data-table>
          <v-row justify="center" align-content="center" v-if="this.disableTable === true">
            <v-col cols="12" align="center">
              <div class="my-5">
                <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
              </div>
              <h2 v-if="StateStatus === 0" style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>ยังไม่มีรายการตั้งค่ากลุ่มคู่ค้า</b></h2>
              <h2 v-else style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>ยังไม่มีรายการตั้งค่ากลุ่มคู่ค้าที่{{ StateStatus === 1 ? 'กำลังใช้งาน' : 'ยกเลิก' }}</b></h2>
            </v-col>
          </v-row>
          <!-- </v-container> -->
        </v-col>
      </v-row>
      <ModalTierDetail ref="ModalTierDetail" />
      <ModalSettingTier ref="ModalSettingTier" />
    </v-card>
  </v-container>
</template>

<script>
import { Tabs } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    ModalTierDetail: () => import('@/components/Shop/Tier/TierDetailModal'),
    ModalSettingTier: () => import('@/components/Shop/Tier/SettingTierModal')
  },
  data () {
    return {
      DataTable: [],
      search: '',
      selected: [],
      page: 1,
      statusRequestPartner: 'no',
      countAllList: 0,
      countActiveList: 0,
      countInactiveList: 0,
      showCountOrder: 0,
      openDialog: false,
      disableTable: false,
      documentPartnerList: [
        { document_name: 'สำเนาหนังสือรับรองบริษัท / หจก.' },
        { document_name: 'สำเนาใบทะเบียนภาษีมูลค่าเพิ่ม ภ.พ.20' }
      ],
      headers: [
        { text: 'ชื่อกลุ่มคู่ค้า', value: 'tier_name', width: '200', align: 'start', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'วันที่สร้าง', value: 'created_at', sortable: false, width: '180', filterable: false, align: 'start', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', value: 'status', sortable: false, filterable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'manage', sortable: false, filterable: false, width: '100', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      tierList: {}
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/SettingTierMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/SettingTier' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.$EventBus.$on('settingTierSuccess', this.refreshData)
      this.$EventBus.$on('editSettingTierSuccess', this.refreshData)
      this.getTierListReturn(0)
    }
  },
  beforeDestroy () {
    this.$EventBus.$off('settingTierSuccess')
    this.$EventBus.$off('editSettingTierSuccess')
  },
  methods: {
    countCompany (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    refreshData () {
      this.getTierListReturn(this.StateStatus)
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    async getTierListReturn (item) {
      this.$store.commit('openLoader')
      const shopId = localStorage.getItem('shopSellerID')
      const data = { seller_shop_id: shopId }
      await this.$store.dispatch('actionsSettingTierList', data)
      const res = await this.$store.state.ModuleSettingTier.stateSettingTierList
      if (res.message === 'List main document partner success') {
        this.$store.commit('closeLoader')
        this.page = 1
        this.tierList = res.data
        this.StateStatus = item
        if (this.StateStatus === 0) {
          this.DataTable = this.tierList.data_all
        } else if (this.StateStatus === 1) {
          this.DataTable = this.tierList.data_active
        } else if (this.StateStatus === 2) {
          this.DataTable = this.tierList.data_inactive
        }
        if (this.DataTable.length === 0) {
          this.disableTable = true
        } else {
          this.disableTable = false
        }
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR',
          text: `${res.message}`
        })
      } else if (res.message === 'Not found data') {
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        if (res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 7000,
            timerProgressBar: true,
            icon: 'error',
            title: 'ดำเนินการไม่สำเร็จ',
            text: `${res.message}`
          })
        }
      }
    },
    async turnOnOffRequessPartner () {
      this.$store.commit('openLoader')
      const shopId = localStorage.getItem('shopSellerID')
      const data = { seller_shop_id: shopId, on_off: this.tierList.on_off === null ? 'no' : this.tierList.on_off }
      await this.$store.dispatch('actionsTurnOnOffRequessPartner', data)
      const res = await this.$store.state.ModuleSettingTier.stateTurnOnOffRequessPartner
      if (res.message === 'Update on/off success') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ดำเนินการสำเร็จ'
        })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: `${res.message}`
        })
      }
    },
    addTier () {
      const data = ''
      this.$refs.ModalSettingTier.open('create', data)
    },
    cancel () {
      this.openDialog = !this.openDialog
    },
    save () {
      this.openDialog = !this.openDialog
    },
    openDetail (item) {
      const data = { tier_manage_id: item.id, seller_shop_id: item.seller_shop_id }
      this.$refs.ModalTierDetail.open(data)
    }
  }
}
</script>
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(4) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(4) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.55rem;
}
.changeBorderbottom /deep/ .ant-tabs-bar {
  margin: 0 0 16px 0;
  border-bottom: 2px solid #DAF1E9 !important;
  outline: none;
  transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}
</style>
