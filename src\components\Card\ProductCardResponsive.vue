<template>
  <v-hover
    v-slot="{ hover }"
  >
  <v-card v-if="itemProduct !== undefined" class="card" :href="itemProduct.link ? itemProduct.link : pathProductDetail" :elevation="hover ? 4 : 0" :class="{ 'on-hover': hover }" style="cursor: pointer; border-radius: 8px;" onclick="return false;" @click.prevent="DetailProduct(itemProduct)">
      <div class="image-container" v-if="itemProduct.images_URL.length !== 0">
        <v-img
         loading='lazy'
         :gradient="(itemProduct.normalOutStock === true && itemProduct.normalOutStock !== undefined) || (itemProduct.product_type === 'general' && itemProduct.stock_status === 'out of stock') ? '#33333373, #33333373' : ''"
         v-lazyload
         :src="itemProduct.images_URL[0]"
         style="border-radius: 8px 8px 0px 0px;"
         height="188px"
         width="188px"
         max-height="188px"
         max-width="188px"
         alt="ImageProduct"
         ref="ProductImage"
         class="base-image"
        >
          <template v-slot:placeholder>
            <v-row
              class="fill-height ma-0"
              align="center"
              justify="center"
            >
              <v-progress-circular
                indeterminate
                color="grey lighten-5"
              ></v-progress-circular>
            </v-row>
          </template>
          <v-chip v-if="(itemProduct.normalOutStock === true && itemProduct.normalOutStock !== undefined) || (itemProduct.product_type === 'general' && itemProduct.stock_status === 'out of stock')" color="#33333380" text-color="white" style="position:absolute; z-index:3; top:38%; left:26%">สินค้าหมด</v-chip>
        </v-img>
        <img v-if="itemProduct.etax === 'Y' && itemProduct.otop === 'N'" :src="require('@/assets/icon_etax.png')" alt="eTaxImage" class="overlay-image" height="100%" width="100%" />
        <img v-if="itemProduct.etax === 'N' && itemProduct.otop === 'Y'" :src="require('@/assets/OTOP.png')" alt="otopImage" class="overlay-image" height="100%" width="100%" />
        <img v-if="itemProduct.etax === 'Y' && itemProduct.otop === 'Y'" :src="require('@/assets/E-TAX_OTOP.png')" alt="eTaxOtopImage" class="overlay-image" height="100%" width="100%" />
      </div>
      <div class="image-container" v-else>
        <v-img
          v-lazyload
          :gradient="(itemProduct.normalOutStock === true && itemProduct.normalOutStock !== undefined) || (itemProduct.product_type === 'general' && itemProduct.stock_status === 'out of stock') ? '#33333373, #33333373' : ''"
          src="@/assets/NoImage.png"
          style="border-radius: 8px 8px 0px 0px;"
          height="188px"
          width="188px"
          max-height="188px"
          max-width="188px"
        >
        <v-chip v-if="(itemProduct.normalOutStock === true && itemProduct.normalOutStock !== undefined) || (itemProduct.product_type === 'general' && itemProduct.stock_status === 'out of stock')" color="#33333380" text-color="white" style="position:absolute; z-index:3; top:38%; left:26%">สินค้าหมด</v-chip>
        </v-img>
        <img v-if="itemProduct.etax === 'Y' && itemProduct.otop === 'N'" :src="require('@/assets/icon_etax.png')" alt="eTaxImage" class="overlay-image" height="100%" width="100%" />
        <img v-if="itemProduct.etax === 'N' && itemProduct.otop === 'Y'" :src="require('@/assets/OTOP.png')" alt="otopImage" class="overlay-image" height="100%" width="100%" />
        <img v-if="itemProduct.etax === 'Y' && itemProduct.otop === 'Y'" :src="require('@/assets/E-TAX_OTOP.png')" alt="eTaxOtopImage" class="overlay-image" height="100%" width="100%" />
      </div>
      <v-card-text class="pa-2">
        <!-- ชื่อสินค้า -->
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <!-- <v-col cols="12" md="12">
              <v-row no-gutters justify="center">
                <v-col cols="12" md="12"> -->
                  <h1 v-bind="attrs" v-on="on" class="mb-0" style="height: 43px; max-height: 46px; color: #0B1A35; width: 178px; font-size: 16px; font-weight: 400; -webkit-line-clamp: 2; -webkit-box-orient: vertical; display: -webkit-box; overflow: hidden; text-overflow: ellipsis;"
                 >{{ itemProduct.name }}</h1>
                <!-- </v-col> -->
                <!-- <v-col cols="2" md="2">
                  <v-btn icon small  @click="CheckaddFavorites()" @click.stop="DetailProduct('no')"  onclick="return false;" v-if="(roleUser.role !== 'purchaser' && roleUser.role !== 'sale_order' && roleUser.role !== 'sale_order_no_JV') && itemProduct.isFavorite !== undefined">
                    <v-icon size="15" color="#D1392B" v-if="itemProduct.isFavorite === false || itemProduct.isFavorite === 'false'">mdi-heart-outline</v-icon>
                    <v-icon size="15" color="#D1392B" v-else>mdi-heart</v-icon>
                  </v-btn>
                </v-col> -->
              <!-- </v-row>
            </v-col> -->
          </template>
          <span>{{ itemProduct.name }}</span>
        </v-tooltip>
        <!-- ราคาสินค้า -->
        <div v-if="(parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include) === itemProduct.fake_price || itemProduct.real_price === itemProduct.fake_price) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)">
          <span style="font-size: 18px; font-weight: 700; color: #1B5DD6;">฿ {{ Number( itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {maximumFractionDigits: 2}) }}</span>
        </div>
        <div v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="specialPrice" style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {maximumFractionDigits: 2}) }}</span>
          <!-- <span class="priceDecrese" style="font-size: 12px; font-weight: 600;">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span> -->
          <v-chip class="ml-2 px-1" color="#FEE7E8" text-color="#F5222D" style="font-size: 10px; font-weight: 500;" x-small v-if="itemProduct.discount_percent !== '0%'">ส่วนลด -{{itemProduct.discount_percent}}</v-chip>
        </div>
        <span v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)" style="font-size: 24px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="specialPrice" style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.special_price) + parseFloat(itemProduct.vat_include) : itemProduct.special_price).toLocaleString(undefined, {maximumFractionDigits: 2}) }}</span>
          <!-- <span class="priceDecrese" style="font-size: 12px; font-weight: 600;">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span> -->
          <v-chip class="ml-2 px-1 py-1" color="#FEE7E8" text-color="#F5222D" style="font-size: 10px; font-weight: 500;" x-small v-if="itemProduct.discount_percent !== '0%'">ส่วนลด -{{itemProduct.discount_percent}}</v-chip>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 16px;">
          ติดต่อสอบถามเจ้าหน้าที่
        </span>
        <v-row dense style="height: 28px; align-content: center;" v-if="itemProduct.otop === 'Y'">
          <v-img :src="require('@/assets/Tag_Product_OTOP.png')" :alt="tagProductOTOP" contain max-height="18px" max-width="78px"></v-img>
        </v-row>
        <v-row dense style="height: 28px;" v-else></v-row>
        <div class="d-flex">
          <div class="mr-auto" style="font-size: 12px; font-weight: 400; color: #989898;"><v-icon size="12" color="#FAAD14">mdi-star</v-icon> {{ itemProduct.stars % 1 === 0 ? itemProduct.stars : itemProduct.stars.toFixed(2) }} ({{ (itemProduct.review === null || itemProduct.review === '' || itemProduct.review === undefined) ? '0' : itemProduct.review }})</div>
          <div class="ml-auto" style="font-size: 12px; font-weight: 400; color: #989898;">ขายแล้ว {{itemProduct.sold | formatNumber }} ชิ้น</div>
        </div>
        <!-- Tag จังหวัด -->
        <div class="d-flex" v-if="itemProduct.province !== '' && itemProduct.province !== undefined">
          <v-icon size="10" color="#989898">mdi-map-marker-outline</v-icon>
          <span style="font-size: 10px; font-weight: 400; color: #989898;">จังหวัด{{ itemProduct.province }}</span>
        </div>
      </v-card-text>
    </v-card>
  </v-hover>
</template>

<script>
import { Decode } from '@/services'
import Vue from 'vue'
export default {
  props: ['itemProduct'],
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      discription: 'หมวกนิรภัยป้องกันอุบัติหมวกนิรภัยป้องกันอุบัติ',
      rating: 5,
      favorite: false,
      priceSame: false,
      oneData: [],
      pathProductDetail: '',
      path: process.env.VUE_APP_DOMAIN,
      productID: '',
      namesPath: '',
      roleUser: '',
      productImage: ''
    }
  },
  created () {
    this.$EventBus.$on('checkRoleCardRes', this.checkRoleCardRes)
    if (localStorage.getItem('roleUser') !== null) {
      this.roleUser = JSON.parse(localStorage.getItem('roleUser'))
    } else {
      this.roleUser = {
        role: 'ext_buyer'
      }
    }
    this.productImage = this.itemProduct.images_URL[0]
    this.formatSold()
    if (this.itemProduct !== undefined) {
      if (this.itemProduct.id !== undefined && this.itemProduct.id !== '') {
        if (this.itemProduct.link) {
          // console.log('tt1')
          this.pathProductDetail = this.itemProduct.link
        } else {
          // console.log('els')
          this.pathProductDetail = this.path + 'DetailProduct/' + encodeURIComponent(this.itemProduct.name.replace(/\s/g, '-') + '-' + this.itemProduct.id)
        }
      } else if (this.itemProduct.product_id !== undefined && this.itemProduct.product_id !== '') {
        if (this.itemProduct.link) {
          this.pathProductDetail = this.itemProduct.link
        } else {
          this.pathProductDetail = this.path + 'DetailProduct/' + encodeURIComponent(this.itemProduct.name.replace(/\s/g, '-') + '-' + this.itemProduct.product_id)
        }
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    formatSold () {
      Vue.filter('formatNumber', function (value) {
        if (!value) return 0
        if (value >= 1000) {
          return (value / 1000).toFixed(1) + 'พัน'
        }
        return value.toString()
      })
    },
    checkRoleCardRes () {
      this.roleUser = ''
      if (localStorage.getItem('roleUser') !== null) {
        this.roleUser = JSON.parse(localStorage.getItem('roleUser'))
      } else {
        this.roleUser = {
          role: 'ext_buyer'
        }
      }
    },
    DetailProduct (val) {
      if (val !== 'no') {
        // console.log(val)
        // console.log('DetailProduct2', val)
        localStorage.removeItem('an_id')
        const nameCleaned = val.name.replace(/\s/g, '-')
        if (val.id !== undefined && val.id !== '') {
          this.$router.push({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.id}` } }).catch(() => {})
        } else {
          this.$router.push({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.product_id}` } }).catch(() => {})
        }
        // const routeData = this.$router.resolve({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.id}` } })
        // window.location.assign(routeData.href, '_blank')
        // this.$router.push({ path: `${routeData.href}` }).catch(() => {})
      }
    },
    CheckaddFavorites () {
      if (localStorage.getItem('oneData') !== null) {
        var ProductID
        if (this.itemProduct.id !== undefined && this.itemProduct.id !== '') {
          ProductID = this.itemProduct.id
        } else if (this.itemProduct.product_id !== undefined && this.itemProduct.product_id !== '') {
          ProductID = this.itemProduct.product_id
        }
        this.addFavorites(ProductID)
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'กรุณาเข้าสู่ระบบ เพื่อเพิ่มลงในสินค้าที่ถูกใจของคุณ'
        })
      }
    },
    async addFavorites (val) {
      if (localStorage.getItem('roleUser') !== null) {
        this.roleUser = JSON.parse(localStorage.getItem('roleUser'))
      } else {
        this.roleUser = {
          role: 'ext_buyer'
        }
      }
      var companyId
      if (localStorage.getItem('SetRowCompany') !== null) {
        companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      }
      var data
      if (this.roleUser.role === 'purchaser') {
        data = {
          role_user: this.roleUser.role,
          product_id: val,
          company_id: companyId.company.company_id,
          company_position_id: companyId.position.role_id,
          com_perm_id: companyId.position.com_perm_id
        }
      } else if (this.roleUser.role === 'ext_buyer') {
        data = {
          role_user: this.roleUser.role,
          product_id: val,
          company_id: -1,
          company_position_id: -1,
          com_perm_id: -1
        }
      }
      await this.$store.dispatch('actionsUPSAddFavoriteProduct', data)
      var response = await this.$store.state.ModuleFavoriteProduct.stateAddFavoriteProduct
      // console.log('response favorite =======>', response)
      if (response.result === 'SUCCESS') {
        this.$EventBus.$emit('GetPopularProduct')
        this.$EventBus.$emit('getAllFavoriteProduct')
        this.$EventBus.$emit('getResultSearch')
        // this.$EventBus.$emit('getNewProduct')
        // this.$EventBus.$emit('getProductRecommentBrand')
        if (this.$router.currentRoute.name === 'DetailProduct') {
          this.$EventBus.$emit('getProductDetail')
        }
        if (this.$router.currentRoute.name === 'ViewArticle') {
          this.$EventBus.$emit('getProductAritcle')
        }
        if (this.$router.currentRoute.name === 'ViewArticleMobile') {
          this.$EventBus.$emit('getProductAritcle')
        }
        this.$EventBus.$emit('ClickFavorites')
        this.$EventBus.$emit('getAllNewProduct')
        this.$EventBus.$emit('getAllBestSeller')
        this.$EventBus.$emit('getAllSameProductShop')
        this.$EventBus.$emit('getAllProductCategory')
        this.$EventBus.$emit('getBuyProductAgain')
        this.$EventBus.$emit('getAllProductSame')
        if (this.roleUser.role === 'ext_buyer') {
          this.$EventBus.$on('getHomepageItems', this.getRecommentProductExt)
        } else {
          this.$EventBus.$on('getBestSeller', this.getBestSeller)
        }
        this.$EventBus.$emit('getSellerShopPage')
        this.$EventBus.$emit('getAllProductShop')
        // this.$EventBus.$emit('getAllProductCategoryDetail')
        this.$EventBus.$emit('getRecommendedProducts')
        // this.$EventBus.$emit('getAllNewProductInShop')
        // this.$EventBus.$emit('getAllBestSellerInShop')
        // this.$EventBus.$emit('getAllRecomenProductShopInShop')
        // this.$EventBus.$emit('getAllProductShopInShop')
        this.$EventBus.$emit('getDataListDetailArticle')
        if (this.$route.params.data === 'flashSaleShop') {
          this.$EventBus.$emit('getProductFlashSale')
        }
        this.$EventBus.$emit('getAllProductFlashSale')
        this.$EventBus.$emit('getProductGroupShop')
        this.$EventBus.$emit('getDataProductGroupShop')
      }
    }
  }
}
</script>
<style scoped>
.image-container {
  position: relative;
}

.base-image {
  width: 100%;
  height: 100%;
  z-index: 0;
}

.overlay-image {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
  height: 70%;
  width: 120px;
  max-width: 100%;
  max-height: 100%;
}
.square-chip {
  padding: 1px 0px 0px 1px;
  width: 53%; /* กำหนดความกว้าง */
  height: 14px; /* กำหนดความสูง */
  font-size: 10px; /* ขนาดตัวอักษร */
}
.fontMobile{
  line-height: 18px;
  height: 35px;
  max-height: 35px;
}
.fontIpad{
  line-height: 18px;
  height: 38.5px;
  max-height: 40px;
}
.font{
 line-height: 20px;
 height: 40px;
 max-height: 40px;
}
.card {
  height: 100%;
  max-height: 100%;
  width: 100% !important;
  max-width: 188px;
}
.custom-card {
  border: 1px solid #BDE7D9 !important; /* สีขอบของการ์ด */
  border-color: #BDE7D9 !important; /* สีขอบของการ์ดเมื่อไม่ได้โฮเวอร์ */
}
@media (max-width: 1366px) and (min-width: 1181px) {
  /* Media Query สำหรับ iPad Pro (1024px) */
  .square-chip {
    /* padding: 1px 0px 0px 1px; */
    width: 52%;
    /* height: 14px; */
    /* font-size: 10px; */
  }
  .card {
    max-width: 19.5vw;
  }
}
@media (max-width: 1180px) and (min-width: 1025px) {
  /* Media Query สำหรับ iPad air แนวนอน */
  .square-chip {
    /* padding: 0px !important; */
    width: 52%;
    /* height: 14px;
    font-size: 10px; */
  }
  .card {
    max-width: 18vw;
  }
}
@media (max-width: 1250px) and (min-width: 1181px) {
  /* Media Query สำหรับ โน๊ตบุ๊คหน้าจอขนาดเล็ก */
  .card {
    max-width: 16.5vw;
  }
}
</style>
