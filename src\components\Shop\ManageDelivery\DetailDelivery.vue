<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" class="d-flex align-center justify-space-between" v-if="!MobileSize">
        <div class="d-flex align-center"><v-icon class="mr-2" style="color: #27AB9C;" @click="backToListDelivery2()">mdi-chevron-left</v-icon>รายละเอียดใบส่งสินค้า</div>
        <v-btn outlined  v-if="this.dataDetails.status === 'waiting' || this.dataDetails.status === 'inprogress'" @click="openDialogDelete()" color="#E31C26" style="border-radius: 20px;" class="px-4">ยกเลิกการจัดส่ง</v-btn>
      </v-card-title>
      <v-card-title style="font-weight: 700;" class="d-flex align-center justify-space-between" v-else>
        <div class="d-flex align-center"><v-icon class="mr-2" color="#27AB9C" @click="backToListDelivery2()">mdi-chevron-left</v-icon>รายละเอียดใบส่งสินค้า</div>
        <v-btn outlined v-if="this.dataDetails.status === 'waiting' || this.dataDetails.status === 'inprogress'" @click="openDialogDelete()" color="#E31C26" style="border-radius: 20px;" class="px-4">ยกเลิกการจัดส่ง</v-btn>
      </v-card-title>

      <v-col>
        <v-row>
          <v-col cols="12">
            <v-card elevation="0" style="background-color: #F9FAFD;">
              <v-col>
                <v-row>
                  <v-col :cols="MobileSize || IpadSize ? 12 : 7">
                    <v-row>
                      <v-col :cols="MobileSize ? 5 : 3">
                        <span><b>รหัสการสั่งซื้อ : </b></span>
                      </v-col>
                      <v-col :cols="MobileSize ? 7 : 9">
                        <span>{{ this.dataDetails.order_number }}</span>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col :cols="MobileSize ? 5 : 3">
                        <span><b>รหัสใบส่งสินค้า : </b></span>
                      </v-col>
                      <v-col :cols="MobileSize ? 7 : 9">
                        <span>{{ this.dataDetails.delivery_number }}</span>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col :cols="MobileSize ? 5 : 3">
                        <span><b>วันที่ทำรายการ : </b></span>
                      </v-col>
                      <v-col :cols="MobileSize ? 7 : 9">
                        <span>{{ this.dataDetails.created_at }}</span>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col :cols="MobileSize || IpadSize ? 12 : 5">
                    <v-card elevation="0" style="background-color: white;" class="pa-3">
                      <v-row style="display: flex; align-items: center;">
                        <v-col :cols="MobileSize ? 5 : 4">
                          <span><b>สถานะคำสั่งซื้อ : </b></span>
                        </v-col>
                        <v-col :cols="MobileSize ? 7 : 8">
                          <v-chip v-if="this.dataDetails.status === 'waiting'" text-color="#FAAD14" color="#FFF2E0">รอจัดส่งสินค้า</v-chip>
                          <v-chip v-if="this.dataDetails.status === 'inprogress'" text-color="#3178BB" color="#EAF4FF">กำลังจัดส่งสินค้า</v-chip>
                          <v-chip v-if="this.dataDetails.status === 'success'" text-color="#31BB31" color="#EAFFF4">จัดส่งสินค้าสำเร็จ</v-chip>
                        </v-col>
                      </v-row>
                      <v-row style="display: flex; align-items: center;" v-if="this.dataDetails.status === 'waiting' || this.dataDetails.status === 'inprogress'">
                        <v-col :cols="MobileSize ? 5 : 4">
                          <span><b>วัน-เวลาส่งสินค้า : </b></span>
                        </v-col>
                        <v-col :cols="MobileSize ? 4 : 5">
                          <span>{{ this.dataDetails.start_date }}</span>
                        </v-col>
                        <v-col cols="3">
                          <v-btn small outlined icon width="40" height="40" style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;" @click="openDialogEdit()"><v-icon color="#27AB9C">mdi-pencil-outline</v-icon></v-btn>
                        </v-col>
                      </v-row>
                      <v-row style="display: flex; align-items: center;" v-else>
                        <v-col :cols="MobileSize ? 5 : 4">
                          <span><b>วัน-เวลาส่งสินค้า : </b></span>
                        </v-col>
                        <v-col :cols="MobileSize ? 7 : 8">
                          <span>{{ this.dataDetails.start_date }}</span>
                        </v-col>
                      </v-row>
                      <v-row>
                        <v-col :cols="MobileSize ? 5 : 4">
                          <span><b>วัน-เวลารับสินค้า : </b></span>
                        </v-col>
                        <v-col :cols="MobileSize ? 7 : 8">
                          <span v-if="this.dataDetails.end_shipping === 'Invalid date'">-</span>
                          <span v-else>{{ this.dataDetails.end_shipping }}</span>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-card>
          </v-col>
          <v-col cols="12">
            <div class="d-flex">
              <v-avatar rounded size="20">
                <v-img contain :src="require('@/assets/shopDelivery/map.png')"></v-img>
              </v-avatar>
              <span class="ml-3" style="font-size: 16px;"><b>ที่อยู่ในการจัดส่งสินค้า</b></span>
            </div>
          </v-col>
          <v-col cols="12">
            <v-row>
              <v-col cols="auto">
                <span><b>{{ this.dataDetails.buyer_name }}</b></span><span style="margin: 0 8px;">|</span><span><b>{{ this.dataDetails.phone }}</b></span>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12">
            <span>{{ this.dataDetails.address }}</span>
          </v-col>
          <v-col cols="12">
            <div class="d-flex">
              <v-avatar rounded size="20">
                <v-img contain :src="require('@/assets/shopDelivery/shopping.png')"></v-img>
              </v-avatar>
              <span class="ml-3" style="font-size: 16px;"><b>รายการสั่งซื้อสินค้า</b></span>
            </div>
          </v-col>
          <v-col cols="12">
            <v-data-table
              :headers="headers"
              :items="productDetails"
              hide-default-footer
              fixed-header
              style="max-height: 400px; overflow-y: auto;"
            >
              <template v-slot:[`item.detail`]="{ item }">
                <v-list-item class="pa-0">
                  <v-img
                    v-if="item.image && item.image !== '-' && item.image !== null"
                    :src="item.image"
                    max-height="70"
                    max-width="70"
                    class="mr-2"
                  ></v-img>
                  <img
                    v-else
                    src="@/assets/NoImage.png"
                    style="max-width: 70px; max-height: 70px;"
                    class="mr-2"
                  />
                  <v-list-item-content>
                    <v-list-item-title v-if="MobileSize" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 180px;">
                      {{ item.product_name }}
                    </v-list-item-title>
                    <v-list-item-title v-else style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 500px;">
                      {{ item.product_name }}
                    </v-list-item-title>
                    <v-list-item-subtitle>รหัสสินค้า: {{ item.product_id }}</v-list-item-subtitle>
                    <v-list-item-subtitle>รูปแบบ: {{ item.attribute }}</v-list-item-subtitle>
                  </v-list-item-content>
                </v-list-item>
              </template>
              <template v-slot:[`item.real_price`]="{ item }">
                <span>{{ Number(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
              </template>
              <template v-slot:[`item.total_price`]="{ item }">
                <span>{{ Number(item.total_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
              </template>
            </v-data-table>
          </v-col>
        </v-row>
      </v-col>
    </v-card>

    <v-dialog v-model="dialogEdit" :width="MobileSize ? '100%' : IpadSize ? '100%' : '500'" persistent>
      <v-card
      elevation="0"
      style="background: #ffffff; border-radius: 24px; overflow-x: hidden; overflow-y: hidden"
    >
      <v-form ref="FormSettingTier" :lazy-validation="lazy">
        <v-card-text class="px-0 pt-0">
          <div
            :style="
              MobileSize
                ? 'width: 100%'
                : IpadSize
                ? 'width: 100%'
                : 'width: 500px'
            "
            class="backgroundHead"
            style="position: absolute; height: 120px"
          >
            <v-row style="height: 120px">
              <v-col style="text-align: center" class="pt-4">
                <span
                  :class="
                    MobileSize
                      ? 'title-mobile white--text'
                      : 'title white--text'
                  "
                  :style="MobileSize ? 'font-size: 20px;' : 'font-size: 24px;'"
                  ><b>แก้ไขวัน - เวลาส่งสินค้า</b></span
                >
              </v-col>
              <v-btn fab small @click="closeDialogEdit()" icon class="mt-3"
                ><v-icon color="white">mdi-close</v-icon></v-btn
              >
            </v-row>
          </div>
          <div
            style="
              position: relative;
              padding: 0px 12px 0px;
              display: flex;
              padding-top: 60px;
            "
          >
            <v-row
              :width="MobileSize ? '100%' : '650px'"
              style="
                height: 50px;
                border-radius: 24px 24px 0px 0px;
                background: #ffffff;
              "
            >
              <v-col style="text-align: center"> </v-col>
            </v-row>
          </div>
          <div class="" style="position: relative">
            <v-card
              elevation="0"
              width="100%"
              height="100%"
              style="background: #ffffff; border-radius: 20px 20px 0px 0px"
              :style="
                MobileSize
                  ? 'padding: 20px 20px 10px 20px;'
                  : 'padding: 40px 48px 10px 48px;'
              "
            >
              <v-col cols="12">
                <v-row>
                  <!-- วันที่จัดส่งสินค้า -->
                  <v-col cols="12" md="6">
                    <span style="font-size: 16px;">วันที่จัดส่งสินค้า</span>
                    <v-dialog v-model="modalRangeDate" persistent width="290px">
                      <template v-slot:activator="{ on, attrs }">
                        <v-text-field
                          readonly
                          v-model="deliveryDate"
                          v-bind="attrs"
                          v-on="on"
                          outlined
                          dense
                          hide-details
                          placeholder="วว/ดด/ปปปป"
                          style="border-radius: 8px;"
                        >
                        <template v-slot:append>
                          <v-icon color="#CCCCCC">
                            mdi-calendar-multiselect
                          </v-icon>
                        </template>
                      </v-text-field>
                    </template>
                      <v-date-picker
                        color="#27AB9C"
                        v-model="selectDeliveryDate"
                        scrollable
                        reactive
                        locale="Th-th"
                      >
                        <v-spacer></v-spacer>
                        <v-btn outlined color="red" @click="modalRangeDate = false">ยกเลิก</v-btn>
                        <v-btn outlined color="primary" @click="setDate(selectDeliveryDate)">ตกลง</v-btn>
                      </v-date-picker>
                    </v-dialog>
                  </v-col>

                  <!-- เวลาที่จัดส่งสินค้า -->
                  <v-col cols="12" md="6">
                    <span style="font-size: 16px;">เวลาที่จัดส่งสินค้า</span>
                    <v-dialog v-model="modalTimePicker" persistent width="290px">
                      <template v-slot:activator="{ on, attrs }">
                        <v-text-field
                          readonly
                          v-model="deliveryTime"
                          v-bind="attrs"
                          v-on="on"
                          outlined
                          dense
                          hide-details
                          placeholder="00:00"
                          style="border-radius: 8px;"
                          >
                          <template v-slot:append>
                            <v-icon color="#CCCCCC">
                              mdi-clock-outline
                            </v-icon>
                          </template>
                        </v-text-field>
                      </template>
                      <v-time-picker
                        v-model="selectedTime"
                        color="#27AB9C"
                        format="24hr"
                      >
                        <v-spacer></v-spacer>
                        <v-btn outlined color="red" @click="modalTimePicker = false">ยกเลิก</v-btn>
                        <v-btn outlined color="primary" @click="setTime(selectedTime)">ตกลง</v-btn>
                      </v-time-picker>
                    </v-dialog>
                  </v-col>
                </v-row>
              </v-col>
            </v-card>
          </div>
        </v-card-text>
      </v-form>
      <v-col>
        <v-row>
          <v-card-text class="px-0" style="background-color: #F5FCFB;">
            <v-row dense justify="space-around">
              <v-btn
                :width="MobileSize ? '125' : '156'"
                height="38"
                outlined
                rounded
                color="#27AB9C"
                class="mr-4"
                @click="closeDialogEdit()"
                >ยกเลิก</v-btn
              >
              <v-btn
                :width="MobileSize ? '125' : '156'"
                height="38"
                class="white--text"
                rounded
                color="#27AB9C"
                :disabled="!isDataComplete"
                @click="confirmEditDateDelivery()"
                >ยืนยัน</v-btn
              >
            </v-row>
          </v-card-text>
        </v-row>
      </v-col>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogSuccessEdit" persistent max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto"  @click="dialogSuccessEdit = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #F8FFF5">
          <v-img
            :src="require('@/assets/shopDelivery/success.png')"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 24px;"><b>แก้ไขวัน - เวลาส่งสินค้าสำเร็จ</b></span><br><br>
            <span style="font-size: 16px;">คุณได้ทำแก้ไขวัน - เวลาส่งสินค้าแล้ว</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white; font-size: 16px;" @click="dialogSuccessEdit = false">ปิด</v-btn>
        </v-card-actions>
        <br>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogDelete" persistent max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogDelete = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #fffef5">
          <v-img
            :src="require('@/assets/shopDelivery/info.png')"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 24px;"><b>ยกเลิกจัดส่งสินค้า</b></span><br><br>
            <span style="font-size: 16px;">คุณแน่ใจหรือไม่ว่าต้องการจัดส่งสินค้าของรายการนี้</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px; font-size: 16px;" @click="dialogDelete = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white; font-size: 16px;" @click="DeleteDelivery()">ยืนยัน</v-btn>
        </v-card-actions>
        <br>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogSuccessDelete" persistent max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto"  @click="backToListDelivery2()" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #F8FFF5">
          <v-img
            :src="require('@/assets/shopDelivery/success.png')"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 24px;"><b>ยกเลิกส่งสินค้าสำเร็จ</b></span><br><br>
            <span style="font-size: 16px;">คุณได้ทำการยกเลิกการจัดส่งสินค้าแล้ว</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white; font-size: 16px;" @click="backToListDelivery2()">ปิด</v-btn>
        </v-card-actions>
        <br>
      </v-card>
    </v-dialog>

    </v-container>
</template>

<script>
export default {
  data () {
    return {
      dataDetails: [],
      productDetails: [],
      headers: [
        { text: 'รหัส SKU', value: 'sku', sortable: false, align: 'start', class: 'fontTable--text fontSizeDetail', width: '150' },
        { text: 'รายละเอียดสินค้า', value: 'detail', sortable: false, align: 'start', class: 'fontTable--text fontSizeDetail', width: '250' },
        { text: 'ราคาต่อชิ้น', value: 'real_price', sortable: false, align: 'start', class: 'fontTable--text fontSizeDetail', width: '100' },
        { text: 'จำนวน', value: 'shipping_amount', sortable: false, align: 'start', class: 'fontTable--text fontSizeDetail', width: '100' },
        { text: 'ราคารวม', value: 'total_price', sortable: false, align: 'start', class: 'fontTable--text fontSizeDetail', width: '100' }
      ],
      lazy: false,
      dialogEdit: false,
      dialogSuccessEdit: false,
      modalRangeDate: false,
      deliveryDate: '',
      sendDeliveryDate: '',
      selectDeliveryDate: '',
      modalTimePicker: false,
      deliveryTime: '',
      selectedTime: '',
      dialogDelete: false,
      dialogSuccessDelete: false
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    isDataComplete () {
      return !!this.selectDeliveryDate && !!this.deliveryTime
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    window.scrollTo(0, 0)
    this.orderDeliveryNumber = this.$route.query.orderDeliveryNumber
    this.orderDeliveryOrderDetail()
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: `/DetailDeliveryMobile?orderDeliveryNumber=${this.orderDeliveryNumber}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/DetailDelivery?orderDeliveryNumber=${this.orderDeliveryNumber}` }).catch(() => {})
      }
    }
  },
  methods: {
    backToListDelivery2 () {
      const targetPath = this.MobileSize ? '/ListDeliveryMobile' : '/ListDelivery'
      this.$router.push({ path: targetPath, query: { tab: 'delivery' } }).catch(() => {})
    },
    async orderDeliveryOrderDetail () {
      this.$store.commit('openLoader')
      var data = {
        order_delivery_number: this.orderDeliveryNumber
      }
      await this.$store.dispatch('actionOrderDeliveryOrderDetail', data)
      var responseData = await this.$store.state.ModuleShop.stateOrderDeliveryOrderDetail
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.dataDetails = responseData.data.DeliveryDetail[0]
        this.productDetails = responseData.data.DeliveryDetail[0].product_details
        // console.log('dataDetail', this.dataDetails)
        // console.log('productDetail', this.productDetails)
      }
    },
    formatDate (date) {
      return new Date(date).toLocaleDateString('th-TH', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      })
    },
    setDate (date) {
      this.deliveryDate = this.formatDate(date)
      this.modalRangeDate = false
    },
    setTime (time) {
      this.deliveryTime = time
      this.modalTimePicker = false
    },
    openDialogEdit () {
      this.dialogEdit = true
    },
    closeDialogEdit () {
      this.dialogEdit = false
      this.modalRangeDate = false
      this.deliveryDate = ''
      this.selectDeliveryDate = ''
      this.modalTimePicker = false
      this.deliveryTime = ''
      this.selectedTime = ''
    },
    async confirmEditDateDelivery () {
      this.$store.commit('openLoader')
      var data = {
        order_delivery_number: this.orderDeliveryNumber,
        date: this.selectDeliveryDate,
        time: this.deliveryTime,
        status: 'waiting'
      }
      await this.$store.dispatch('actionUpdateDateDelivery', data)
      var responseData = await this.$store.state.ModuleShop.stateUpdateDateDelivery
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.dialogSuccessEdit = true
        this.orderDeliveryOrderDetail()
        this.dialogEdit = false
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: responseData.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    },
    openDialogDelete () {
      this.dialogDelete = true
    },
    async DeleteDelivery () {
      this.$store.commit('openLoader')
      var data = {
        order_delivery_number: this.orderDeliveryNumber
      }
      await this.$store.dispatch('actionDeleteDelivery', data)
      var responseData = await this.$store.state.ModuleShop.stateDeleteDelivery
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.dialogSuccessDelete = true
        this.dialogDelete = false
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: responseData.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    }
  }
}
</script>

<style scoped>
  ::v-deep(.theme--light.v-data-table.v-data-table--fixed-header thead th) {
    background: #F3F5F7 !important;
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.12) !important;
  }
</style>
