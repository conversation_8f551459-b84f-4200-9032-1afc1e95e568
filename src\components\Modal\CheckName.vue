<template>
  <v-container v-if="pathname !== '/registerLineOA'" :class="!MobileSize ? 'pt-0 px-0 pb-0' : 'pt-0 px-0 pb-0'">
    <v-dialog width="1150px" height="100%" v-model="ModalCheckName" persistent>
      <v-card width="100%" height="100%" style="border-radius: 8px; overflow-x: hidden;" :elevation="MobileSize ? 1 : 0" :class="MobileSize ? 'my-4' : 'mb-0'">
        <v-form ref="FormUserDetail" :lazy-validation="lazy">
          <!-- ส่วนรูปภาพ -->
          <v-card-text class="px-0 py-0">
            <v-img style="position: relative; max-width: 100%; z-index: 0;" :height="MobileSize ? '130px' : IpadSize ? '210px' : ''" :max-width="MobileSize ? '390px' : '100%'" :src="require('@/assets/ImageINET-Marketplace/ICONProfile/BackgroundUserDetail.png')">
              <v-row justify="end" dense class="mt-6 mr-6" v-if="isEdited === true">
                <!-- <v-btn width="75" height="40" text rounded color="#27AB9C" class="mr-2" @click="CloseModeEdit()">
                  ยกเลิก
                </v-btn> -->
                <v-btn width="100" height="40" rounded color="#27AB9C" class="white--text" @click="ConfirmModalEditAccount()">
                  {{ $t('CheckName.Save') }}
                </v-btn>
              </v-row>
            </v-img>
            <v-row justify="center" v-if="isEdited === true">
              <v-hover v-slot="{ hover }">
                <div :elevation="hover ? 16 : 2" :class="{ 'on-hover': hover }" class="mx-auto" color="grey lighten-4">
                  <div style="position: relative;" @click="changePic()">
                    <v-avatar :width="MobileSize ? 115 : 210" :height="MobileSize ? 115 : 210" style="border: 8px solid #FFFFFF; position: relative; z-index: 1; cursor: pointer;" :style="MobileSize ? 'margin-top: -5vh;' : IpadSize ? 'margin-top: -8vh;' : IpadProSize ? 'margin-top: -7vh;' : 'margin-top: -13vh;'">
                      <v-img v-if="userdetail.img_path === null || userdetail.img_path === undefined"  src="@/assets/noprofile.png" :max-width="MobileSize ? 100 : 210" :max-height="MobileSize ? 100 : 210"/>
                      <v-img v-else :src="userdetail.img_path" :max-width="MobileSize ? 100 : 210" :max-height="MobileSize ? 100 : 210" contain />
                    </v-avatar>
                    <span :style="MobileSize ? 'margin-top: 35px; right: 0px;' : 'margin-top: 65px; right: 15px;'" style="position: absolute; z-index: 2; height: 44px; width: 44px; background: #FFFFFF; border-radius: 47px; cursor: pointer;"><v-icon color="#27AB9C" size="24" style="text-align: center; margin: auto; display: flex; padding-top: 10px;">mdi-camera-outline</v-icon></span>
                  </div>
                </div>
              </v-hover>
              <input
              type="file"
              style="display: none"
              ref="image"
              id="picTure"
              accept="image/*"
              @change="showPicture"
              >
            </v-row>
          </v-card-text>
          <!-- ส่วนข้อมูล -->
          <v-card-text class="pt-9 px-9" v-if="isEdited === true">
             <v-row dense justify="center">
              <v-col cols="12" align="center">
                <span style="font-size: 24px; font-weight: 700; color: #333333;">{{ username }}</span>
              </v-col>
            </v-row>
            <v-row dense justify="center">
              <!-- ชื่อ -->
              <v-col cols="12" md="6" sm="6">
                <span style="font-size: 16px; font-weight: 400; color: #333333;">ชื่อ <span style="color: red;">*</span></span>
                <v-text-field
                  class="input_text"
                  :class="MobileSize ? '' : 'pr-3'"
                  placeholder="ระบุชื่อ"
                  outlined
                  dense
                  v-model="first_name"
                  :maxLength="20"
                  :rules="Rules.first_name"
                  style="border-radius: 8px;"
                  counter="20"
                  oninput="this.value = this.value.replace(/[^A-Za-zก-๙]/, '')">
                </v-text-field>
              </v-col>
              <!-- นามสกุล -->
              <v-col cols="12" md="6" sm="6">
                <span :class="MobileSize ? '' : 'pl-3'" style="font-size: 16px; font-weight: 400; color: #333333;">นามสกุล <span style="color: red;">*</span></span>
                <v-text-field
                  class="input_text"
                  placeholder="ระบุนามสกุล"
                  :class="MobileSize ? '' : 'pl-3'"
                  outlined
                  dense
                  @keypress="CheckSpacebar($event)"
                  v-model="last_name"
                  style="border-radius: 8px;"
                  :maxLength="30"
                  :rules="Rules.last_name"
                  counter="30"
                  oninput="this.value = this.value.replace(/[^A-Za-zก-๙\s]/g, '')">
                </v-text-field>
              </v-col>
              <!-- หมายเลขโทรศัพท์ -->
              <v-col cols="12" md="6" sm="6">
                <span style="font-size: 16px; font-weight: 400; color: #333333;">หมายเลขโทรศัพท์</span>
                <v-text-field class="input_text" :class="MobileSize ? '' : 'pr-3'" style="border-radius: 8px;" placeholder="เบอร์โทรศัพท์" outlined dense v-model="phone" disabled :maxLength="10"
                oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
              </v-col>
              <!-- E-mail -->
              <v-col cols="12" md="6" sm="6">
                <span :class="MobileSize ? '' : 'pl-3'" style="font-size: 16px; font-weight: 400; color: #333333;">E-mail</span>
                <v-text-field class="input_text" :class="MobileSize ? '' : 'pl-3'" style="border-radius: 8px;" placeholder="E-mail" outlined dense v-model="email" disabled
                ></v-text-field>
              </v-col>
            </v-row>
          </v-card-text>
        </v-form>
      </v-card>
    </v-dialog>
    <!-- Modal Confirm Edit Account -->
    <v-dialog v-model="ModalConfirmEditAccount" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="ModalConfirmEditAccount = !ModalConfirmEditAccount"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>บันทึกข้อมูล</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูลส่วนตัว</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="ModalConfirmEditAccount = !ModalConfirmEditAccount">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="EditAccount()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Tier -->
    <v-dialog v-model="ModalSuccessEditAccount" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeModalSuccess()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>บันทึกเสร็จสิ้น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูลส่วนตัวเรียบร้อย</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" :block="MobileSize ? true : false" class="white--text" rounded color="#27AB9C" @click="closeModalSuccess()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      pathname: '',
      ModalCheckName: false,
      ModalConfirmEditAccount: false,
      ModalSuccessEditAccount: false,
      isEdited: true,
      lazy: false,
      userdetail: [],
      img_path: '',
      showImage: '',
      DataImage: [],
      imageName: '',
      first_name: '',
      last_name: '',
      user: '',
      phone: '',
      email: '',
      username: '',
      Rules: {
        username: [
          v => !!v || 'กรุณากรอกชื่อผู้ใช้',
          v => /^[A-Za-z0-9]+$/.test(v) || 'กรอกได้เฉพาะตัวอักษรและตัวเลขเท่านั้น'
        ],
        password: [
          v => !!v || 'กรุณากรอกรหัสผ่าน'
        ],
        first_name: [
          v => !!v || 'กรุณากรอกชื่อจริง',
          v => v.length <= 20 || 'ห้ามกรอกชื่อจริงเกิน 20 ตัวอักษร',
          v => /[^๑-๙฿@#$%&*()_+{}:;<>,.?~]+$/.test(v) || 'กรอกได้เฉพาะตัวอักษรเท่านั้น'
        ],
        last_name: [
          v => !!v || 'กรุณากรอกนามสกุล',
          v => v.length <= 30 || 'ห้ามกรอกนามสกุลเกิน 30 ตัวอักษร',
          v => /[^๑-๙฿@#$%&*()_+{}:;<>,.?~]+$/.test(v) || 'กรอกได้เฉพาะตัวอักษรเท่านั้น'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => v.length === 10 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก'
        ],
        emailRules: [
          v => !!v || 'กรุณากรอกอีเมล',
          v => /.+@.+\..+/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[A-Za-z0-9_@.,/#&+-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ',
          v => /^\S*$/.test(v) || 'ห้ามใส่ช่องว่างในอีเมล'
        ],
        user: [
          v => !!v || 'กรุณากรอกชื่อผู้ใช้'
        ]
      }
    }
  },
  async created () {
    this.pathname = window.location.pathname.replace(/\/$/, '')
    if (localStorage.getItem('oneData') === null) {
      // this.$router.push({ path: '/' }).catch(() => {})
    } else {
      // this.getProfile()
    }
  },
  watch: {
    // MobileSize (val) {
    //   if (val === true) {
    //     this.$router.push({ path: '/userprofileDetail' }).catch(() => {})
    //   } else {
    //     this.$router.push({ path: '/userprofile' }).catch(() => {})
    //   }
    // }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    CheckSpacebar (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    open (phone, email, username) {
      this.phone = phone
      this.email = email
      this.user = username
      this.ModalCheckName = true
    },
    // async getProfile () {
    //   this.$store.commit('openLoader')
    //   this.img_path = ''
    //   if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
    //     var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    //     this.oneUserType = onedata.user.type_user
    //     // console.log('type user', this.oneUserType)
    //     var dataRole = JSON.parse(localStorage.getItem('roleUser'))
    //     var data = {
    //       role_user: dataRole.role
    //     }
    //     await this.$store.dispatch('actionsUserDetailPage', data)
    //     const userdetail = await this.$store.state.ModuleUser.stateUserDetailPage
    //     // console.log('userdetail data', userdetail)
    //     if (userdetail.message !== 'This user is unauthorized.') {
    //       this.$store.commit('closeLoader')
    //       this.userdetail = userdetail.data[0]
    //       this.img_path = this.userdetail.img_path
    //       // Username
    //       if (this.userdetail.username !== null && this.userdetail.username_oneid === null) {
    //         this.username = this.userdetail.username
    //       } else if (this.userdetail.username === null && this.userdetail.username_oneid !== null) {
    //         this.username = this.userdetail.username_oneid
    //       } else if (this.userdetail.username !== null && this.userdetail.username_oneid !== null) {
    //         this.username = this.userdetail.username
    //       } else {
    //         this.username = 'ไม่มีชื่อผู้ใช้งาน'
    //       }
    //       // Full name
    //       if ((this.userdetail.first_name_th === '' || this.userdetail.first_name_th === null) && (this.userdetail.last_name_th === '' || this.userdetail.last_name_th === null)) {
    //         this.fullname = 'ไม่มีชื่อ - นามสกุล'
    //       } else {
    //         this.fullname = this.userdetail.first_name_th + ' ' + this.userdetail.last_name_th
    //       }
    //       if (this.userdetail.address_data !== undefined) {
    //         this.userAddress = this.userdetail.address_data[0]
    //       } else {
    //         this.userAddress = []
    //       }
    //       this.userShop = this.userdetail.shop_data[0]
    //       // console.log('userdetail data', userdetail, this.userAddress, this.userShop)
    //     } else {
    //       this.$store.commit('closeLoader')
    //       this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
    //       window.location.assign('/')
    //     }
    //   } else {
    //     this.$store.commit('closeLoader')
    //     this.$router.push({ path: '/ ' }).catch(() => {})
    //   }
    // },
    CloseModeEdit () {
      if (this.userdetail.img_path !== '') {
        this.userdetail.img_path = this.img_path
        this.$refs.image.value = null
      } else {
        this.userdetail.img_path = null
        this.$refs.image.value = null
      }
      this.imageBase = ''
      this.$refs.FormUserDetail.resetValidation()
    },
    changePic () {
      document.getElementById('picTure').click()
    },
    onPickFile () {
      this.$refs.image.click()
    },
    showPicture (e) {
      const files = e.target.files
      if (files[0] !== undefined) {
        this.imageName = files[0].name
        const element = files[0]
        const reader = new FileReader()
        reader.readAsDataURL(element)
        reader.onload = () => {
          this.imageBase = reader.result.split(',')[1]
          this.showImage = URL.createObjectURL(element)
          this.userdetail.img_path = this.showImage
        }
      }
    },
    ConfirmModalEditAccount () {
      if (this.$refs.FormUserDetail.validate(true)) {
        this.ModalConfirmEditAccount = true
      }
    },
    async uploadToS3 (image) {
      var data = {
        image: [],
        type: '',
        seller_shop_id: ''
      }
      data.image.push(image)
      data.type = 'user_profile'
      data.seller_shop_id = '-1'
      await this.$store.dispatch('actionsUploadToS3', data)
      var response = await this.$store.state.ModuleShop.stateUploadToS3
      if (response.message === 'List Success.') {
        this.imageBase = response.data.list_path[0].path
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
      }
    },
    async EditAccount () {
      if (this.imageBase !== undefined) {
        await this.uploadToS3(this.imageBase)
      }
      this.ModalConfirmEditAccount = false
      this.$store.commit('openLoader')
      var data = {
        first_name_th: this.first_name,
        last_name_th: this.last_name,
        username: this.user,
        phone: this.phone,
        image: this.imageBase
      }
      // console.log(data, 'data')
      await this.$store.dispatch('actionsEditUserProfile', data)
      var responseEditAccount = await this.$store.state.ModuleUser.stateEditUserProfile
      // console.log(responseEditAccount, 'responseEditAccount')
      if (responseEditAccount.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.ModalSuccessEditAccount = true
        // window.location.reload()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', title: 'ไม่สามารถดำเนินการได้', showConfirmButton: false, timer: 1500 })
      }
    },
    closeModalSuccess () {
      // this.getProfile()
      this.$EventBus.$emit('getUserDetail')
      // this.isEdited = false
      this.ModalSuccessEditAccount = false
      this.ModalCheckName = false
    }
  }
}
</script>
