<template>
  <div>
    <v-col :class="!MobileSize ? 'pt-6 pl-6' : ''">
      <span class="" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;"
        v-if="!MobileSize">
        โปรแกรม Affiliate
      </span>
      <span style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backtoUser()">mdi-chevron-left</v-icon>
        โปรแกรม Affiliate
      </span>
    </v-col>
    <div class="d-flex justify-center">
      <v-card width="90%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4' : 'mb-4' ]">
        <v-card style="overflow-x: hidden;">
          <v-row class="py-8" dense no-gutters>
            <v-img src="@/assets/inetlogo.png" contain height="58" width="121" position="center" class="mr-4"></v-img>
          </v-row>
          <v-card-text style="font-size: 18px; text-align:center; overflow: hidden;">{{title}}</v-card-text>
          <v-divider></v-divider>
          <v-card-text style="height: 300px;">
            <div v-html="this.detail" style="padding: 15px 0;"></div>
          </v-card-text>
          <v-divider></v-divider>
        </v-card>
        <v-card-text style="overflow: hidden;">
          <v-checkbox v-model="checkbox">
            <template v-slot:label>
              <div>
                รับทราบ
                <span style="text-decoration: underline; color:#27AB9C;">ข้อกำหนดดังกล่าว</span>
              </div>
            </template>
          </v-checkbox>
        </v-card-text>
        <v-row justify="center" class="mb-4">
        <v-card-actions>
          <v-btn :disabled="!checkbox" dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="confirmConsent()">
              รับทราบ
          </v-btn>
        </v-card-actions>
        </v-row>
      </v-card>
    </div>
  </div>
</template>

<script>
import { Decode } from '@/services'
export default {
  components: {
  },
  data () {
    return {
      one_id: '',
      dialog: false,
      data: '',
      checkbox: false,
      title: '',
      detail: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    if (localStorage.getItem('oneData') !== null) {
      // this.getDataCoupons()
      // this.checkeKYC()
      this.getText()
      // this.getPoint()
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
  },
  methods: {
    async getText () {
      await this.$store.dispatch('actionsGetTextConsent')
      var res = await this.$store.state.ModuleUser.stateGetTextConsent
      this.title = res.data.title
      this.detail = res.data.consent_text
      // console.log('getText ===>', res)
    },
    // async checkeKYC () {
    //   // alert('123456789')
    //   // this.checkeKYCUser = false
    //   await this.$store.dispatch('actionsCheckeKYC')
    //   var response = await this.$store.state.ModuleBusiness.stateCheckeKYC
    //   // console.log(response)
    //   if (response.result === 'SUCCESS') {
    //     this.checkeKYCUser = response.data.eKYC_approve === 'yes'
    //     if (response.data.eKYC_approve === 'no') {
    //       this.checkeKYCUser = false
    //       if (this.MobileSize) {
    //         this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">ยังไม่ได้ยืนยันตัวตน</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">ท่านยังไม่ได้ยืนยันตัวตนผ่าน eKYC </p><span style="line-height: normal;">กรุณายืนยันตัวตนก่อน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; padding: 8px;">กดที่นี่เพื่อยืนยันตัวตนผ่าน eKYC</a>', showConfirmButton: false })
    //       } else {
    //         this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">ยังไม่ได้ยืนยันตัวตน</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">ท่านยังไม่ได้ยืนยันตัวตนผ่าน eKYC </p><span style="line-height: normal;">กรุณายืนยันตัวตนก่อน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; margin: 0 1.875em; padding: 8px;">กดที่นี่เพื่อยืนยันตัวตนผ่าน eKYC</a>', showConfirmButton: false })
    //       }
    //     } else {
    //       if (response.data.eKYC_expire === 'yes') {
    //         if (this.MobileSize) {
    //           this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">หมดอายุ</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">กรุณาต่ออายุ eKYC </p><span style="line-height: normal;">ก่อนยืนยันตัวตน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; padding: 8px;">กดที่นี่เพื่อต่ออายุ eKYC</a>', showConfirmButton: false })
    //         } else {
    //           this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">หมดอายุ</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">กรุณาต่ออายุ eKYC </p><span style="line-height: normal;">ก่อนยืนยันตัวตน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; margin: 0 1.875em; padding: 8px;">กดที่นี่เพื่อต่ออายุ eKYC</a>', showConfirmButton: false })
    //         }
    //       }
    //     }
    //   }
    // },
    backtoUser () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.progress-gradient {
  width: 100%;
  height: 100%;
  border-radius: 48px;
  background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-progress-linear {
  background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.border-scroll {
    border: 1px solid #EBEBEB;
}
</style>
