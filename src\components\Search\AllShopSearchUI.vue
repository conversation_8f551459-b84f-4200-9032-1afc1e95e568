<template>
  <div>
    <v-breadcrumbs :items="RoleUser == 'sale_order' ? itemsSale : items" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
      <template v-slot:divider>
        <v-icon color="#3EC6B6">mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item
        :href="item.href"
        :disabled="item.disabled"
        >
        <span :style="{ color: item.color, cursor: item.disabled !== true ? 'pointer' : 'none', fontSize: '16px' }" v-snip="1">{{ item.text }}</span>
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <v-container class="pt-2">
      <v-overlay :value="overlay2">
        <v-progress-circular indeterminate size="64"></v-progress-circular>
      </v-overlay>
      <v-row class="mt-4">
        <!-- <v-col cols="12" md="12"> -->
        <span style="font-weight: 700; font-size: 18px; color: #333333;" class="mt-2 ml-3">ร้านค้าที่เกี่ยวข้องกับ "<span style="color: #27AB9C;">{{ textSearch }}</span>"</span>
        <v-spacer></v-spacer>
        <!-- <v-btn text color="#27AB9C" @click="gotoShowAllShop()">ร้านค้าอื่นๆ<v-icon small class="pt-1">mdi-chevron-right</v-icon></v-btn> -->
        <!-- </v-col> -->
      </v-row>
      <v-row>
        <v-col cols="12" md="12" v-if="showSkeletonLoader">
          <v-skeleton-loader
            v-bind="attrs"
            type="image"
          ></v-skeleton-loader>
        </v-col>
        <v-col cols="12" md="12" v-else>
          <v-card class="mx-auto mt-5 mb-5" max-width="100%" outlined hover v-for="(item, index) in paginated" :key="index" @click.prevent="gotoShopDetail(item)" :href="item.link">
            <v-card-text style="padding: 1.5625rem;">
              <v-row no-gutters justify="start">
                <v-col cols="4" md="1" sm="2" xs="12" :align="IpadSize ? 'center' : ''">
                  <v-avatar size="60" @click.prevent="gotoShopDetail(item)" v-if="item.seller_shop_logo !== ''" style="cursor: pointer;">
                    <v-img
                      alt="user"
                      :src="`${item.seller_shop_logo}?=${new Date().getTime()}`"
                      style="border: 1px solid #EBEBEB"
                      contain
                    ></v-img>
                  </v-avatar>
                  <v-avatar size="60" v-else style="cursor: pointer;" @click.prevent="gotoShopDetail(item)">
                    <v-img src="@/assets/ImageINET-Marketplace/Shop/Store.png"></v-img>
                  </v-avatar>
                </v-col>
                <v-col cols="8" md="5" sm="10" xs="12" :class="MobileSize? 'pa-0 ma-0' : ''">
                  <v-row dense justify="start">
                    <v-col cols="12" md="12" sm="12" xs="12">
                      <p style="font-weight: bold; font-size: 15px;">{{ item.name_th }}</p>
                    </v-col>
                    <v-col cols="12" md="12" sm="12" xs="12" :class="MobileSize? 'pa-0 ma-0' : ''">
                      <v-row dense>
                        <!-- <v-btn outlined small color="#27AB9C" @click="gotoShopDetail()"><v-icon small class="pr-1">mdi-chat</v-icon> แชท</v-btn>
                        <v-divider vertical class="ml-2 mr-2" ></v-divider> -->
                        <v-btn :class="MobileSize? 'pa-0 ma-0' : ''" small color="#27AB9C" @click.prevent="gotoShopDetail(item)" text><v-icon small class="pr-1">mdi-storefront</v-icon> ดูร้านค้า</v-btn>
                        <!-- <v-divider vertical class="ml-2 mr-2" ></v-divider>
                        <v-btn outlined small color="#27AB9C" @click="gotoShopDetail()"><v-icon small class="pr-1">mdi-account-plus</v-icon> ติดตาม</v-btn> -->
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
      <v-row justify="center" class="my-6">
        <v-pagination
         color="#3EC6B6"
         v-model="pageNumber"
         :length="pageMax"
         :total-visible="MobileSize ? 5 : 7"
         class="paginationStyle"
         @change="pageChange()"
        ></v-pagination>
      </v-row>
      <!-- <a-row type="flex">
        <a-col :span="24">
          <a-row type="flex">
            <a-col :span="12">
              <span style="font-weight: bold;">ร้านค้าที่เกี่ยวข้องกับ "{{ textSearch }}"</span>
            </a-col>
          </a-row>
        </a-col>
        <a-col :span="24" style="padding: 0; margin-top: -20px; margin-bottom: -20px;">
          <a-divider></a-divider>
        </a-col>
        <a-col :span="24" style="margin-top: 0px;">
          <v-card class="mx-auto mt-5 mb-5" max-width="100%" outlined hover v-for="(item, index) in allShopData" :key="index" @click="gotoShopDetail(item)">
            <v-card-text style="padding: 1.5625rem;">
              <v-row no-gutters justify="start">
                  <v-col cols="1" md="1" sm="12" xs="12">
                  <v-avatar size="60" @click="gotoShopDetail(item)" v-if="item.path_logo !== ''" style="cursor: pointer;">
                    <img
                    alt="user"
                    :src="`${PathImage}${item.path_logo}?=${new Date().getTime()}`"
                    >
                  </v-avatar>
                  <v-avatar size="60" v-else style="cursor: pointer;" @click="gotoShopDetail(item)">
                    <v-icon>
                      mdi-storefront
                    </v-icon>
                  </v-avatar>
                  </v-col>
                  <v-col cols="2" md="2" sm="12" xs="12">
                  <v-row dense no-gutters justify="start">
                      <v-col cols="12" md="12" sm="12" xs="12">
                        <p style="font-weight: bold; font-size: 15px;">{{ item.name_th }}</p>
                      </v-col>
                      <v-col cols="12" md="12" sm="12" xs="12">
                        <v-btn outlined small color="orange" @click="gotoShopDetail(item)"><v-icon small class="pr-1">mdi-storefront</v-icon> ดูร้านค้า</v-btn>
                      </v-col>
                  </v-row>
                  </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </a-col>
      </a-row> -->
    </v-container>
  </div>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      current: 1,
      pageSize: 6,
      allShopData: [],
      overlay2: true,
      textSearch: '',
      PathImage: process.env.VUE_APP_IMAGE,
      path: process.env.VUE_APP_DOMAIN,
      pageMax: null,
      items: [
        {
          text: 'หน้าแรก',
          disabled: false,
          color: '#636363',
          href: '/'
        },
        {
          text: 'ผลการค้นหา',
          disabled: true,
          color: '#3EC6B6',
          href: ''
        }
      ],
      showSkeletonLoader: false,
      RoleUser: ''
    }
  },
  created () {
    this.$EventBus.$emit('getPath')
    this.showSkeletonLoader = true
    if (localStorage.getItem('roleUser') !== null) {
      this.RoleUser = JSON.parse(localStorage.getItem('roleUser')).role
    } else {
      this.RoleUser = 'ext_buyer'
    }
    if (this.RoleUser === 'sale_order') {
      this.pathshop = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale'))).path
      this.itemsSale = [
        {
          text: 'หน้าแรก',
          disabled: false,
          color: '#636363',
          href: '/'
        },
        {
          text: 'ผลการค้นหา',
          disabled: true,
          color: '#3EC6B6',
          href: '/shoppingcart'
        }
      ]
    }
    if (localStorage.getItem('AllShopSearch') !== null) {
      this.getAllItem()
    } else {
      this.$swal.fire({
        icon: 'error',
        title: 'ไม่เจอร้านค้า',
        showConfirmButton: false,
        timer: 1500
      })
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  computed: {
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      return this.allShopData.slice(this.indexStart, this.indexEnd)
    },
    MobileSize () {
      // console.log('mobile', this.$vuetify.breakpoint)
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      // console.log('ipad pro w:1024', this.$vuetify.breakpoint)
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    getAllItem () {
      this.allShopData = []
      this.textSearch = this.$router.currentRoute.params.data
      var setBeforeData = JSON.parse(Decode.decode(localStorage.getItem('AllShopSearch')))
      for (var i = 0; i < setBeforeData.length; i++) {
        setBeforeData[i].link = this.path + 'shoppage/' + encodeURIComponent(setBeforeData[i].name_th.replace(/\s/g, '-') + '-' + setBeforeData[i].id)
      }
      this.allShopData = setBeforeData
      this.pageMax = parseInt(this.allShopData.length / 6) === 0 ? 1 : Math.ceil(this.allShopData.length / 6)
      // console.log(parseInt(this.allShopData.length / 5))
      this.overlay2 = false
      this.showSkeletonLoader = false
      // console.log(this.allShopData)
    },
    gotoShopDetail (val) {
      const shopCleaned = encodeURIComponent(val.name_th.replace(/\s/g, '-'))
      this.$router.push(`/shoppage/${shopCleaned}-${val.id}`).catch(() => {})
    }
  }
}
</script>

<style scoped>
.paginationStyle /deep/ .v-pagination__item {
  background: transparent;
  border-radius: 6px !important;
  border: 1px solid #E6E6E6 !important;
  font-size: 1rem;
  height: 40px;
  margin: 0.3rem;
  min-width: 40px;
  padding: 0 5px;
  text-decoration: none;
  transition: 0.3s cubic-bezier(0, 0, 0.2, 1);
  width: auto;
  box-shadow: none !important;
}
.paginationStyle /deep/ .v-pagination__navigation {
  box-shadow: none !important;
  border-radius: 6px !important;
  border: 1px solid #E6E6E6 !important;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  height: 40px;
  width: 40px;
  margin: 0.3rem 10px;
}
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 12px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
.container {
  max-width: 1400px !important;
}
.v-btn-toggle--group > .v-btn.v-btn {
  background-color: transparent !important;
  border-color: transparent;
  margin: 0px !important;
  min-width: auto;
}
</style>
