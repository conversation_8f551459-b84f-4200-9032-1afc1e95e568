<template>
  <v-container style="min-height: 650px" :class="MobileSize ? 'mt-3' : 'mx-0'">
    <v-card
      width="100%"
      height="100%"
      elevation="0"
      :class="MobileSize ? 'px-2' : 'px-2'"
    >
      <v-row dense class="mb-4 px-0">
        <v-col cols="12" class="mt-3" :class="MobileSize ? 'px-5' : 'px-0'">
          <v-row class="mx-0" v-if="!MobileSize">
            <v-card-title
              style="
                font-weight: bold;
                font-size: 24px;
                line-height: 32px;
                color: #333333;
              "
              >{{ approvalTitle }}</v-card-title
            >
          </v-row>
          <v-row v-else>
            <v-card-title
              class="pl-2"
              style="
                font-weight: bold;
                font-size: 18px;
                line-height: 32px;
                color: #333333;
              "
            >
              <v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()"
                >mdi-chevron-left</v-icon
              >จัดการกลุ่มลูกค้า</v-card-title
            >
          </v-row>
        </v-col>
        <v-col cols="12" md="5" sm="12">
          <v-text-field
            v-model="search"
            style="border-radius: 8px"
            append-icon="mdi-magnify"
            placeholder="ค้นหาจากชื่อกลุ่มลูกค้า"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
        <v-col
          cols="12"
          sm="7"
          align="end"
          class="mt-1"
          v-if="!MobileSize && !IpadSize"
        >
          <v-btn
            :block="MobileSize"
            rounded
            class="pt-0"
            width="153"
            height="40"
            :class="MobileSize ? 'mt-0' : 'mt-0 '"
            color="#27AB9C"
            dark
            @click="DetailCreateEdit('create')"
            ><v-icon left>mdi-plus</v-icon>เพิ่มรายการ</v-btn
          >
        </v-col>
        <v-col cols="12" v-if="!MobileSize && !IpadSize">
          <span style="font-weight: 600">รายการผู้อนุมัติทั้งหมด {{ dataTable.length }} รายการ</span>
        </v-col>
        <v-col cols="7" v-if="MobileSize" class="d-flex align-center">
          <span style="font-weight: 600">รายการผู้อนุมัติทั้งหมด {{ dataTable.length }} รายการ</span>
        </v-col>
        <v-col cols="8" v-if="IpadSize" class="d-flex align-center">
          <span style="font-weight: 600">รายการผู้อนุมัติทั้งหมด {{ dataTable.length }} รายการ</span>
        </v-col>
        <v-col
          cols="5"
          align="end"
          class="mt-1"
          v-if="MobileSize"
        >
          <v-btn
            rounded
            class="pt-0"
            width="125"
            height="35"
            color="#27AB9C"
            dark
            @click="DetailCreateEdit('create')"
            ><v-icon left>mdi-plus</v-icon>เพิ่มรายการ</v-btn
          >
        </v-col>
        <v-col
          cols="4"
          align="end"
          class="mt-1"
          v-if="IpadSize"
        >
          <v-btn
            rounded
            class="pt-0"
            width="153"
            height="40"
            color="#27AB9C"
            dark
            @click="DetailCreateEdit('create')"
            ><v-icon left>mdi-plus</v-icon>เพิ่มรายการ</v-btn
          >
        </v-col>
        <v-col class="mt-2">
          <v-data-table
            :headers="headers"
            :items="dataTable"
            :footer-props="{ 'items-per-page-text': 'จำนวนแถว' }"
            :search="search"
            item-key="referance_id"
            color="blue"
            class="elevation-1 px-0"
            no-results-text="ไม่พบกลุ่มคู่ค้าที่ค้นหา"
          >
            <template v-slot:[`item.name`]="{ item }">
              {{ item.name }}
            </template>
            <template v-slot:[`item.manage`]="{ item }">
              <v-row no-gutters justify="space-around" align="center" class="px-13">
                <v-card class="" title="รายละเอียด">
                  <v-btn text color="#27AB9C" small @click="GetDetailApprover(item)">
                    <v-icon class="pr-1" color="27AB9C">mdi-file-document-outline</v-icon>
                  </v-btn>
                </v-card>
                <v-card class="" title="แก้ไข">
                  <v-btn text color="#27AB9C" small @click="DetailCreateEdit('edit', item)">
                    <v-icon class="" color="27AB9C">mdi-pencil-outline</v-icon>
                  </v-btn>
                </v-card>
                <v-card class="" title="ลบ">
                  <v-btn text x-small color="#F5222D" small @click="openDialogAwait(item)">
                    <v-icon color="#F5222D">mdi-delete-outline</v-icon>
                  </v-btn>
                </v-card>
              </v-row>
            </template>
          </v-data-table>
        </v-col>
      </v-row>
    </v-card>
    <v-dialog
      v-model="detailApprover"
      :width="MobileSize ? '100%' : IpadSize ? '100%' : '782'"
      persistent
    >
      <v-card
        elevation="0"
        style="background: #ffffff; border-radius: 24px; overflow-x: hidden; overflow-y: hidden"
      >
        <v-form ref="FormSettingTier" :lazy-validation="lazy">
          <v-card-text class="px-0 pt-0">
            <div
              :style="
                MobileSize
                  ? 'width: 100%'
                  : IpadSize
                  ? 'width: 100%'
                  : 'width: 782px'
              "
              class="backgroundHead"
              style="position: absolute; height: 120px"
            >
              <v-row style="height: 120px">
                <v-col style="text-align: center" class="pt-4">
                  <span
                    :class="
                      MobileSize
                        ? 'title-mobile white--text'
                        : 'title white--text'
                    "
                    :style="MobileSize ? 'font-size: 20px;' : 'font-size: 24px;'"
                    ><b>รายละเอียด</b></span
                  >
                </v-col>
                <v-btn fab small @click="cancel()" icon class="mt-3"
                  ><v-icon color="white">mdi-close</v-icon></v-btn
                >
              </v-row>
            </div>
            <div
              style="
                position: relative;
                padding: 0px 12px 0px;
                display: flex;
                padding-top: 60px;
              "
            >
              <v-row
                :width="MobileSize ? '100%' : '782px'"
                style="
                  height: 50px;
                  border-radius: 24px 24px 0px 0px;
                  background: #ffffff;
                "
              >
                <v-col style="text-align: center"> </v-col>
              </v-row>
            </div>
            <div class="" style="position: relative">
              <v-card
                elevation="0"
                width="100%"
                height="100%"
                style="background: #ffffff; border-radius: 20px 20px 0px 0px"
                :style="
                  MobileSize
                    ? 'padding: 20px 20px 10px 20px;'
                    : 'padding: 40px 48px 10px 48px;'
                "
              >
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" class="d-flex" v-if="!MobileSize">
                      <v-row dense class="mr-auto">
                        <v-img
                          src="@/assets/Create_Store/partnerShopDetail.png"
                          max-height="62"
                          max-width="62"
                        ></v-img>
                        <span
                          class="pt-5 pl-4"
                          style="
                            font-weight: 600;
                            color: #333333;
                            font-size: 16px;
                          "
                        >
                          {{ selectDetail }}
                        </span>
                      </v-row>
                      <!-- เก็บไว้ใช้แก้ไข -->
                      <!-- <span class="ml-auto pt-3" style="text-align: end">
                        <v-row dense>
                          <span
                            class="mt-2 pr-2"
                            style="
                              font-weight: 400;
                              color: #333333;
                              font-size: 16px;
                            "
                            >สถานะการใช้งาน :</span
                          >
                          <span class="mt-1" v-if="statusDetail === 'active'">
                            <v-chip color="#F0FEE8" text-color="#52C41A"
                              >กำลังใช้งาน</v-chip
                            >
                          </span>
                          <span class="mt-1" v-if="statusDetail === 'inactive'">
                            <v-chip color="#FEE7E8" text-color="#F5222D"
                              >ยกเลิกใช้งาน</v-chip
                            >
                          </span>
                        </v-row>
                      </span> -->
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12">
                      <v-row dense>
                        <v-col cols="12" md="4" sm="2">
                          <span
                            class=""
                            :style="MobileSize ? 'font-size: 14px;' : ''"
                            >ชื่อรูปแบบการอนุมัติ : <b>{{name}}</b></span
                          >
                        </v-col>
                        <v-col cols="12" md="8" sm="10">
                          <span class="" v-if="approverA.length !== 0"><b class="detailTierText">ผู้อนุมัติลำดับที่ : 1</b></span>
                          <v-row class="mt-1" v-if="approverA.length !== 0">
                            <v-col cols="12" class="mb-2" v-for="(item, index) of approverA" :key="index">
                              <v-card>
                                <v-card-text>
                                  <v-row no-gutters justify="end">
                                    <v-col cols="12" md="3">
                                      <v-avatar size="60">
                                        <v-img
                                          v-if="item.img_path !== null"
                                          :src="item.img_path"
                                        ></v-img>
                                        <v-img v-else src="@/assets/NoImage.png"></v-img>
                                      </v-avatar>
                                    </v-col>
                                    <v-col cols="12" md="9">
                                      <p>
                                        ชื่อ: <b>{{item.name}}</b>
                                      </p>
                                      <p>
                                        อีเมล: <b>{{item.email}}</b>
                                      </p>
                                    </v-col>
                                    <v-btn
                                      v-if="Action === 'EditB'"
                                      rounded
                                      small
                                      outlined
                                      color="red"
                                      @click="Delete(index, item)"
                                      ><v-icon>mdi-delete</v-icon></v-btn
                                    >
                                  </v-row>
                                </v-card-text>
                              </v-card>
                            </v-col>
                          </v-row>
                          <span class="" v-if="approverB.length !== 0"><b class="detailTierText">ผู้อนุมัติลำดับที่ : 2</b></span>
                          <v-row class="mt-1" v-if="approverB.length !== 0">
                            <v-col cols="12" class="mb-2" v-for="(item, index) of approverB" :key="index">
                              <v-card>
                                <v-card-text>
                                  <v-row no-gutters justify="end">
                                    <v-col cols="12" md="3">
                                      <v-avatar size="60">
                                        <v-img
                                          v-if="item.img_path !== null"
                                          :src="item.img_path"
                                        ></v-img>
                                        <v-img v-else src="@/assets/NoImage.png"></v-img>
                                      </v-avatar>
                                    </v-col>
                                    <v-col cols="12" md="9">
                                      <p>
                                        ชื่อ: <b>{{item.name}}</b>
                                      </p>
                                      <p>
                                        อีเมล: <b>{{item.email}}</b>
                                      </p>
                                    </v-col>
                                    <v-btn
                                      v-if="Action === 'EditB'"
                                      rounded
                                      small
                                      outlined
                                      color="red"
                                      @click="Delete(index, item)"
                                      ><v-icon>mdi-delete</v-icon></v-btn
                                    >
                                  </v-row>
                                </v-card-text>
                              </v-card>
                            </v-col>
                          </v-row>
                          <span class="" v-if="approverC.length !== 0"><b class="detailTierText">ผู้อนุมัติลำดับที่ : 3</b></span>
                          <v-row class="mt-1" v-if="approverC.length !== 0">
                            <v-col cols="12" class="ma-0" v-for="(item, index) of approverC" :key="index">
                              <v-card>
                                <v-card-text>
                                  <v-row no-gutters justify="end">
                                    <v-col cols="12" md="3">
                                      <v-avatar size="60">
                                        <v-img
                                          v-if="item.img_path !== null"
                                          :src="item.img_path"
                                        ></v-img>
                                        <v-img v-else src="@/assets/NoImage.png"></v-img>
                                      </v-avatar>
                                    </v-col>
                                    <v-col cols="12" md="9">
                                      <p>
                                        ชื่อ: <b>{{item.name}}</b>
                                      </p>
                                      <p>
                                        อีเมล: <b>{{item.email}}</b>
                                      </p>
                                    </v-col>
                                    <v-btn
                                      v-if="Action === 'EditB'"
                                      rounded
                                      small
                                      outlined
                                      color="red"
                                      @click="Delete(index, item)"
                                      ><v-icon>mdi-delete</v-icon></v-btn
                                    >
                                  </v-row>
                                </v-card-text>
                              </v-card>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
          </v-card-text>
        </v-form>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogAwaitTier" width="424" persistent>
      <v-card style="background: #ffffff; border-radius: 24px">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn color="#CCCCCC" icon @click="closeDialogAwait()">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center">
            <p
              style="
                font-weight: 700;
                font-size: 24px;
                line-height: 24px;
                color: #333333;
              "
              class="my-4"
            >
              <b>{{ deleteTitle }}</b>
            </p>
            <span
              style="
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                color: #9a9a9a;
              "
              >คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span
            >
          </v-card-text>
          <v-card-text class="px-0">
            <v-row dense justify="center">
              <v-btn
                :width="MobileSize ? '125' : '156'"
                height="38"
                outlined
                rounded
                color="#27AB9C"
                class="mr-4"
                @click="closeDialogAwait()"
                >ยกเลิก</v-btn
              >
              <v-btn
                :width="MobileSize ? '125' : '156'"
                height="38"
                class="white--text"
                rounded
                color="#27AB9C"
                @click="DeleteApprover()"
                >ตกลง</v-btn
              >
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      lazy: true,
      detailApprover: false,
      dialogAwaitTier: false,
      sellerShopID: '',
      Action: '',
      search: '',
      name: '',
      deleteID: '',
      dataTable: [],
      approverA: [],
      approverB: [],
      approverC: [],
      headers: [
        {
          text: 'ชื่อรูปแบบการอนุมัติ',
          value: 'name',
          width: '200',
          align: 'start',
          sortable: false,
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'จัดการ',
          value: 'manage',
          sortable: false,
          width: '300',
          filterable: false,
          align: 'center',
          class: 'backgroundTable fontTable--text'
        }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    approvalTitle () {
      const isPartnerPage = this.$route.path.includes('Partner')
      return isPartnerPage ? 'จัดการรูปแบบการอนุมัติคู่ค้า' : 'จัดการรูปแบบการอนุมัติฝ่ายขาย'
    },
    selectDetail () {
      const isPartnerPage = this.$route.path.includes('Partner')
      return isPartnerPage ? 'รายละเอียดรูปแบบการอนุมัติคู่ค้า ' : 'รายละเอียดรูปแบบการอนุมัติฝ่ายขาย'
    },
    deleteTitle () {
      const isPartnerPage = this.$route.path.includes('Partner')
      return isPartnerPage ? 'ลบรูปแบบการอนุมัติคู่ค้า' : 'ลบรูปแบบการอนุมัติฝ่ายขาย'
    }
    // finisthTitle () {
    //   const isPartnerPage = this.$route.path.includes('Partner')
    //   return isPartnerPage ? 'ลบรูปแบบการอนุมัติคู่ค้าสำเร็จ' : 'ลบรูปแบบการอนุมัติฝ่ายขายสำเร็จ'
    // }
  },
  watch: {},
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
    this.$EventBus.$emit('changeNav')
    var sellerShopID = localStorage.getItem('shopSellerID')
    this.sellerShopID = sellerShopID
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.onedata.user.type_user === 'general_user') {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    }
    this.$EventBus.$on('GetListPositionSalesOrder', this.GetListPositionSalesOrder)
    this.GetListPositionSalesOrder()
  },
  methods: {
    openDialogAwait (item) {
      this.deleteID = item.id
      this.dialogAwaitTier = true
    },
    closeDialogAwait () {
      this.dialogAwaitTier = false
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    async GetDetailApprover (item) {
      this.$store.commit('openLoader')
      this.detailApprover = true
      var data = {
        seller_shop_id: this.sellerShopID,
        approve_position_id: item.id
      }
      await this.$store.dispatch('actionsGetDetailApproverSalesOrder', data)
      var res = await this.$store.state.ModuleSaleOrder.stateGetDetailApproverSalesOrder
      if (res.message === 'Get Detail Approver Success.') {
        // console.log(res.data.approver_list)
        this.name = res.data.approve_position_data.approve_position_name
        this.approverA = res.data.approver_list.approver_list_1.approver
        this.approverB = res.data.approver_list.approver_list_2.approver
        this.approverC = res.data.approver_list.approver_list_3.approver
      } else {
        if (res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        }
      }
      this.$store.commit('closeLoader')
    },
    cancel () {
      this.detailApprover = false
    },
    async openDetail (item) {
      this.detailApprover = true
    },
    async DeleteApprover () {
      const isPartnerPage = this.$route.path.includes('Partner')
      const confirm = isPartnerPage ? 'ลบรูปแบบการอนุมัติคู่ค้าสำเร็จ' : 'ลบรูปแบบการอนุมัติฝ่ายขายสำเร็จ'

      var data = {
        seller_shop_id: this.sellerShopID,
        approve_position_id: this.deleteID
      }
      await this.$store.dispatch('actionsDeleteApproverSalesOrder', data)
      var res = await this.$store.state.ModuleSaleOrder.stateDeleteApproverSalesOrder
      if (res.message === 'Delete approve Position Success.') {
        this.dialogAwaitTier = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'success',
          title: confirm
        })
        this.GetListPositionSalesOrder()
      } else {
        if (res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            title: 'SERVER ERROR',
            text: `${res.message}`
          })
        }
      }
    },
    async GetListPositionSalesOrder (type) {
      this.$store.commit('openLoader')
      console.log('GetListPositionSalesOrder', type)
      var data = {
        seller_shop_id: this.sellerShopID,
        // type: type
        type: type !== undefined ? type : this.$router.currentRoute.path.includes('Partner') ? 'partner_order' : 'sale_order'
      }
      await this.$store.dispatch('actionsGetListPositionSalesOrder', data)
      var res = await this.$store.state.ModuleSaleOrder
        .stateGetListPositionSalesOrder
      if (res.result === 'SUCCESS') {
        if (res.message === 'Get List approve Position Success.') {
          this.$store.commit('closeLoader')
          this.dataTable = res.data.position_list
          // console.log(this.dataTable)
          if (this.dataTable.length === 0) {
            this.disableTable = true
          } else {
            this.disableTable = false
          }
        }
      } else {
        this.$store.commit('closeLoader')
        if (res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            title: 'SERVER ERROR',
            text: `${res.message}`
          })
        }
      }
    },
    DetailCreateEdit (type, item) {
      var header = ''
      var path = this.$router.currentRoute.path.includes('Partner') ? 'ManageSalesPartnerApprovalDetail' : 'ManageSalesApprovalDetail'
      var Mobilepath = this.$router.currentRoute.path.includes('Partner') ? 'ManageSalesPartnerApprovalDetailMobile' : 'ManageSalesApprovalDetailMobile'
      if (type === 'create') {
        header = 'create'
        if (!this.MobileSize) {
          this.$router.push({ path: `/${path}?Status=${header}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/${Mobilepath}?Status=${header}` }).catch(() => {})
        }
      } else if (type === 'edit') {
        header = 'edit'
        if (!this.MobileSize) {
          this.$router.push({ path: `/${path}?Status=${header}&id=${item.id}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/${Mobilepath}?Status=${header}&id=${item.id}` }).catch(() => {})
        }
        this.search = ''
        // this.$router.push({ path: `/ManageSalesApprovalDetail?Status=${header}&id=${item.id}` }).catch(() => {})
      }
    }
  }
}
</script>
