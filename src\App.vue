<template>
  <v-app>
    <router-view :key="$i18n.locale"/>
    <Loader/>
  </v-app>
</template>

<script>
// import '@/styles/main.scss'
import Loader from '@/components/sharedComponent/Loader'
import { Decode } from '@/services'
export default {
  components: {
    Loader
  },
  data () {
    return {
      pathname: ''
    }
  },
  metaInfo () {
    return {
      title: 'Nex Gen Commerce | ซื้อขายออนไลน์ ส่งเสริมธุรกิจไทย',
      titleTemplate: '%s',
      htmlAttrs: {
        lang: 'th-TH'
      },
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { property: 'og:title', content: 'Nex Gen Commerce | ซื้อขายออนไลน์ ส่งเสริมธุรกิจไทย', template: 'Nex Gen Commerce', vmid: 'og:title' },
        { vmid: 'description', name: 'description', content: 'Nex Gen Commerce ซื้อขายออนไลน์ สินค้าและบริการ B2B2C เพิ่มโอกาสทางการขาย ต่อยอดธุรกิจ ส่งเสริมธุรกิจไทย ออก e-Tax invoice "GP ต่ำ" ค่าส่งถูก Affiliate' }
      ],
      link: [
        { rel: 'canonical', href: 'https://nexgencommerce.one.th/' }
      ]
    }
  },

  async created () {
    this.pathname = window.location.pathname.replace(/\/$/, '')
    // this.$EventBus.$on('checkPDPA', this.checkPDPA)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.addEventListener('storage', this.handleStorageChange)
  },
  watch: {
    async $route (to) {
      this.$store.dispatch('actionsCreateLogUser')
      const response = await this.$store.state.ModuleHompage.stateCreateLogUser
      if (response.result === 'SUCCESS') {
      }
    }
  },
  beforeDestroy () {
    window.removeEventListener('storage', this.handleStorageChange)
  },
  methods: {
    async handleStorageChange (event) {
      // console.log(event)
      if (localStorage.length === 0) {
        window.location.reload()
      } else {
        if (event.key === 'roleUser') {
          // console.log('event.oldValue', event.oldValue)
          // console.log('event.newValue', event.newValue)
          if ((event.newValue === '{"role":"sale_order"}' || event.newValue === '{"role":"sale_order_no_JV"}' || event.newValue === '{"role":"sale_order_vendor"}') && event.newValue !== null) {
            // this.$swal.fire({
            //   showConfirmButton: false,
            //   timer: 3500,
            //   timerProgressBar: true,
            //   icon: 'warning',
            //   html: '<h3>ได้มีการเปลี่ยนสิทธิ์ผู้ใช้งาน</h3>'
            // })
            this.$EventBus.$emit('CheckPermission')
            await this.$router.push({ path: '/' }).catch(() => {})
            window.location.reload()
          } else if ((event.newValue === '{"role":"ext_buyer"}' || event.newValue === '{"role":"purchaser"}') && event.newValue !== null) {
            // console.log(event.oldValue)
            // console.log(event.newValue)
            // this.$swal.fire({
            //   showConfirmButton: false,
            //   timer: 3500,
            //   timerProgressBar: true,
            //   icon: 'warning',
            //   html: '<h3>ได้มีการเปลี่ยนสิทธิ์ผู้ใช้งาน</h3>'
            // })
            this.$EventBus.$emit('CheckPermission')
            this.$router.push({ path: '/' }).catch(() => {})
            window.location.reload()
          }
        }
        if (event.key === 'oneData') {
          var NewValue = JSON.parse(Decode.decode(event.newValue))
          var oldValue = JSON.parse(Decode.decode(event.oldValue))
          if (oldValue.cartData.length !== 0 && NewValue.cartData.length === 0) {
            // console.log(1)
            this.$EventBus.$emit('getCart2Tab')
            this.$EventBus.$emit('getCartPopOver')
          }
          if (oldValue.cartData.shop_list.length !== NewValue.cartData.shop_list.length) {
            // console.log(2)
            this.$EventBus.$emit('getCart2Tab')
            this.$EventBus.$emit('getCartPopOver')
          }
          for (let i = 0; i < oldValue.cartData.shop_list.length; i++) {
            // general
            if (oldValue.cartData.shop_list[i].product_general !== undefined && NewValue.cartData.shop_list[i].product_general !== undefined) {
              if (oldValue.cartData.shop_list[i].product_general.length !== NewValue.cartData.shop_list[i].product_general.length) {
                // console.log(3)
                this.$EventBus.$emit('getCart2Tab')
                this.$EventBus.$emit('getCartPopOver')
              }
            } else if (oldValue.cartData.shop_list[i].product_general === undefined && NewValue.cartData.shop_list[i].product_general === undefined) {
            } else {
              // console.log(4)
              this.$EventBus.$emit('getCart2Tab')
              this.$EventBus.$emit('getCartPopOver')
            }
            // onetime
            if (oldValue.cartData.shop_list[i].product_onetime !== undefined && NewValue.cartData.shop_list[i].product_onetime !== undefined) {
              if (oldValue.cartData.shop_list[i].product_onetime.length !== NewValue.cartData.shop_list[i].product_onetime.length) {
                // console.log(5)
                this.$EventBus.$emit('getCart2Tab')
                this.$EventBus.$emit('getCartPopOver')
              }
            } else if (oldValue.cartData.shop_list[i].product_onetime === undefined && NewValue.cartData.shop_list[i].product_onetime === undefined) {
            } else {
              // console.log(6)
              this.$EventBus.$emit('getCart2Tab')
              this.$EventBus.$emit('getCartPopOver')
            }
            // recurring
            if (oldValue.cartData.shop_list[i].product_recurring !== undefined && NewValue.cartData.shop_list[i].product_recurring !== undefined) {
              if (oldValue.cartData.shop_list[i].product_recurring.length !== NewValue.cartData.shop_list[i].product_recurring.length) {
                // console.log(7)
                this.$EventBus.$emit('getCart2Tab')
                this.$EventBus.$emit('getCartPopOver')
              }
            } else if (oldValue.cartData.shop_list[i].product_recurring === undefined && NewValue.cartData.shop_list[i].product_recurring === undefined) {
            } else {
              // console.log(8)
              this.$EventBus.$emit('getCart2Tab')
              this.$EventBus.$emit('getCartPopOver')
            }
          }
        }
        if (event.key === 'cartUpdated') {
          if (event.newValue !== event.oldValue) {
            // console.log(9)
            // this.$EventBus.$emit('getCartTable')
            this.$EventBus.$emit('getCartPopOver')
            // await this.onSelectChange([], [], [])
          }
        }
        if (event.key === 'LoginTime') { // ตรวจสอบว่าหน้านี้รีเฟรชแล้วหรือยัง
          if (event.newValue !== event.oldValue) {
            window.location.reload() // ทำการรีเฟรชหน้าเว็บ
          }
        }
      }
    }
  }
}
</script>
