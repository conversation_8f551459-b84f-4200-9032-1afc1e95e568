export default {
  default: 'th',
  SystemBar: {
    DownloadApp: 'ดาวน์โหลดแอปพลิเคชัน',
    ContactUs: 'ติดต่อเรา',
    Help: 'ช่วยเหลือ',
    OpenShop: 'เปิดร้านค้า',
    RegisterPartner: 'ลงทะเบียนคู่ค้า'
  },
  DialogDownloadApp: {
    ForBuyer: '(สำหรับผู้ซื้อ)',
    ForSeller: '(สำหรับร้านค้า)',
    textDownload: 'สามารถดาวน์โหลดได้ทั้ง'
  },
  AppBar: {
    Search: 'ค้นหาสินค้าและร้านค้า',
    Register: 'สมัครสมาชิก',
    or: 'หรือ',
    Login: 'เข้าสู่ระบบ',
    SearchNoJV: 'ค้นหาสินค้า',
    SearchJV: 'ค้นหาสินค้าในร้านค้า',
    ViewCart: 'ดูรถเข็นของคุณ',
    NoCart: 'ยังไม่มีสินค้าในรถเข็น',
    sku: 'รหัสสินค้า',
    Quantity: 'จำนวน'
  },
  Notification: {
    notification: 'การแจ้งเตือน',
    noNotification: 'ไม่มีการแจ้งเตือน',
    textNoNoti: 'การแจ้งเตือนจะปรากฎที่นี่เมื่อคุณได้รับข้อความ',
    AllNotifications: 'การแจ้งเตือนทั้งหมด',
    ReadAll: 'อ่านทั้งหมด',
    New: 'ใหม่',
    Before: 'ก่อนหน้านี้'
  },
  TextHome: {
    textRecommend: 'แนะนำ!',
    textProduct: 'สินค้าสำหรับคุณ',
    ViewAllProducts: 'ดูสินค้าทั้งหมด',
    textSearch: 'โปรดใส่คำที่จะค้นหา'
  },
  Footer: {
    Account: 'บัญชีส่วนตัว',
    Chat: 'แชท',
    Shop: 'เลือกดูสินค้า',
    Payment: 'จ่ายเงิน',
    Shipping: 'จัดส่ง',
    CustomerService: 'บริการหลังการขาย',
    Detail: {
      Account: 'ดูแลบัญชีผู้ใช้ของคุณให้ปลอดภัย ด้วย One ID',
      Chat: 'แชทคุยกับพ่อค้าแม่ค้าได้ทุกเวลาผ่านระบบแชทของเรา One Chat',
      Shop: 'เลือกสินค้าที่คุณต้องการบนเว็บไซต์ของเรา',
      Payment: 'จ่ายเงินด้วยเครดิตการ์ดของคุณผ่าน Thaidotcom payment',
      Shipping: 'เราจัดส่งสินค้าของคุณให้ถึงมืออย่างรวดเร็วและปลอดภัย',
      CustomerService: 'บริการหลังการขายที่มีประสิทธิภาพที่จะทำให้คุณมั่นใจในสินค้าของเรา'
    },
    HeadOffice: 'ที่อยู่',
    HelpCenter: 'ศูนย์ช่วยเหลือ',
    Categories: 'หมวดหมู่',
    aboutAs: 'เกี่ยวกับเรา',
    PaymentMethods: 'ช่องทางการชำระเงิน',
    FollowUs: 'ติดตามเรา',
    Certification: 'เครื่องหมายรับรอง',
    DetailAddress: {
      Company: 'บริษัท เน็กซ์ เจน ชอป จำกัด',
      INET: 'บริษัทในเครือของบริษัท อินเทอร์เน็ตประเทศไทย จำกัด (มหาชน)',
      Address: '1768 อาคารไทยซัมมิท ทาวเวอร์ ชั้น 16 ถ.เพชรบุรีตัดใหม่ แขวงบางกะปิ เขตห้วยขวาง กรุงเทพมหานคร 10310',
      Phone: 'เบอร์โทรศัพท์ : 02-257-7155 กด 5',
      Email: 'อีเมล: <EMAIL>'
    },
    DetailAbout: {
      Policy: 'นโยบาย',
      // TermsOfUse: 'เงื่อนไขการใช้งาน'
      TermsOfUse: 'Terms of Use'
    },
    DetailPayment: {
      // Payment: 'ไทย ดอท คอม เพย์เมนท์'
      Payment: 'Thaidotcompayment'
    }
  },
  Menu: {
    Language: 'ภาษา',
    Profile: 'บัญชีของฉัน',
    Orders: 'รายการสั่งซื้อของฉัน',
    Select: 'เลือกสิทธิ์ผู้ใช้งาน',
    DetailSelect: {
      Buyer: 'ผู้ซื้อทั่วไป',
      Purchaser: 'ผู้ซื้อองค์กร',
      Seller: 'ผู้ขาย',
      Sales: 'Sales',
      Admin: 'แอดมิน',
      Approver: 'ผู้อนุมัติ',
      AdminSystem: 'Admin ระบบ'
    },
    BusinessEntity: 'ข้อมูลนิติบุคคล',
    Company: 'ข้อมูลบริษัท',
    Shops: 'ร้านค้า',
    ShopRegister: 'สร้างร้านค้า',
    Logout: 'ออกจากระบบ'
  },
  Headers: {
    Home: 'หน้าแรก',
    RecommendedProducts: 'สินค้าแนะนำ',
    RecommendedForYou: 'สินค้าแนะนำสำหรับคุณ',
    Detail: {
      Categories: 'หมวดหมู่สินค้า',
      Category: 'หมวดหมู่',
      AllCategories: 'หมวดหมู่ทั้งหมด',
      Price: 'ราคา'
    },
    ProductText: {
      Popular: 'สินค้ายอดนิยม',
      FlashSale: 'Flash Sale',
      NoCategory: 'ไม่มีหมวดหมู่สินค้า',
      NoProduct: 'ยังไม่มีสินค้า',
      NewProducts: 'สินค้ามาใหม่',
      BestSeller: 'สินค้าขายดี',
      SameShop: 'สินค้าจากร้านเดียวกัน',
      All: 'สินค้าทั้งหมด',
      SameCategory: 'สินค้าจากหมวดหมู่เดียวกัน',
      RelatedCategory: 'สินค้าหมวดหมู่ที่เกี่ยวข้อง',
      Product: 'สินค้า',
      Products: 'สินค้า'
    },
    HomeText: {
      RecommendedProducts: 'สินค้าแนะนำ',
      NewProducts: 'สินค้ามาใหม่',
      BestSeller: 'สินค้าขายดี',
      NoRecommendedProducts: 'ยังไม่มีรายการสินค้าแนะนำ',
      NoBestSellers: 'ยังไม่มีรายการสินค้าขายดี',
      NoNewArrivals: 'ยังไม่มีรายการสินค้ามาใหม่',
      ViewAll: 'ดูทั้งหมด',
      Shops: 'ร้านค้า',
      AllShops: 'ร้านค้าทั้งหมด'
    }
  },
  userProfileNewUI: {
    UserProfile: 'ข้อมูลของฉัน',
    MyAccount: 'บัญชีของฉัน',
    Edit: 'แก้ไขข้อมูล',
    Cancel: 'ยกเลิก',
    Save: 'บันทึก',
    Confirm: 'ตกลง',
    PersonalInformation: 'ข้อมูลส่วนตัว',
    FirstName: 'ชื่อ',
    LastName: 'นามสกุล',
    Phone: 'หมายเลขโทรศัพท์',
    Email: 'อีเมล',
    EnterFirstName: 'ระบุชื่อ',
    EnterLastName: 'ระบุนามสกุล',
    EnterPhone: 'ระบุหมายเลขโทรศัพท์',
    EnterEmail: 'ระบุอีเมล',
    BankName: 'ชื่อธนาคาร',
    BankAccountNumber: 'เลขบัญชีธนาคาร',
    BankAccountName: 'ชื่อบัญชีธนาคาร',
    EditUserProfile: 'แก้ไขข้อมูลของฉัน',
    CheckedInformation: 'คุณได้ตรวจสอบข้อมูลเรียบร้อยแล้วใช่หรือไม่',
    OnlyLetterEnter: 'กรอกได้เฉพาะตัวอักษรเท่านั้น',
    PleaseEnterName: 'กรุณากรอกชื่อจริง',
    NotEnterMoreName: 'ห้ามกรอกชื่อจริงเกิน 30 ตัวอักษร',
    PleaseEnterSurName: 'กรุณากรอกนามสกุล',
    NotEnterMoreSurName: 'ห้ามกรอกนามสกุลเกิน 30 ตัวอักษร',
    NoUsername: 'ไม่มีชื่อผู้ใช้งาน',
    NoFullname: 'ไม่มีชื่อ - นามสกุล',
    PleaseAddImage: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
    UnableProceed: 'ไม่สามารถดำเนินการได้',
    EditSuccess: 'แก้ไขข้อมูลสำเร็จ',
    SuccessEditYourData: 'คุณได้ทำการแก้ไขข้อมูลเรียบร้อย'
  },
  ShoppingCartUI: {
    ShoppingCart: 'รถเข็นสินค้า',
    Home: 'หน้าแรก',
    NoProduct: 'ยังไม่มีสินค้าในรถเข็น',
    GoShop: 'กลับไปหน้าร้านค้า',
    GoBuy: 'ช้อปปิ้งกันเลย',
    RemoveSelectItems: 'ลบสินค้าที่เลือก',
    Store: 'ร้านค้า',
    StoreNoSell: 'ร้านค้าไม่พร้อมขาย',
    DetailProduct: 'รายละเอียดสินค้า',
    UnitPrice: 'ราคาต่อชิ้น',
    Quantity: 'จำนวน',
    TotalPrice: 'ราคารวม',
    CannotPurchaseTogether: 'ไม่สามารถซื้อสินค้าบริการและสินค้าทั่วไปร่วมกันได้',
    Delete: 'ลบ',
    NotReady: 'สินค้าไม่พร้อมขาย',
    SoldOut: 'สินค้าหมด',
    ProductCannotReduce: 'ไม่สามารถลดจำนวนสินค้าได้',
    item: 'ชิ้น',
    items: 'ชิ้น',
    DueMinimumOrder: 'เนื่องจากจำนวนสินค้ากำกับการสั่งซื้อน้อยสุดที่',
    ItemLeftTH: 'เหลือสินค้าอยู่',
    ItemLeftEN: '',
    PreOrder: 'พรีออเดอร์',
    StoreDiscountCoupon: 'ใช้คูปองส่วนลดร้านค้า',
    StoreDiscountPoint: 'ใช้แต้มส่วนลดร้านค้า',
    GeneralProduct: 'สินค้าทั่วไป',
    B2BProduct: 'สินค้าบริการ',
    ProductCannotIncrease: 'ไม่สามารถเพิ่มจำนวนสินค้าได้',
    DueMaximumOrder: 'เนื่องจากจำนวนสินค้ากำกับการสั่งซื้อสูงสุดที่',
    Free: 'แถมฟรี',
    StoreDiscount: 'ใช้ส่วนลดร้านค้า ลด',
    ShippingDiscount: 'ใช้ส่วนลดค่าส่ง ลด',
    PointsDiscount: 'ใช้แต้มลด',
    SystemDiscountCoupon: 'คูปองส่วนลดระบบ',
    UseSystemDiscount: 'ใช้ส่วนลดระบบ',
    Discount: 'ส่วนลด',
    OrderSummary: 'สรุปราคาสินค้าที่สั่งซื้อ',
    PriceExcludingVAT: 'ราคาไม่รวมภาษีมูลค่าเพิ่ม',
    VAT: 'ภาษีมูลค่าเพิ่ม',
    PriceIncludingVAT: 'ราคารวมภาษีมูลค่าเพิ่ม',
    Baht: 'บาท',
    Month: 'เดือน',
    TotalPriceAll: 'ราคารวมทั้งหมด',
    StoreDiscountSummary: 'ส่วนลดคูปอง (ร้านค้า)',
    ShippingDiscountSummary: 'ส่วนลดคูปอง (ค่าส่ง)',
    PointsDiscountSummary: 'ส่วนลดแต้ม',
    SystemDiscountSummary: 'ส่วนลดคูปอง (ระบบ)',
    PriceAfterDiscount: 'ราคาหลังหักส่วนลด',
    PartnerDiscount: 'ส่วนลดคู่ค้า',
    CustomerDiscount: 'ส่วนลดลูกค้า',
    PriceAfterPartnerDiscount: 'ราคาหลังหักส่วนลดคู่ค้า',
    ProductPriceIncludingDiscount: 'ราคาสินค้ารวมส่วนลด',
    ThisPriceStandardPriceVary: 'ราคานี้เป็นมาตรฐาน - ราคาอาจแตกต่างกันไป',
    DependingProductDestination: 'ขึ้นอยู่กับสินค้า/ปลายทาง เจ้าหน้าที่จัดส่งจะติดต่อคุณ',
    PointsEarnedThisTime: 'แต้มที่ได้ในครั้งนี้',
    Points: 'แต้ม',
    OrderProducts: 'สั่งซื้อสินค้า',
    DeleteProduct: 'ลบสินค้า',
    HaveDeletedProduct: 'คุณได้ทำการลบสินค้า',
    Cancel: 'ยกเลิก',
    Confirm: 'ยืนยัน',
    ShippingFee: 'ค่าจัดส่ง',
    ConfirmTransaction: 'คุณต้องการทำรายการนี้ ใช่ หรือไม่',
    TotalPointsEarnedThisTime: 'แต้มที่ได้ในครั้งนี้ทั้งหมด',
    PointsEarned: 'แต้มที่ได้รับ',
    TotalPointsEarned: 'แต้มรวมทั้งหมด',
    LargeProduct: '*สินค้าขนาดใหญ่',
    ProductChanged: '*สินค้ามีการเปลี่ยนแปลง กรุณานำออกจากรถเข็น',
    OutStock: 'ไม่มีสินค้าในสต็อก',
    InsufficientStock: 'สินค้าในสต็อกไม่เพียงพอ',
    AddProductCart: 'รบกวนเพิ่มสินค้าลงในรถเข็น',
    UnableProceed: 'ไม่สามารถดำเนินการได้',
    CartAddedMaximum: 'ตระกร้าสินค้าสามารถเพิ่มสูงสุดได้ 150 รายการเท่านั้น',
    NotConditionsDiscountProductPriceLessMinimumPrice: 'ไม่ตรงตามเงื่อนไขที่สามารถใช้ส่วนลดได้ เนื่องจากราคาสินค้าน้อยกว่าราคาขั้นต่ำที่คูปองกำหนด',
    CouponOutStock: 'ไม่สามารถใช้คูปองนี้ได้ เนื่องจากคูปองนี้หมดแล้ว',
    CouponReserved: 'ไม่สามารถใช้คูปองนี้ได้ เนื่องจากคูปองนี้ถูกจองแล้ว',
    ServerError: 'ข้อผิดพลาดของเซิร์ฟเวอร์',
    ChangedRemoveCartAddItem: 'นี้มีการเปลี่ยนแปลง กรุณานำออกจากรถเข็นและทำการเพิ่มสินค้าใหม่',
    NoStockRemoveCart: 'ไม่มีสินค้าในสต็อก กรุณานำออกจากรถเข็น',
    InsufficientStockRemoveCart: 'สินค้าในสต็อกไม่เพียงพอ กรุณานำออกจากรถเข็น',
    ProductID: 'สินค้ารหัส',
    WantDeleteItem: 'คุณต้องการลบสินค้าหรือไม่',
    QuantityLessSpecified: 'จำนวนสินค้าน้อยกว่ากำหนด',
    ChangedRemoveCart: 'นี้มีการเปลี่ยนแปลง กรุณานำออกจากรถเข็น',
    CartRemoveSuccess: 'ลบสินค้าในรถเข็นเรียบร้อย',
    OK: 'ตกลง',
    LessSpecifiedQuantity: 'สินค้าน้อยกว่าจำนวนที่กำหนด',
    MoreSpecifiedQuantity: 'สินค้าเกินจำนวนที่กำหนด',
    CannotIncreaseQuantityItemsSpecifiedQuantity: 'ไม่สามารถเพิ่มจำนวนสินค้าได้ เนื่องจากคุณเพิ่มสินค้าถึงจำนวนที่กำหนดแล้ว',
    LargeShippingOnlyItem: 'เนื่องจากสินค้าชิ้นนี้ใช้การขนส่งแบบ ขนาดใหญ่ จึงสามารถสั่งซื้อได้เพียงครั้งละ 1 ชิ้น',
    TotalPriceLess: 'ราคารวมทั้งหมดไม่ควรมีค่าน้อยกว่า 1 บาท',
    PleaseSelect: 'กรุณาเลือก',
    NewAgain: 'ใหม่อีกครั้ง',
    UsePoints: 'ใช้คะแนน',
    PointsScore: 'คะแนน',
    NoShopIDNGS: 'ยังไม่มี ShopID ใน NGS',
    NoRecommendedItems: 'ยังไม่มีรายการสินค้าแนะนำ'
  },
  Coupon: {
    Coupon: 'คูปอง',
    Coupons: 'คูปอง',
    GeneralDiscount: 'คูปองส่วนลดร้านค้าทั่วไป',
    NoGeneralDiscount: 'ยังไม่มีคูปองส่วนลดร้านค้าทั่วไป',
    StoreSpecific: 'คูปองเฉพาะร้านค้า',
    NoStoreSpecific: 'ยังไม่มีคูปองเฉพาะร้านค้า',
    ForStore: 'สำหรับร้านค้า',
    Only: 'เท่านั้น',
    Conditions: 'เงื่อนไขโค้ดส่วนลด',
    PromotionType: 'ประเภทโปรโมชัน',
    ShippingDiscount: 'ส่วนลดค่าจัดส่งสินค้า',
    ProductDiscount: 'ส่วนลดราคาสินค้า',
    UsageRight: 'สิทธิ์การใช้คูปอง',
    Usable: 'สามารถใช้ได้',
    Rights: 'สิทธิ์',
    MaxDiscount: 'ส่วนลดสูงสุด',
    MaxReduce: 'ลดสูงสุด ',
    Currency: ' บาท',
    Unlimited: 'ไม่จำกัด',
    ParticipatingStores: 'ร้านค้าที่เข้าร่วม',
    AllStores: 'รายการร้านค้าทั้งหมด',
    Shop: 'ร้านค้า',
    ValidEverywhere: 'ใช้ได้ทุกร้านค้า',
    MinSpend: 'ขั้นต่ำ',
    MaxSpend: 'ไม่เกิน',
    ValidUntil: 'ใช้ได้ถึงวันที่',
    ValidFrom: 'ใช้ได้ตั้งแต่วันที่',
    ValidOn: 'ใช้ได้วันที่',
    NoExpiry: 'ไม่มีวันหมดอายุใช้งาน',
    ViewConditions: 'ดูเงื่อนไข',
    Claim: 'เก็บ',
    Claimed: 'เก็บแล้ว',
    ClaimAll: 'เก็บทั้งหมด',
    ShowMore: 'ดูเพิ่มเติม',
    ShowLess: 'แสดงน้อยลง'
  },
  CheckOut: {
    ShippingAddress: 'ที่อยู่ในการจัดส่งสินค้า',
    ChangeAddress: 'เปลี่ยนที่อยู่',
    Notation: 'หมายเหตุ',
    IncompleteAddress: 'ที่อยู่จัดส่งสินค้าไม่สมบูรณ์ กรุณาแก้ไขหรือเลือกที่อยู่ใหม่',
    AddAddress: 'เพิ่มที่อยู่ใหม่',
    OrderList: 'รายการสั่งซื้อสินค้า',
    QuotationSample: 'ตัวอย่างใบเสนอราคา',
    ShopName: 'ร้านค้า',
    UseCouponShop: 'ใช้คูปองส่วนลดร้านค้า',
    GeneralProduct: 'สินค้าทั่วไป',
    ServiceProduct: 'สินค้าบริการ',
    ApplyStoreVoucher: 'ใช้คูปองส่วนลดร้านค้า ลด',
    ApplyStoreVoucherMobile: 'ใช้ส่วนลดร้านค้า ลด',
    FreeShipping: 'ใช้คูปองส่วนลดค่าส่งร้านค้า ลด',
    FreeShippingMobile: 'ใช้ส่วนลดค่าส่ง ลด',
    UseStorePoint: 'ใช้แต้มส่วนลดร้านค้า',
    Free: 'แถมฟรี',
    Discount: 'ใช้แต้มลด',
    AddressTaxInvoiceIssuance: 'ที่อยู่ในการออกใบกำกับภาษี',
    ReceiveTaxInvoice: 'รับใบกำกับภาษี',
    NotReceiveTaxInvoice: 'ไม่รับใบกำกับภาษี',
    BranchCode: 'รหัสสาขา',
    TaxNumber: 'เลขประจำตัวผู้เสียภาษี',
    Address: 'ที่อยู่',
    ShippingType: 'รูปแบบการจัดส่ง',
    PickUpAStore: 'รับสินค้าหน้าร้าน',
    ProductDelivery: 'จัดส่งสินค้า',
    ChooseShipping: 'เลือกการจัดส่ง',
    ChooseOtherShipping: 'เลือกขนส่งอื่น',
    SentByStore: 'จัดส่งโดยร้านค้า',
    HouseNumber: 'บ้านเลขที่',
    SubDistrict: 'ตำบล/แขวง',
    District: 'อำเภอ/เขต',
    Province: 'จังหวัด',
    ReceivingDate: 'วันรับสินค้า',
    ReceivingTime: 'เวลารับสินค้า',
    UnitPrice: 'ราคาต่อชิ้น',
    Quantity: 'จำนวน',
    TotalPrice: 'ราคารวม',
    PricePerMonth: 'บาท/เดือน',
    SKU: 'รหัสสินค้า',
    SystemDiscountCoupon: 'คูปองส่วนลดระบบ',
    UseSystemDiscountCoupon: 'ใช้ส่วนลดระบบ',
    DiscountCoupon: 'ส่วนลด',
    DiscountShipping: 'คูปองส่วนลดขนส่ง',
    Relationships: 'สานสัมพันธ์',
    RelationshipsCoupon: 'โค้ดสานสัมพันธ์',
    ChangeRelationshipsCoupon: 'เปลี่ยนโค้ดสานสัมพันธ์',
    PriceExcludingVAT: 'ราคาไม่รวมภาษีมูลค่าเพิ่ม',
    VAT: 'ภาษีมูลค่าเพิ่ม',
    Baht: 'บาท',
    PriceIncludingVAT: 'ราคารวมภาษีมูลค่าเพิ่ม',
    SummaryOrdered: 'สรุปราคาสินค้าที่สั่งซื้อ',
    CouponDiscountSeller: 'ส่วนลดคูปอง (ร้านค้า)',
    CouponDiscountNexgen: 'ส่วนลดคูปอง (ระบบ)',
    NexgenPointDiscount: 'ส่วนลดแต้ม',
    SubTotal: 'ราคาหลังหักส่วนลด',
    ShippingFee: 'ค่าจัดส่ง',
    ShippingCostDesicription1: 'ราคานี้เป็นมาตรฐาน - ราคาอาจแตกต่างกันไป',
    ShippingCostDesicription2: 'รกับสินค้า/ปลายทาง เจ้าหน้าที่จัดส่งจะติดต่อคุณ',
    ShippingDiscountSeller: 'ส่วนลดค่าจัดส่ง (ร้านค้า)',
    ShippingDiscountNexgen: 'ส่วนลดค่าจัดส่ง (ระบบ)',
    CreditCardInstallmentOption: 'Credit Card แบบผ่อนชำระ',
    SelectPaymentMethod: 'เลือกวิธีชำระเงิน',
    InstallmentPaymentNotAvailableBecause: 'ไม่สามารถผ่อนชำระได้ เนื่องจากไม่ถึง',
    Minimum: '"ขั้นต่ำขั้นต่ำ"',
    RequiredAmountHasNotBeenMet: 'ที่กำหนดไว้',
    SelectInstallmentPeriod: 'เลือกระยะเวลาผ่อนชำระ',
    Krungthai: 'กรุงไทย (KTC)',
    PointsEarned: 'แต้มที่ได้ในครั้งนี้',
    Point: 'แต้ม',
    TotalPriceAll: 'ราคารวมทั้งหมด',
    ConfirmOrder: 'ยืนยันการสั่งซื้อ',
    ComfirmModal: 'คุณต้องการทำรายการนี้ ใช่ หรือ ไม่',
    Confirm: 'ตกลง',
    Cancel: 'ยกเลิก',
    PleaseWaitSellerApproveOrder: 'กรุณารอผู้ขายอนุมัติการสั่งซื้อ',
    OrderConfirmationCompleted: 'ยืนยันการสั่งซื้อเสร็จสิ้น',
    ShippingAddressDeletedSuccessfully: 'ลบที่อยู่จัดส่งเสร็จสิ้น',
    YourOrderHasBeenPlacedSuccessfully: 'คุณได้ทำการสั่งซื้อสินค้าเรียบร้อย',
    YouHaveSuccessfullyDeletedTheShippingAddress: 'คุณได้ทำการลบที่อยู่จัดส่งเรียบร้อย',
    GoOrderListPage: 'ไปยังหน้ารายการสั่งซื้อ',
    GoToHomePage: 'ไปยังหน้าหลัก',
    GoQuotationPage: 'ไปยังหน้าใบเสนอราคา',
    ChangeShippingAddress: 'เปลี่ยนที่อยู่ในการจัดส่งสินค้า',
    Edit: 'แก้ไข',
    Delete: 'ลบ',
    NoAddress: 'ไม่มีที่อยู่ในการจัดส่งสินค้า',
    SelectShippingMethod: 'เลือกการจัดส่งสินค้า',
    StartingShippingFee: 'ค่าส่งเริ่มต้น',
    ChangeBillingAddressInvoice: 'เปลี่ยนที่อยู่ในการออกใบกำกับภาษี',
    TaxInvoiceAddress: 'ที่อยู่ใบกำกับภาษี',
    Individual: 'บุคคลธรรมดา',
    JuristicPerson: 'นิติบุคคล',
    DefaultAddress: 'ที่อยู่เริ่มต้น',
    ScanQRCodeMakePayment: 'สแกน QR Code ชำระเงินร้านค้า',
    ScanQRCodeToPay: 'สแกน QR Code ชำระเงิน',
    SaveImage: 'บันทึกรูปภาพ',
    TotalPaymentAmount: 'ยอดชำระเงินจำนวน',
    CanMakePaymentByFollowingStep: 'สามารถชำระเงินได้ตามขั้นตอนนี้ (กรณีชำระเงินผ่านมือถือ)',
    SaveImageStep1: 'ให้แคปหน้าจอ หรือกดค้างที่รูปภาพและกดปุ่ม',
    ForIOSDevices: 'กรณี iOS',
    TakeScreenshot: 'ให้แคปหน้าจอ หรือกดค้างที่รูปภาพและกดปุ่ม',
    TapTheButton: '"บันทึกไปยังแอปรูปภาพ"',
    OpenYourBankingApp: 'เปิดแอปธนาคารของท่านและเลือกเมนูสแกน QR Code',
    SelectScreenshotScanTheQRCode: 'เลือกภาพหน้าจอเพื่อทำการสแกน QR Code',
    RefID: 'รหัสอ้างอิง',
    RequestQuotationEdit: 'ขอแก้ไขใบเสนอราคา',
    EditCouponDisscription: 'การแก้ไขใบเสนอราคาจะยังไม่สามารถใช้คูปองส่วนลดได้จนกว่าผู้ขายจะทำการอนุมัติ',
    EditCouponConfirm: '"ยืนยันการดำเนินการต่อหรือไม่"',
    DeleteTaxInvoiceAddress: 'ลบที่อยู่ในการออกใบกำกับภาษี',
    ConfirmDelectTaxInvoiceAddress: 'คุณต้องการทำรายการนี้ ใช่ หรือ ไม่',
    DeleteAddressSuccessfully: 'ลบที่อยู่เสร็จสิ้น',
    YouHaveSuccessfullyDeletedTaxInvoiceAddress: 'คุณได้ทำการลบที่อยู่ในการออกใบกำกับภาษีเรียบร้อย',
    TotalPointsEarnedInThisTransaction: 'แต้มที่ได้ในครั้งนี้ทั้งหมด',
    Store: 'ร้านค้า',
    PointEarned: 'แต้มที่ได้รับ',
    TotalPoint: 'แต้มรวมทั้งหมด',
    HomePage: 'หน้าแรก',
    ShoppingCart: 'รถเข็นสินค้า',
    OperatingBudget: 'งบดำเนินการ',
    investmentBudget: 'งบลงทุน',
    regularExpenditureBudget: 'งบรายจ่ายประจำ',
    COGS: 'ต้นทุนขาย (COGS)',
    SGnA: 'ค่าใช้จ่ายและบริการ (SG&A)',
    RnD: 'ต้นทุนวิจัยและพัฒนา (R&D)',
    Month: 'เดือน',
    PleaseEnterYourFullName: 'กรุณากรอกชื่อ-สกุล',
    PleasEnterYourPhoneNumber: 'กรุณากรอกเบอร์โทร',
    PleaseEnterValidPhoneNumber: 'กรุณากรอกเบอร์โทรให้ถูกต้อง',
    PleasEnterYourPosition: 'กรุณากรอกตำแหน่ง',
    PleaseEnterYourEmailAddress: 'กรุณากรอก Email',
    ProductDetails: 'รายละเอียดสินค้า',
    RedeemPoints: 'ใช้คะแนน',
    Points: 'คะแนน',
    TheCartHasBeenUpdated: 'ตระกร้ามีการเปลี่ยนแปลง',
    PleaseEnterTheShippingAddress: 'กรุณากรอกที่อยู่สำหรับจัดส่งสินค้า',
    ToViewsampleQuotation: 'เพื่อดูตัวอย่างใบเสนอราคา',
    PleaseEnterTheAddressForTheTaxTnvoice: 'กรุณากรอกที่อยู่สำหรับใบกำกับภาษี',
    ToViewSampleQuotation: 'เพื่อดูตัวอย่างใบเสนอราคา',
    UnavailableSampleQuotation: 'ระบบขัดข้องไม่สามารถดูตัวอย่างใบเสนอราคาได้',
    FailedToDeleteTheShippingAddress: 'ลบที่อยู่ในการจัดส่งสินค้าไม่สำเร็จ',
    SetAsDefaultAddressSuccessfully: 'ตั้งค่าเป็นที่อยู่เริ่มต้นแล้ว',
    FailedToSetAsDefaultAddress: 'ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ',
    AddShippingAddress: 'เพิ่มที่อยู่ในการจัดส่งสินค้า',
    EditShippingAddress: 'แก้ไขที่อยู่ในการจัดส่งสินค้า',
    SelectAddressFirst: 'กรุณาเลือกที่อยู่จัดส่งสินค้าก่อน หรือ กรอก/เพิ่มที่อยู่จัดส่งสินค้าให้สมบูรณ์ก่อน',
    Phone: 'เบอร์โทรศัพท์',
    SystemErrorHasOccurredPleaseContactSupport: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่',
    EditTaxInvoiceAddress: 'แก้ไขที่อยู่ในการออกใบกำกับภาษี',
    AddTaxInvoiceAddress: 'เพิ่มที่อยู่ในการออกใบกำกับภาษี',
    PleaseSelectShippingMethodStoreDelivery: 'กรุณาเลือกขนส่งในการจัดส่งสินค้าของร้าน',
    NoTaxInvoiceAddressAvailable: 'ไม่มีที่อยู่ในการออกใบกำกับภาษี',
    SomeItemsAreNotEligibleForTheFreeGiftDueToInsufficientQuantity: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม ได้แก่',
    PleaseRemoveTheseItemsFromYourCart: 'กรุณาลบสินค้าออกจากรถเข็น',
    ShoppingCartNotFound: 'ไม่พบตะกร้า',
    SomeItemsAreNoLongerAvailableInTheSystem: 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว ได้แก่',
    PleaseRemoveThemFromYourCart: 'กรุณาลบสินค้าออกจากรถเข็น',
    SomeItemsAreNotAvailableInSufficientQuantityForPurchase: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการซื้อ ได้แก่',
    TheTotalPriceMustBeGreaterThanZerobaht: 'ราคารวมทั้งหมดต้องมีค่ามากกว่า 0 บาท',
    PleaseEnterDiscountThatDoesNotExceedTheProductPrice: 'กรุณากรอกส่วนลดไม่เกินราคา',
    YouHaveNotAddedAnAddressYet: 'คุณยังไม่ได้เพิ่มข้อมูลที่อยู่',
    Incompleteinformation: 'ใส่ข้อมูลไม่ครบ',
    PleaseConfirmYourOrderAgain: 'กรุณาทำรายการยืนยันคำสั่งซื้ออีกครั้ง',
    WeightOver50kilograms: 'สินค้าของคุณมีน้ำหนักรวมของสินค้าเกิน 50 กิโลกรัม หากต้องการสั่งซื้อกรุณาติดต่อเจ้าหน้าที่ ขอบคุณครับ',
    UserPermissionsChange: 'กรุณาตรวจสอบ เนื่องจากมีการเปลี่ยนแปลงสิทธิ์ของผู้ใช้งาน',
    TheCouponCanBeUsed: 'สามารถใช้คูปองได้',
    TheCouponCannotBeUsed: 'ไม่สามารถใช้คูปองได้',
    Paymentincomplete: 'การชำระเงินไม่เสร็จสมบูรณ์',
    ModalShippingIMG: require('@/assets/shippingbannermini.png'),
    ShippingBanner: require('@/assets/shippingbanner.png'),
    NoteToStore: 'หมายเหตุถึงร้านค้า(ไม่บังคับ)'
  },
  register: {
    Register: 'สมัครสมาชิก',
    SignUp: 'สมัครสมาชิก',
    SignIn: 'เข้าสู่ระบบ',
    Username: 'ชื่อผู้ใช้งาน',
    EnterUsername: 'ระบุชื่อผู้ใช้งาน',
    ValidateUsername1: 'กรุณากรอกชื่อผู้ใช้งาน',
    ValidateUsername2: 'กรุณากรอกเฉพาะภาษาอังกฤษและตัวเลขเท่านั้น',
    ValidateUsername3: 'กรุณากรอกให้ครบ 6 อักขระหรือมากกว่านั้น',
    Password: 'รหัสผ่าน',
    ValidatePassword1: 'กรุณาระบุรหัสผ่าน',
    ValidatePassword2: 'ต้องประกอบด้วยตัวอักษรภาษาอังกฤษผสมตัวเลขอย่างน้อยหนึ่งหลัก โดยใช้อักขระพิเศษได้',
    ValidatePassword3: 'ต้องประกอบด้วยตัวอักษรภาษาอังกฤษน้อยหนึ่งหลัก',
    ValidatePassword4: 'ต้องมีตัวเลขอย่างน้อยหนึ่งหลัก',
    ValidatePassword5: 'ความยาวต้องไม่น้อยกว่า 8 ตัวอักษร',
    TooltipPassword1: 'ต้องประกอบด้วยตัวอักษรภาษาอังกฤษผสมตัวเลขอย่างน้อยหนึ่งหลัก โดยใช้อักขระพิเศษได้',
    TooltipPassword2: 'ความยาวต้องไม่น้อยกว่า 8 ตัวอักษร',
    EnterPassword: 'ระบุรหัสผ่าน',
    ConfirmPassword: 'ยืนยันรหัสผ่าน',
    ValidateConfirmPassword1: 'กรุณาใส่รหัสผ่านให้ตรงกัน',
    EnterConfirmPassword: 'ระบุยืนยันรหัสผ่าน',
    Email: 'อีเมล',
    EnterEmail: 'ระบุอีเมล',
    ValidateEmail1: 'กรุณากรอกอีเมล',
    ValidateEmail2: 'กรุณากรอกอีเมลให้ถูกต้อง',
    ValidateEmail3: 'กรุณากรอกเฉพาะภาษาอังกฤษ',
    ValidateEmail4: 'ห้ามใส่ช่องว่างในอีเมล',
    Phone: 'เบอร์โทรศัพท์',
    EnterPhone: 'ระบุเบอร์โทรศัพท์',
    ValidatePhone1: 'กรุณากรอกเบอร์โทรศัพท์',
    Accept: 'ยอมรับ',
    ServiceOneId: 'ข้อกำหนดการใช้บริการ ONE ID',
    And: 'และ',
    Policy: 'นโยบายความคุ้มครองข้อมูลส่วนบุคคล',
    AlreadyAccount: 'คุณมีบัญชีอยู่แล้ว?',
    OTP: 'รับ OTP',
    EnterOTP: 'กรอกรหัส OTP',
    SendOTPPhone: 'ระบบได้ส่งรหัส OTP ไปยังเบอร์',
    WrongOTP: 'รหัส OTP ไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง',
    OTPrequestAgain: 'สามารถขอรหัส OTP ได้อีกครั้งภายใน',
    SendOTPButton: 'ส่ง OTP',
    LeavePage: 'ออกจากหน้านี้',
    ExitPage: 'คุณต้องการทำรายการนี้ ใช่หรือไม่',
    Cancel: 'ยกเลิก',
    Confirm: 'ตกลง',
    EasyOneID: 'ยืนยันการสมัคร Account ใหม่ด้วย Easy OneID',
    OneIDSuccess: 'หากสมัครสำเร็จแล้วจะไม่สามารถตั้งเบอร์โทรนี้ login',
    OtherAccount: 'ใน Account อื่นได้',
    OTPCorrect: 'ยืนยัน OTP สำเร็จ !',
    RegisterFail1: 'ชื่อผู้ใช้งานนี้มีอยู่ในระบบแล้ว กรุณาใช้ชื่อผู้ใช้งานอื่น',
    RegisterFail2: 'ข้อมูลไม่ครบ กรุณาตรวจสอบข้อมูลอีกครั้ง',
    RegisterFail3: 'หมายเลขโทรศัพท์มือถือซ้ำ หรือถูกใช้งานโดยผู้ใช้อื่นแล้ว',
    AlreadyUseEmail: 'อีเมลนี้มีอยู่ในระบบแล้ว กรุณาใช้อีเมลอื่น'
  },
  Login: {
    Welcome: 'ยินดีต้อนรับเข้าสู่ระบบ',
    ForgotPassword: 'ลืมรหัสผ่าน',
    Login: 'เข้าสู่ระบบ',
    LoginOne: 'เข้าสู่ระบบทดสอบ ONE ID',
    Or: 'หรือ',
    HaveAccount: 'คุณยังไม่มีบัญชีผู้ใช้ ใช่หรือไม่?',
    Register: 'ลงทะเบียน',
    LoginFail1: 'ชื่อผู้ใช้งานและรหัสผ่านไม่ตรงกับในระบบ',
    LoginFail2: 'ชื่อผู้ใช้งานหรือรหัสผ่านไม่สามารถเป็นค่าว่างได้',
    LoginFail3: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่',
    LoginWithPhone: 'เข้าสู่ระบบด้วยหมายเลขโทรศัพท์'
  },
  OTPPage: {
    LoginWithOTP: 'เข้าสู่ระบบด้วยเบอร์โทรศัพท์',
    DescribeOTP: 'ระบบจะทำการส่งเลข OTP ไปยังเบอร์โทรศัพท์ของคุณ',
    PhoneOTP: 'เบอร์โทรศัพท์',
    EnterPhone: 'ระบุเบอร์โทรศัพท์',
    ValidateOTP1: 'กรุณากรอกเบอร์โทรศัพท์',
    ValidateOTP2: 'กรุณาระบุเบอร์โทรศัพท์ 10 หลัก',
    OTPNotRegister1: 'เบอร์โทรศัพท์นี้ยังไม่ได้ลงทะเบียน',
    OTPNotRegister2: 'คุณต้องการลงทะเบียน ใช่ หรือ ไม่',
    OTPFail: 'ไม่สามารถดำเนินการได้',
    yes: 'ใช่',
    no: 'ไม่',
    OTPSend: 'ระบบได้ส่งรหัส OTP ไปยังเบอร์',
    ReferenceCode: 'รหัสอ้างอิง',
    ConfirmOTP: 'ยืนยัน OTP'
  },
  ForgotPassword: {
    PhoneType: 'เบอร์โทรศัพท์',
    headers: 'ลืมรหัสผ่านใช่หรือไม่ ?',
    describe: 'ระบบจะส่งอีเมลเพื่อรีเซ็ตรหัสผ่านใหม่ไปให้คุณ',
    send: 'ส่ง',
    describeOTP: 'ระบบจะทำการส่งเลข OTP ไปยังเบอร์โทรศัพท์ของคุณ',
    sendOTP: 'ส่ง OTP',
    newPassword: 'รหัสผ่านใหม่',
    enterNewPassword: 'ระบุรหัสผ่านใหม่',
    confirmNewPassword: 'ยืนยันรหัสผ่านใหม่',
    EnterConfirmPassword: 'ระบุยืนยันรหัสผ่านใหม่',
    resetEmailSuccess: 'ส่งอีเมลเพื่อรีเซ็ตรหัสผ่านเรียบร้อย!',
    resetOTPSuccess: 'ยืนยัน OTP สำเร็จ !',
    detailReset: 'กรุณาตรวจสอบที่อีเมลของท่าน'
  },
  ProductPage: {
    AllReviews: 'รีวิวทั้งหมด',
    ShippingFee: 'ค่าจัดส่ง',
    Wholesale: 'ขายส่ง',
    Like: 'ถูกใจ',
    Share: 'แชร์',
    ViewShippingFee: 'ดูค่าขนส่ง',
    ShowMore: 'ดูเพิ่มเติม',
    AddToCart: 'หยิบใส่รถเข็น',
    BuyNow: 'ซื้อสินค้าเลย',
    MinOrderLimit: 'สินค้าชิ้นนี้จำกัดการสั่งซื้อต่ำสุดที่',
    Unit: 'ชิ้น',
    AndUp: 'ชิ้นขึ้นไป',
    MaxOrderLimit: 'และการสั่งซื้อสูงสุดที่',
    PerOrder: 'ต่อออเดอร์',
    ViewStore: 'ดูร้านค้า',
    ProductDetails: 'รายละเอียดสินค้า',
    ProductReviews: 'รีวิวสินค้า',
    SameStoreProducts: 'สินค้าจากร้านเดียวกัน',
    RecommendedProducts: 'สินค้าที่คุณอาจสนใจ',
    ViewAll: 'ดูทั้งหมด',
    OverallRating: 'คะแนนรวมจากผู้ซื้อ',
    ReviewCount: 'จำนวนรีวิว',
    All: 'ทั้งหมด',
    OutOfStock: 'สินค้าหมด',
    InStock: 'สินค้าพร้อมส่ง',
    PreOrder: 'สินค้าพรีออเดอร์',
    ContactSupport: 'ติดต่อสอบถามเจ้าหน้าที่',
    SpecialDiscount: 'ส่วนลดพิเศษ',
    Discount: 'ส่วนลด',
    Note: 'หมายเหตุ',
    TotalPreOrderItems: 'มีสินค้าพรีออเดอร์ทั้งหมด',
    TotalItems: 'มีสินค้าทั้งหมด',
    PayType: 'Pay Type',
    SelectPayType: 'เลือก Pay Type',
    Price: 'ราคา',
    NoProductDetails: 'สินค้านี้ยังไม่มีรายละเอียดสินค้า',
    Options: 'ตัวเลือก',
    SellerResponse: 'ผู้ขายตอบกลับ',
    TimeIndicator: 'น.', // ย่อของ "นาฬิกา" เช่น "12:45 น."
    NoProductComments: 'ยังไม่มีความคิดเห็นของสินค้ารายการนี้',
    ServiceProduct: 'สินค้าบริการ',
    Chat: 'แชท',
    MyLink: 'ลิงก์ของฉัน',
    CopyLink: 'คัดลอกลิงก์',
    WholesalePrice: 'ราคาขายส่ง',
    AndAbove: 'ขึ้นไป',
    ShippingCost: 'ราคาค่าจัดส่ง',
    ShareProduct: 'แชร์สินค้า',
    RelatedCategory: 'สินค้าหมวดหมู่ที่เกี่ยวข้อง',
    Sku: 'รหัส SKU',
    SelectATB: 'กรุณาเลือกชนิดของสินค้า',
    AddToCartComplete: 'เพิ่มสินค้าลงในรถเข็นเรียบร้อย'
  },
  ShopPage: {
    Shop: 'ร้านค้า',
    PendingApproval: 'รออนุมัติ',
    StoreRating: 'คะแนนร้านค้า',
    NoRatingYet: 'ยังไม่มีคะแนน',
    AllProductsList: 'รายการสินค้าทั้งหมด',
    AllProducts: 'สินค้าทั้งหมด',
    RecommendedPromotion: 'โปรโมชันแนะนำ',
    NewArrival: 'สินค้ามาใหม่',
    DiscountedProducts: 'สินค้าลดราคา',
    BestSellers: 'สินค้าขายดี',
    Recommended: 'สินค้าแนะนำ',
    GeneralProducts: 'สินค้าทั่วไป',
    SameStoreProducts: 'สินค้าจากร้านเดียวกัน',
    OutOfStock: 'สินค้าหมด',
    All: 'ทั้งหมด',
    AboutStore: 'เกี่ยวกับร้านค้า',
    RegisteredOnly: 'สินค้าสำหรับผู้ลงทะเบียนคู่ค้าเท่านั้น',
    RegisterAsPartner: 'ลงทะเบียนคู่ค้า',
    StoreNotFound: 'ไม่พบร้านค้าในระบบ',
    PleaseCheckStore: 'โปรดตรวจสอบร้านค้าที่คุณต้องการอีกครั้ง',
    ContactStore: 'กรุณาติดต่อร้านค้า',
    OrContactSupport1: 'หรือเจ้าหน้าที่',
    ClickIfInterested: 'หากสนใจสามารถกด',
    NoProductYet: 'ร้านค้านี้ยังไม่มีสินค้า',
    RequestSubmitted: 'คุณได้ทำการยื่นคำขอแล้ว กรุณารอร้านค้าอนุมัติการเป็นคู่ค้า',
    ExternalOrder: 'สั่งซื้อภายนอกองค์กร',
    CustomerCode: 'รหัสลูกค้า',
    SelectCustomerInfo: 'เลือกข้อมูลลูกค้า',
    InternalOrder: 'สั่งซื้อภายในองค์กร',
    StoreCoupon: 'โค้ดส่วนลดจากร้านค้า',
    ViewAll: 'ดูทั้งหมด',
    CouponConditions: 'เงื่อนไขการใช้คูปอง',
    NoExpiry: 'ไม่มีวันหมดอายุ',
    Used: 'ใช้แล้ว',
    Coupon: 'คูปอง',
    Discount: 'ส่วนลด',
    Freebie: 'แถมฟรี',
    SaveCode: 'เก็บโค้ด',
    SavedCode: 'เก็บโค้ดแล้ว',
    Minimum: 'ขั้นต่ำ',
    THB: 'บาท',
    FreeShippingCode: 'โค้ดส่งฟรี',
    Title: 'หน้าแรก',
    AllProductsCategory: 'สินค้าทั้งหมด / หมวดหมู่ทั้งหมด',
    Category: 'หมวดหมู่',
    Type: 'ประเภท',
    Price: 'ราคา',
    NoProduct: 'ยังไม่มีสินค้า',
    SortBy: 'เรียงตาม',
    Filter: 'ตัวกรอง',
    Article: 'บทความ',
    ReadMore: 'อ่านต่อ',
    NoArticles: 'ไม่มีบทความภายในร้านค้า',
    CustomerTypeList: 'รายการประเภทลูกค้า',
    Company: 'บริษัท',
    CompanyOrder: 'การสั่งซื้อให้องค์กรบริษัท',
    Individual: 'บุคคล',
    PersonalOrder: 'การสั่งซื้อให้บุคคลธรรมดา',
    AllCoupons: 'คูปองทั้งหมด',
    ValidUntil: 'ใช้ได้ถึง',
    DiscountCodeConditions: 'เงื่อนไขโค้ดส่วนลด',
    PromotionType: 'ประเภทโปรโมชัน',
    ProductDiscount: 'ส่วนลดราคาสินค้า',
    ShippingDiscount: 'ส่วนลดค่าจัดส่ง',
    CouponEligibility: 'สิทธิ์การใช้คูปอง',
    Unlimited: 'ไม่จำกัดสิทธิ์',
    DiscountCodeDetails: 'รายละเอียดโค้ดส่วนลด',
    FreebieDetails: 'รายละเอียดของแถม',
    OrderList: 'รายการสั่งซื้อ',
    ProductName: 'ชื่อสินค้า',
    Options: 'ตัวเลือก',
    Quantity: 'จำนวน',
    FreeGift: 'ของแถม',
    CustomerList: 'รายชื่อลูกค้า',
    AddCustomerInfo: 'เพิ่มข้อมูลลูกค้า',
    FindNamePartner: 'ค้นหาชื่อ',
    AddInfo: 'เพิ่มข้อมูล',
    SelectAddress: 'เลือกที่อยู่จัดส่งสินค้า',
    AllAddresses: 'รายการที่อยู่ทั้งหมด',
    ItemList: 'รายการ',
    AddNewAddress: 'เพิ่มที่อยู่ใหม่',
    PartnerRequest: 'ยื่นคำขอเป็นคู่ค้า',
    StoreName: 'ชื่อร้านค้า',
    PartnerRequestDetails: 'รายละเอียดยื่นคำขอเป็นคู่ค้า',
    Email: 'E-mail',
    EmailNote: '*ใช้ในกรณีแจ้งเตือนการวางขายสินค้าให้กับคู่ค้า',
    EmailNote2: 'ใช้ในกรณีแจ้งเตือนการวางขายสินค้า',
    CustomerGroup: 'กลุ่มลูกค้า',
    SpecifyGroup: 'ระบุกลุ่มลูกค้า',
    RequiredDocuments: 'เอกสารที่ต้องอัปโหลด',
    UploadNote: '(รองรับไฟล์นามสกุล .pdf และขนาดไฟล์ไม่เกิน 5mb)',
    UploadFile: 'อัปโหลดไฟล์',
    DropFileHere: 'เพิ่มไฟล์ของคุณที่นี่',
    OrChooseFile: 'หรือเลือกไฟล์จากคอมพิวเตอร์ของคุณ',
    FileNote: '(ไฟล์นามสกุล .PDF ขนาดไม่เกิน 5 mb)',
    Cancel: 'ยกเลิก',
    SubmitRequest: 'ส่งคำขอ',
    CreateQuotation: 'สร้างใบเสนอราคา',
    Budget: 'งบประมาณ',
    DeductBudget: 'ตัดงบ',
    Confirm: 'ตกลง',
    DocumentSample: 'ตัวอย่างเอกสาร',
    MustRegisterToView: 'คุณต้องทำการขอลงทะเบียนคู่ค้ากับทางผู้ขาย เพื่อดูสินค้าและบริการจากทางร้านค้า',
    OrContactSupport: 'หรือ ติดต่อเจ้าหน้าที่',
    ConfirmPartnerRequest: 'คุณต้องการยื่นคำขอเป็นคู่ค้ากับร้านค้านี้ ใช่ หรือไม่',
    PartnerRequestSent: 'ยื่นคำขอเป็นคู่ค้าเรียบร้อย',
    AlreadyRequested: 'คุณได้ทำการยื่นคำขอเป็นคู่ค้าเรียบร้อย',
    Clear: 'ล้างค่า',
    ConfirmAction: 'ยืนยัน'
  },
  AllShopsPage: {
    Home: 'หน้าแรก',
    Shop: 'ร้านค้า',
    AllShops: 'ร้านค้าทั้งหมด',
    AllShop: 'ร้านค้าทั้งหมด',
    SearchResults: 'ผลการค้นหา',
    SearchShops: 'ค้นหาจากชื่อร้านค้า',
    NoShop: 'ไม่มีร้านค้า',
    ShopNotFound: 'ไม่พบร้านค้า',
    ShopSearchEmpty: 'ไม่พบร้านค้าที่คุณค้นหา กรุณาตรวจสอบอีกครั้ง'
  },
  YourOrderPage: {
    banner: require('@/assets/new_yourorder.png'),
    PaymentInformation: 'การชำระเงิน',
    OrderID: 'รหัสการสั่งซื้อ',
    TotalAmount: 'ยอดชำระเงิน',
    NexgenDiscountCoupon: 'ส่วนลดคูปองระบบ',
    SellerDiscount: 'ส่วนลดร้านค้าทั้งหมด',
    NexgenPoinsDiscount: 'ส่วนลดจากการใช้แต้ม',
    SystemCoupon: 'คูปองระบบ (ส่งฟรี)',
    StoreCoupon: 'คูปองร้านค้า (ส่งฟรี)',
    DateTimeofPayment: 'วันและเวลาที่ชำระเงิน',
    PaymentMethod: 'รูปแบบการชำระเงิน',
    PaymentResult: 'ผลการชำระเงิน',
    OrderDetails: 'รายละเอียดการสั่งซื้อสินค้า',
    Store: 'ร้านค้า',
    ProductDetails: 'รายละเอียดสินค้า',
    UnitPrice: 'ราคาต่อชิ้น',
    Quantity: 'จำนวน',
    TotalPrice: 'ราคารวม',
    ShippingAddress: 'ที่อยู่ในการจัดส่งสินค้า',
    InvoiceAddress: 'ที่อยู่ใบกำกับภาษี',
    PickupDate: 'วันรับสินค้า',
    PickupTime: 'เวลารับสินค้า',
    ShippingNew: require('@/assets/shipping_new.png'),
    PickUpNew: require('@/assets/pickup_new.png'),
    Baht: 'บาท',
    PaymentConfirmed: 'ชำระเงินสำเร็จ',
    Freegift: 'แถมฟรี',
    DiscountCode: 'โค้ดส่วนลด',
    ListOf: 'รายชื่อพนักงาน',
    employees: 'รายชื่อ',
    Department: 'แผนก',
    Company: 'บริษัท',
    SubDistrict: 'ตำบล/แขวง',
    District: 'อำเภอ/เขต',
    Province: 'จังหวัด',
    Time: 'น.',
    GeneralProducts: 'สินค้าทั่วไป',
    ServiceProduct: 'สินค้าบริการ',
    GotoOrderList: 'ไปยังหน้ารายการสั่งซื้อ',
    GotoHomepage: 'ไปยังหน้าหลัก',
    Paymentincomplete: 'การชำระเงินไม่เสร็จสมบูรณ์',
    NoAddress: 'คุณยังไม่ได้เพิ่มข้อมูลที่อยู่',
    Incompleteinformation: 'ใส่ข้อมูลไม่ครบ',
    ConfirmOrder: 'กรุณาทำรายการยืนยันคำสั่งซื้ออีกครั้ง',
    OverWeight: 'สินค้าของคุณมีน้ำหนักรวมของสินค้าเกิน 50 กิโลกรัม หากต้องการสั่งซื้อกรุณาติดต่อเจ้าหน้าที่ ขอบคุณครับ',
    Sku: 'รหัสสินค้า',
    WeightProblem: 'มีปัญหาเรื่องน้ำหนัก กรุณาติดต่อเจ้าหน้าที่',
    UserPermission: 'กรุณาตรวจสอบ เนื่องจากมีการเปลี่ยนแปลงสิทธิ์ของผู้ใช้งาน'
  },
  ListCoupon: {
    Header: 'คูปองส่วนลดร้านค้า',
    HeaderSystemCode: 'คูปองส่วนลดระบบ',
    HeaderSystemCodeShipping: 'คูปองส่วนลดขนส่งระบบ',
    PlaceHolder: 'ค้นหาคูปองส่วนลด',
    GeneralDiscount: 'ส่วนลดทั่วไป',
    ShippingDiscount: 'ส่วนลดค่าจัดส่ง',
    Min: 'ขั้นต่ำ',
    Max: 'ไม่เกิน',
    Validfrom: 'ใช้ได้ตั้งแต่วันที่',
    Onwards: 'เป็นต้นไป',
    SelectCoupon: 'เลือกคูปอง',
    FreeGiftCoupon: 'คูปองแถมฟรี',
    ShowMoreCoupons: 'แสดงคูปองเพิ่มเติม',
    ForStore: 'สำหรับร้านค้า',
    Only: 'เท่านั้น',
    NoCoupon: 'ยังไม่มีคูปองส่วนลดร้านค้าทั่วไป',
    NoCouponSystem: 'ยังไม่มีคูปองส่วนลดระบบ',
    CouponNotFound: 'ไม่พบคูปอง หรือไม่อยู่ในช่วงเวลาใช้คูปอง',
    PointError: 'แต้มขัดข้อง กรุณาลองใหม่ภายหลัง',
    PleaseSelect: 'กรุณาเลือกส่วนลดก่อน',
    NotExceed1: 'กรุณาเลือกแต้มส่วนลดให้',
    NotExceed2: 'ไม่เกิน 25% ของราคาสินค้า',
    CannotUse: 'ไม่สามารถใช้คูปองนี้ได้ เนื่องจากเงื่อนไขการใช้งานไม่ตรงตามที่กำหนด',
    errorMessage1: 'ไม่พบคูปอง',
    errorMessage2: 'คูปองใช้เกินกำหนด',
    errorMessage3: 'ข้อมูลมีการเปลี่ยนแปลง',
    errorMessage4: 'คุณต้องการ บันทึก หรือ ยกเลิก ข้อมูลที่เปลี่ยนแปลง',
    errorMessage5: 'กรุณาตรวจสอบข้อมูลโค้ดส่วนลดอีกครั้ง เนื่องจากไม่ตรงตามเงื่อนไขของโค้ดส่วนลด',
    errorMessage6: 'ราคาสินค้าไม่ถึงขั้นต่ำที่กำหนด',
    errorMessage7: 'ไม่สามารถใช้ส่วนลดนี้สำหรับการรับสินค้าหน้าร้าน',
    errorMessage8: 'เก็บคูปองสำเร็จ',
    errorMessage9: 'ไม่สามารถเก็บคูปองได้',
    errorMessage10: 'ไม่พบโค้ด',
    errorMessage11: 'มีโค้ด',
    errorMessage12: 'อยู่แล้ว กรุณาตรวจสอบอีกครั้ง',
    errorMessage13: 'กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง',
    Balance: 'คงเหลือ',
    Save: 'บันทึก'
  },
  AllProductCategory: {
    HomePage: 'หน้าแรก',
    Categories: 'หมวดหมู่สินค้า',
    NoProduct: 'ยังไม่มีสินค้า'
  },
  SearchPage: {
    HomePage: 'หน้าแรก',
    RelatedShops: 'ร้านค้าที่เกี่ยวข้องกับ',
    All: 'ทั้งหมด',
    Shop: 'ร้าน',
    OtherShops: 'ร้านค้าอื่นๆ',
    ViewShop: 'ดูร้านค้า',
    SearchKeyword: 'ค้นหาคำว่า',
    NoResults: 'ไม่พบผลการค้นหา',
    CheckSpelling: 'โปรดตรวจสอบตัวสะกดว่าถูกต้องหรือไม่',
    TrySimilarWords: 'หรือค้นหาโดยใช้คำที่ใกล้เคียง',
    TryAgain: 'กรุณาลองค้นหาใหม่อีกครั้ง',
    SearchResults: 'ผลการค้นหา',
    list: 'รายการ',
    Store: 'ร้านค้า'
  },
  SelectPrice: {
    Price: 'ราคา',
    All: 'ทั้งหมด',
    Low: 'ราคา: จากน้อยไปมาก',
    High: 'ราคา: จากมากไปน้อย',
    Higher: 'จากมากไปน้อย',
    Lower: 'จากน้อยไปมาก',
    AllCategories: 'หมวดหมู่ทั้งหมด',
    NotFoundTag: 'ไม่พบสินค้าใน tag สินค้าที่ท่านเลือก',
    NotTag: 'ไม่มีรายการแท็กสินค้า',
    TagProduct: 'แท็กสินค้า'
  },
  BankAccountPage: {
    header: 'บัญชีธนาคาร',
    headerModal: 'บัญชีของฉัน',
    index: 'บัญชีลำดับที่',
    addAccount: 'เพิ่มบัญชี',
    editAccount: 'แก้ไข',
    editAccountModal: 'แก้ไขบัญชี',
    deleteAccount: 'ลบ',
    overTenAccount: 'ไม่สามารถเพิ่มบัญชีได้ (เพิ่มได้สูงสุด 10 รายการ)',
    accountName: 'ชื่อบัญชี',
    accountNameModal: 'ชื่อบัญชี',
    PlaceHolderAccountName: 'ระบุชื่อบัญชี',
    validateAccountName1: 'กรุณากรอกชื่อบัญชีธนาคาร',
    validateAccountName2: 'กรอกได้เฉพาะตัวอักษรเท่านั้น',
    bankName: 'ธนาคาร',
    bankNameModal: 'ธนาคาร',
    PlaceHolderBankName: 'เลือกธนาคาร',
    validateBankName1: 'กรุณาเลือกธนาคาร',
    accountNumber: 'เลขบัญชี',
    accountNumberModal: 'เลขบัญชี',
    PlaceHolderAccountNumber: 'ระบุเลขบัญชี',
    validateAccountNumber1: 'กรุณากรอกเลขบัญชีธนาคาร',
    validateAccountNumber2: 'กรุณากรอกเลขบัญชีธนาคาร',
    accountDefault: 'ตั้งค่าเป็นบัญชีเริ่มต้น',
    addAwait: 'เพิ่ม',
    editAwait: 'แก้ไข',
    deleteAwait: 'ลบ',
    MyAccount: 'บัญชีของฉัน',
    Proceed: 'คุณต้องการทำรายการนี้ ใช่ หรือ ไม่',
    Review: 'คุณได้ตรวจสอบข้อมูลเรียบร้อยแล้วใช่หรือไม่',
    Success: 'บัญชีสำเร็จ',
    DetailSuccess1: 'คุณได้ทำการ',
    DetailSuccess2: 'บัญชีเรียบร้อย',
    NoFound: 'ยังไม่มีบัญชีของคุณ',
    Press: 'สามารถกด'
  },
  FavoritePage: {
    header: 'รายการสินค้าที่ถูกใจ',
    PlaceHolderName: 'ค้นหาชื่อสินค้า',
    PleceHolderCategory: 'เลือก',
    Type: 'ประเภท',
    Category: 'ประเภท',
    Wishlist: 'รายการสินค้าที่ถูกใจทั้งหมด',
    list: 'รายการ',
    TypeList: {
      All: 'ทั้งหมด',
      GeneralProducts: 'สินค้าทั่วไป',
      ServiceProduct: 'สินค้าบริการ'
    },
    NoProduct1: 'ไม่มีรายการสินค้าที่ถูกใจ',
    NoProduct2: 'สามารถกด',
    NoProduct3: 'ซื้อสินค้า'
  },
  CouponProfile: {
    header: 'คูปองและคะแนน',
    Condition: 'เงื่อนไขคะแนนเป็นไปตามที่ร้านค้ากำหนด',
    headerTable: 'รายการแต้มการสั่งซื้อสินค้าจากร้านค้าที่เข้าร่วมแต้มส่วนลด',
    subHeaderTable: {
      Shop: 'ชื่อร้านค้า',
      getPoint: 'อัตราส่วนคะแนนที่ได้ต่อยดคำสั่งซื้อ',
      usePoint: 'คะแนนที่ใช้ได้',
      detailTable: 'รายละเอียด',
      detail: {
        OrderList: 'รายการสั่งซื้อ',
        ProductPrice: 'ราคาสินค้า',
        PointsEarned: 'คะแนนที่ได้',
        PointsUsed: 'คะแนนที่ใช้',
        total: 'รวม',
        ViewDetails: 'ดูรายละเอียด'
      },
      Baht: 'บาท',
      Per: 'ต่อ',
      Point: 'คะแนน',
      NumberOfRows: 'จำนวนแถว'
    },
    Coupon: {
      CanUse: 'ที่ใช้งานได้',
      CannotUse: 'ใช้งานไม่ได้/หมดอาายุ',
      CouponCard: {
        CannotUseWith: 'ใช้ร่วมกับโค้ดส่วนลดอื่นๆไม่ได้',
        Minimum: 'ซื้อสินค้าขั้นต่ำ',
        Maximum: 'ลดสูงสุด',
        ValidUntil: 'ใช้ได้ถึง',
        NoExpirationDate: 'ไม่มีวันหมดอายุ',
        Used: 'ใช้แล้ว',
        Remaining: 'คงเหลือ',
        ViewConditions: 'ดูเงื่อนไข',
        Coupons: 'คูปอง',
        Discount: 'ส่วนลด',
        FreeGift: 'แถมฟรี'
      }
    },
    ConditionModal: {
      header: 'เงื่อนไขการใช้คูปอง',
      PromotionType: 'ประเภทโปรโมชัน',
      PromotionTypeList: {
        ProductPriceDiscount: 'ส่วนลดราคาสินค้า',
        ShippingDiscount: 'ส่วนลดค่าจัดส่งสินค้า',
        FreeGiftCoupon: 'คูปองแถมฟรี'
      },
      CouponEligibilityList: {
        CouponEligibility: 'สิทธิ์การใช้คูปอง',
        CanBeUsed: 'สามารถใช้ได้',
        Times: 'สิทธิ์'
      },
      AvailableCreditLimit: 'วงเงินที่ใช้ได้',
      MaximumDiscount: 'ส่วนลดสูงสุด',
      Unlimited: 'ไม่จำกัด',
      ParticipatingStores: 'ร้านค้าที่เข้าร่วม',
      ListOfAll: 'รายการร้านค้าทั้งหมด',
      Stores: 'ร้านค้า',
      ValidAtAllStores: 'ใช้ได้ทุกร้านค้า',
      ShowLess: 'แสดงน้อยลง',
      ShowMore: 'ดูเพิ่มเติม',
      DiscountCodeDetails: 'รายละเอียดโค้ดส่วนลด',
      FreeGiftDetails: 'รายละเอียดของแถม',
      OrderList: 'รายการสั่งซื้อ',
      ProductName: 'ชื่อสินค้า',
      Attribute: 'ตัวเลือก',
      Amount: 'จำนวน',
      FreeGift: 'ของแถม',
      GoBack: 'ย้อนกลับ',
      NoCouponsAvailable: 'ไม่มีรายการคูปอง'
    }
  },
  AddressProfilePage: {
    headerPage: 'ที่อยู่ในการจัดส่งสินค้า',
    headerInvoice: 'ที่อยู่ในการออกใบกำกับภาษี',
    AddAddressBtn: 'เพิ่มที่อยู่',
    OverAddress: 'ไม่สามารถเพิ่มที่อยู่ได้ (เพิ่มได้สูงสุด 10 รายการ)',
    OverAddressInvoice: 'ไม่สามารถเพิ่มที่อยู่ได้ (เพิ่มได้สูงสุด 5 รายการ)',
    SetDefaultAddress: 'ตั้งค่าเป็นที่อยู่เริ่มต้น',
    SuccessSetDefaultAddress: 'ตั้งค่าเป็นที่อยู่เริ่มต้นแล้ว',
    FailedSetDefaultAddress: 'ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ',
    Individual: 'บุคคลธรรมดา',
    LegalEntity: 'นิติบุคคล',
    remark: 'หมายเหตุ',
    BranchNo: 'รหัสสาขา',
    TaxID: 'เลขประจำตัวผู้เสียภาษี',
    NotHaveAddress: 'ยังไม่มีที่อยู่ของคุณ',
    ShippingPurpose: 'เพื่อใช้ในการจัดส่งสินค้า',
    TaxInvoicePurpose: 'เพื่อใช้ในการออกใบกำกับภาษี',
    Click: 'สามารถกด',
    ShippingAddressModal: {
      TitleAddAddress: 'เพิ่มที่อยู่ในการจัดส่งสินค้า',
      TitleEditAddress: 'แก้ไขอยู่ในการจัดส่งสินค้า',
      RecipientInformation: 'ข้อมูลผู้รับสินค้า',
      FirstName: 'ชื่อ',
      PlaceHolderFirstName: 'ระบุชื่อ',
      LastName: 'นามสกุล',
      PlaceHolderLastName: 'ระบุนามสกุล',
      Phone: 'เบอร์โทรศัพท์',
      PlaceHolderPhone: 'ระบุเบอร์โทรศัพท์',
      Email: 'อีเมล',
      PlaceHolderEmail: 'ระบุอีเมล',
      HouseNo: 'เลขที่',
      PlaceHolderHouseNo: 'ระบุเลขที่อยู่',
      RoomNo: 'ห้องเลขที่',
      PlaceHolderRoomNo: 'ระบุเลขห้อง',
      Floor: 'ชั้นที่',
      PlaceHolderFloor: 'ระบุชั้น',
      BuildingName: 'อาคาร',
      PlaceHolderBuildingName: 'ชื่ออาคาร,อพาร์ทเมนต์,คอนโดมิเนียม',
      Village: 'หมู่บ้าน',
      PlaceHolderVillage: 'ชื่อหมู่บ้าน',
      VillageNo: 'หมู่ที่',
      PlaceHolderVillageNo: 'ระบุหมู่',
      Alley: 'ตรอก/ซอย',
      PlaceHolderAlley: 'ระบุตรอก,ซอย',
      Junction: 'แยก',
      PlaceHolderJunction: 'ระบุแยก',
      Road: 'ถนน',
      PlaceHolderRoad: 'ชื่อถนน',
      SubDistrict: 'แขวง/ตำบล',
      PlaceHolderSubDistrict: 'ระบุแขวง/ตำบล',
      District: 'เขต/อำเภอ',
      PlaceHolderDistrict: 'ระบุเขต/อำเภอ',
      Province: 'จังหวัด',
      PlaceHolderProvince: 'ระบุจังหวัด',
      Zipcode: 'รหัสไปรษณีย์',
      PlaceHolderZipCode: 'ระบุรหัสไปรษณีย์',
      PlaceHolderRemark: 'ระบุหมายเหตุ',
      error1: 'ข้อมูลไม่ถูกต้อง',
      error2: 'กรุณากรอกชื่อผู้รับ',
      error3: 'กรอกได้ไม่เกิน 20 ตัวอักษร',
      error4: 'กรุณากรอกนามสกุลผู้รับ',
      error5: 'กรอกได้ไม่เกิน 30 ตัวอักษร',
      error6: 'กรุณากรอกเบอร์โทรศัพท์',
      error7: 'เบอร์โทรศัพท์ควรขึ้นต้นด้วยเลข 0',
      error8: 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก',
      error9: 'กรุณากรอกอีเมล',
      error10: 'กรุณากรอกอีเมลให้ถูกต้อง',
      error11: 'ห้ามใช้อักขระภาษาไทย',
      error12: 'ห้ามใช้อักขระพิเศษ',
      error13: 'ห้ามใส่ช่องว่างในอีเมล',
      error14: 'กรุณาระบุเลขที่',
      error15: 'กรุณากรอกข้อมูลให้ถูกต้อง',
      error16: 'ระบุข้อมูลไม่ถูกต้อง',
      error17: 'กรุณาระบุตัวเลขเท่านั้น',
      error18: 'กรอกได้ไม่เกิน 120 ตัวอักษร',
      error19: 'ระบุข้อมูลไม่ถูกต้อง',
      error20: 'กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง',
      error21: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)',
      error22: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)',
      Success1: 'เพิ่มที่อยู่จัดส่งเสร็จสิ้น',
      Success2: 'แก้ไขที่อยู่จัดส่งเสร็จสิ้น',
      Success3: 'คุณได้ทำการเพิ่มที่อยู่จัดส่งเรียบร้อย',
      Success4: 'คุณได้ทำการแก้ไขที่อยู่จัดส่งเรียบร้อย'
    },
    TaxInvoiceAddressModal: {
      TitleAddAddressInvoice: 'เพิ่มที่อยู่ในการออกใบกำกับภาษี',
      TitleEditAddressInvoice: 'แก้ไขที่อยู่ในการออกใบกำกับภาษี',
      GeneralInformation: 'ข้อมูลเบื้องต้น',
      TaxInvoiceInformation: 'ข้อมูลในการออกใบกำกับภาษี',
      SameAddress: 'ใช้ที่อยู่เดียวกับที่อยู่ในการจัดส่งสินค้า',
      TaxInvoiceName: 'ชื่อที่ใช้ในการออกใบกำกับภาษี',
      BranchNo: 'รหัสสาขา',
      enterCode: '(กรุณาระบุ รหัส 00000 กรณีเป็นสำนักงานใหญ่)',
      TaxInvoiceNo: 'เลขประจำตัวผู้เสียภาษี',
      TaxInvoiceEmail: 'อีเมลผู้เสียภาษี',
      TaxInvoiceAddress: 'ที่อยู่ในการออกใบกำกับภาษี',
      error2: 'กรุณาเลือกข้อมูล',
      error3: 'กรุณากรอกชื่อที่ใช้ในการออกใบกำกับภาษี',
      error4: 'ห้ามกรอกชื่อที่ใช้ในการออกใบกำกับภาษีเกิน 100 ตัวอักษร',
      error5: 'กรุณากรอกเลขประจำตัวผู้เสียภาษีผู้รับ',
      error6: 'กรุณากรอกเลขประจำตัวผู้เสียภาษีให้ครบ 13 หลัก',
      error7: 'เลขประจำตัวผู้เสียภาษีไม่ถูกต้อง',
      error8: 'กรุณาระบุที่อยู่',
      error9: 'กรุณากรอกอีเมล',
      error10: 'กรุณากรอกอีเมลให้ถูกต้อง',
      error11: 'ห้ามกรอกอีเมลผู้เสียภาษีเกิน 256 ตัวอักษร',
      error12: 'กรุณากรอกเบอร์โทรศัพท์',
      error13: 'เบอร์โทรศัพท์ควรขึ้นต้นด้วยเลข 0',
      error14: 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก',
      error15: 'กรุณากรอกรหัสสาขา',
      error16: 'กรุณากรอกรหัสสาขา 5 หลัก',
      error17: 'กรุณาเลือก',
      error18: 'ไม่มีที่อยู่ในการจัดส่งสินค้า',
      error19: 'กรุณาเลือกตั้งค่าเป็นที่อยู่ในการจัดส่งสินค้าเริ่มต้นก่อน',
      error20: 'กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง',
      error21: 'กรุณากรอกข้อมูลให้ครบ',
      error22: 'กรุณาตรวจสอบข้อมูลอีกครั้ง',
      error23: 'โปรดเลือก (บุคคลธรรมดา หรือ นิติบุคคล)',
      Success1: 'เพิ่มที่อยู่เสร็จสิ้น',
      Success2: 'แก้ไขที่อยู่เสร็จสิ้น',
      Success3: 'คุณได้ทำการเพิ่มที่อยู่ในการออกใบกำกับภาษีเรียบร้อย',
      Success4: 'คุณได้ทำการแก้ไขที่อยู่ในการออกใบกำกับภาษีเรียบร้อย'
    },
    deleteAddress: {
      TitleShippingAddress: 'ลบที่อยู่จัดส่ง',
      TitleTaxInvoiceAddress: 'ลบที่อยู่ใบกำกับภาษี',
      DeleteShippingAddressSuccess: 'ลบที่อยู่จัดส่งเสร็จสิ้น',
      DeleteShippingAddressSuccess1: 'คุณได้ทำการลบที่อยู่จัดส่งเรียบร้อย',
      DeleteTaxInvoiceAddressSuccess: 'ลบที่อยู่เสร็จสิ้น',
      DeleteTaxInvoiceAddressSuccess1: 'คุณได้ทำการลบที่อยู่ในการออกใบกำกับภาษีเรียบร้อย'
    }
  },
  ListPoint: {
    StoreDiscountPoint: 'แต้มส่วนลดร้านค้า',
    Points: 'แต้ม',
    RedeemPoints: 'ใช้แต้มสะสม',
    PointUsageRules: 'ข้อกำหนดการใช้แต้มสะสม',
    MaximumUsablePoints: 'แต้มที่ใช้ได้สูงสุด',
    Baht: 'บาท',
    Spend: 'ซื้อครบ',
    Earn: 'ได้รับแต้ม',
    PointUsageMustNotExceed: 'การใช้แต้มต้องไม่เกิน',
    OrderTotal: 'ของยอดคำสั่งซื้อ',
    Cancel: 'ยกเลิก',
    Confirm: 'ตกลง',
    NoDocument: 'ไม่มีเอกสาร',
    CompanyCertificateCopy: 'สำเนาหนังสือรับรองบริษัท',
    CompanyCertificateCopyDocument: 'เอกสารสำเนาหนังสือรับรองบริษัท',
    PointError: 'แต้มขัดข้อง กรุณาลองใหม่ภายหลัง',
    SystemError: 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่',
    PleaseSelectDiscount: 'กรุณาเลือกส่วนลดก่อน',
    PointExceedLimit: 'กรุณาเลือกแต้มส่วนลดให้<br>ไม่เกิน 25% ของราคาสินค้า'
  },
  ListCode: {
    Title: 'คูปองสานสัมพันธ์',
    SearchPlaceholder: 'ค้นหาคูปองส่วนลด',
    EmployeeList: 'รายชื่อพนักงาน',
    ListName: 'รายชื่อ',
    Department: 'แผนก',
    Company: 'บริษัท',
    EmployeeName: 'ชื่อพนักงาน',
    NotAdded: 'โค้ดยังไม่ถูกเพิ่ม',
    Cancel: 'ยกเลิก',
    Confirm: 'ตกลง',
    Delete: 'ลบ',
    TitleConfirm: 'ข้อมูลมีการเปลี่ยนแปลง',
    Description: 'คุณต้องการ บันทึก หรือ ยกเลิก ข้อมูลที่เปลี่ยนแปลง',
    Save: 'บันทึก',
    InvalidCouponCondition: 'กรุณาตรวจสอบข้อมูลโค้ดส่วนลดอีกครั้ง เนื่องจากไม่ตรงตามเงื่อนไขของโค้ดส่วนลด',
    NotFound: 'ไม่พบโค้ด',
    AlreadyUsed_Prefix: 'มีโค้ด',
    AlreadyUsed_Suffix: 'อยู่แล้ว กรุณาตรวจสอบอีกครั้ง',
    InvalidInput: 'กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง',
    PointError: 'แต้มขัดข้อง กรุณาลองใหม่ภายหลัง',
    SystemError: 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่'
  },
  withdrawAffiliate: {
    WithdrawHistory: 'ประวัติการถอนเงิน',
    ConfirmTitle: 'คุณต้องการทำการถอนเงิน ใช่ หรือ ไม่',
    Cancel: 'ยกเลิก',
    Confirm: 'ตกลง',
    TopEarning: 'รายได้สูงสุด',
    TopProduct: 'สินค้าขายดี',
    SearchPlaceholder: 'ค้นหาข้อมูลภายในตาราง',
    NoData: 'ไม่พบรายการ',
    Views: 'ยอดเข้าชม',
    Orders: 'คำสั่งซื้อ',
    TotalSales: 'ยอดขายรวม',
    Commission: 'ค่าคอมมิชชั่น',
    Times: 'ครั้ง',
    Items: 'รายการ',
    Baht: 'บาท',
    Withdraw: 'ถอนเงิน',
    FeeOnly: 'ค่าธรรมเนียม 5 บาท',
    MinWithdrawNote: 'ถอนเงินขั้นต่ำ 20 บาท',
    EKYCWarning: 'คุณต้องทำการยืนยัน eKYC ให้เรียบร้อยก่อน จึงจะสามารถถอนเงินได้',
    EKYCWarning1: 'คุณต้องทำการยืนยัน eKYC ให้เรียบร้อยก่อน',
    EKYCWarning2: 'จึงจะสามารถถอนเงินได้',
    OrderNumber: 'เลขคำสั่งซื้อ',
    Shop: 'ร้านค้า',
    OrderAffiliate: 'คำสั่งซื้อ Affiliate',
    TotalAffiliate: 'รายได้ Affiliate (บาท)',
    TimeTransfer: 'เวลาการโอน',
    TransferAffiliate: 'สถานะการโอน',
    SystemError: 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่',
    NoWithdrawData: 'ไม่มีข้อมูลการถอนเงิน Affiliate'
  },
  clickAffiliateBuyer: {
    Title: 'รายงานการคลิก',
    ClickTime: 'เวลาคลิก',
    ClickTimePlaceholder: 'ค้นหาเวลาคลิก',
    ClickCode: 'รหัสคลิก',
    ClickCodePlaceholder: 'ค้นหาเวลารหัสคลิก',
    Region: 'ภาคที่คลิก',
    RegionPlaceholder: 'ค้นหาคลิกภูมิภาค',
    SubId: 'Sub_id',
    SubIdPlaceholder: 'ค้นหา sub_id',
    Clear: 'ล้างค่า',
    TotalResult: 'ยอดรวม',
    Items: 'รายการ',
    Export: 'Export',
    NoClickData: 'ไม่พบรายงานการคลิก',
    EmptyMessage: 'คุณยังไม่มีรายงานการคลิก',
    Cancel: 'ยกเลิก',
    Confirm: 'ตกลง',
    Reference: 'อ้างอิง'
  },
  ReviewListBuyer: {
    title: 'การประเมินความพึงพอใจ',
    notEvaluation: 'ยังไม่ได้ประเมิน',
    assessed: 'ประเมินแล้ว',
    searchOrder: 'ค้นหาจากรหัสการสั่งซื้อ',
    orderNot: 'รายการยังไม่ได้ประเมินทั้งหมด',
    orderSuccess: 'รายการประเมินแล้วทั้งหมด',
    order: 'รายการ',
    noResultsText: 'ไม่มีข้อมูลการประเมินความพึงพอใจสินค้าที่ค้นหา',
    noDataText: 'ไม่มีข้อมูลการประเมินความพึงพอใจสินค้าในตาราง',
    numberRows: 'จำนวนแถว',
    status1: 'ประเมินความพึงพอใจแล้ว',
    status2: 'รอการประเมินความพึงพอใจ',
    btnDetail: 'รายละเอียด',
    btnReview: 'ประเมินความพึงพอใจ',
    noOrderEvaluated: 'คุณยังไม่มีรายการประเมินความพึงพอใจที่',
    thOrder: 'รหัสการสั่งซื้อ',
    thStatus: 'สถานะ',
    thExpired: 'ประเมินภายในวันที่',
    thManage: 'จัดการ',
    textDialog: 'กรุณาประเมินความพึงพอใจสินค้า',
    titleSKU: 'รหัสสินค้า',
    titleEdit: 'แก้ไข',
    titleReviewEdit: 'เขียนการประเมินความพึงพอใจ',
    titleDateEdit: 'ประเมินความพึงพอใจ วันที่',
    titleDateSuccess: 'แก้ไขการประเมินความพึงพอใจ วันที่',
    titleReviewSuccess: 'การประเมินความพึงพอใจ',
    noImage: 'ไม่มีรูปภาพ',
    uploadImage: 'เพิ่มรูปภาพของคุณที่นี่',
    fixedSizeImage: '(อัปโหลดไม่เกิน 6 ภาพ ขนาดไม่เกิน 6 mb นามสกุลไฟล์ .png,jpeg,jpg)',
    uploadVideo: 'เพิ่มวิดีโอของคุณที่นี่',
    fixedSizeVideo: '(อัปโหลดไม่เกิน 1 วิดีโอ ขนาดไม่เกิน 20 mb นามสกุลไฟล์ .mp4)',
    anonymous: 'ไม่แสดงตัวตน',
    btnCancel: 'ยกเลิก',
    btnSuccess: 'บันทึก'
  },
  Affiliate: {
    AffiliateShop: 'ร้านค้า Affiliate',
    AffiliateShopDescription: 'ร้านค้าที่เข้าร่วม Affiliate',
    SearchShop: 'ค้นหาชื่อร้านค้า',
    AllShops: 'ทั้งหมด',
    JoinAffiliate: 'เข้าร่วม Affiliate',
    WaitingForApproval: 'รอการอนุมัติ',
    NotJoined: 'ไม่เข้าร่วม Affiliate',
    CancelRequest: 'ยกเลิก',
    List: 'รายการ',
    NoShopFound: 'ไม่พบร้านค้าที่ค้นหา',
    NoAffiliateShop: 'ไม่มีร้านค้า Affiliate',
    UpTo: 'สูงสุดถึง',
    NotSpecified: 'ยังไม่กำหนด',
    StartDate: 'วันที่เริ่มต้น',
    EndDate: 'วันที่สิ้นสุด',
    Unlimited: 'ไม่จำกัด',
    ViewProducts: 'ดูรายการสินค้า',
    NoItems: 'คุณยังไม่มีรายการ',
    NotJoinedAffiliate: 'ไม่ได้เข้าร่วม Affiliate',
    JoinAllAffiliateShops: 'เข้าร่วม Affiliate ร้านค้าทั้งหมด',
    ConfirmShopNameBeforeJoining: 'กรุณาตรวจสอบความถูกต้องของชื่อร้านค้า ก่อนยืนยันเข้าร่วม Affiliate',
    Confirm: 'ตกลง',
    CancelSelection: 'ยกเลิกการเลือก',
    ConfirmShopNameBeforeCancelAll: 'กรุณาตรวจสอบความถูกต้องของชื่อร้านค้าก่อนที่จะยกเลิกการ Affiliate ทั้งหมด',
    AllShopsAffiliate: 'ร้านค้า Affiliate ทั้งหมด',
    JoinAll: 'เข้าร่วม Affiliate ทั้งหมด',
    WaitingAll: 'รอการอนุมัติทั้งหมด',
    RejectAll: 'ไม่เข้าร่วม Affiliate ทั้งหมด',
    DataTable: {
      RowCount: 'จำนวนแถว',
      ShopName: 'ชื่อร้านค้า',
      OfferDuration: 'ระยะเวลาข้อเสนอ',
      CommissionRate: 'อัตราค่าคอมมิชชั่น',
      Status: 'สถานะ',
      Note: 'หมายเหตุ',
      Action: 'จัดการ'
    },
    WaitingApproval: 'รอการอนุมัติจากร้านค้า',
    JoinSuccess: 'เข้าร่วม Affiliate สำเร็จ',
    ProductList: 'รายการสินค้า',
    Shop: 'ร้านค้า',
    SearchProductName: 'ค้นหาชื่อสินค้า',
    AllAffiliateProducts: 'รายการสินค้าที่เข้าร่วม Affiliate ทั้งหมด',
    NoAffiliateProductFound: 'ไม่พบสินค้าที่เข้าร่วม',
    SelectAllOnPage: 'เลือกผลิตภัณฑ์ทั้งหมดในหน้านี้',
    Select: 'เลือก',
    GetAllLinks: 'รับลิงก์แบบทีเดียวทั้งหมด',
    ValidateSelectionBeforeGetLink: 'กรุณาตรวจสอบความถูกต้องของรายการสินค้า Affiliate ที่ได้เลือกก่อนรับลิงก์แบบทีเดียวทั้งหมด',
    ConfirmCancelAllSelection: 'ยืนยันยกเลิกการเลือกทั้งหมด',
    ConfirmCancelAllMessage: 'คุณมั่นใจที่จะยกเลิกการเลือกสินค้าทั้งหมด',
    Product: {
      FDAApproved: 'เครื่องหมาย อย.',
      Sold: 'ขายแล้ว',
      K: 'พัน',
      Unit: 'ชิ้น',
      ContactSupport: 'ติดต่อสอบถามเจ้าหน้าที่',
      CommissionRate: 'อัตราค่าคอมมิชชั่น',
      GetLink: 'รับลิงก์',
      ProductOfferLink: 'ลิงก์ข้อเสนอสินค้า',
      PleaseCopyShortLink: 'กรุณาคัดลอกลิงก์สั้น',
      CopyLink: 'คัดลอกลิงก์',
      Close: 'ปิด',
      CopySuccess: 'คัดลอกลิงก์สำเร็จ',
      CopyError: 'เกิดข้อผิดพลาด ไม่สามารถรับลิงก์ได้',
      MultipleProductLinks: 'ลิงก์สินค้าหลายลิงก์.xlsx'
    },
    ProductListTitle: 'รายการสินค้า Affiliate',
    SystemError: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
  },
  PaymentSettings: {
    Title: 'ตั้งค่าการชำระเงิน',
    AccountType: 'ประเภทบัญชี',
    PaymentInfo: 'ข้อมูลการชำระเงิน',
    AccountName: 'ชื่อบัญชี',
    BankName: 'ชื่อธนาคาร',
    BankBranch: 'ชื่อสาขาธนาคาร',
    BankNumber: 'หมายเลขบัญชีธนาคาร',
    BankBookImage: 'รูปหน้าบัญชีธนาคาร',
    Upload: 'อัปโหลด',
    TaxInfo: 'ข้อมูลภาษี',
    TaxId: 'หมายเลขประจำตัวผู้เสียภาษี',
    AddressCard: 'ที่อยู่ตามบัตรประชาชน',
    AddressNo: 'เลขที่',
    RoomNo: 'ห้องเลขที่',
    Floor: 'ชั้นที่',
    Building: 'อาคาร',
    Village: 'หมู่บ้าน',
    Moo: 'หมู่ที่',
    Soi: 'ตรอก/ซอย',
    Intersection: 'แยก',
    Road: 'ถนน',
    SubDistrict: 'แขวง/ตำบล',
    District: 'เขต/อำเภอ',
    Province: 'จังหวัด',
    PostalCode: 'รหัสไปรษณีย์',
    IsAccountType: {
      Savings: 'ออมทรัพย์',
      FixedDeposit: 'ฝากประจำ'
    },
    PaymentForm: {
      SelectAccountType: 'เลือกประเภทบัญชี',
      EnterAccountName: 'ระบุชื่อบัญชี',
      SelectBankName: 'เลือกชื่อธนาคาร',
      EnterBankBranch: 'ระบุชื่อสาขาธนาคาร',
      EnterBankNumber: 'ระบุหมายเลขบัญชีธนาคาร',
      EnterTaxId: 'ระบุเลขประจำตัวผู้เสียภาษี',
      EnterAddressNo: 'ระบุเลขที่อยู่',
      EnterRoomNo: 'ระบุเลขห้อง',
      EnterFloor: 'ระบุชั้น',
      EnterBuilding: 'ชื่ออาคาร,อพาร์ทเมนต์,คอนโดมิเนียม',
      EnterVillage: 'ชื่อหมู่บ้าน',
      EnterMoo: 'ระบุหมู่',
      EnterSoi: 'ระบุตรอก,ซอย',
      EnterIntersection: 'ระบุแยก',
      EnterRoad: 'ระบุชื่อถนน',
      EnterSubDistrict: 'ระบุแขวง/ตำบล',
      EnterDistrict: 'ระบุเขต/อำเภอ',
      EnterProvince: 'ระบุจังหวัด',
      EnterPostalCode: 'ระบุรหัสไปรษณีย์'
    },
    Validation: {
      SelectAccountType: 'กรุณาเลือกประเภทบัญชี',
      EnterAccountName: 'กรุณาระบุชื่อบัญชี',
      SelectBankName: 'กรุณาเลือกชื่อธนาคาร',
      EnterBankBranch: 'กรุณาระบุชื่อสาขาธนาคาร',
      EnterBankNumber: 'กรุณาระบุหมายเลขบัญชีธนาคาร',
      NumbersOnly: 'กรุณากรอกเฉพาะตัวเลข',
      EnterTaxId: 'กรุณาระบุเลขประจำตัวผู้เสียภาษี',
      TaxIdLength: 'เลขประจำตัวผู้เสียภาษีต้องมี 13 หลักและเป็นตัวเลขเท่านั้น',
      InvalidTaxId: 'เลขประจำตัวผู้เสียภาษีไม่ถูกต้อง',
      EnterAddressNo: 'กรุณาระบุเลขที่',
      EnterValidData: 'กรุณากรอกข้อมูลให้ถูกต้อง',
      InvalidInput: 'ระบุข้อมูลไม่ถูกต้อง',
      DigitsOnly: 'กรุณาระบุตัวเลขเท่านั้น',
      MaxCharacters: 'กรอกได้ไม่เกิน 120 ตัวอักษร'
    },
    UpdateSuccess: 'อัปเดทสำเร็จ',
    FileSizeExceeded: 'ขนาดไฟล์เกิน 5 MB',
    InvalidFileType: 'ต้องเป็นไฟล์ในรูปแบบของ JPEG, PNG, JPG เท่านั้น',
    FileTooLarge: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 5 MB',
    AllowOnlyImage: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพที่สกุล jpeg/jpg/png เท่านั้น',
    Edit: 'แก้ไข',
    Cancel: 'ยกเลิก',
    Save: 'บันทึก',
    Close: 'ปิด',
    InvalidInfo: 'ข้อมูลไม่ถูกต้อง'
  },
  SocialMediaSettings: {
    Title: 'ตั้งค่าบัญชีโซเชียลมีเดีย',
    Facebook: 'Facebook',
    TikTok: 'TikTok',
    Youtube: 'Youtube',
    Instagram: 'Instagram',
    Line: 'Line',
    EnterLink: 'ระบุลิงก์',
    UpdateSuccess: 'อัปเดทสำเร็จ',
    UpdateFailed: 'อัปเดทไม่สำเร็จ',
    RequireAtLeastOne: 'กรุณากรอกอย่างน้อย 1 บัญชีโซเชียล',
    Edit: 'แก้ไข',
    Cancel: 'ยกเลิก',
    Save: 'บันทึก'
  },
  MenuBuyer: {
    titleUserProfile: 'ข้อมูลผู้ใช้',
    menuMyProfile: 'ข้อมูลของฉัน',
    menuBankAccount: 'บัญชีธนาคาร',
    menuShippingAddress: 'ที่อยู่ในการจัดส่งสินค้า',
    menuFavoriteProduct: 'รายการสินค้าที่ถูกใจ',
    menuCouponPoint: 'คูปองและคะแนนของฉัน',
    titleManageOrders: 'จัดการรายการสั่งซื้อ',
    menuMyOrders: 'รายการสั่งซื้อของฉัน',
    menuProfileRecord: 'ประวัติรายการสั่งซื้อของฉัน',
    menuReview: 'การประเมินความพึงพอใจ',
    titleAffiliate: 'โปรแกรม Affiliate',
    menuShopSeller: 'ข้อเสนอร้านค้า',
    menuProductAffiliate: 'ข้อเสนอสินค้า',
    menuEditPay: 'การชำระเงิน',
    menuEditSocial: 'บัญชีโซเชียลมีเดีย',
    menuMyShared: 'การแชร์ของฉัน',
    menuClick: 'รายงานการคลิก',
    menuOrderAffiliate: 'รายงานคำสั่งซื้อ',
    menuWithdraw: 'ประวัติการถอนเงิน',
    titleChat: 'แชท',
    menuMyChat: 'แชทของฉัน',
    titleManageSales: 'จัดการรายการสั่งซื้อ Sales Order',
    breadcrumbHome: 'หน้าแรก',
    FileExtensions: 'ไฟล์นามสกุล .JPEG, .PNG, .JPG'
  },
  PobuyerRecord: {
    title: 'ประวัติการสั่งซื้อสินค้า',
    search: 'ค้นหาชื่อสินค้าหรือชื่อร้านค้า',
    viewShop: 'ดูร้านค้า',
    orderDate: 'วันที่สั่งซื้อ',
    quantity: 'จำนวน',
    orderSummary: 'รวมรายการสั่งซื้อ',
    addCart: 'หยิบใส่รถเข็น',
    reorder: 'สั่งซื้ออีกครั้ง',
    discount: 'ส่วนลด',
    noOrder: 'ยังไม่มีการสั่งซื้อ',
    textAddCart: 'เพิ่มสินค้าลงในรถเข็นเรียบร้อย',
    textHaveProduct: 'มีสินค้ารายการนี้อยู่ในรถเข็นแล้ว',
    textIncomplete: 'ใส่ข้อมูลไม่ครบ',
    textInsert: 'กรุณาใส่จำนวนสินค้าอย่างน้อย 1 ชิ้น',
    textNotFoundAttribute: 'กรุณาเลือกตัวเลือกของสินค้าก่อน',
    textUnable: 'ไม่สามารถดำเนินการได้',
    textReAdd: 'มีสินค้ารายการนี้อยู่ในรถเข็นแล้วของคุณแล้ว และสินค้านี้มีการเปลี่ยนแปลง กรุณานำออกจากรถเข็นและทำการเพิ่มสินค้านี้ใหม่',
    textAddOnly: 'ตระกร้าสินค้าสามารถเพิ่มสูงสุดได้ 30 รายการเท่านั้น'
  },
  MyOrders: {
    title: 'รายการสั่งซื้อของฉัน',
    search: 'ค้นหาข้อมูลจากรหัสการสั่งซื้อและร้านค้า',
    orderStatus: 'สถานะสั่งซื้อ',
    statusAll: 'ทั้งหมด',
    statusSuccess: 'ชำระเงินสำเร็จ',
    statusCancel: 'ยกเลิกสินค้า',
    statusPending: 'รออนุมัติ',
    statusApprove: 'อนุมัติ',
    statusFail: 'ชำระเงินไม่สำเร็จ',
    statusNotPaid: 'ยังไม่ชำระเงิน',
    orderDate: 'วันที่สั่งซื้อ',
    formatDate: 'วว/ดด/ปปปป',
    btnCancel: 'ยกเลิก',
    btnConfirm: 'ตกลง',
    btnSave: 'บันทึก',
    shop: 'ร้านค้า',
    cancelOrder: 'ยกเลิกคำสั่งซื้อ',
    payment: 'ชำระเงิน',
    review: 'รีวิวสินค้า',
    received: 'ได้รับสินค้าแล้ว',
    reorder: 'สั่งซื้ออีกครั้ง',
    totalOrder: 'จำนวนสินค้าทั้งหมด',
    paymentSuccess: 'ชำระเงินสำเร็จ',
    pickedUp: 'พัสดุเข้าระบบ',
    shippingInprogress: 'กำลังจัดส่งสินค้า',
    delivered: 'จัดส่งสินค้าสำเร็จ',
    pendingPayment: 'รอชำระเงิน',
    generalProduct: 'สินค้าทั่วไป',
    serviceProduct: 'สินค้าบริการ',
    attribute: 'ตัวเลือก',
    quantity: 'จำนวน',
    price: 'ราคา',
    totalPrice: 'ราคารวมทั้งหมด',
    seeMore: 'ดูเพิ่มเติม',
    freeGift: 'สินค้าแถม',
    noOrderAt: 'คุณยังไม่มีรายการสั่งซื้อสินค้าที่',
    noOrder: 'คุณยังไม่มีรายการสั่งซื้อสินค้า',
    selectPayment: 'เลือกวิธีชำระเงิน',
    installmentTerm: 'ระยะเวลาผ่อนชำระ',
    selectTerm: 'เลือกระยะเวลาผ่อนชำระ',
    scanQR: 'สแกน QR Code ชำระเงิน',
    btnSaveImage: 'บันทึกรูปภาพ',
    paymentAmount: 'Payment Amount',
    referenceCode: 'รหัสอ้างอิง',
    stepMobile: 'สามารถชำระเงินได้ตามขั้นตอนนี้ (กรณีชำระเงินผ่านมือถือ)',
    capture: 'กดปุ่ม "บันทึกรูปภาพ" หรือแคปหน้าจอ',
    caseiOS: '* กรณี iOS',
    clickSave: 'ให้แคปหน้าจอ หรือกดค้างที่รูปภาพและกดปุ่ม',
    saveToApp: '"บันทึกไปยังแอปรูปภาพ"',
    selectMenuScan: 'เปิดแอพธนาคารของท่าน และเลือกเมนูสแกน QR Code',
    selectScreenshot: 'เลือกภาพหน้าจอเพื่อทำการสแกน QR Code',
    refundAccount: 'บัญชีการคืนเงิน',
    reasonCancel: 'เหตุผลในการยกเลิกคำสั่งซื้อ',
    reasonCancelPlaceholder: 'เหตุผลในการยกเลิกคำสั่งซื้อ',
    textAddAccount: 'คุณยังไม่มีข้อมูลบัญชี กรุณาเพิ่มบัญชีสำหรับการรับเงินคืน',
    accountName: 'ชื่อบัญชีธนาคาร',
    textEnterName: 'ระบุชื่อบัญชีธนาคาร',
    bankName: 'ชื่อธนาคาร',
    textEnterBank: 'ระบุธนาคาร',
    accountNumber: 'เลขบัญชีธนาคาร',
    textAccountNumber: 'ระบุเลขบัญชีธนาคาร',
    textcancel: 'คุณแน่ใจหรือไม่ว่าต้องการยกเลิก',
    textThisOrder: 'คำสั่งซื้อรายการนี้',
    cancelSuccess: 'ยกเลิกคำสั่งซื้อเรียบร้อย',
    textHaveCancel: 'คุณได้ทำการยกเลิกคำสั่งซื้อ',
    receivedRefund: '*จะได้รับเงินคืนเวลา 22.00 น.',
    byDate: 'ภายในวันที่ร้านค้าอนุมัติ',
    image: 'รูป',
    productName: 'ชื่อสินค้า',
    thCreateAt: 'วันที่ทำรายการ',
    thTransactionNumber: 'รหัสการสั่งซื้อ',
    thTransactionStatus: 'สถานะสั่งซื้อ',
    thReceipt: 'เลขที่ใบเสร็จ',
    thPaymentIcon: 'ใบเสนอราคา',
    thInvoice: 'ใบแจ้งหนี้',
    thTransactionCode: 'ใบกำกับภาษี',
    thPaidDate: 'วันที่สั่งซื้อ',
    thPayment: 'จ่ายเงิน',
    thShop: 'ร้านค้า',
    thTransportation: 'สถานะขนส่ง',
    thNotReceipt: 'ยังไม่รับ',
    thReceived: 'รับของแล้ว',
    UnableCancel: 'ไม่สามารถยกออร์เดอร์ได้',
    textError: 'ระบบขัดข้องไม่สามารถสั่งซื้อสินค้าอีกครั้งได้',
    textCopy: 'คัดลอกสำเร็จ',
    textConfirmReceipt: 'ยืนยันการตรวจสอบและได้รับสินค้าแล้ว',
    textPlsSelect: 'กรุณาเลือกวันที่สั่งซื้อสินค้า',
    textNotSend: 'สินค้ากำลังจัดส่ง',
    textSaveSuccess: 'บันทึกข้อมูลสำเร็จ',
    textNotFoundInvoice: 'ไม่พบเอกสารใบกำกับภาษี',
    textNotFree: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม ได้แก่ ',
    textContact: ' กรุณาติดต่อเจ้าหน้าที่',
    textNoDataProduct: 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว ได้แก่ ',
    textNotEnough: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการซื้อ ได้แก่ ',
    textUnablePayment: 'ไม่สามารถชำระเงินได้',
    textForGeneral: 'สำหรับผู้ซื้อทั่วไปเท่านั้น',
    textErrorPayment: 'ระบบ Payment มีปัญหา',
    textPaymentError: 'ERROR ไม่สามารถชำระเงินได้',
    Fail: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่',
    Paymentincomplete: 'การชำระเงินไม่เสร็จสมบูรณ์',
    textConfirmPay: 'คุณต้องการทำรายการนี้ ใช่ หรือ ไม่',
    ConfirmReceipt: 'ยืนยันการรับสินค้าแล้ว'
  },
  DetailOrderBuyer: {
    OrderDetails: 'รายละเอียดการสั่งซื้อสินค้า',
    CancelOrder: 'ยกเลิกคำสั่งซื้อ',
    TrackCancellationRequest: 'ติดตามคำขอยกเลิก',
    ItemReceived: 'ได้รับสินค้าแล้ว',
    ProductReview: 'รีวิวสินค้า',
    OrderAgain: 'สั่งซื้ออีกครั้ง',
    UploadSlip: 'อัปโหลดสลิป',
    OrderStatus: 'สถานะการสั่งซื้อ',
    OrderNumber: 'รหัสการสั่งซื้อ',
    NewOrder: 'คำสั่งซื้อใหม่',
    PaymentSuccessful: 'ชำระเงินแล้ว',
    EnterInformation: 'กรอกข้อมูลทั่วไป',
    PreparingForShipment: 'เตรียมจัดส่ง',
    OutForDelivery: 'กำลังจัดส่ง',
    DeliverySuccessful: 'จัดส่งสำเร็จ',
    OrdersWithCompletedPayment: 'คำสั่งซื้อที่ชำระเงินแล้ว',
    OrderHasBeenShipped: 'คำสั่งซื้อทำการจัดส่งแล้ว',
    BuyerReviewedAndRequestedReturn: 'ผู้ซื้อตรวจสอบและขอคืนสินค้า',
    WaitingForRefundApproval: 'รออนุมัติคืนเงิน',
    RefundSuccessful: 'คืนเงินสำเร็จ',
    WaitingForPickup: 'รอเข้ารับสินค้า',
    ReceivedSuccessfully: 'รับสำเร็จ',
    ToReceived: 'ที่ต้องได้รับ',
    DataNotFound: 'ไม่พบข้อมูล',
    ShippingMethod: 'รูปแบบการจัดส่ง',
    DocumentDetails: 'รายละเอียดเอกสาร',
    QuotationNumber: 'เลขที่ใบเสนอราคา',
    PurchaseOrderNumber: 'เลขที่ใบสั่งซื้อ (PO)',
    ReceiptNumber: 'เลขที่ใบเสร็จ',
    PRNumber: 'เลขที่ใบขอซื้อ',
    SONumber: 'เลขที่ Sale Order',
    PickupAddress: 'ที่อยู่ในการรับสินค้า',
    ShippingAddress: 'ที่อยู่ในการจัดส่งสินค้า',
    PickUpAtStore: 'รับสินค้าหน้าร้าน',
    Remarks: 'หมายเหตุ',
    TaxInvoiceDeliveryAddress: 'ที่อยู่ในการจัดส่งใบกำกับภาษี',
    OrderList: 'รายการสั่งซื้อสินค้า',
    GeneralProducts: 'สินค้าทั่วไป',
    ServiceProducts: 'สินค้า service',
    FreeGift: 'สินค้าแถม',
    Payment: 'การชำระเงิน',
    PriceExcludingVAT: 'ราคาไม่รวมภาษีมูลค่าเพิ่ม',
    VAT: 'ภาษีมูลค่าเพิ่ม',
    PriceIncludingVAT: 'ราคารวมภาษีมูลค่าเพิ่ม',
    StoreDiscountSummary: 'ส่วนลดคูปอง (ร้านค้า)',
    ShippingDiscountSummary: 'ส่วนลดคูปอง (ค่าส่ง)',
    PointsDiscountSummary: 'ส่วนลดแต้ม (ร้านค้า)',
    SystemDiscountSummary: 'ส่วนลดคูปอง (ระบบ)',
    SubTotal: 'ราคาหลังหักส่วนลด',
    ShippingFee: 'ค่าจัดส่ง',
    ShippingDiscountSeller: 'ส่วนลดค่าจัดส่ง (ร้านค้า)',
    ShippingDiscountNexgen: 'ส่วนลดค่าจัดส่ง (ระบบ)',
    TotalPriceAll: 'ราคารวมทั้งหมด',
    PendingPayment: 'รอการชำระเงิน',
    PaymentSuccessfully: 'ชำระเงินสำเร็จ',
    PaymentMethod: 'ช่องทางชำระเงิน',
    YourOrderHasBeenCancelled: 'คำสั่งซื้อสินค้าของคุณถูกยกเลิก',
    CancelledOrderDetails: 'รายละเอียดใบสั่งซื้อที่ถูกยกเลิก',
    Approver: 'ผู้อนุมัติ',
    Approve: 'อนุมัติ',
    NotApprove: 'ไม่อนุมัติ',
    WaitingForApproval: 'รออนุมัติ',
    ApprovalDate: 'วันที่อนุมัติ',
    YourPaymentWasUnsuccessfulPleaseCheckYourPaymentAnd: 'คุณชำระเงินไม่สำเร็จ กรุณาตรวจสอบการชำระเงินของคุณอีกครั้ง',
    Payment2: 'ชำระเงิน',
    YouHaveNotCompletedPayment: 'คุณยังไม่ได้ทำการชำระเงิน กรุณาชำระเงินผ่านบริการของ Thaidotcom Payment โดยสามารถชำระเงินได้ที่นี่',
    DiscountCoupon: 'โค้ดส่วนลด',
    ListOf: 'รายชื่อพนักงาน',
    Employees: 'รายการ',
    Department: 'แผนก',
    Company: 'บริษัท',
    PurchaseDate: 'วันที่สั่งซื้อ',
    PaymentDate: 'วันที่ชำระเงิน',
    PreparingShipmentDate: 'วันที่เตรียมส่ง',
    Date: 'วันที่',
    WaitingConfirmation: 'รอตรวจและกดรับสินค้า',
    ShippingDate: 'วันที่ทำการจัดส่ง',
    ReturnDate: 'วันที่คืนสินค้า',
    CancelDate: 'วันที่ยกเลิก',
    RefundDate: 'วันที่คืนเงิน',
    CashPayment: 'ชำระเงินสด',
    PickupDate: 'วันที่เข้ารับสินค้า',
    ReceivingDate: 'วันที่รับสินค้า',
    PRNumber2: 'เลขที่ใบขอซื้อ (PR)',
    RemarksToSeller: 'หมายเหตุถึงร้านค้า',
    Baht: 'บาท',
    refundAccount: 'บัญชีการคืนเงิน',
    reasonCancel: 'เหตุผลในการยกเลิกคำสั่งซื้อ',
    Cancel: 'ยกเลิก',
    Confirm: 'ตกลง',
    textAddAccount: 'คุณยังไม่มีข้อมูลบัญชี กรุณาเพิ่มบัญชีสำหรับการรับเงินคืน',
    accountName: 'ชื่อบัญชีธนาคาร',
    textEnterName: 'ระบุชื่อบัญชีธนาคาร',
    BankName: 'ชื่อธนาคาร',
    textEnterBank: 'ระบุธนาคาร',
    BankAccountNumber: 'เลขบัญชีธนาคาร',
    textAccountNumber: 'ระบุเลขบัญชีธนาคาร',
    BuyerName: 'ชื่อผู้ซื้อ',
    CancelOrderSuccess: 'คุณได้ยกเลิกคำสั่งซื้อสินค้าเรียบร้อยแล้ว',
    CreateAt: 'วันที่ทำรายการ',
    Status: 'สถานะ',
    SellerReason: 'เหตุผลของร้านค้า',
    PleaseEnterCancellationReason: 'กรุณาระบุเหตุผลยกเลิกคำสั่งซื้อ',
    ScanQRCodeForPayment: 'สแกน QR Code ชำระเงิน',
    btnSaveImage: 'บันทึกรูปภาพ',
    TotalPaymentAmount: 'ยอดชำระเงินจำนวน',
    referenceCode: 'รหัสอ้างอิง',
    CanMakePaymentByFollowingStep: 'สามารถชำระเงินได้ตามขั้นตอนนี้ (กรณีชำระเงินผ่านมือถือ)',
    capture: 'กดปุ่ม "บันทึกรูปภาพ" หรือแคปหน้าจอ',
    caseiOS: '* กรณี iOS',
    clickSave: 'ให้แคปหน้าจอ หรือกดค้างที่รูปภาพและกดปุ่ม',
    saveToApp: '"บันทึกไปยังแอปรูปภาพ"',
    selectMenuScan: 'เปิดแอพธนาคารของท่าน และเลือกเมนูสแกน QR Code',
    selectScreenshot: 'เลือกภาพหน้าจอเพื่อทำการสแกน QR Code',
    selectPayment: 'เลือกวิธีชำระเงิน',
    installmentTerm: 'ระยะเวลาผ่อนชำระ',
    selectTerm: 'เลือกระยะเวลาผ่อนชำระ',
    InstallmentPaymentNotAvailableBecause: 'ไม่สามารถผ่อนชำระได้ เนื่องจากไม่ถึง',
    Min: 'ขั้นต่ำ',
    Max: 'ไม่เกิน',
    RequiredAmountHasNotBeenMet: 'ที่กำหนดไว้',
    ComfirmModal: 'คุณต้องการทำรายการนี้ ใช่ หรือ ไม่',
    cancelOrder: 'ยกเลิกคำสั่งซื้อ',
    textcancel: 'คุณแน่ใจหรือไม่ว่าต้องการยกเลิก',
    textThisOrder: 'คำสั่งซื้อรายการนี้',
    cancelSuccess: 'ยกเลิกคำสั่งซื้อเรียบร้อย',
    textHaveCancel: 'คุณได้ทำการยกเลิกคำสั่งซื้อ',
    receivedRefund: '*จะได้รับเงินคืนเวลา 22.00 น.',
    byDate: 'ภายในวันที่ร้านค้าอนุมัติ',
    SelectImage: 'เลือกรูป',
    ChangeShippingAddress: 'เปลี่ยนที่อยู่ในการจัดส่งสินค้า',
    FileExtensions: '(ไฟล์นามสกุล .JPEG, .PNG, .JPG)',
    Edit: 'แก้ไข',
    Delete: 'ลบ',
    Notation: 'หมายเหตุ',
    NoAddress: 'ไม่มีที่อยู่ในการจัดส่งสินค้า',
    AddAddress: 'เพิ่มที่อยู่ใหม่',
    DeleteShippingAddress: 'ลบที่อยู่จัดส่ง',
    textDeleteShippingAddress: 'คุณต้องการทำรายการนี้ ใช่ หรือ ไม่',
    ProductDetails: 'รายละเอียดสินค้า',
    UnitPrice: 'ราคาต่อชิ้น',
    Quantity: 'จำนวน',
    TotalPrice: 'ราคารวม',
    Amount: 'Amount',
    validateAccountNumber1: 'กรุณากรอกเลขบัญชีธนาคาร',
    NoInstallment: 'ไม่ผ่อนชำระ',
    Month: 'เดือน',
    PricePerMonth: 'บาท/เดือน',
    ShippingAddressChanged: 'เปลี่ยนที่อยู่จัดส่งสำเร็จ',
    AddShippingAddress: 'เพิ่มที่อยู่ในการจัดส่งสินค้า',
    EditShippingAddress: 'แก้ไขที่อยู่ในการจัดส่งสินค้า',
    FailedToDeleteTheShippingAddress: 'ลบที่อยู่ในการจัดส่งสินค้าไม่สำเร็จ',
    AllowOnlyImage: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
    UploadSlipSuccess: 'อัปโหลดสลิปสำเร็จ',
    CanNotUploadSlip: 'ไม่สามารถอัปโหลดสลิปได้',
    SystemErrorUnableToPlaceTheOrderAgain: 'ระบบขัดข้องไม่สามารถสั่งซื้อสินค้าอีกครั้งได้',
    SomeItemsRequirementsForFreeGift: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม ได้แก่ ',
    PleaseContactCustomerSupport: ' กรุณาติดต่อเจ้าหน้าที่',
    SomeItemsAreNotEligibleForTheFreeGiftDueToInsufficientQuantity: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม ได้แก่',
    PleaseRemoveTheseItemsFromYourCart: 'กรุณาลบสินค้าออกจากรถเข็น',
    ShoppingCartNotFound: 'ไม่พบตะกร้า',
    SomeItemsAreNoLongerAvailableInTheSystem: 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว ได้แก่ ',
    PleaseRemoveThemFromYourCart: 'กรุณาลบสินค้าออกจากรถเข็น',
    SomeItemsAreNotAvailableInSufficientQuantityForPurchase: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการซื้อ ได้แก่ ',
    TheTotalPriceMustBeGreaterThanZerobaht: 'ราคารวมทั้งหมดต้องมีค่ามากกว่า 0 บาท',
    PleaseEnterDiscountThatDoesNotExceedTheProductPrice: 'กรุณากรอกส่วนลดไม่เกินราคา',
    UnableCancelTheOrder: 'ไม่สามารถยกออร์เดอร์ได้',
    PendingPayments: 'รอชำระเงิน',
    ItemDelivered: 'จัดส่งสินค้าสำเร็จ',
    SellerPreparingTheParcel: 'รอขนส่งเข้ารับพัสดุ',
    SellerPreparingTheParcel2: 'ผู้ส่งกำลังเตรียมพัสดุ',
    pickedUp: 'พัสดุเข้าระบบ',
    shippingInprogress: 'กำลังจัดส่งสินค้า'
  },
  reportOrderAffiliate: {
    PageTitle: 'รายงานการสั่งซื้อ',
    OrderID: 'รหัสการสั่งซื้อ',
    OrderDate: 'วันที่สั่งซื้อ',
    OrderStatus: 'สถานะสั่งซื้อ',
    SearchOrderDate: 'ค้นหาวันที่สั่งซื้อ',
    Cancel: 'ยกเลิก',
    Confirm: 'ตกลง',
    Clear: 'ล้างค่า',
    Search: 'ค้นหา',
    Total: 'ยอดรวม',
    Items: 'รายการ',
    NoData: 'ไม่พบรายงานการการสั่งซื้อ',
    View: 'ดู',
    NoOrders: 'คุณยังไม่มีรายงานการสั่งซื้อ',
    OrderDetailTitle: 'รายละเอียดคำสั่งซื้อ',
    ProductName: 'ชื่อสินค้า',
    ProductType: 'ชนิดสินค้า',
    ProductAttribute: 'ลักษณะสินค้า',
    ProductPrice: 'ราคาสินค้า',
    Quantity: 'จำนวน(ชิ้น)',
    CommissionPercent: 'คอมมิชชันจากสินค้า(%)',
    CommissionBaht: 'คอมมิชชันจากสินค้า(บาท)',
    CommissionValue: 'ค่าคอมมิชชัน(บาท)',
    TaxDeducted: 'หักภาษี(บาท)',
    NetCommission: 'ค่าคอมมิชชันที่ได้(บาท)',
    OrderDateTitle: 'วันที่สั่งซื้อสินค้า',
    TotalCommission: 'ค่าคอมมิชชันทั้งหมด (บาท)',
    Detail: 'รายละเอียด',
    All: 'ทั้งหมด',
    Success: 'ชำระเงินสำเร็จ',
    NotPaid: 'ยังไม่ชำระเงิน',
    CancelStatus: 'ยกเลิกสินค้า',
    Fail: 'ชำระเงินไม่สำเร็จ',
    Pending: 'รออนุมัติ',
    Approve: 'วางบิล',
    Credit: 'ชำระเงินแบบเครดิตเทอม',
    DataIncomplete: 'ข้อมูลไม่สมบูรณ์',
    Refund: 'คืนสินค้า'
  },
  DashboardAffiliate: {
    titleWeb: 'แดชบอร์ด',
    titleMobile: 'แดชบอร์ด Affiliate',
    yearly: 'รายปี',
    monthly: 'รายเดือน',
    daily: 'รายวัน',
    year: 'ปี',
    month: 'เดือน',
    day: 'วัน',
    display: 'แสดงผล',
    titleSelectMonth: 'เลือกเดือน',
    formatDate: 'วว/ดด/ปปปป',
    btnCancel: 'ยกเลิก',
    btnConfirm: 'ตกลง',
    btnSave: 'ยืนยัน',
    filter: 'ตัวกรอง',
    clsValue: 'ล้างค่า',
    selectShop: 'เลือกร้านค้า',
    allLinks: 'รายการลิงก์ทั้งหมด',
    incomeInfo: 'ข้อมูลรายได้',
    commission: 'รายการค่าคอมมิชชันแต่ละลิงค์',
    topClick: 'Top 10 สินค้าที่มีคนกดลิงค์มากที่สุด',
    topSeller: 'TOP 10 สินค้าที่ขายได้มากที่สุด',
    clickInfo: 'ข้อมูลการคลิก',
    incomeGraph: 'กราฟแสดงรายได้',
    theCommission: 'ค่าคอมมิชชัน',
    clickGraph: 'กราฟแสดงการคลิก',
    clicking: 'การคลิก',
    totalCommission: 'ค่าคอมมิชชันทั้งหมด',
    unit: 'บาท',
    totalClicks: 'จำนวนการคลิกทั้งหมด',
    unitClick: 'ครั้ง',
    unitOrder: 'รายการ',
    noClick: 'ไม่มีรายการการกด',
    numberClicks: 'จำนวนการกด',
    totalPeriods: 'จำนวนรวมทั้งหมดของทุกช่วงเวลา',
    noPurchase: 'ไม่มีรายการการซื้อสินค้า',
    commissionFee: 'ค่าคอมมิชชัน',
    items: 'ชิ้น',
    productList: 'รายการสินค้า',
    AllProductsList: 'รายการสินค้าทั้งหมด',
    productType: 'ชนิดสินค้า',
    attribute: 'ลักษณะสินค้า',
    price: 'ราคาสินค้า',
    commissionIncome: 'ค่าคอมมิชชันที่ได้',
    orderDetail: 'รายละเอียดคำสั่งซื้อ',
    productName: 'ชื่อสินค้า',
    quantity: 'จำนวน',
    taxDeduction: 'หักภาษี',
    Jan: 'มกราคม',
    Feb: 'กุมภาพันธ์',
    Mar: 'มีนาคม',
    Apr: 'เมษายน',
    May: 'พฤษภาคม',
    Jun: 'มิถุนายน',
    Jul: 'กรกฎาคม',
    Aug: 'สิงหาคม',
    Sep: 'กันยายน',
    Oct: 'ตุลาคม',
    Nov: 'พฤศจิกายน',
    Dec: 'ธันวาคม',
    link: 'ลิงก์',
    status: 'สถานะ',
    thPrice: 'ราคาสินค้า (บาท)',
    thComProduct: 'คอมมิชชันจากสินค้า',
    thComPrice: 'ค่าคอมมิชชัน (บาท)',
    attributeList: 'รายละเอียด',
    quantitySold: 'จำนวนที่ขายได้ (ชิ้น)',
    influPayment: 'ค่าคอมมิชชันที่ได้ (บาท)',
    textSeries: 'ค่า commission ที่ได้',
    textSeriesClick: 'จำนวนคลิก',
    allShop: 'ทั้งหมด',
    typeLanguage: 'th-TH',
    linkStatusAC: 'เปิดใช้งาน',
    linkStatusIn: 'ไม่เปิดใช้งาน',
    excelCommission: 'ตารางค่าคอมิชชัน',
    excelTopProducts: 'Top 10 สินค้า',
    textLinkCopy: 'คัดลอกลิงก์สำเร็จ'
  },
  ProductCard: {
    AlreadySold: 'ขายแล้ว',
    Sold: 'ชิ้น',
    OutOfStock: 'สินค้าหมด',
    ContactSupport: 'ติดต่อสอบถามเจ้าหน้าที่'
  },
  ModalMenu: {
    titleSeller: 'บริษัทของฉัน',
    textSearchSeller: 'ค้นหาจากชื่อบริษัท',
    unitOrder: 'รายการ',
    addCompany: 'เพิ่มบริษัท',
    noData: 'ไม่มีรายชื่อบริษัทของฉันในตาราง',
    noResults: 'ไม่พบรายชื่อบริษัทในตาราง',
    numberRows: 'จำนวนแถว',
    selectCompany: 'เลือกบริษัท',
    shopList: 'รายชื่อร้านค้า',
    myShop: 'ร้านค้าของฉัน',
    textSearchName: 'ค้นหาจากชื่อร้านค้า',
    noDataShop: 'ไม่มีรายชื่อร้านค้าของฉันในตาราง',
    noResultsShop: 'ไม่พบรายชื่อร้านค้าในตาราง',
    selectShop: 'เลือกร้านค้า',
    positionCompany: 'ข้อมูลตำแหน่งของบริษัท',
    myLocation: 'ตำแหน่งของบริษัทของฉัน',
    noDataLocation: 'ไม่มีรายชื่อตำแหน่งในตาราง',
    noResultsLocation: 'ไม่พบรายชื่อตำแหน่งในตาราง',
    list: 'รายชื่อ',
    selectPosition: 'เลือกตำแหน่ง',
    listBusiness: 'รายชื่อนิติบุคคล',
    myBusiness: 'นิติบุคคลของฉัน',
    regisBusiness: 'ลงทะเบียนนิติบุคคล',
    noDataCor: 'ไม่มีรายชื่อนิติบุคคลของฉันในตาราง',
    noResultsCor: 'ไม่พบรายชื่อนิติบุคคลในตาราง',
    selectCor: 'เลือกนิติบุคคล',
    textSearchCor: 'ค้นหาจากชื่อนิติบุคคล',
    saleOrderFormat: 'รูปแบบ Sale Order',
    orderForCus: '* การสั่งซื้อให้ Customer',
    orderInet: '* การสั่งซื้อในองค์กร INET',
    general: 'ทั่วไป',
    externalCus: '* การสั่งซื้อให้ลูกค้านอกระบบ บริษัท หรือ บุคคล',
    listPartner: 'รายชื่อคู่ค้า',
    myPartners: 'คู่ค้าของฉัน',
    textSearchPartners: 'ค้นหาจากชื่อบริษัทคู่ค้า',
    noDataPartner: 'ไม่มีรายชื่อคู่ค้าบริษัทของฉันในตาราง',
    noResultsPartner: 'ไม่พบรายชื่อคู่ค้าบริษัทในตาราง',
    selectPartner: 'เลือกคู่ค้า',
    no: 'ลำดับ',
    companyName: 'ชื่อบริษัท',
    shopName: 'ชื่อร้านค้า',
    partnerName: 'ชื่อคู่ค้า',
    nameCor: 'ชื่อนิติบุคคล',
    notPermission: 'ไม่มีสิทธิ์นี้ในระบบ',
    textContact: 'กรุณาติดต่อเจ้าหน้าที่',
    noID: 'ไม่มี Business ID',
    createCompany: 'กรุณาสร้างบริษัท',
    createComSuccess: 'สร้างบริษัทสำเร็จ',
    noDataYourCor: 'ไม่พบข้อมูลนิติบุคคลของคุณ',
    role: 'ตำแหน่ง : ',
    company: '   บริษัท : ',
    plsLogin: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง',
    channelNot: 'ไม่พบ Channel โปรดเช็คแล้วลองใหม่อีกครั้ง',
    shopNot: 'ไม่พบร้านค้า โปรดเช็คแล้วลองใหม่อีกครั้ง',
    employNot: 'ไม่พบข้อมูลการเป็นพนักงาน โปรดตรวจสอบและลองใหม่อีกครั้ง',
    shopNo: 'ไม่พบร้านค้า',
    noAccess: 'มีการเปลี่ยนแปลงตำแหน่ง หรือเปลี่ยนแปลงสิทธิ์การใช้งาน กรุณาลองใหม่อีกครั้ง',
    noBus: 'ไม่พบข้อมูลนิติบุคคลของคุณ',
    noRegis: 'คุณยังไม่ได้ลงทะเบียนบัญชีนิติบุคคลของตัวต้องการสมัครหรือไม่',
    regis: 'ลงทะเบียน',
    myPurchaser: 'บริษัทของฉัน',
    selectPurchaser: 'เลือกบริษัท',
    textSearchPurchaser: 'ค้นหาจากชื่อบริษัท'
  },
  ProductCardFlashSale: {
    Sold: 'ขายแล้ว'
  },
  GroupShopHomepage: {
    TitleAllShops: 'ร้านค้าทั้งหมด',
    TitleShop: 'ร้าน',
    NoProduct: 'ไม่มีสินค้า',
    SearchByShopName: 'ค้นหาจากชื่อร้านค้า',
    Province: 'จังหวัด',
    Category: 'หมวดหมู่',
    AllFound: 'ค้นพบทั้งหมด',
    NoShops: 'ไม่มีร้านค้า',
    NoItemsInGroup: 'กลุ่มร้านค้านี้ยังไม่มีรายการ',
    ShopNotFound: 'ไม่พบร้านค้า',
    ShopSearchNotFound: 'ไม่พบร้านค้าที่คุณค้นหา กรุณาตรวจสอบอีกครั้ง',
    Home: 'หน้าแรก',
    ShopType: 'ประเภทร้านค้า',
    Product: 'สินค้า',
    Shop: 'ร้านค้า',
    All: 'ทั้งหมด',
    CopyLinkSuccess: 'คัดลอกลิงก์สำเร็จ',
    CheckName: {
      Save: 'บันทึก',
      Name: 'ชื่อ',
      EnterName: 'ระบุชื่อ',
      LastName: 'นามสกุล',
      EnterLastName: 'ระบุนามสกุล',
      Phone: 'หมายเลขโทรศัพท์',
      EnterPhone: 'เบอร์โทรศัพท์',
      SaveData: 'บันทึกข้อมูล',
      YouHaveMadeChanges: 'คุณได้ทำการแก้ไขข้อมูลส่วนตัว'
    }
  }
}
