<template>
  <v-container class="" style="max-width: 100% !important;">
    <v-row v-if="!MobileSize" class="mb-6">
      <!-- Website -->
      <v-col cols="12" md="3" sm="4" xs="12" v-if="!MobileSize && !IpadSize && !IpadProSize" class="mb-6">
        <v-card class="mt-6" style="border-radius: 8px; border: 1px solid #F2F2F2;" elevation="0">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-storefront</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="white-space: normal; font-weight: bold; line-height: 30px;">กลับไปหน้าการซื้อขาย</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <!-- Dashboard Admin -->
            <v-list-group
              v-for="item in MenuAdminPanit"
              :key="item.title"
              v-model="DashboardActive"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectDashboard"
                :mandatory="defaultSelectDashboard !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  color="#27AB9C"
                  link
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <!-- <v-list-item-content v-if="child.key === 12" @click="openChat()">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content> -->
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- dashboard transport -->
            <v-list-group
              v-for="item in MenuDashboardTransport"
              :key="item.title"
              v-model="DashboardTransport"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectDashboardTransport"
                :mandatory="defaultSelectDashboardTransport !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Manage Admin User -->
            <v-list-group
              v-for="item in MenuManageUser"
              :key="item.title"
              v-model="ManageUser"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageUser"
                :mandatory="defaultSelectManageUser !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Manage Shop And Company -->
            <v-list-group
              v-for="item in MenuManageShopAndCompany"
              :key="item.title"
              v-model="ManageShopAndCompany"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageShopAndCompany"
                :mandatory="defaultSelectManageShopAndCompany !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Manage Shipping Admin -->
            <v-list-group
              v-for="item in MenuManageShipping"
              :key="item.title"
              v-model="ManageShipping"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageShipping"
                :mandatory="defaultSelectManageShipping !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Manage Landing Page -->
            <v-list-group
              v-for="item in MenuManageLandingPage"
              :key="item.title"
              v-model="ManageLandingPage"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectLandingPage"
                :mandatory="defaultSelectLandingPage !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Chat Admin -->
            <v-list-group
              v-for="item in MenuChatAdmin"
              :key="item.title"
              v-model="ChatAdmin"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectChatAdmin"
                :mandatory="defaultSelectChatAdmin !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="openChat()">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Admin Order -->
            <v-list-group
              v-for="item in MenuAdminOrder"
              :key="item.title"
              v-model="AdminOrder"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageOrder"
                :mandatory="defaultSelectManageOrder !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Admin Promotion -->
            <v-list-group
              v-for="item in MenuPromotion"
              :key="item.title"
              v-model="AdminPromotion"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManagePromotion"
                :mandatory="defaultSelectManagePromotion !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Admin Affiliate -->
            <v-list-group
              v-for="item in MenuAdminAffiliate"
              :key="item.title"
              v-model="AdminAffiliate"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageAffiliate"
                :mandatory="defaultSelectManageAffiliate !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Admin ERP -->
            <v-list-group
              v-for="item in MenuAdminERP"
              :key="item.title"
              v-model="AdminERP"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageERP"
                :mandatory="defaultSelectManageERP !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Software Marketplace -->
            <v-list-group
              v-for="item in MenuAdminMarketplacePartner"
              :key="item.title"
              v-model="AdminMarketplacePartner"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageMarketplacePartner"
                :mandatory="defaultSelectManageMarketplacePartner !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Reach -->
            <v-list-group
              v-for="item in MenuAdminReach"
              :key="item.title"
              v-model="AdminReach"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageReach"
                :mandatory="defaultSelectManageReach !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
          </v-list>
        </v-card>
      </v-col>
      <!-- IPAD PRO -->
      <v-col cols="12" md="3" sm="4" xs="12" v-else-if="!MobileSize && !IpadSize && IpadProSize" class="mb-6">
        <v-card class="mt-6" style="border-radius: 8px; border: 1px solid #F2F2F2;" elevation="0">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-storefront</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="white-space: normal; font-weight: bold; line-height: 30px;">กลับไปหน้าการซื้อขาย</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <!-- Dashboard Admin -->
            <v-list-group
              v-for="item in MenuAdminPanit"
              :key="item.title"
              v-model="DashboardActive"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectDashboard"
                :mandatory="defaultSelectDashboard !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  color="#27AB9C"
                  link
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <!-- <v-list-item-content v-if="child.key === 12" @click="openChat()">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content> -->
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- dashboard transport -->
            <v-list-group
              v-for="item in MenuDashboardTransport"
              :key="item.title"
              v-model="DashboardTransport"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectDashboardTransport"
                :mandatory="defaultSelectDashboardTransport !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Manage Admin User -->
            <v-list-group
              v-for="item in MenuManageUser"
              :key="item.title"
              v-model="ManageUser"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageUser"
                :mandatory="defaultSelectManageUser !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Manage Shop And Company -->
            <v-list-group
              v-for="item in MenuManageShopAndCompany"
              :key="item.title"
              v-model="ManageShopAndCompany"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageShopAndCompany"
                :mandatory="defaultSelectManageShopAndCompany !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Manage Shipping Admin -->
            <v-list-group
              v-for="item in MenuManageShipping"
              :key="item.title"
              v-model="ManageShipping"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageShipping"
                :mandatory="defaultSelectManageShipping !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Manage Landing Page -->
            <v-list-group
              v-for="item in MenuManageLandingPage"
              :key="item.title"
              v-model="ManageLandingPage"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectLandingPage"
                :mandatory="defaultSelectLandingPage !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Chat Admin -->
            <v-list-group
              v-for="item in MenuChatAdmin"
              :key="item.title"
              v-model="ChatAdmin"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectChatAdmin"
                :mandatory="defaultSelectChatAdmin !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="openChat()">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Admin Order -->
            <v-list-group
              v-for="item in MenuAdminOrder"
              :key="item.title"
              v-model="AdminOrder"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageOrder"
                :mandatory="defaultSelectManageOrder !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Admin Promotion -->
            <v-list-group
              v-for="item in MenuPromotion"
              :key="item.title"
              v-model="AdminPromotion"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManagePromotion"
                :mandatory="defaultSelectManagePromotion !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Admin Affiliate -->
            <v-list-group
              v-for="item in MenuAdminAffiliate"
              :key="item.title"
              v-model="AdminAffiliate"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageAffiliate"
                :mandatory="defaultSelectManageAffiliate !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Admin ERP -->
            <v-list-group
              v-for="item in MenuAdminERP"
              :key="item.title"
              v-model="AdminERP"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageERP"
                :mandatory="defaultSelectManageERP !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Software Marketplace -->
            <v-list-group
              v-for="item in MenuAdminMarketplacePartner"
              :key="item.title"
              v-model="AdminMarketplacePartner"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageMarketplacePartner"
                :mandatory="defaultSelectManageMarketplacePartner !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Reach -->
            <v-list-group
              v-for="item in MenuAdminReach"
              :key="item.title"
              v-model="AdminReach"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageReach"
                :mandatory="defaultSelectManageReach !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
          </v-list>
        </v-card>
      </v-col>
      <!-- IPAD -->
      <v-col cols="12" md="3" sm="4" xs="12" v-else-if="!MobileSize && IpadSize && !IpadProSize" class="mb-6">
        <v-card class="mt-6" style="border-radius: 8px; border: 1px solid #F2F2F2;" elevation="0">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-storefront</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="white-space: normal; font-weight: bold; line-height: 30px;">กลับไปหน้าการซื้อขาย</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <!-- Dashboard Admin -->
            <v-list-group
              v-for="item in MenuAdminPanit"
              :key="item.title"
              v-model="DashboardActive"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectDashboard"
                :mandatory="defaultSelectDashboard !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  color="#27AB9C"
                  link
                  dense
                  class="pl-10"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <!-- <v-list-item-content v-if="child.key === 12" @click="openChat()">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content> -->
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- dashboard transport -->
            <v-list-group
              v-for="item in MenuDashboardTransport"
              :key="item.title"
              v-model="DashboardTransport"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectDashboardTransport"
                :mandatory="defaultSelectDashboardTransport !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Manage Admin User -->
            <v-list-group
              v-for="item in MenuManageUser"
              :key="item.title"
              v-model="ManageUser"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageUser"
                :mandatory="defaultSelectManageUser !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-10"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Manage Shop And Company -->
            <v-list-group
              v-for="item in MenuManageShopAndCompany"
              :key="item.title"
              v-model="ManageShopAndCompany"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageShopAndCompany"
                :mandatory="defaultSelectManageShopAndCompany !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-10"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Manage Shipping Admin -->
            <v-list-group
              v-for="item in MenuManageShipping"
              :key="item.title"
              v-model="ManageShipping"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageShipping"
                :mandatory="defaultSelectManageShipping !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Manage Landing Page -->
            <v-list-group
              v-for="item in MenuManageLandingPage"
              :key="item.title"
              v-model="ManageLandingPage"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectLandingPage"
                :mandatory="defaultSelectLandingPage !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-10"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Chat Admin -->
            <v-list-group
              v-for="item in MenuChatAdmin"
              :key="item.title"
              v-model="ChatAdmin"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectChatAdmin"
                :mandatory="defaultSelectChatAdmin !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-10"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="openChat()">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Admin Order -->
            <v-list-group
              v-for="item in MenuAdminOrder"
              :key="item.title"
              v-model="AdminOrder"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageOrder"
                :mandatory="defaultSelectManageOrder !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-10"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Admin Promotion -->
            <v-list-group
              v-for="item in MenuPromotion"
              :key="item.title"
              v-model="AdminPromotion"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManagePromotion"
                :mandatory="defaultSelectManagePromotion !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-10"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Admin Affiliate -->
            <v-list-group
              v-for="item in MenuAdminAffiliate"
              :key="item.title"
              v-model="AdminAffiliate"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageAffiliate"
                :mandatory="defaultSelectManageAffiliate !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-10"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Admin ERP -->
            <v-list-group
              v-for="item in MenuAdminERP"
              :key="item.title"
              v-model="AdminERP"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageERP"
                :mandatory="defaultSelectManageERP !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-10"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Software Marketplace -->
            <v-list-group
              v-for="item in MenuAdminMarketplacePartner"
              :key="item.title"
              v-model="AdminMarketplacePartner"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageMarketplacePartner"
                :mandatory="defaultSelectManageMarketplacePartner !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- Reach -->
            <v-list-group
              v-for="item in MenuAdminReach"
              :key="item.title"
              v-model="AdminReach"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageReach"
                :mandatory="defaultSelectManageReach !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
          </v-list>
        </v-card>
      </v-col>
      <!-- </v-navigation-drawer> -->
      <v-col cols="12" md="9" sm="8" xs="12" class="pl-0 pr-0 mb-6">
        <v-main style="padding: 0px;">
          <v-container>
            <v-card max-height="100%" height="100%" width="100%" class="mt-3" elevation="0" style="border-radius: 8px; border: 1px solid #F2F2F2;">
              <router-view></router-view>
            </v-card>
          </v-container>
        </v-main>
      </v-col>
    </v-row>
    <!-- ซ่อนลิงก์ไว้ -->
    <a :href="chatLinkAdmin" id="chatLink" target="_blank" style="display: none;"></a>
  </v-container>
</template>

<script>
import { createChat } from '@/components/library/CallChatMe/callChatMe'
export default {
  // components: {
  //   Footer: () => import('@/components/Home/Footer')
  // },
  data () {
    return {
      chatLinkAdmin: '',
      defaultSelectDashboard: 0,
      defaultSelectManageShopAndCompany: null,
      defaultSelectManageShipping: null,
      defaultSelectDashboardTransport: null,
      defaultSelectManageUser: null,
      defaultSelectChatAdmin: null,
      defaultSelectLandingPage: null,
      defaultSelectManageOrder: null,
      defaultSelectManagePromotion: null,
      defaultSelectManageAffiliate: null,
      defaultSelectManageERP: null,
      defaultSelectManageMarketplacePartner: null,
      defaultSelectManageReach: null,
      defaultSelectManagePayment: null,
      DashboardActive: true,
      DashboardTransport: false,
      ManageUser: false,
      ManageShopAndCompany: false,
      ManageShipping: false,
      ManageLandingPage: false,
      ChatAdmin: false,
      AdminOrder: false,
      AdminPromotion: false,
      AdminAffiliate: false,
      AdminERP: false,
      AdminMarketplacePartner: false,
      AdminReach: false,
      ManagePayment: false,
      MenuAdminPanit: [
        {
          key: 'sub1',
          action: 'mdi-chart-box',
          active: true,
          title: 'Dashboard Admin NGC',
          items: [
            { key: 0, title: 'แดชบอร์ดแอดมิน', path: '/dashboardShopAdmin', isDisabled: false },
            { key: 1, title: 'แดชบอร์ด JV', path: '/dashboardJV', isDisabled: false },
            { key: 2, title: 'แดชบอร์ด user active', path: '/dashboardUserActive', isDisabled: false },
            { key: 3, title: 'แดชบอร์ด คำที่ค้นหาภายในระบบ', path: '/DashboardAdminSearch', isDisabled: false },
            { key: 4, title: 'แดชบอร์ด OTOP', path: '/dashboardOTOP', isDisabled: false }
            // { key: 3, title: 'แดชบอร์ดขนส่ง', path: '/dashboardTransport', isDisabled: false }
          ]
        }
      ],
      MenuDashboardTransport: [
        {
          key: 'sub2',
          action: 'mdi-bookshelf',
          active: true,
          title: 'Dashboard ขนส่ง',
          items: [
            { key: 0, title: 'Operation Dashboard', path: '/dashboardTransport', isDisabled: false },
            { key: 1, title: 'Order stage คงค้าง', path: '/pendingOrderStage', isDisabled: false },
            { key: 2, title: 'ข้อมูลออเดอร์คงค้าง', path: '/pendingOrderInfo', isDisabled: false }
          ]
        }
      ],
      MenuManageUser: [
        {
          key: 'sub3',
          action: 'mdi-shield-account',
          active: false,
          title: 'จัดการผู้ใช้ NGC',
          items: [
            { key: 0, title: 'จัดการแอดมินของระบบ', path: '/adminPanitManage', isDisabled: false },
            { key: 1, title: 'รายชื่อผู้ใช้งานในระบบ', path: '/adminUserWeb', isDisabled: false },
            { key: 2, title: 'จัดการผู้ใช้งานร้านค้า', path: '/manageUserShop', isDisabled: false },
            { key: 3, title: 'จัดการผู้ใช้งานบริษัท', path: '/manageUserCompany', isDisabled: false }
          ]
        }
      ],
      MenuManageShopAndCompany: [
        {
          key: 'sub4',
          action: 'mdi-domain',
          active: false,
          title: 'จัดการร้านค้าและบริษัท',
          items: [
            { key: 0, title: 'จัดการร้านค้าของระบบ', path: '/adminShopManage', isDisabled: false },
            { key: 1, title: 'จัดการบริษัทของระบบ', path: '/adminBusinessManage', isDisabled: false },
            // { key: 7, title: 'จัดการนิติบุคคลของระบบ', path: '/BusinessManage', isDisabled: false },
            { key: 2, title: 'รายการ Stock สินค้าทั้งหมด', path: '/stockAdmin', isDisabled: false },
            { key: 3, title: 'หน้ารายงาน e-Tax ของระบบ', path: '/eTaxAdmin', isDisabled: false },
            { key: 4, title: 'ร้านค้าเปิด-ปิดในระบบ', path: '/dashboardStatusShop', isDisabled: false },
            { key: 5, title: 'ร้านค้าที่มีสินค้าในระบบ', path: '/DashboardProductShop', isDisabled: false },
            { key: 6, title: 'จัดการหมวดหมู่สินค้า', path: '/ManageCategory', isDisabled: false },
            { key: 7, title: 'รายชื่อร้านค้าที่สนใจเข้าร่วม', path: '/ManageRegisterShop', isDisabled: false },
            { key: 8, title: 'รายการลงทะเบียนคู่ค้า', path: '/ManageRegisterInfoPartner', isDisabled: false },
            { key: 9, title: 'รายการลงทะเบียนเปิดร้าน', path: '/ManageRegisterInfoShop', isDisabled: false }
          ]
        }
      ],
      MenuManageShipping: [
        {
          key: 'sub5',
          action: 'mdi-truck',
          active: false,
          title: 'จัดการขนส่งระบบ',
          items: [
            { key: 0, title: 'จัดการขนส่งระบบ', path: '/manageShipping', isDisabled: false },
            { key: 1, title: 'จัดการรายการขนส่งระบบ', path: '/manageOrderShipping', isDisabled: false },
            { key: 2, title: 'แก้ไขที่อยู่ขนส่ง', path: '/UpdateShippingAddress', isDisabled: false }
          ]
        }
      ],
      MenuManageLandingPage: [
        {
          key: 'sub6',
          action: 'mdi-monitor-dashboard',
          active: false,
          title: 'จัดการ Landing Page',
          items: [
            { key: 0, title: 'จัดการ Landing Page', path: '/adminBannerManage', isDadminBusinessManageisabled: false },
            { key: 1, title: 'จัดการ Flash Sale ร้านค้า', path: '/adminFlashsaleManage', isDisabled: false },
            { key: 2, title: 'จัดการประเภทร้านค้า', path: '/groupStore', isDisabled: false },
            { key: 3, title: 'จัดการลำดับร้านค้า', path: '/manageListStore', isDisabled: false },
            { key: 4, title: 'แบนเนอร์โฆษณาและข่าวสาร', path: '/AdminManagePopup', isDisabled: false },
            // { key: 5, title: 'ปรับแต่งประเภทร้านค้า', path: '/adminGroupShopBGManage', isDisabled: false }
            { key: 6, title: 'ปรับแต่ง Banner', path: '/customBanner', isDisabled: false }
          ]
        }
      ],
      MenuChatAdmin: [
        {
          key: 'sub7',
          action: 'mdi-forum-outline',
          active: false,
          title: 'Chat Admin NGC',
          items: [
            { key: 0, title: 'แชทแอดมิน', path: '', isDisabled: false }
          ]
        }
      ],
      MenuAdminOrder: [
        {
          key: 'sub8',
          action: 'mdi-cart-outline',
          active: false,
          title: 'รายการสั่งซื้อ',
          items: [
            { key: 0, title: 'Order JV', path: '/orderJV', isDisabled: false },
            { key: 1, title: 'Search Order', path: '/SearchOrder', isDisabled: false },
            { key: 2, title: 'สร้าง Order JV ERP', path: '/CreateOrderJVERP', isDisabled: false }
          ]
        }
      ],
      MenuPromotion: [
        {
          key: 'sub9',
          action: 'mdi-ticket-percent',
          active: false,
          title: 'จัดการโปรโมชัน',
          items: [
            { key: 0, title: 'จัดการคูปอง', path: '/adminManageCoupon', isDisabled: false }
            // { key: 1, title: 'จัดการแจ้งเตือน', path: '/adminManageNotification', isDisabled: false }
          ]
        }
      ],
      MenuAdminAffiliate: [
        {
          key: 'sub10',
          action: 'mdi mdi-chart-line',
          active: false,
          title: 'โปรแกรม Affiliate',
          items: [
            { key: 0, title: 'ผู้สมัครเข้าร่วม Affiliate', path: '/userJoinAffiliate', isDisabled: false },
            { key: 1, title: 'ร้านค้าเข้าร่วม Affiliate', path: '/sellerJoinAffiliate', isDisabled: false },
            { key: 2, title: 'ผู้สมัครเข้าร่วมร้านค้า Affiliate', path: '/userJoinSellerAffiliate', isDisabled: false },
            { key: 3, title: 'รายงานค่าคอมมิชชัน Affiliate', path: '/reportCommissionAffiliateAdmin', isDisabled: false }
          ]
        }
      ],
      MenuAdminERP: [
        {
          key: 'sub11',
          action: 'mdi-cog',
          active: false,
          title: 'ERP',
          items: [
            { key: 0, title: 'จัดการ ERP', path: '/ManageERP', isDisabled: false }
          ]
        }
      ],
      MenuAdminMarketplacePartner: [
        {
          key: 'sub12',
          action: 'mdi-handshake',
          active: false,
          title: 'Software Marketplace',
          items: [
            { key: 0, title: 'สินค้าบริการของ Partner', path: '/AdminManageProductPartner', isDisabled: false },
            { key: 1, title: 'ร้านค้าที่เชื่อมต่อ Partner', path: '/AdminManageShopPartner', isDisabled: false },
            { key: 2, title: 'จัดการ Partner', path: '/managePositionBussiness', isDisabled: false },
            { key: 3, title: 'จัดการคู่มือการใช้งานและสัมมนา', path: '/AdminAddUserManualAndSeminarLink', isDisabled: false }
          ]
        }
      ],
      MenuAdminReach: [
        {
          key: 'sub13',
          action: 'mdi-thumb-up',
          active: false,
          title: 'Reach',
          items: [
            { key: 0, title: 'การเข้าชม', path: '/AdminReach', isDisabled: false }
          ]
        }
      ]
    }
  },
  async created () {
    // console.log('เข้า create in shop')
    // var dataUser = JSON.parse(Decode.decode(localStorage.getItem('DetailUser')))
    // console.log('dataUser', dataUser)
    // var responseID = this.$store.state.ModuleAdminManage.stateSellerShopID
    this.$EventBus.$emit('closeModalLogin')
    this.$EventBus.$emit('closeModalRegister')
    this.$EventBus.$emit('closeModalSuccess')
    this.$EventBus.$emit('LinkPage', 'admin')
    this.$EventBus.$emit('resetSearch')
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      // var shopId = ''
      // if (this.$router.currentRoute.path === '/AdminPanit' || this.$router.currentRoute.name === 'adminPanitManage') {
      //   if (localStorage.getItem('pathAdmin') !== null && localStorage.getItem('pathAdmin') !== 'BackToMobile') {
      //     var path = localStorage.getItem('pathAdmin')
      //     if (path === 'adminPanitManage') {
      //       this.$router.push({ path: '/adminPanitManage' }).catch(() => {})
      //     } else if (path === 'adminShopManage') {
      //       this.$router.push({ path: '/adminShopManage' }).catch(() => {})
      //     } else if (path === 'adminBusinessManage') {
      //       this.$router.push({ path: '/adminBusinessManage' }).catch(() => {})
      //     } else if (path === 'adminBannerManage') {
      //       this.$router.push({ path: '/adminBannerManage' }).catch(() => {})
      //     } else if (path === 'adminFlashsaleManage') {
      //       this.$router.push({ path: '/adminFlashsaleManage' }).catch(() => {})
      //     } else if (path === 'adminUserWeb') {
      //       this.$router.push({ path: '/adminUserWeb' }).catch(() => {})
      //     } else if (path === 'dashboardForAdmin') {
      //       this.$router.push({ path: '/dashboardForAdmin' }).catch(() => {})
      //     } else if (path === 'eTaxAdmin') {
      //       this.$router.push({ path: '/eTaxAdmin' }).catch(() => {})
      //     } else if (path === 'groupStore') {
      //       this.$router.push({ path: '/groupStore' }).catch(() => {})
      //     } else if (path === '/manageGroupStore') {
      //       var statusManagegroup = localStorage.getItem('status_managegroup')
      //       this.$router.push({ path: `/manageGroupStore/${statusManagegroup}` }).catch(() => {})
      //     } else if (path === 'stockAdmin') {
      //       this.$router.push({ path: '/stockAdmin' }).catch(() => {})
      //     } else if (path === 'orderJV') {
      //       this.$router.push({ path: '/orderJV' }).catch(() => {})
      //     } else if (path === 'SearchOrder') {
      //       this.$router.push({ path: '/SearchOrder' }).catch(() => {})
      //     } else if (path === 'CreateOrderJVERP') {
      //       this.$router.push({ path: '/CreateOrderJVERP' }).catch(() => {})
      //     } else if (path === 'manageCoupon') {
      //       this.$router.push({ path: '/manageCoupon' }).catch(() => {})
      //     } else if (path === 'userJoinAffiliate') {
      //       this.$router.push({ path: '/userJoinAffiliate' }).catch(() => {})
      //     } else if (path === 'dashboardAdmin') {
      //       this.$router.push({ path: '/AdminPanit' }).catch(() => {})
      //     } else if (path === 'sellerJoinAffiliate') {
      //       this.$router.push({ path: '/sellerJoinAffiliate' }).catch(() => {})
      //     } else if (path === 'sellerJoinAffiliateMobile') {
      //       this.$router.push({ path: '/sellerJoinAffiliateMobile' }).catch(() => {})
      //     } else if (path === 'userJoinSellerAffiliate') {
      //       this.$router.push({ path: '/userJoinSellerAffiliate' }).catch(() => {})
      //     } else if (path === 'userJoinSellerAffiliate') {
      //       this.$router.push({ path: '/userJoinSellerAffiliate' }).catch(() => {})
      //     } else if (path === 'reportCommissionAffiliateAdmin') {
      //       this.$router.push({ path: '/reportCommissionAffiliateAdmin' }).catch(() => {})
      //     } else if (path === 'reportCommissionAffiliateAdminMobile') {
      //       this.$router.push({ path: '/reportCommissionAffiliateAdminMobile' }).catch(() => {})
      //     } else if (path === 'listUserJoinSellerAffiliate') {
      //       this.$router.push({ path: '/userJoinSellerAffiliate' }).catch(() => {})
      //     } else if (path === 'listUserJoinSellerAffiliateMobile') {
      //       this.$router.push({ path: '/userJoinSellerAffiliateMobile' }).catch(() => {})
      //     } else if (path === 'dashboardUserActive') {
      //       this.$router.push({ path: '/dashboardUserActive' }).catch(() => {})
      //     } else if (path === 'dashboardStatusShop') {
      //       this.$router.push({ path: '/dashboardStatusShop' }).catch(() => {})
      //     } else if (path === 'DashboardProductShop') {
      //       this.$router.push({ path: '/DashboardProductShop' }).catch(() => {})
      //     } else if (path === 'reportCommissionAffiliateUserDetailAdmin') {
      //       this.$router.push({ path: '/reportCommissionAffiliateUserDetailAdmin' }).catch(() => {})
      //     } else if (path === 'reportCommissionAffiliateUserDetailAdminMobile') {
      //       this.$router.push({ path: '/reportCommissionAffiliateUserDetailAdminMobile' }).catch(() => {})
      //     } else if (path === 'reportCommissionAffiliateDetailAdminMobile') {
      //       this.$router.push({ path: '/reportCommissionAffiliateDetailAdminMobile' }).catch(() => {})
      //     } else if (path === 'listProductsAffiliate') {
      //       this.$router.push({ path: '/sellerJoinAffiliate' }).catch(() => {})
      //     } else if (path === 'listProductsAffiliateMobile') {
      //       this.$router.push({ path: '/sellerJoinAffiliateMobile' }).catch(() => {})
      //     } else if (path === 'ManageCategoryMobile') {
      //       this.$router.push({ path: '/ManageCategoryMobile' }).catch(() => {})
      //     } else if (path === 'reportCommissionAffiliateDetailAdmin') {
      //       this.$router.push({ path: '/reportCommissionAffiliateDetailAdmin' }).catch(() => {})
      //     } else if (path === 'ManageCategory') {
      //       this.$router.push({ path: '/ManageCategory' }).catch(() => {})
      //     } else if (path === 'manageUserShop') {
      //       this.$router.push({ path: '/manageUserShop' }).catch(() => {})
      //     } else if (path === 'manageListStore') {
      //       this.$router.push({ path: '/manageListStore' }).catch(() => {})
      //     } else if (path === 'manageUserList') {
      //     } else if (path === 'AdminManagePopup') {
      //       this.$router.push({ path: '/AdminManagePopup' }).catch(() => {})
      //     } else if (path === 'manageUserList') {
      //       var id = parseInt(localStorage.getItem('paramID'))
      //       var shopName = localStorage.getItem('shopName')
      //       this.$router.push({ path: `/manageUserList?seller_shop_id=${id}&shop_name=${shopName}` }).catch(() => {})
      //     } else if (path === 'ManageERP') {
      //       this.$router.push({ path: '/ManageERP' }).catch(() => {})
      //     } else if (path === 'manageUserCompany') {
      //       this.$router.push({ path: '/manageUserCompany' }).catch(() => {})
      //     } else if (path === 'ManageUserListCompany') {
      //       var companyId = parseInt(localStorage.getItem('paramID'))
      //       var name = localStorage.getItem('paramName')
      //       this.$router.push({ path: `/ManageUserListCompany?company_id=${companyId}&name=${name}` }).catch(() => {})
      //     } else if (path === 'dashboardTransport') {
      //       this.$router.push({ path: '/dashboardTransport' }).catch(() => {})
      //     } else if (path === 'pendingOrderStage') {
      //       this.$router.push({ path: '/pendingOrderStage' }).catch(() => {})
      //     } else if (path === 'pendingOrderStageMobile') {
      //       this.$router.push({ path: '/pendingOrderStageMobile' }).catch(() => {})
      //     } else if (path === 'pendingOrderInfo') {
      //       this.$router.push({ path: '/pendingOrderInfo' }).catch(() => {})
      //     } else if (path === 'pendingOrderInfoMobile') {
      //       this.$router.push({ path: '/pendingOrderInfoMobile' }).catch(() => {})
      //     } else if (path === 'manageOrderShipping') {
      //       this.$router.push({ path: '/manageOrderShipping' }).catch(() => {})
      //     } else if (path === 'manageOrderShippingMobile') {
      //       this.$router.push({ path: '/manageOrderShippingMobile' }).catch(() => {})
      //     } else if (path === 'AdminManageProductPartner') {
      //       var partnerCode = parseInt(localStorage.getItem('partnerCode'))
      //       this.$router.push({ path: `/AdminManageProductPartner?partnerCode=${partnerCode}` }).catch(() => {})
      //     } else if (path === 'AdminManageProductPartnerMobile') {
      //       var partnerCodeMobile = parseInt(localStorage.getItem('partnerCode'))
      //       this.$router.push({ path: `/AdminManageProductPartnerMobile?partnerCode=${partnerCodeMobile}` }).catch(() => {})
      //     } else if (path === 'AdminManageShopPartner') {
      //       this.$router.push({ path: '/AdminManageShopPartner' }).catch(() => {})
      //     } else if (path === 'AdminManageShopPartnerMobile') {
      //       this.$router.push({ path: '/AdminManageShopPartnerMobile' }).catch(() => {})
      //     } else if (path === 'DetailShopPartner') {
      //       this.$router.push({ path: '/AdminManageShopPartner' }).catch(() => {})
      //     } else if (path === 'DetailShopPartnerMobile') {
      //       this.$router.push({ path: '/AdminManageShopPartnerMobile' }).catch(() => {})
      //     } else if (path === 'adminManageCouponMobile') {
      //       this.$router.push({ path: '/adminManageCouponMobile' }).catch(() => {})
      //     } else if (path === 'adminManageNotificationMobile') {
      //       this.$router.push({ path: '/adminManageNotificationMobile' }).catch(() => {})
      //     } else if (path === 'freeShippingCouponMobile') {
      //       this.$router.push({ path: '/freeShippingCouponMobile' }).catch(() => {})
      //     } else if (path === 'AdminAddUserManualAndSeminarLink') {
      //       this.$router.push({ path: '/AdminAddUserManualAndSeminarLink' }).catch(() => {})
      //     } else if (path === 'AdminAddUserManualAndSeminarLinkMobile') {
      //       this.$router.push({ path: '/AdminAddUserManualAndSeminarLinkMobile' }).catch(() => {})
      //     } else if (path === `/editFreeShippingCoupon?status=edit&id=${id}`) {
      //       this.$router.push({ path: `/editFreeShippingCouponMobile?status=edit&id=${id}` }).catch(() => {})
      //     } else if (path === 'BusinessManage') {
      //       this.$router.push({ path: '/BusinessManage' }).catch(() => {})
      //     } else if (path === 'BusinessManageMobile') {
      //       this.$router.push({ path: '/BusinessManageMobile' }).catch(() => {})
      //     } else if (path === 'ManageRegisterShopDetail') {
      //       shopId = localStorage.setItem('shopIdParam')
      //       this.$router.push({ path: `/ManageRegisterShopDetail?shopId=${shopId}` }).catch(() => {})
      //     } else if (path === 'ManageRegisterShopDetailMobile') {
      //       shopId = localStorage.setItem('shopIdParam')
      //       this.$router.push({ path: `/ManageRegisterShopDetailMobile?shopId=${shopId}` }).catch(() => {})
      //     } else if (path === 'DashboardAdminSearch') {
      //       this.$router.push({ path: '/DashboardAdminSearch' }).catch(() => {})
      //     } else if (path === 'DashboardAdminSearchMobile') {
      //       this.$router.push({ path: '/DashboardAdminSearchMobile' }).catch(() => {})
      //     } else if (path === 'UpdateShippingAddress') {
      //       this.$router.push({ path: '/UpdateShippingAddress' }).catch(() => {})
      //     } else if (path === 'UpdateShippingAddressMobile') {
      //       this.$router.push({ path: '/UpdateShippingAddressMobile' }).catch(() => {})
      //     } else if (path === 'dashboardOTOP') {
      //       this.$router.push({ path: '/dashboardOTOP' }).catch(() => {})
      //     } else if (path === 'dashboardOTOPMobile') {
      //       this.$router.push({ path: '/dashboardOTOPMobile' }).catch(() => {})
      //     }
      //     await this.SelectPathAdmin()
      //   } else {
      //     this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
      //   }
      // } else {
      //   this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
      // }
    }
    // this.SelectPathAdmin()
  },
  beforeDestroy () {
    localStorage.removeItem('pathAdmin')
  },
  watch: {
    MobileSize (val) {
      // var responseID = this.$store.state.ModuleAdminManage.stateSellerShopID
      if (this.$router.currentRoute.name === 'dashboardAdminMobile' || this.$router.currentRoute.name === 'dashboardAdmin') {
        if (val === true) {
          this.$router.push({ path: '/dashboardAdminMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardAdmin' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'Transaction' || this.$router.currentRoute.name === 'TransactionMobile') {
        if (val === true) {
          this.$router.push({ path: '/TransactionMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/Transaction' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'dashboardAdminForGPMobile' || this.$router.currentRoute.name === 'dashboardAdminForGP') {
        if (val === true) {
          this.$router.push({ path: '/dashboardAdminForGPMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardAdminForGP' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'adminPanitManageMobile' || this.$router.currentRoute.name === 'adminPanitManage') {
        if (val === true) {
          this.$router.push({ path: '/adminPanitManageMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/adminPanitManage' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'adminShopManageMobile' || this.$router.currentRoute.name === 'adminShopManage') {
        if (val === true) {
          this.$router.push({ path: '/adminShopManageMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/adminShopManage' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'adminBusinessManageMobile' || this.$router.currentRoute.name === 'adminBusinessManage') {
        if (val === true) {
          this.$router.push({ path: '/adminBusinessManageMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/adminBusinessManage' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'adminUserWebMobile' || this.$router.currentRoute.name === 'adminUserWeb') {
        if (val === true) {
          this.$router.push({ path: '/adminUserWebMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/adminUserWeb' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'adminBannerManageMobile' || this.$router.currentRoute.name === 'adminBannerManage') {
        if (val === true) {
          this.$router.push({ path: '/adminBannerManageMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/adminBannerManage' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'adminFlashsaleManageMobile' || this.$router.currentRoute.name === 'adminFlashsaleManage') {
        if (val === true) {
          this.$router.push({ path: '/adminFlashsaleManageMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/adminFlashsaleManage' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'dashboardShopAdminMobile' || this.$router.currentRoute.name === 'dashboardShopAdmin') {
        if (val === true) {
          this.$router.push({ path: '/dashboardShopAdminMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'dashboardJVMobile' || this.$router.currentRoute.name === 'dashboardJV') {
        if (val === true) {
          this.$router.push({ path: '/dashboardJVMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardJV' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'eTaxAdminMobile' || this.$router.currentRoute.name === 'eTaxAdmin') {
        if (val === true) {
          this.$router.push({ path: '/eTaxAdminMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/eTaxAdmin' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'groupStoreMobile' || this.$router.currentRoute.name === 'groupStore') {
        if (val === true) {
          this.$router.push({ path: '/groupStoreMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/groupStore' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'manageGroupStoreMobile' || this.$router.currentRoute.name === 'manageGroupStore') {
        var statusManagegroup = localStorage.getItem('status_managegroup')
        if (val === true) {
          this.$router.push({ path: `/manageGroupStoreMobile/${statusManagegroup}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/manageGroupStore/${statusManagegroup}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'stockAdminMobile' || this.$router.currentRoute.name === 'stockAdmin') {
        if (val === true) {
          this.$router.push({ path: '/stockAdminMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/stockAdmin' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'SearchOrderMobile' || this.$router.currentRoute.name === 'SearchOrder') {
        if (val === true) {
          this.$router.push({ path: '/SearchOrderMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/SearchOrder' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'CreateOrderJVERPMobile' || this.$router.currentRoute.name === 'CreateOrderJVERP') {
        if (val === true) {
          this.$router.push({ path: '/CreateOrderJVERPMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/CreateOrderJVERP' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'orderJVMobile' || this.$router.currentRoute.name === 'orderJV') {
        if (val === true) {
          this.$router.push({ path: '/orderJVMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/orderJV' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'manageCouponMobile' || this.$router.currentRoute.name === 'manageCoupon') {
        if (val === true) {
          this.$router.push({ path: '/manageCouponMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageCoupon' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'userJoinAffiliateMobile' || this.$router.currentRoute.name === 'userJoinAffiliate') {
        if (val === true) {
          this.$router.push({ path: '/userJoinAffiliateMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/userJoinAffiliate' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'sellerJoinAffiliateMobile' || this.$router.currentRoute.name === 'sellerJoinAffiliate') {
        if (val === true) {
          this.$router.push({ path: '/sellerJoinAffiliateMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/sellerJoinAffiliate' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'userJoinSellerAffiliateMobile' || this.$router.currentRoute.name === 'userJoinSellerAffiliate') {
        if (val === true) {
          this.$router.push({ path: '/userJoinSellerAffiliateMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/userJoinSellerAffiliate' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'reportCommissionAffiliateAdminMobile' || this.$router.currentRoute.name === 'reportCommissionAffiliateAdmin') {
        if (val === true) {
          this.$router.push({ path: '/reportCommissionAffiliateAdminMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/reportCommissionAffiliateAdmin' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listUserJoinSellerAffiliateMobile' || this.$router.currentRoute.name === 'listUserJoinSellerAffiliate') {
        if (val === true) {
          this.$router.push({ path: '/userJoinSellerAffiliateMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/userJoinSellerAffiliate' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'dashboardUserActiveMobile' || this.$router.currentRoute.name === 'dashboardUserActive') {
        if (val === true) {
          this.$router.push({ path: '/dashboardUserActiveMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardUserActive' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'dashboardStatusShopMobile' || this.$router.currentRoute.name === 'dashboardStatusShop') {
        if (val === true) {
          this.$router.push({ path: '/dashboardStatusShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardStatusShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'DashboardProductShopMobile' || this.$router.currentRoute.name === 'DashboardProductShop') {
        if (val === true) {
          this.$router.push({ path: '/DashboardProductShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/DashboardProductShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'reportCommissionAffiliateUserDetailAdminMobile' || this.$router.currentRoute.name === 'reportCommissionAffiliateUserDetailAdmin') {
        if (val === true) {
          this.$router.push({ path: '/reportCommissionAffiliateUserDetailAdminMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/reportCommissionAffiliateUserDetailAdmin' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'reportCommissionAffiliateDetailAdminMobile' || this.$router.currentRoute.name === 'reportCommissionAffiliateDetailAdmin') {
        if (val === true) {
          this.$router.push({ path: '/reportCommissionAffiliateDetailAdminMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/reportCommissionAffiliateDetailAdmin' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listProductsAffiliateMobile' || this.$router.currentRoute.name === 'listProductsAffiliate') {
        if (val === true) {
          this.$router.push({ path: '/sellerJoinAffiliateMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/sellerJoinAffiliate' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageCategoryMobile' || this.$router.currentRoute.name === 'ManageCategory') {
        if (val === true) {
          this.$router.push({ path: '/ManageCategoryMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageCategory' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'manageUserShopMobile' || this.$router.currentRoute.name === 'manageUserShop') {
        if (val === true) {
          this.$router.push({ path: '/manageUserShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageUserShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'manageListStoreMobile' || this.$router.currentRoute.name === 'manageListStore') {
        if (val === true) {
          this.$router.push({ path: '/manageListStoreMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageListStore' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'AdminManagePopupMobile' || this.$router.currentRoute.name === 'AdminManagePopup') {
        if (val === true) {
          this.$router.push({ path: '/AdminManagePopupMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/AdminManagePopup' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'manageUserListMobile' || this.$router.currentRoute.name === 'manageUserList') {
        var id = localStorage.getItem('paramID')
        var shopName = localStorage.getItem('shopName')
        if (val === true) {
          this.$router.push({ path: `/manageUserListMobile?seller_shop_id=${id}&shop_name=${shopName}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/manageUserList?seller_shop_id=${id}&shop_name=${shopName}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageERPMobile' || this.$router.currentRoute.name === 'ManageERP') {
        if (val === true) {
          this.$router.push({ path: '/ManageERPMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageERP' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageUserListCompanyMobile' || this.$router.currentRoute.name === 'ManageUserListCompany') {
        var companyId = parseInt(localStorage.getItem('paramID'))
        var name = localStorage.getItem('paramName')
        if (val === true) {
          this.$router.push({ path: `/ManageUserListCompanyMobile?company_id=${companyId}&name=${name}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/ManageUserListCompany?company_id=${companyId}&name=${name}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'manageUserCompanyMobile' || this.$router.currentRoute.name === 'manageUserCompany') {
        if (val === true) {
          this.$router.push({ path: '/manageUserCompanyMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageUserCompany' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'dashboardTransportMobile' || this.$router.currentRoute.name === 'dashboardTransport') {
        if (val === true) {
          this.$router.push({ path: '/dashboardTransportMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardTransport' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'pendingOrderStageMobile' || this.$router.currentRoute.name === 'pendingOrderStage') {
        if (val === true) {
          this.$router.push({ path: '/pendingOrderStageMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/pendingOrderStage' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'pendingOrderInfoMobile' || this.$router.currentRoute.name === 'pendingOrderInfo') {
        if (val === true) {
          this.$router.push({ path: '/pendingOrderInfoMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/pendingOrderInfo' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'manageOrderShippingMobile' || this.$router.currentRoute.name === 'manageOrderShipping') {
        if (val === true) {
          this.$router.push({ path: '/manageOrderShippingMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageOrderShipping' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'AdminManageProductPartnerMobile' || this.$router.currentRoute.name === 'AdminManageProductPartner') {
        if (val === true) {
          this.$router.push({ path: 'AdminManageProductPartnerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: 'AdminManageProductPartner' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'DetailProductShopPartnerMobile' || this.$router.currentRoute.name === 'DetailProductShopPartner') {
        if (val === true) {
          var partnerCode = parseInt(localStorage.getItem('partnerCode'))
          this.$router.push({ path: `/DetailProductShopPartnerMobile?partnerCode=${partnerCode}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/DetailProductShopPartner?partnerCode=${partnerCode}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'AdminManageShopPartnerMobile' || this.$router.currentRoute.name === 'AdminManageShopPartner') {
        if (val === true) {
          this.$router.push({ path: '/AdminManageShopPartnerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/AdminManageShopPartner' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'DetailShopPartnerMobile' || this.$router.currentRoute.name === 'DetailShopPartner') {
        if (val === true) {
          this.$router.push({ path: '/AdminManageShopPartnerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/AdminManageShopPartner' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'adminManageCouponMobile' || this.$router.currentRoute.name === 'adminManageCoupon') {
        if (val === true) {
          this.$router.push({ path: '/adminManageCouponMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/adminManageCoupon' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'adminManageNotificationMobile' || this.$router.currentRoute.name === 'adminManageNotification') {
        if (val === true) {
          this.$router.push({ path: '/adminManageNotificationMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/adminManageNotification' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'freeShippingCouponMobile' || this.$router.currentRoute.name === 'freeShippingCoupon') {
        if (val === true) {
          this.$router.push({ path: '/freeShippingCouponMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/freeShippingCoupon' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'editDiscountCouponMobile' || this.$router.currentRoute.name === 'editDiscountCoupon') {
        id = this.$store.getters.CurrentEditCouponPlatformIdPath
        if (val === true) {
          this.$router.push({ path: `/editDiscountCouponMobile?id=${id}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/editDiscountCoupon?id=${id}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'discountCouponMobile' || this.$router.currentRoute.name === 'discountCoupon') {
        if (val === true) {
          this.$router.push({ path: `/discountCouponMobile?id=${id}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/discountCoupon?id=${id}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'editFreeShippingCouponMobile' || this.$router.currentRoute.name === 'editFreeShippingCoupon') {
        id = this.$store.getters.CurrentEditCouponPlatformIdPath
        if (val === true) {
          this.$router.push({ path: `/editFreeShippingCouponMobile?status=edit&id=${id}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/editFreeShippingCoupon?status=edit&id=${id}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'adminGroupShopBGManageMobile' || this.$router.currentRoute.name === 'adminGroupShopBGManage') {
        if (val === true) {
          this.$router.push({ path: '/adminGroupShopBGManageMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/adminGroupShopBGManage' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'AdminReachMobile' || this.$router.currentRoute.name === 'AdminReach') {
        if (val === true) {
          this.$router.push({ path: '/AdminReachMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/AdminReach' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'EditImagesFlashSaleMobile' || this.$router.currentRoute.name === 'EditImagesFlashSale') {
        if (val === true) {
          this.$router.push({ path: '/EditImagesFlashSaleMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/EditImagesFlashSale' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'managePositionBussinessMobile' || this.$router.currentRoute.name === 'managePositionBussiness') {
        if (val === true) {
          this.$router.push({ path: '/managePositionBussinessMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/managePositionBussiness' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'AdminAddUserManualAndSeminarLinkMobile' || this.$router.currentRoute.name === 'AdminAddUserManualAndSeminarLink') {
        if (val === true) {
          this.$router.push({ path: '/AdminAddUserManualAndSeminarLinkMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/AdminAddUserManualAndSeminarLink' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'customBannerMobile' || this.$router.currentRoute.name === 'customBanner') {
        if (val === true) {
          this.$router.push({ path: '/customBannerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/customBanner' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'BusinessManageMobile' || this.$router.currentRoute.name === 'BusinessManage') {
        if (val === true) {
          this.$router.push({ path: '/BusinessManageMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/BusinessManage' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageRegisterShopMobile' || this.$router.currentRoute.name === 'ManageRegisterShop') {
        if (val === true) {
          this.$router.push({ path: '/ManageRegisterShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageRegisterShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageRegisterShopDetailMobile' || this.$router.currentRoute.name === 'ManageRegisterShopDetail') {
        var shopId = localStorage.getItem('shopIdParam')
        if (val === true) {
          this.$router.push({ path: `/ManageRegisterShopDetailMobile?shopId=${shopId}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/ManageRegisterShopDetail?shopId=${shopId}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'AdminListOrderCancelMobile' || this.$router.currentRoute.name === 'AdminListOrderCancel') {
        if (val === true) {
          this.$router.push({ path: '/AdminListOrderCancelMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/AdminListOrderCancel' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'DashboardAdminSearchMobile' || this.$router.currentRoute.name === 'DashboardAdminSearch') {
        if (val === true) {
          this.$router.push({ path: '/DashboardAdminSearchMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/DashboardAdminSearch' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'UpdateShippingAddressMobile' || this.$router.currentRoute.name === 'UpdateShippingAddress') {
        if (val === true) {
          this.$router.push({ path: '/UpdateShippingAddressMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/UpdateShippingAddress' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'dashboardOTOPMobile' || this.$router.currentRoute.name === 'dashboardOTOP') {
        if (val === true) {
          this.$router.push({ path: '/dashboardOTOPMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardOTOP' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'recommentProductManageMobile' || this.$router.currentRoute.name === 'recommentProductManage') {
        if (val === true) {
          this.$router.push({ path: '/recommentProductManageMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/recommentProductManage' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'paymentManageMobile' || this.$router.currentRoute.name === 'paymentManage') {
        if (val === true) {
          this.$router.push({ path: '/paymentManageMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/paymentManage' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageRegisterInfoPartnerMobile' || this.$router.currentRoute.name === 'ManageRegisterInfoPartner') {
        if (val === true) {
          this.$router.push({ path: '/ManageRegisterInfoPartnerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageRegisterInfoPartner' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageRegisterInfoPartnerDetailMobile' || this.$router.currentRoute.name === 'ManageRegisterInfoPartnerDetail') {
        var businessIdPartner = localStorage.getItem('businessIdParam')
        if (val === true) {
          this.$router.push({ path: `/ManageRegisterInfoPartnerDetailMobile?businessId=${businessIdPartner}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/ManageRegisterInfoPartnerDetail?businessId=${businessIdPartner}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageRegisterInfoShopMobile' || this.$router.currentRoute.name === 'ManageRegisterInfoShop') {
        if (val === true) {
          this.$router.push({ path: '/ManageRegisterInfoShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageRegisterInfoShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageRegisterInfoShopDetailMobile' || this.$router.currentRoute.name === 'ManageRegisterInfoShopDetail') {
        var businessIdShop = localStorage.getItem('businessIdParam')
        if (val === true) {
          this.$router.push({ path: `/ManageRegisterInfoShopDetailMobile?businessId=${businessIdShop}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/ManageRegisterInfoShopDetail?businessId=${businessIdShop}` }).catch(() => {})
        }
      }
      // } else if (this.$router.currentRoute.name === 'dashboardOTOPMobile' || this.$router.currentRoute.name === 'dashboardOTOP') {
      //   if (val === true) {
      //     this.$router.push({ path: '/dashboardOTOPMobile' }).catch(() => {})
      //   } else {
      //     this.$router.push({ path: '/dashboardOTOP' }).catch(() => {})
      //   }
      // }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    this.SelectPathAdmin()
    this.$EventBus.$on('changeNavAdmin', this.SelectPathAdmin)
    this.$on('hook:beforeDestroy', () => {
      // this.$EventBus.$off('AuthorityUsers')
      this.$EventBus.$off('changeNavAdmin')
    })
  },
  methods: {
    async openChat () {
      const sharedToken = await createChat.sharetoken()
      // https://www.chatme.co.th/uat/chatmeservice/converseme/AuthPlugin?shared_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************&shop_name=ADMIN-NGC&shop_id=99
      const adminData = await 'https://www.chatme.co.th/chatmeservice/converseme/AuthPlugin?shared_token=' + sharedToken.data.shared_token + '&shop_name=ADMIN-NGC&shop_id=1'
      // console.log('adminData', adminData)
      this.chatLinkAdmin = adminData
      setTimeout(() => {
        document.getElementById('chatLink').click()
      }, 200)
    },
    changePage (val) {
      if (val === '/') {
        this.$EventBus.$emit('LinkPage', 'ext_buyer', '')
        localStorage.removeItem('roleUserAdmin')
        this.$router.push({ path: `${val}` }).catch(() => {})
      } else {
        this.$router.push({ path: `${val}` }).catch(() => {})
      }
    },
    Gopage (val) {
      // console.log(val)
      this.$router.push({ path: `${val.path}` }).catch(() => {})
    },
    ChangeActiveMenu (val) {
      this.activeMenu = val
    },
    SelectPathAdmin () {
      this.DashboardActive = false
      this.DashboardTransport = false
      this.ManageUser = false
      this.ManageShopAndCompany = false
      this.ManageShipping = false
      this.ManageLandingPage = false
      this.ChatAdmin = false
      this.AdminOrder = false
      this.AdminPromotion = false
      this.AdminAffiliate = false
      this.AdminERP = false
      this.AdminMarketplacePartner = false
      this.AdminReach = false
      this.ManagePayment = false
      this.defaultSelectDashboard = null
      this.defaultSelectManageShopAndCompany = null
      this.defaultSelectDashboardTransport = null
      this.defaultSelectManageUser = null
      this.defaultSelectChatAdmin = null
      this.defaultSelectLandingPage = null
      this.defaultSelectManageAffiliate = null
      this.defaultSelectManageOrder = null
      this.defaultSelectManagePromotion = null
      this.defaultSelectManageERP = null
      this.defaultSelectManageShipping = null
      this.defaultSelectManageMarketplacePartner = null
      this.defaultSelectManageReach = null
      // Dashboard Admin NGC
      if (this.$router.currentRoute.name === 'dashboardShopAdmin') {
        this.DashboardActive = true
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectDashboard = 0
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageUser = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'dashboardJV') {
        this.DashboardActive = true
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectDashboard = 1
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageUser = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'dashboardUserActive') {
        this.DashboardActive = true
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectDashboard = 2
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageUser = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'DashboardAdminSearch') {
        this.DashboardActive = true
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageShipping = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = 3
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'dashboardOTOP') {
        this.DashboardActive = true
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageShipping = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = 4
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'dashboardTransport') {
        // Dashboard ขนส่ง
        this.DashboardActive = false
        this.DashboardTransport = true
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectDashboardTransport = 0
        this.defaultSelectManageUser = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'pendingOrderStage') {
        this.DashboardActive = false
        this.DashboardTransport = true
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectDashboardTransport = 1
        this.defaultSelectManageUser = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'pendingOrderInfo') {
        this.DashboardActive = false
        this.DashboardTransport = true
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectDashboardTransport = 2
        this.defaultSelectManageUser = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
        // จัดการผู้ใช้ NGC
      } else if (this.$router.currentRoute.name === 'adminPanitManage') {
        // จัดการผู้ใช้ NGC
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = true
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageUser = 0
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
        // Manage Shop And Company
      } else if (this.$router.currentRoute.name === 'adminUserWeb') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = true
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageUser = 1
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'manageUserShop') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = true
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageAffiliate = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = 2
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'manageUserCompany') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = true
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageAffiliate = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = 3
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'adminShopManage' || this.$router.currentRoute.name === 'editShopAdmin') {
        // จัดการร้านค้าและบริษัท
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = true
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageShopAndCompany = 0
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'adminBusinessManage') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = true
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageShopAndCompany = 1
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'stockAdmin') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = true
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageShopAndCompany = 2
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'eTaxAdmin') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = true
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageShopAndCompany = 3
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'dashboardStatusShop') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = true
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageShopAndCompany = 4
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'DashboardProductShop') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = true
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageShopAndCompany = 5
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'ManageCategory') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = true
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageShopAndCompany = 6
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
        // Menage Landing Page
      } else if (this.$router.currentRoute.name === 'ManageRegisterShop' || this.$router.currentRoute.name === 'ManageRegisterShopDetail') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = true
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageShipping = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = 7
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'ManageRegisterInfoPartner' || this.$router.currentRoute.name === 'ManageRegisterInfoPartnerDetail') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = true
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageShipping = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = 8
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'ManageRegisterInfoShop' || this.$router.currentRoute.name === 'ManageRegisterInfoShopDetail') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = true
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageShipping = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = 9
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'manageShipping') {
        // จัดการขนส่งระบบ
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = true
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageShipping = 0
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'manageOrderShipping') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = true
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageShipping = 1
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'UpdateShippingAddress') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = true
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageShipping = 2
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'adminBannerManage' || this.$router.currentRoute.name === 'BigBannerEdit' || this.$router.currentRoute.name === 'BigBannerAEdit' || this.$router.currentRoute.name === 'BannerBEdit' || this.$router.currentRoute.name === 'BannerCEdit') {
        // จัดการ Landing Page
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = true
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectLandingPage = 0
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'adminFlashsaleManage' || this.$router.currentRoute.name === 'EditImagesFlashSale') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = true
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectLandingPage = 1
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'groupStore' || this.$router.currentRoute.name === 'manageGroupStore') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = true
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectLandingPage = 2
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'manageListStore') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = true
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectLandingPage = 3
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'AdminManagePopup') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = true
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectLandingPage = 4
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'adminGroupShopBGManage') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = true
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectLandingPage = 5
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'customBanner') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = true
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectLandingPage = 6
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'recommentProductManage') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = true
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageShipping = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = 7
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'orderJV') {
        // รายการสั่งซื้อ
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = true
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageOrder = 0
        this.defaultSelectManagePromotion = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'SearchOrder') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = true
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageOrder = 1
        this.defaultSelectManagePromotion = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'CreateOrderJVERP') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = true
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageOrder = 2
        this.defaultSelectManagePromotion = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'adminManageCoupon') {
        // จัดการโปรโมชัน
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = true
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageShipping = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = 0
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'adminManageNotification') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = true
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageShipping = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = 1
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'userJoinAffiliate') {
        // โปรแกรม affiliate
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = true
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageAffiliate = 0
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'sellerJoinAffiliate') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = true
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageAffiliate = 1
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'userJoinSellerAffiliate') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = true
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageAffiliate = 2
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'reportCommissionAffiliateAdmin') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = true
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageAffiliate = 3
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShipping = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'ManageERP') {
        // ERP
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = true
        this.AdminMarketplacePartner = false
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageERP = 0
        this.defaultSelectDashboardTransport = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageShipping = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'AdminManageProductPartner' || this.$router.currentRoute.name === 'DetailProductShopPartner') {
        // Software Marketplace
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = true
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageMarketplacePartner = 0
        this.defaultSelectManageShipping = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'AdminManageShopPartner') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = true
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageMarketplacePartner = 1
        this.defaultSelectManageShipping = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'managePositionBussiness') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = true
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageMarketplacePartner = 2
        this.defaultSelectManageShipping = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'AdminAddUserManualAndSeminarLink') {
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = true
        this.AdminReach = false
        this.ManagePayment = false
        this.defaultSelectManageMarketplacePartner = 3
        this.defaultSelectManageShipping = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageReach = null
      } else if (this.$router.currentRoute.name === 'AdminReach') {
        // Reach
        this.DashboardActive = false
        this.DashboardTransport = false
        this.ManageUser = false
        this.ManageShopAndCompany = false
        this.ManageShipping = false
        this.ManageLandingPage = false
        this.ChatAdmin = false
        this.AdminOrder = false
        this.AdminPromotion = false
        this.AdminAffiliate = false
        this.AdminERP = false
        this.AdminMarketplacePartner = false
        this.AdminReach = true
        this.ManagePayment = false
        this.defaultSelectManageMarketplacePartner = null
        this.defaultSelectManageShipping = null
        this.defaultSelectDashboardTransport = null
        this.defaultSelectLandingPage = null
        this.defaultSelectManageERP = null
        this.defaultSelectManageShopAndCompany = null
        this.defaultSelectManageUser = null
        this.defaultSelectDashboard = null
        this.defaultSelectChatAdmin = null
        this.defaultSelectManageAffiliate = null
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePromotion = null
        this.defaultSelectManageReach = 0
      }
      // console.log(this.checkCreateShop)
    }
  }
}
</script>

<style scoped>
.v-application ul, .v-application ol {
    padding: 0px 0px !important;
}
.v-application ol, .v-application ul {
    padding: 0px 0px !important;
}
</style>
