<template>
  <div class="text-center">
    <v-dialog v-model="openModalSettingTier" :width="MobileSize ? '100%' : IpadSize ? '100%' : '782'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-form ref="FormSettingTier" :lazy-validation="lazy">
          <!-- <v-toolbar dark dense elevation="0" color="#BDE7D9">
            <v-row>
              <v-col class="d-flex justify-space-around">
                <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobil' : ''"><b>{{ title }}</b></span></v-toolbar-title>
              </v-col>
            </v-row>
            <v-btn fab small @click="cancel()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
          </v-toolbar> -->
          <v-card-text class="px-0 pt-0">
            <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 782px'" class="backgroundHead" style="position: absolute; height: 120px;">
              <v-row style="height: 120px;">
                <v-col style="text-align: center;" class="pt-4">
                  <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ title }}</b></span>
                </v-col>
                <v-btn fab small @click="cancel()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '782px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 20px 20px 10px 20px;' : 'padding: 40px 48px 10px 48px;'">
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" class="d-flex" v-if="!MobileSize">
                      <v-row dense class="mr-auto">
                        <v-img src="@/assets/Create_Store/partnerShopDetail.png" max-height="62" max-width="62"></v-img>
                        <span class="pt-5 pl-4" style="font-weight: 600; color: #333333; font-size: 16px;"> ข้อมูลเทียร์ของร้านค้า </span>
                      </v-row>
                      <span class="ml-auto pt-3" style="text-align: end;" v-if="actions === 'edit'">
                        <v-row dense>
                          <span class="mt-2 pr-2" style="font-weight: 400; color: #333333; font-size: 16px;">สถานะการใช้งาน :</span>
                          <v-switch color="#52C41A" class="mt-0 px-1 pt-2" inset v-model="changeStatus" @click="updateStatusSettingTier(changeStatus)"></v-switch>
                          <span class="mt-1" v-if="data.status === 'active'">
                            <v-chip color="#F0FEE8" text-color="#52C41A">กำลังใช้งาน</v-chip>
                          </span>
                          <span class="mt-1" v-else>
                            <v-chip color="#FEE7E8" text-color="#F5222D">ยกเลิกใช้งาน</v-chip>
                          </span>
                        </v-row>
                      </span>
                    </v-col>
                    <v-col cols="12" v-else>
                      <v-row dense>
                        <v-img src="@/assets/Create_Store/partnerShopDetail.png" max-height="62" max-width="62"></v-img>
                        <span class="pt-5 pl-4" style="font-weight: 600; color: #333333; font-size: 14px;"> ข้อมูลเทียร์ของร้านค้า </span>
                      </v-row>
                      <v-row class="pt-3 pl-4" v-if="actions === 'edit'">
                        <v-row dense>
                          <span class="mt-2 pr-2" style="font-weight: 400; color: #333333; font-size: 14px;">สถานะการใช้งาน :</span>
                          <v-switch color="#52C41A" class="mt-0 px-1 pt-2" inset v-model="changeStatus" @click="updateStatusSettingTier(changeStatus)"></v-switch>
                          <span class="mt-1" v-if="data.status === 'active'">
                            <v-chip color="#F0FEE8" text-color="#52C41A">กำลังใช้งาน</v-chip>
                          </span>
                          <span class="mt-1" v-else>
                            <v-chip color="#FEE7E8" text-color="#F5222D">ยกเลิกใช้งาน</v-chip>
                          </span>
                        </v-row>
                      </v-row>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="7" sm="7">
                      <span>ชื่อกลุ่มคู่ค้า<span style="color: red;">*</span></span>
                      <v-text-field style="border-radius: 8px;" class="input_text namedoc_input" placeholder="ระบุชื่อ" outlined dense v-model="data.tier_name" :rules="Rules.empty" @keypress="CheckSpacebar($event)"></v-text-field>
                    </v-col>
                    <!-- oninput="this.value = this.value.replace(/^0/, '')" -->
                    <v-col cols="12" md="5" sm="5">
                      <span>ส่วนลด<span style="color: red;">*</span></span>
                      <v-text-field style="border-radius: 8px;" type="number" :disabled="UseDiscount === 'Y' ? false : true" class="input_text namedoc_input" placeholder="ระบุส่วนลด" outlined dense v-model="data.discount_percent" :rules="Rules.percentPattern">
                        <v-icon slot="append" color="#c6c6c6">mdi-percent-outline</v-icon>
                      </v-text-field>
                    </v-col>
                    <v-col cols="12">
                      <v-row dense>
                        <v-col cols="9">
                          <span class="fontStyleTitle" :style="MobileSize ? 'font-size: 14px;' : ''">ไม่ใช้ส่วนลดสำหรับคู่ค้านี้</span>
                        </v-col>
                        <v-col cols="3">
                          <v-radio-group row v-model="UseDiscount" class="mt-0 float-left" hide-details>
                            <v-radio value="Y" color="#27AB9C" label="ใช่"></v-radio>
                            <v-radio value="N" color="#27AB9C" label="ไม่ใช่"></v-radio>
                          </v-radio-group>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12">
                      <v-row dense>
                        <v-col cols="9">
                          <span class="fontStyleTitle" :style="MobileSize ? 'font-size: 14px;' : ''">ต้องการกำหนดเอกสารในการขอเป็นคู่ค้าหรือไม่</span><br/>
                          <span style="font-size: 12px; font-weight: 400; color: #CCCCCC; line-height: 16px;">*ถ้าต้องการกำหนดเอกสารจะบังคับให้แสดงข้อมูลเป็นใช่</span>
                        </v-col>
                        <v-col cols="3">
                          <v-radio-group row v-model="data.req_document" class="mt-0 float-left" hide-details>
                            <v-radio value="Y" color="#27AB9C" label="ใช่"></v-radio>
                            <v-radio value="N" color="#27AB9C" label="ไม่ใช่"></v-radio>
                          </v-radio-group>
                        </v-col>
                        <v-card v-if="data.req_document === 'Y'" class="pa-5" elevation="0" width="100%" height="100%" style="background: #F2F2F2; border-radius: 8px;">
                          <v-row>
                            <v-col cols="6" class="pa-3">
                              <span style="font-weight: bold; line-height: 26px; color: #333333;" :style="MobileSize ? 'font-size: 14px;' : 'font-size: 18px;'">รายการเอกสารที่ร้องขอ</span>
                            </v-col>
                            <v-col cols="6" align="end" class="pa-3">
                              <v-btn text @click="addDocumentName()" color="#27AB9C" style="color: #27AB9C;">
                                <v-icon left color="#27AB9C">mdi-plus</v-icon>เพิ่มข้อมูล
                              </v-btn>
                            </v-col>
                          </v-row>
                          <v-card-text class="px-0">
                            <v-row class="mt-3" v-for="(item, index) in data.tier_document_list" :key="index">
                              <v-col cols="12" class="py-0">
                                <span class="mb-0">เอกสาร</span>
                              </v-col>
                              <v-col :cols="MobileSize ? 10 : 11" class="py-0">
                                <v-text-field class="namedoc_input" v-model="item.name_document" placeholder="ระบุชื่อเอกสาร" outlined dense :rules="Rules.empty" @keypress="CheckSpacebar($event)"></v-text-field>
                              </v-col>
                              <v-col :cols="MobileSize ? 2 : 1" class="pl-0 py-0">
                                <v-btn @click="deleteDocumentName(index)" elevation="0" icon><v-icon color="#757D8A">mdi-delete-outline</v-icon></v-btn>
                              </v-col>
                            </v-row>
                          </v-card-text>
                          <span style="font-size: 12px; color: #767676;"><span style="color: red;">*</span> กรอบการกำหนดเอกสาร</span>
                        </v-card>
                      </v-row>
                    </v-col>
                    <v-col cols="12">
                      <v-row dense>
                        <v-col cols="9">
                          <span class="fontStyleTitle">ต้องการแสดงข้อมูลให้ผู้ซื้อเห็น</span>
                        </v-col>
                        <v-col cols="3">
                          <v-radio-group row v-model="data.show_buyer" class="mt-0 float-left" hide-details :rules="Rules.emptyCheckbox">
                            <v-radio value="Y" color="#27AB9C" label="ใช่"></v-radio>
                            <v-radio value="N" color="#27AB9C" label="ไม่ใช่"></v-radio>
                          </v-radio-group>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
          </v-card-text>
          <v-card-actions>
            <v-row dense style="height: 88px; background: #F5FCFB;" :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
              <v-btn rounded width="125" height="40" class="my-auto" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
              <v-spacer></v-spacer>
              <v-btn rounded width="125" height="40" class="white--text my-auto" color="#27AB9C" @click="openAwaitModal()">บันทึก</v-btn>
            </v-row>
          </v-card-actions>
        </v-form>
        <!-- MobileSize -->
        <!-- <v-container v-if="MobileSize" grid-list-xs>
          <v-row class="px-5">
            <v-col cols="12">
              <v-img class="float-left" src="@/assets/icons/File.png" contain width="60px" height="60px"></v-img>
              <v-card-title style="font-weight: bold; font-size: 16px; line-height: 26px; color: #333333;">ข้อมูลเทียร์ของร้านค้า</v-card-title>
            </v-col>
          </v-row>
          <v-form ref="FormSettingTier" :lazy-validation="lazy">
            <v-row class="px-5" no-gutters>
              <v-col cols="12" class="mt-6 pb-0">
                <span>ชื่อกลุ่มคู่ค้า<span style="color: red;">*</span></span>
              </v-col>
              <v-col cols="12">
                <v-text-field class="input_text namedoc_input" placeholder="ระบุชื่อ" outlined dense v-model="data.tier_name" :rules="Rules.empty" @keypress="CheckSpacebar($event)"></v-text-field>
              </v-col>
              <v-col cols="12" class="pb-0">
                <span>ส่วนลด<span style="color: red;">*</span></span>
              </v-col>
              <v-col cols="12">
                <v-text-field type="number" class="input_text namedoc_input" placeholder="ระบุส่วนลด" outlined dense v-model="data.discount_percent" :rules="Rules.percentPattern" oninput="this.value = this.value.replace(/^0/, '')">
                  <v-icon slot="append" color="#c6c6c6">mdi-percent-outline</v-icon>
                </v-text-field>
              </v-col>
              <v-col cols="12" class="mt-3">
                <span>ต้องการกำหนดเอกสารในการขอเป็นคู่ค้าหรือไม่</span>
              </v-col>
              <v-col cols="12" class="mb-0 pb-0">
                <v-checkbox align="start" class="checkbox-qu float-left" dense v-model="data.req_document" label="ใช่" value="Y" @click="changeSettingShowBuyer()" :rules="Rules.emptyCheckbox"></v-checkbox>
                <v-checkbox align="start" class="checkbox-qu float-left pl-3" dense v-model="data.req_document" label="ไม่" value="N" :rules="Rules.emptyCheckbox" @click="changeSettingShowBuyer()"></v-checkbox>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 10px; color: #767676;"><span style="color: red;">*</span> ถ้าต้องการกำหนดเอกสาร จะบังคับให้แสดงข้อมูลเป็น ใช่</span>
              </v-col>
              <v-card v-if="data.req_document === 'Y'" class="pa-5" elevation="0" width="100%" height="100%" style="background: #F2F2F2; border-radius: 8px;">
                <v-row>
                  <v-col cols="6" class="pa-3">
                    <span style="font-weight: bold; font-size: 18px; line-height: 26px; color: #333333;">รายการเอกสารที่ร้องขอ</span>
                  </v-col>
                  <v-col cols="6" align="end" class="pa-3">
                    <v-btn text @click="addDocumentName()" color="#27AB9C" style="color: #27AB9C;">
                      <v-icon left color="#27AB9C">mdi-plus</v-icon>เพิ่มข้อมูล
                    </v-btn>
                  </v-col>
                </v-row>
                <v-card-text class="px-0">
                  <v-row class="mt-3" v-for="(item, index) in data.tier_document_list" :key="index">
                    <v-col cols="12" class="py-0">
                      <span class="mb-0">เอกสาร</span>
                    </v-col>
                    <v-col :cols="MobileSize ? 10 : 11" class="py-0">
                      <v-text-field class="namedoc_input" v-model="item.name_document" placeholder="ระบุชื่อเอกสาร" outlined dense :rules="Rules.empty" @keypress="CheckSpacebar($event)"></v-text-field>
                    </v-col>
                    <v-col :cols="MobileSize ? 2 : 1" class="pl-0 py-0">
                      <v-btn @click="deleteDocumentName(index)" elevation="0" icon><v-icon color="#757D8A">mdi-delete-outline</v-icon></v-btn>
                    </v-col>
                  </v-row>
                </v-card-text>
                <span style="font-size: 12px; color: #767676;"><span style="color: red;">*</span> กรอบการกำหนดเอกสาร</span>
              </v-card>
              <v-col cols="12" class="mt-5">
                <span>ต้องการแสดงข้อมูลให้ผู้ซื้อเห็นหรือไม่</span>
              </v-col>
              <v-col cols="12">
                <v-checkbox :readonly="data.req_document === 'Y'" class="checkbox-qu float-left" dense v-model="data.show_buyer" label="ใช่" value="Y" :rules="Rules.emptyCheckbox"></v-checkbox>
                <v-checkbox :readonly="data.req_document === 'Y'" class="checkbox-qu float-left pl-3" dense v-model="data.show_buyer" label="ไม่" value="N" :rules="Rules.emptyCheckbox"></v-checkbox>
              </v-col>
            </v-row>
          </v-form>
          <v-card-actions class="mt-5" :class="actions === 'edit' ? 'px-5' : 'px-0'">
            <v-spacer></v-spacer>
            <v-btn outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
            <v-btn v-if="actions === 'edit' && data.status === 'active'" class="px-5 white--text" color="#D1392B" @click="updateStatusSettingTier('inactive')">ยกเลิกใช้งาน</v-btn>
            <v-btn v-if="actions === 'edit' && data.status === 'inactive'" class="px-5 white--text" color="#1AB759" @click="updateStatusSettingTier('active')">เปิดใช้งาน</v-btn>
            <v-btn class="white--text" color="#27AB9C" @click="sendSettingTier()">บันทึก</v-btn>
          </v-card-actions>
        </v-container> -->
        <!-- desktopSize -->
        <!-- <v-container v-else grid-list-xs>
          <v-row class="px-5">
            <v-col cols="12">
              <v-img class="float-left" src="@/assets/icons/File.png" contain width="60px" height="60px"></v-img>
              <v-card-title style="font-weight: bold; font-size: 18px; line-height: 26px; color: #333333;">ข้อมูลเทียร์ของร้านค้า</v-card-title>
            </v-col>
          </v-row>
          <v-form ref="FormSettingTier" :lazy-validation="lazy">
            <v-row class="px-5" no-gutters>
              <v-col cols="9" class="mt-9 pb-0 pr-3">
                <span>ชื่อกลุ่มคู่ค้า<span style="color: red;">*</span></span>
              </v-col>
              <v-col cols="3" class="mt-9 pb-0">
                <span>ส่วนลด<span style="color: red;">*</span></span>
              </v-col>
              <v-col cols="9">
                <v-text-field class="input_text namedoc_input pr-3" placeholder="ระบุชื่อ" outlined dense v-model="data.tier_name" :rules="Rules.empty" @keypress="CheckSpacebar($event)"></v-text-field>
              </v-col>
              <v-col cols="3">
                <v-text-field type="number" class="input_text namedoc_input" placeholder="ระบุส่วนลด" outlined dense v-model="data.discount_percent" :rules="Rules.percentPattern" oninput="this.value = this.value.replace(/^0/, '')">
                  <v-icon slot="append" color="#c6c6c6">mdi-percent-outline</v-icon>
                </v-text-field>
              </v-col>
              <v-col cols="12">
                <span>ต้องการกำหนดเอกสารในการขอเป็นคู่ค้าหรือไม่</span>
                <v-checkbox align="end" class="float-right pl-3" dense v-model="data.req_document" label="ไม่" value="N" :rules="Rules.emptyCheckbox" @click="changeSettingShowBuyer()"></v-checkbox>
                <v-checkbox align="end" class="float-right pl-3" dense v-model="data.req_document" label="ใช่" value="Y" @click="changeSettingShowBuyer()" :rules="Rules.emptyCheckbox"></v-checkbox>
                <br><span style="font-size: 12px; color: #767676;"><span style="color: red;">*</span> ถ้าต้องการกำหนดเอกสาร จะบังคับให้แสดงข้อมูลเป็น ใช่</span>
              </v-col>
              <v-card v-if="data.req_document === 'Y'" class="pa-5" elevation="0" width="100%" height="100%" style="background: #F2F2F2; border-radius: 8px;">
                <v-row>
                  <v-col cols="6" class="pa-3">
                    <span style="font-weight: bold; font-size: 18px; line-height: 26px; color: #333333;">รายการเอกสารที่ร้องขอ</span>
                  </v-col>
                  <v-col cols="6" align="end" class="pa-3">
                    <v-btn text @click="addDocumentName()" color="#27AB9C" style="color: #27AB9C;">
                      <v-icon left color="#27AB9C">mdi-plus</v-icon>เพิ่มข้อมูล
                    </v-btn>
                  </v-col>
                </v-row>
                <v-card-text class="px-0">
                  <v-row class="mt-3" v-for="(item, index) in data.tier_document_list" :key="index">
                    <v-col cols="12" class="py-0">
                      <span class="mb-0">เอกสาร</span>
                    </v-col>
                    <v-col :cols="MobileSize ? 10 : 11" class="py-0">
                      <v-text-field class="namedoc_input" v-model="item.name_document" placeholder="ระบุชื่อเอกสาร" outlined dense :rules="Rules.empty" @keypress="CheckSpacebar($event)"></v-text-field>
                    </v-col>
                    <v-col :cols="MobileSize ? 2 : 1" class="pl-0 py-0">
                      <v-btn @click="deleteDocumentName(index)" elevation="0" icon><v-icon color="#757D8A">mdi-delete-outline</v-icon></v-btn>
                    </v-col>
                  </v-row>
                </v-card-text>
                <span style="font-size: 12px; color: #767676;"><span style="color: red;">*</span> กรอบการกำหนดเอกสาร</span>
              </v-card>
              <v-col cols="12" class="mt-5">
                <span>ต้องการแสดงข้อมูลให้ผู้ซื้อเห็นหรือไม่</span>
                <v-checkbox :readonly="data.req_document === 'Y'" align="end" class="float-right pl-3" dense v-model="data.show_buyer" label="ไม่" value="N" :rules="Rules.emptyCheckbox"></v-checkbox>
                <v-checkbox :readonly="data.req_document === 'Y'" align="end" class="float-right pl-3" dense v-model="data.show_buyer" label="ใช่" value="Y" :rules="Rules.emptyCheckbox"></v-checkbox>
              </v-col>
            </v-row>
          </v-form>
          <v-card-actions class="px-5">
            <v-spacer></v-spacer>
            <v-btn outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
            <v-btn v-if="actions === 'edit' && data.status === 'active'" class="px-5 white--text" color="#D1392B" @click="updateStatusSettingTier('inactive')">ยกเลิกใช้งาน</v-btn>
            <v-btn v-if="actions === 'edit' && data.status === 'inactive'" class="px-5 white--text" color="#1AB759" @click="updateStatusSettingTier('active')">เปิดใช้งาน</v-btn>
            <v-btn class="white--text" color="#27AB9C" @click="sendSettingTier()">บันทึก</v-btn>
          </v-card-actions>
        </v-container> -->
      </v-card>
    </v-dialog>
    <!-- Await Tier -->
    <v-dialog v-model="dialogAwaitTier" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeDialogAwait()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ actions === 'edit' ? 'แก้ไขข้อมูลกลุ่มคู่ค้า' : 'เพิ่มกลุ่มคู่ค้า' }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text class="px-0">
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '125' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeDialogAwait()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '125' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="sendSettingTier()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Tier -->
    <v-dialog v-model="dialogSuccessTier" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeModalSuccess()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ actions === 'edit' ? 'แก้ไขข้อมูลกลุ่มคู่ค้าเรียบร้อย' : 'เพิ่มกลุ่มคู่ค้าเรียบร้อย' }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ actions === 'edit' ? 'คุณได้ทำการแก้ไขข้อมูลกลุ่มคู่ค้าเรียบร้อย' : 'คุณได้ทำการเพิ่มกลุ่มคู่ค้าเรียบร้อย' }}</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" :block="MobileSize ? true : false" class="white--text" rounded color="#27AB9C" @click="closeModalSuccess()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
// import { Decode } from '@/services'
// import eventBus from '@/components/eventBus'
export default {
  // props: ['ModalTierDetail'],
  data () {
    return {
      dialogAwaitTier: false,
      dialogSuccessTier: false,
      changeStatus: false,
      checkShopName: '',
      listData: '',
      lazy: false,
      title: '',
      openModalSettingTier: false,
      disabledCheckbox: false,
      actions: '',
      UseDiscount: 'Y',
      data: {
        tier_name: '',
        discount_percent: '',
        show_buyer: '',
        req_document: '',
        tier_document_list: []
      },
      updateDocumentList: [],
      Rules: {
        empty: [v => !!v || 'กรุณากรอกข้อมูล'],
        emptyCheckbox: [v => !!v || ''],
        percentPattern: [
          v => !!v || 'กรุณาเลือกข้อมูล',
          v => v <= 100 || 'กรอกจำนวนที่มีค่าไม่เกิน 100'
        ]
      }
    }
  },
  watch: {
    UseDiscount (val) {
      if (val === 'N') {
        this.data.discount_percent = '0'
      } else {
        this.data.discount_percent = ''
      }
    }
  },
  mounted () {
  },
  created () {
    this.checkShopToAdd()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    CheckSpacebar (e) {
      if (e.keyCode === 32) {
        e.preventDefault()
      }
    },
    open (action, data) {
      this.openModalSettingTier = true
      this.actions = action
      if (this.actions === 'edit') {
        this.title = 'แก้ไขข้อมูลกลุ่มคู่ค้า'
        this.data = data
        // console.log(this.data)
        if (this.data.status === 'active') {
          this.changeStatus = true
        } else {
          this.changeStatus = false
        }
        if (this.data.discount_percent === '0.00') {
          this.UseDiscount = 'N'
        } else {
          this.UseDiscount = 'Y'
        }
      } else {
        this.title = 'เพิ่มข้อมูลกลุ่มคู่ค้า'
      }
    },
    cancel () {
      this.openModalSettingTier = false
      if (this.actions === 'edit') {
        this.$EventBus.$emit('cancelProcessGetNewData')
      }
      this.clearData()
    },
    clearData () {
      this.$refs.FormSettingTier.resetValidation()
      this.$refs.FormSettingTier.reset()
    },
    addDocumentName () {
      const data = { name_document: '' }
      if (this.actions === 'create') {
        this.data.tier_document_list.push(data)
      } else {
        if (this.data.tier_document_list === null) {
          this.data.tier_document_list = []
          this.data.tier_document_list.push(data)
        } else {
          this.data.tier_document_list.push(data)
        }
      }
    },
    deleteDocumentName (indexData) {
      this.data.tier_document_list.splice(indexData, 1)
    },
    changeSettingShowBuyer () {
      if (this.data.req_document === 'Y') {
        this.data.show_buyer = 'Y'
        this.disabledCheckbox = true
      } else {
        // this.data.show_buyer = ''
        this.disabledCheckbox = false
      }
    },
    closeDialogAwait () {
      this.dialogAwaitTier = false
    },
    checkShopToAdd () {
      const shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      this.checkShopName = shopDetail.name
      // console.log('checkShopName', this.checkShopName)
    },
    sendSettingTier () {
      // if (this.$refs.FormSettingTier.validate(true)) {
      if (this.actions === 'create') {
        if (this.data.req_document === 'Y' && this.data.tier_document_list.length === 0) {
          this.$swal.fire({ showConfirmButton: false, timer: 3000, timerProgressBar: true, icon: 'warning', title: 'ตรวจสอบข้อมูลอีกครั้ง', text: 'เนื่องคุณกำหนดเอกสารในการขอเป็นคู่ค้าเป็น "ใช่" ดังนั้นจำเป็นต้องเพิ่มเอกสารที่ร้องขออย่างน้อย 1 รายการ' })
        } else if (this.checkShopName === this.data.tier_name) {
          this.openModalSettingTier = false
          this.$swal.fire({ showConfirmButton: false, timer: 3000, timerProgressBar: true, icon: 'warning', title: 'ตรวจสอบข้อมูลอีกครั้ง', text: 'ไม่สามารถเพิ่มบริษัทตัวเองได้' })
        } else {
          this.addSettingTier()
        }
      } else {
        if (this.data.req_document === 'Y' && this.data.tier_document_list.length === 0) {
          this.$swal.fire({ showConfirmButton: false, timer: 3000, timerProgressBar: true, icon: 'warning', title: 'ตรวจสอบข้อมูลอีกครั้ง', text: 'เนื่องคุณกำหนดเอกสารในการขอเป็นคู่ค้าเป็น "ใช่" ดังนั้นจำเป็นต้องเพิ่มเอกสารที่ร้องขออย่างน้อย 1 รายการ' })
        } else if (this.checkShopName === this.data.tier_name) {
          this.openModalSettingTier = false
          this.$swal.fire({ showConfirmButton: false, timer: 3000, timerProgressBar: true, icon: 'warning', title: 'ตรวจสอบข้อมูลอีกครั้ง', text: 'ไม่สามารถแก้ไขเป็นบริษัทตัวเองได้' })
        } else {
          this.editSettingTier()
        }
      }
      // } else {
      //   this.$swal.fire({ showConfirmButton: false, timer: 3000, timerProgressBar: true, icon: 'warning', title: 'ตรวจสอบข้อมูลอีกครั้ง', text: 'กรุณากรอกข้อมูลให้ครบ' })
      // }
    },
    openAwaitModal () {
      if (this.$refs.FormSettingTier.validate(true)) {
        this.openModalSettingTier = !this.openModalSettingTier
        this.dialogAwaitTier = true
      }
    },
    async addSettingTier () {
      this.dialogAwaitTier = false
      this.$store.commit('openLoader')
      const shopId = localStorage.getItem('shopSellerID')
      const data = {
        seller_shop_id: shopId,
        tier_name: this.data.tier_name,
        discount_percent: parseInt(this.data.discount_percent),
        show_buyer: this.data.show_buyer,
        req_document: this.data.req_document,
        tier_document_list: this.data.req_document === 'Y' ? this.data.tier_document_list : []
      }
      await this.$store.dispatch('actionsCreateTier', data)
      const res = await this.$store.state.ModuleSettingTier.stateCreateTier
      if (res.message === 'Add tier and extra document success') {
        this.$store.commit('closeLoader')
        this.dialogSuccessTier = true
        // this.$swal.fire({
        //   showConfirmButton: false,
        //   timer: 1500,
        //   timerProgressBar: true,
        //   icon: 'success',
        //   title: 'ดำเนินการสำเร็จ'
        // })
        // this.$EventBus.$emit('settingTierSuccess')
        // this.clearData()
        // this.openModalSettingTier = !this.openModalSettingTier
      } else if (res.message === 'Cannot update status because some partner use this tier') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: 'ไม่สามารถอัปเดตสถานะได้เนื่องจากมีผู้ใช้งานอยู่'
        })
        this.changeStatus = true
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่'
        })
      } else if (res.message === 'Your tier can insert maximum 20 tiers.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: 'ไม่สามารถเพิ่มกลุ่มคู่ค้าเกิน 20 กลุ่มคู่ค้าได้'
        })
      } else {
        this.$store.commit('closeLoader')
        if (res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 7000,
            timerProgressBar: true,
            icon: 'error',
            title: 'ดำเนินการไม่สำเร็จ',
            text: `${res.message}`
          })
        }
      }
    },
    closeModalSuccess () {
      if (this.actions === 'edit') {
        this.$EventBus.$emit('UpdatesettingTierSuccess')
      } else {
        this.$EventBus.$emit('settingTierSuccess')
      }
      this.clearData()
      this.dialogSuccessTier = false
    },
    async editSettingTier () {
      this.dialogAwaitTier = false
      this.$store.commit('openLoader')
      const shopId = localStorage.getItem('shopSellerID')
      const data = {
        seller_shop_id: shopId,
        tier_manage_id: this.data.id,
        tier_name: this.data.tier_name,
        discount_percent: parseInt(this.data.discount_percent),
        show_buyer: this.data.show_buyer,
        req_document: this.data.req_document,
        tier_document_list: this.data.req_document === 'Y' ? this.data.tier_document_list : []
      }
      await this.$store.dispatch('actionsEditTier', data)
      const res = await this.$store.state.ModuleSettingTier.stateEditTier
      if (res.message === 'Update tier and extra document success') {
        this.$store.commit('closeLoader')
        this.dialogSuccessTier = true
        // this.$swal.fire({
        //   showConfirmButton: false,
        //   timer: 1500,
        //   timerProgressBar: true,
        //   icon: 'success',
        //   title: 'ดำเนินการสำเร็จ'
        // })
        // this.$EventBus.$emit('UpdatesettingTierSuccess')
        // this.clearData()
        // this.openModalSettingTier = !this.openModalSettingTier
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: `${res.message}`
        })
      }
    },
    async updateStatusSettingTier (status) {
      const data = {
        tier_manage_id: this.data.id,
        status: status === true ? 'active' : 'inactive'
      }
      await this.$store.dispatch('actionsUpdateStatusTier', data)
      const res = await this.$store.state.ModuleSettingTier.stateUpdateStatusTier
      if (res.message === 'Update status tier and extra document success') {
        this.$EventBus.$emit('UpdatesettingTierSuccess')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ดำเนินการสำเร็จ'
        })
        var setDataNew = {
          seller_shop_id: this.data.seller_shop_id,
          tier_manage_id: this.data.id
        }
        await this.$store.dispatch('actionsDetailTier', setDataNew)
        const res = await this.$store.state.ModuleSettingTier.stateDetailTier
        if (res.message === 'List tier and extra document success') {
          this.data = ''
          this.data = res.data[0]
        }
        // this.openModalSettingTier = false
      } else if (res.message === 'Cannot update status because some partner use this tier') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: 'ไม่สามารถยกเลิกการใช้งานได้เนื่องจากมีกลุ่มคู่ค้าใช้งานอยู่'
        })
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: `${res.message}`
        })
      }
    }
  }
}
</script>
<style lang="css" scoped>
.fontStyleData {
  font-size: 16px;
  color: #333333;
  font-weight: 700;
}
.fontStyleTitle {
  font-size: 16px;
  color: #333333;
  font-weight: 400;
}
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>

<style>
.namedoc_input .v-input__control .v-input__slot {
  background: #FFFFFF !important;
}
.namedoc_input .v-text-field input {
  font-size: 0.9em;
  background: #FFFFFF !important;
}
.v-text-field input {
  font-size: 0.9em;
  /* background: #FFFFFF !important; */
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: #27AB9C;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.title-mobil {
  font-size: 16px;
}
.checkbox-qu .v-messages {
  min-height: 0px;
}
</style>
